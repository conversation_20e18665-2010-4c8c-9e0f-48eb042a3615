import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { RouteKey, UserRouteType } from 'types/enum';

const Content = () => {
  const location = useLocation();
  const isSubscriptionPage =
    location.pathname === `/${UserRouteType.ADVISOR}/${RouteKey.SUBSCRIPTION}`;

  return (
    <div
      className={`w-full ${
        isSubscriptionPage ? '' : 'ml-[11.625rem] max-w-[calc(100%-11.625rem)]'
      } p-8 h-full bg-gray-01`}
    >
      <Outlet />
    </div>
  );
};

export default Content;
