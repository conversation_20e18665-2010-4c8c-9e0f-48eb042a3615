import React from 'react';
import { useSelector } from 'react-redux';
import Button from 'shared-resources/components/Button/Button';
import { getSentFollowUpText } from 'store/selectors/follow-up-selector';
import successImg from 'assets/followUpSuccess.png';

interface FollowUpSuccessModalProps {
  handleModalVisibility: (value: boolean) => void;
}
const FollowUpSuccessModal: React.FC<FollowUpSuccessModalProps> = (props) => {
  const { handleModalVisibility } = props;
  const sentFollowUpText = useSelector(getSentFollowUpText);

  return (
    <div className='flex flex-col'>
      <div className='flex flex-col items-center gap-10 p-9'>
        <div className='flex w-48 h-37.5'>
          <img
            src={successImg}
            className='max-w-full h-auto object-contain'
            alt='follow-up-sent'
          />
        </div>
        <h1 className='text-xl font-medium'>Quick Follow Up Sent !</h1>
        <p className='font-medium'>{sentFollowUpText}</p>
      </div>
      <Button
        onClick={() => {
          handleModalVisibility(false);
        }}
        className='py-4.5 w-full rounded-b-xl rounded-t-none'
      >
        Back
      </Button>
    </div>
  );
};

export default FollowUpSuccessModal;
