import {
  BUSINESS_READINESS_QUESTIONS_DATA,
  FINANCE_QUESTIONS_DATA,
  PLANNING_READINESS_QUESTIONS_DATA,
  TRANSITION_KNOWLEDGE_QUESTIONS_DATA,
  TRANSITION_OBJECTIVE_QUESTIONS_DATA,
} from 'views/contents/Readiness-Assessment/readinessAssessment.constant';
import {
  PlanningReadiness,
  ReadinessAssessmentTitle,
  TransitionKnowledge,
} from '../../utils/Readiness-Assessment/ReadinessAssessmentEnums';

type Response = { [key: string]: number };

interface Question {
  id: string;
  ques: string;
  insight: string;
}

export const calculateCategoryPercentage = (
  assessmentResponse: Response,
  questionData: Question[]
) => {
  if (!assessmentResponse || !questionData) {
    return 0;
  }

  let sum = 0;
  const maxValue = 6;

  questionData?.forEach((question) => {
    const response = assessmentResponse[question.id];
    sum += response;
  });

  const totalPercentage = (sum / (questionData.length * maxValue)) * 100;
  return parseFloat(totalPercentage.toFixed(2));
};

export const calculateOverallPercentage = (
  assessmentResponse: any // Assuming assessmentResponse is of type 'any'
): number => {
  const allQuestions = [
    BUSINESS_READINESS_QUESTIONS_DATA,
    FINANCE_QUESTIONS_DATA,
    TRANSITION_OBJECTIVE_QUESTIONS_DATA,
    TRANSITION_KNOWLEDGE_QUESTIONS_DATA,
    PLANNING_READINESS_QUESTIONS_DATA,
  ];

  let totalPercentage = 0;

  allQuestions.forEach((questionData) => {
    totalPercentage += calculateCategoryPercentage(
      assessmentResponse,
      questionData
    );
  });

  return parseFloat((totalPercentage / allQuestions.length).toFixed(2));
};

export const getResponseById = (
  assessmentResponse: Response,
  questionId: string
): number | undefined => {
  // Check if assessmentResponse is null or undefined
  if (!assessmentResponse) {
    return undefined; // return undefined if assessmentResponse is null or undefined
  }

  return assessmentResponse[questionId];
};

export const calculateCompletedPercentage = (response: Response) => {
  if (!response || Object.keys(response).length === 0) {
    return 0; // Return 0 if data is null, undefined, or an empty object
  }

  const filledKeys = Object.keys(response).length;
  return (filledKeys / 25) * 100;
};

const neglectInsight = (id: string): boolean => {
  const neglectInsightKeys: string[] = [
    ...Object.values(TransitionKnowledge),
    ...Object.values(PlanningReadiness),
  ];

  return neglectInsightKeys.includes(id);
};

export const getSubjectInfo = (
  response: Response,
  quesData: Question[],
  page: 1 | 2
) => {
  if (!response || Object.keys(response).length === 0) {
    return []; // Return 0 if data is null, undefined, or an empty object
  }

  const returnValue: {
    title?: string;
    description: string;
    insight?: string;
  }[] = [];
  const goodResultNumberIds: string[] = [];
  const badResultNumberIds: string[] = [];

  quesData.forEach((ques) => {
    if (response[ques.id] && response[ques.id] > 4) {
      goodResultNumberIds.push(ques.id);
    } else if (response[ques.id] <= 4) {
      badResultNumberIds.push(ques.id);
    }
  });

  const getGoodQues = (index: number) =>
    quesData.find((q) => q.id === goodResultNumberIds[index]);
  const getBadQues = (index: number) =>
    quesData.find((q) => q.id === badResultNumberIds[index]);

  if (page === 1) {
    let i = 0;
    for (; i < goodResultNumberIds.length && i < 3; i += 1) {
      const ques = getGoodQues(i);
      returnValue.push({
        title:
          i === 0
            ? 'Subjects where you responded with above average preparation and/or understanding include:'
            : undefined,
        description: `${ques?.ques} ( Your response was ${getResponseById(
          response,
          goodResultNumberIds[i]
        )} )`,
        insight: neglectInsight(goodResultNumberIds[i])
          ? undefined
          : ques?.insight,
      });
    }
    if (i < 3) {
      for (let j = 0; j < badResultNumberIds.length && j < 3 - i; j += 1) {
        const ques = getBadQues(j);
        returnValue.push({
          title:
            j === 0
              ? 'Subjects for improved understanding and preparedness in this section include:'
              : undefined,
          description: `${ques?.ques} ( Your response was ${getResponseById(
            response,
            badResultNumberIds[j]
          )} )`,
          insight: ques?.insight,
        });
      }
    }
  }

  if (page === 2) {
    let i = 3;
    for (; goodResultNumberIds.length - i > 0; i += 1) {
      const ques = getGoodQues(i);
      returnValue.push({
        title: undefined,
        description: `${ques?.ques} ( Your response was ${getResponseById(
          response,
          goodResultNumberIds[i]
        )} )`,
        insight: neglectInsight(goodResultNumberIds[i])
          ? undefined
          : ques?.insight,
      });
    }
    for (
      let j =
        3 - goodResultNumberIds.length > 0 ? 3 - goodResultNumberIds.length : 0;
      j < badResultNumberIds.length;
      j += 1
    ) {
      const ques = getBadQues(j);
      returnValue.push({
        title:
          j === 0
            ? 'Subjects for improved understanding and preparedness in this section include:'
            : undefined,
        description: `${ques?.ques} ( Your response was ${getResponseById(
          response,
          badResultNumberIds[j]
        )} )`,
        insight: ques?.insight,
      });
    }
  }

  return returnValue;
};

export const getIndicatesText = (
  category: ReadinessAssessmentTitle,
  score?: number
) => {
  if (!score) {
    return '';
  }

  let indicatesString: string;

  if (category === ReadinessAssessmentTitle.BUSINESS_READINESS) {
    if (score < 40) {
      indicatesString =
        ' indicates that you have some work to do to prepare your business for a successful transition.';
    } else if (score >= 40 && score <= 60) {
      indicatesString =
        ' indicates that you have done some preparation of your business for a successful transition, but still have more to do.';
    } else {
      indicatesString =
        ' indicates that your business is prepared for transition in some areas, but there is still work to be done to achieve a successful transition.';
    }
  } else if (category === ReadinessAssessmentTitle.FINANCE) {
    if (score < 40) {
      indicatesString =
        ' indicates that you have some work to do in order to understand the key financial components of a successful transition.';
    } else if (score >= 40 && score <= 60) {
      indicatesString =
        ' indicates that you understand some of these concepts to be important to you and/or you are wanting to understand them better.';
    } else {
      indicatesString =
        ' indicates that you understand many of these concepts to be important; and, in fact, you have acted on some of them.';
    }
  } else if (category === ReadinessAssessmentTitle.TRANSITION_OBJECTIVES) {
    if (score < 40) {
      indicatesString =
        ' indicates that you have some work to do in order to understand and develop the personal objectives that will lead to your successful transition.';
    } else if (score >= 40 && score <= 60) {
      indicatesString =
        ' indicates that you understand that these concepts are important to you and/or you are wanting to understand them better.';
    } else {
      indicatesString =
        ' indicates that you understand the importance of your Transition Objectives; and, in fact, you have acted on some of them.';
    }
  } else if (category === ReadinessAssessmentTitle.TRANSITION_KNOWLEDGE) {
    if (score < 40) {
      indicatesString =
        ' indicates that you have some work to do to understand a variety of transition subjects.';
    } else if (score >= 40 && score <= 60) {
      indicatesString =
        ' indicates that some of these subjects are important to you and/or you are wanting to understand them better.';
    } else {
      indicatesString =
        ' indicates that you understand a variety of transition subjects and, in fact, have acted on some of them.';
    }
  } else if (score < 40) {
    indicatesString =
      ' indicates that you have a lot of planning work to do in order to be positioned for successful transition.';
  } else if (score >= 40 && score <= 60) {
    indicatesString =
      ' indicates that some of these planning subjects are important to you and/or you are wanting to understand them better.';
  } else {
    indicatesString =
      ' indicates that you understand a variety of planning subjects; and, in fact, you have acted on some of them.';
  }

  return indicatesString;
};

export const convertToSnakeCase = (inputString: string) => {
  // Convert input string to lowercase
  const lowerCaseString = inputString.toLowerCase();
  // Replace spaces with underscores
  return lowerCaseString.replace(/[\s-]+/g, '_');
};
