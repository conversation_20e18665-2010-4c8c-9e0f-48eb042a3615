import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  AssessmentStage,
  AssessmentToolProgressStatus,
  AssessmentTools,
  UserType,
} from 'types/enum';
import { getToolName, planDevelopmentTabs } from 'utils/helpers/Helpers';
import classNames from 'classnames';
import { getUserData } from 'store/selectors/user.selector';
import { getBusinessOwnerDetails } from 'store/selectors/business-owner.selector';
import { useParamSelector } from 'store/selectors/base.selectors';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import { fetchBusinessOwner } from 'store/actions/business-owner.action';
import { useLocation } from 'react-router';
import TooltipComponent from 'shared-resources/components/Tooltip/TooltipComponent';

interface ToolSidebarProps {
  onToolSelect: (tool: AssessmentTools) => void;
  disabled?: boolean;
}

const ToolSidebar: React.FC<ToolSidebarProps> = ({
  onToolSelect,
  disabled,
}) => {
  const userData = useSelector(getUserData);
  const location = useLocation();
  const ownerId = location.pathname.split('/')[3];
  const businessOwner = useParamSelector(getBusinessOwnerDetails, {
    id: ownerId,
  }) as BusinessOwner;
  const dispatch = useDispatch();

  useEffect(() => {
    if (userData && userData?.type === UserType.ADVISOR) {
      dispatch(fetchBusinessOwner(ownerId));
    }
  }, [dispatch, userData]);

  const tabDataMap = Object.values(AssessmentTools).map((tool) => {
    const awarenessData = Object.keys(
      businessOwner?.assessment_data?.awareness ?? {}
    );
    const planDevelopmentData = Object.keys(
      businessOwner?.assessment_data?.plan_development ?? {}
    );
    const stage = planDevelopmentTabs.includes(tool)
      ? AssessmentStage.PLAN_DEVELOPMENT
      : AssessmentStage.AWARENESS;
    const toolStatus =
      businessOwner?.assessment_data?.[stage]?.[tool]?.progress_status;

    const isDisabled = ![...awarenessData, ...planDevelopmentData].includes(
      tool
    );
    if (tool === AssessmentTools.FINANCIAL_GAP_ANALYSIS) {
      return {
        name: 'Wealth Gap Analysis',
        value: tool,
        disabled: isDisabled,
        isCompleted: toolStatus === AssessmentToolProgressStatus.COMPLETED,
        status: toolStatus,
      };
    }
    if (tool === AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE) {
      return {
        name: 'Buyer Type-Deal Structure',
        value: tool,
        disabled: isDisabled,
        isCompleted: toolStatus === AssessmentToolProgressStatus.COMPLETED,
        status: toolStatus,
      };
    }
    return {
      name: tool.replace(/_/g, ' '),
      value: tool,
      disabled: isDisabled,
      isCompleted: toolStatus === AssessmentToolProgressStatus.COMPLETED,
      status: toolStatus,
    };
  });

  return (
    <div
      className={classNames(
        'w-[20%] rounded-lg max-h-[calc(100vh-17rem)] overflow-auto no-scrollbar bg-gray-01 flex flex-col gap-4 p-4',
        { 'pointer-events-none opacity-60': disabled }
      )}
    >
      {Object.values(AssessmentTools)
        .filter(
          (tool) => !tabDataMap?.find((tab) => tab.value === tool)?.disabled
        )
        .map((tool) => {
          const isButtonDisabled = !tabDataMap.find((tab) => tab.value === tool)
            ?.isCompleted;

          const status = tabDataMap.find((tab) => tab.value === tool)?.status;
          const button = (
            <div key={tool} className='relative'>
              {status !== AssessmentToolProgressStatus.COMPLETED && (
                <TooltipComponent
                  anchorSelect={`#${tool}`}
                  place='top'
                  offset={0}
                >
                  <div>Incomplete Tool</div>
                </TooltipComponent>
              )}
              <div id={`${tool}`}>
                <button
                  key={tool}
                  className={classNames('w-full text-left p-2 rounded-md', {
                    'hover:bg-blue-02 hover:text-blue-01': !isButtonDisabled,
                    'opacity-50 cursor-not-allowed': isButtonDisabled,
                  })}
                  onClick={() => !isButtonDisabled && onToolSelect(tool)}
                  disabled={isButtonDisabled}
                >
                  {getToolName(tool)}
                </button>
              </div>
            </div>
          );

          return button;
        })}
    </div>
  );
};

export default ToolSidebar;
