import { createSelector } from '@reduxjs/toolkit';
import { AppState } from 'store';

const getReportBuilderState = (state: AppState) => state.reportBuilder;

export const getReports = createSelector(
  getReportBuilderState,
  (reportBuilder) => reportBuilder.reports
);

export const getCurrentReport = createSelector(
  getReportBuilderState,
  (reportBuilder) => reportBuilder.currentReport
);

export const getReportBuilderLoading = createSelector(
  getReportBuilderState,
  (reportBuilder) => reportBuilder.loading
);

export const getReportBuilderError = createSelector(
  getReportBuilderState,
  (reportBuilder) => reportBuilder.error
);

export const getIsReportCreated = createSelector(
  getReportBuilderState,
  (reportBuilder) => reportBuilder.isReportCreated
);
