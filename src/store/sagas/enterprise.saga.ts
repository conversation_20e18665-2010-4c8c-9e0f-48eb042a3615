import { all, call, put, takeLatest } from 'redux-saga/effects';
import { enterpriseService } from 'services/api-services/EnterpriseService';
import { EnterpriseActionType } from 'store/actions/actions.constants';
import {
  getEnterprises,
  setGetEnterpriseLoading,
  setGetEnterpriseError,
  setEnterpriseLoading,
  setEnterpriseError,
  setEnterprisesPageData,
  setEnterprise,
} from 'store/reducers/enterprise.reducer';

function* enterprisesFetchSaga(action: any): any {
  const { filters, onSuccess, onError } = action.payload;
  try {
    yield put(setEnterpriseLoading({ loading: true }));
    if (!filters.search) {
      delete filters.search;
    }
    const response = yield call(
      enterpriseService.getEnterprisesList,
      filters
    );
    yield put(getEnterprises(response.data.data));
    yield put(setEnterprisesPageData({ paginationMeta: response.data }));
    yield put(setEnterpriseLoading({ loading: false }));
    if (onSuccess) {
      onSuccess(response.data.data);
    }
  } catch (e: any) {
    yield put(setEnterpriseError({
      error: true,
      message: e?.response?.data?.message || 'Failed to fetch enterprises'
    }));
    if (onError) {
      onError(e?.response?.data?.message || 'Failed to fetch enterprises');
    }
  }
}

function* enterpriseFetchSaga(action: any): any {
  const { id, onSuccess, onError } = action.payload;
  try {
    yield put(setGetEnterpriseLoading({ loading: true }));
    const response = yield call(
      enterpriseService.getEnterpriseById,
      id
    );

    yield put(setEnterprise(response.data));
    yield put(setGetEnterpriseLoading({ loading: false }));
    if (onSuccess) {
      onSuccess(response.data.data);
    }
  } catch (e: any) {
    yield put(setGetEnterpriseError({
      error: true,
      message: e?.response?.data?.message || 'Failed to fetch enterprise'
    }));
    if (onError) {
      onError(e?.response?.data?.message || 'Failed to fetch enterprise');
    }
  }
}

export function* enterpriseSagaWatcher() {
  yield all([
    takeLatest(
      EnterpriseActionType.GET_ENTERPRISES,
      enterprisesFetchSaga
    ),
    takeLatest(
      EnterpriseActionType.GET_ENTERPRISE,
      enterpriseFetchSaga
    ),
  ]);
}