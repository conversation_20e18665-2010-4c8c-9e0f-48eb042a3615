import React, { FC, useState } from 'react';
import { MdDelete } from 'react-icons/md';
import { AssessmentTools, InviteStatus } from 'types/enum';
import { titleCaseAndRemoveUnderScoreOrHyphen } from 'utils/helpers/Helpers';
import Modal from '../Modal/Modal';
import RevokeAccessModal from './Modals/RevokeAccessModal';
import { SelectOptionValue } from '../Select/Select';

export interface AdvisorType {
  id: number;
  email: string;
  tool: SelectOptionValue;
}

interface AdvisorsCardsTableProps {
  advisorsArray: AdvisorType[] | undefined;
  handleDelete: (id: number, status: InviteStatus) => void;
  heading: string;
  isSecondaryAdvisor?: boolean;
}
const AdvisorsCardsTable: FC<AdvisorsCardsTableProps> = ({
  advisorsArray,
  handleDelete,
  heading,
  isSecondaryAdvisor = false,
}) => {
  const [showPopUp, setShowPopUp] = useState(false);
  const [selectedAdvisor, setSelectedAdvisor] = useState<AdvisorType | null>(
    null
  );

  const getToolName = (tool: string) => {
    if (tool === AssessmentTools.FINANCIAL_GAP_ANALYSIS) {
      return 'Wealth Gap Analysis';
    }
    if (tool === AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE) {
      return 'Buyer Type-Deal Structure';
    }
    return titleCaseAndRemoveUnderScoreOrHyphen(tool);
  };

  const handleRevokeOrCancel = () => {
    if (selectedAdvisor) {
      if (heading.includes('Pending')) {
        handleDelete(selectedAdvisor.id, InviteStatus.CANCELLED);
      } else {
        handleDelete(selectedAdvisor.id, InviteStatus.REVOKED);
      }
    }
  };

  const handleCloseModal = () => {
    setShowPopUp(false);
    setSelectedAdvisor(null);
  };

  return (
    <div>
      <h1 className='text-lg font-medium mb-2'>{heading}</h1>
      <div className='flex flex-col gap-2 overflow-auto scrollbar max-h-[10rem]'>
        {advisorsArray?.length ? (
          advisorsArray.map((advisor) => (
            <div
              key={advisor?.id}
              className='border rounded-lg p-4 bg-white shadow-md gap-1 flex py-3 justify-between items-center'
            >
              <div className='flex flex-col gap-1'>
                <h1 className='text-gray-900 text-wrap  break-words font-bold'>
                  {advisor.email}
                </h1>

                <h1 className='text-gray-900 text-sm '>
                  {getToolName(advisor?.tool?.toString())}
                </h1>
              </div>
              <div className='w-6'>
                <MdDelete
                  aria-label='delete'
                  size={24}
                  color='red'
                  className='w-full min-w-4 h-6 cursor-pointer'
                  onClick={() => {
                    setSelectedAdvisor(advisor);
                    setShowPopUp(true);
                  }}
                />
              </div>
            </div>
          ))
        ) : (
          <h1 className='text-center h-[8rem] pt-12 text-xl'>No data</h1>
        )}
      </div>
      <Modal
        title={isSecondaryAdvisor ? 'Revoke Invite' : 'Cancel Invite'}
        visible={showPopUp}
        handleVisibility={handleCloseModal}
        classname='w-[45%]'
      >
        <RevokeAccessModal
          handleModalVisibility={handleCloseModal}
          isSecondaryAdvisor={isSecondaryAdvisor}
          handleToggle={handleRevokeOrCancel}
        />
      </Modal>
    </div>
  );
};

export default AdvisorsCardsTable;
