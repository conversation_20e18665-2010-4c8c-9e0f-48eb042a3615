import { Form, Formik, FormikValues } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import FormikRadio from 'shared-resources/components/Radio/FormikRadio';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import * as yup from 'yup';
import {
  Screen13Keys,
  YesNoOptions,
  getQuestionBykey,
} from '../AssetProtectionConfig';

interface Props {
  data: any;
  handleNextClick: (values: FormikValues) => void;
  handlebackClick: (values: FormikValues) => void;
  handleSaveAsDraftClick: (values: FormikValues) => void;
}
const Screen13: React.FC<Props> = ({
  handleNextClick,
  handlebackClick,
  handleSaveAsDraftClick,
  data,
}) => {
  const validationSchema = yup.object().shape({
    [Screen13Keys.DOES_THE_BUSINESS_HAVE_ANY_PROPRIETARY]: yup
      .string()
      .required('Value is required'),
    [Screen13Keys.DO_YOU_HAVE_COPYRIGHT_PROTECTION]: yup
      .string()
      .when([Screen13Keys.DOES_THE_BUSINESS_HAVE_ANY_PROPRIETARY], {
        is: (value: string) => value === 'yes',
        then: (schema) => schema.required('value is Required'),
        otherwise: (schema) => schema.nullable(),
      }),
  });

  const initialValue = data?.screen13 || {
    [Screen13Keys.DOES_THE_BUSINESS_HAVE_ANY_PROPRIETARY]: '',
    [Screen13Keys.DO_YOU_HAVE_COPYRIGHT_PROTECTION]: '',
  };

  const isSubmitting = useSelector(getAssessmentToolLoading);

  return (
    <div className='flex flex-col h-full  gap-6 pl-10 py-10 font-medium'>
      <Formik
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={(values: FormikValues) => {
          handleNextClick(values);
        }}
        enableReinitialize
      >
        {({ values, setFieldValue, setTouched }) => (
          <Form className='flex flex-col justify-between '>
            <div className='h-[calc(100vh-20rem)] overflow-auto scrollbar'>
              <div>
                <div className='mt-4'>
                  <span className='font-semibold'>
                    {getQuestionBykey(
                      Screen13Keys.DOES_THE_BUSINESS_HAVE_ANY_PROPRIETARY
                    )}
                  </span>

                  <div className='mt-5 xl:w-[50%]'>
                    <FormikRadio
                      labelClassName='!cursor-pointer'
                      className2='!w-fit'
                      valueChanged={() => {
                        setTouched({
                          [Screen13Keys.DOES_THE_BUSINESS_HAVE_ANY_PROPRIETARY]:
                            false,
                        });

                        setFieldValue(
                          Screen13Keys.DO_YOU_HAVE_COPYRIGHT_PROTECTION,
                          ''
                        );
                      }}
                      className='grid grid-cols-2 gap-4'
                      name={Screen13Keys.DOES_THE_BUSINESS_HAVE_ANY_PROPRIETARY}
                      options={YesNoOptions}
                    />
                  </div>
                </div>
                {values[Screen13Keys.DOES_THE_BUSINESS_HAVE_ANY_PROPRIETARY] ===
                  'yes' && (
                  <div className='mt-4'>
                    <span className='font-semibold'>
                      {getQuestionBykey(
                        Screen13Keys.DO_YOU_HAVE_COPYRIGHT_PROTECTION
                      )}
                    </span>

                    <div className='mt-5 xl:w-[50%]'>
                      <FormikRadio
                        labelClassName='!cursor-pointer'
                        className2='!w-fit'
                        className='grid grid-cols-2 gap-4'
                        name={Screen13Keys.DO_YOU_HAVE_COPYRIGHT_PROTECTION}
                        options={YesNoOptions}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className='pr-5'>
              <BackNextComponent
                backStep={() => handlebackClick(values)}
                buttonType='submit'
                isLoading={isSubmitting}
                isNextDisable={false}
                onSaveToDraftClick={() => {
                  handleSaveAsDraftClick(values);
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Screen13;
