import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface DocumentsState {
  entities: { [key: string]: any };
  loading?: boolean;
  listLoading?: boolean;
  error?: string;
  valuationAmount: number | null;
  valuationAmountLoading: boolean;
  valuationAmountError: string | null;
}

const initialState: DocumentsState = {
  entities: {},
  valuationAmount: null,
  valuationAmountLoading: false,
  valuationAmountError: null,
};

const documentsSlice = createSlice({
  name: 'documents',
  initialState,
  reducers: {
    setDocuments(state, action: PayloadAction<{ [key: string]: any }>) {
      state.loading = false;
      state.entities = action.payload;
    },
    deleteDocument(state, action: PayloadAction<string>) {
      state.loading = false;
      state.entities = state.entities.filter(
        (doc: any) => doc.id !== action.payload
      );
    },
    setDocumentsError(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    setLoading: (state, action: PayloadAction<{ loading: boolean }>) => {
      const { loading } = action.payload;
      state.loading = loading;
    },
    setListLoading: (state, action: PayloadAction<{ loading: boolean }>) => {
      const { loading } = action.payload;
      state.listLoading = loading;
    },
    setValuationAmount: (state, action: PayloadAction<number>) => {
      state.valuationAmount = action.payload;
      state.valuationAmountLoading = false;
      state.valuationAmountError = null;
    },
    setValuationAmountLoading: (state, action: PayloadAction<boolean>) => {
      state.valuationAmountLoading = action.payload;
    },
    setValuationAmountError: (state, action: PayloadAction<string>) => {
      state.valuationAmountError = action.payload;
    },
  },
});

export const {
  setLoading: setDocumentsLoading,
  setDocuments,
  deleteDocument: deleteDocumentAction,
  setDocumentsError,
  setListLoading: setDocumentsListLoading,
  setValuationAmount,
  setValuationAmountLoading,
  setValuationAmountError,
} = documentsSlice.actions;

export default documentsSlice.reducer;
