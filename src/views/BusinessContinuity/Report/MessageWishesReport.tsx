import React, { forwardRef } from 'react';
import { getKey } from 'utils/helpers/Helpers';

interface MessageWishesReportProps {
  data: any[];
}
const MessageWishesReport = forwardRef(
  (props: MessageWishesReportProps, ref: React.Ref<HTMLDivElement>) => {
    const { data } = props;

    return (
      <div ref={ref} className='flex flex-col gap-4 mt-6 w-full'>
        <span className='text-lg font-semibold block text-blue-01 text-center'>
          Message Wishes
        </span>
        <div className='flex flex-col gap-2'>
          {data.map((item, index) => (
            <div
              key={`${item?.message?.slice(0, 6)}${getKey(index)}`}
              className='flex py-2 px-4 border border-gray-02 rounded-lg min-h-28'
            >
              {item?.message}
            </div>
          ))}
        </div>
      </div>
    );
  }
);

MessageWishesReport.displayName = 'MessageWishesReport';
export default MessageWishesReport;
