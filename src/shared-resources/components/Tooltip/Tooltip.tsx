import React, { FC, ReactElement } from 'react';
import RCTooltip from 'rc-tooltip';
import 'rc-tooltip/assets/bootstrap.css';
// types
import {
  TooltipPlacement,
  TooltipTrigger,
} from 'shared-resources/types/Tooltip.type';

interface TooltipProps {
  children: ReactElement;
  overlay: React.ReactNode;
  arrow?: boolean;
  trigger?: TooltipTrigger[];
  placement?: TooltipPlacement;
  zIndex?: number;
  visible?: boolean;
  overlayInnerStyle?: React.CSSProperties;
  overlayClassname: string;
}

const Tooltip: FC<TooltipProps> = ({
  children,
  overlay,
  arrow,
  trigger,
  placement,
  zIndex,
  visible,
  overlayInnerStyle,
  overlayClassname,
}) => (
  <RCTooltip
    placement={placement}
    visible={visible}
    trigger={trigger}
    showArrow={arrow}
    overlay={overlay}
    zIndex={zIndex}
    overlayInnerStyle={overlayInnerStyle}
    overlayClassName={overlayClassname}
  >
    {children}
  </RCTooltip>
);

export default Tooltip;
