import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import 'react-tooltip/dist/react-tooltip.css';
import { RouteKey, UserRouteType } from 'types/enum';
import { SidebarRoutesConfigType } from 'types/routes/routes.type';
import Title from './Title/Title';

interface SidebarItemProps {
  sidebarRoute: SidebarRoutesConfigType;
  disabled?: boolean;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
  sidebarRoute,
  disabled,
}) => {
  const [itemSelected, setItemSelected] = useState(false);
  const navigate = useNavigate();
  const [submenuOpen, setSubmenuOpen] = useState(false);
  const location = useLocation();
  const key = sidebarRoute.key.replaceAll('/', '_');

  useEffect(() => {
    if (
      location.pathname.includes(
        `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}`
      )
    ) {
      setSubmenuOpen(true);
    } else {
      setSubmenuOpen(false);
    }
  }, [location.pathname]);

  const dashboardToolMap: Record<string, string> = {
    [RouteKey.BUSINESS_CONTINUITY_DASHBOARD]: RouteKey.BUSINESS_CONTINUITY,
    [RouteKey.READINESS_ASSESSMENT_DASHBOARD]: RouteKey.READINESS_ASSESSMENT,
    [RouteKey.OWNER_RELIANCE_DASHBOARD]: RouteKey.OWNER_RELIANCE,
    [RouteKey.BUSINESS_VALUATION_DASHBOARD]: RouteKey.BUSINESS_VALUATION,
    [RouteKey.TRANSITION_OBJECTIVES_DASHBOARD]: RouteKey.TRANSITION_OBJECTIVES,
    [RouteKey.STAKEHOLDER_ALIGNMENT_MEETING_DASHBOARD]:
      RouteKey.STAKEHOLDER_ALIGNMENT_MEETING,
  };

  const routeKeySplittedList = sidebarRoute.key.split('/');
  const isAssessmentActive =
    location.pathname.includes(sidebarRoute.key) ||
    location.pathname.includes(
      dashboardToolMap[routeKeySplittedList[routeKeySplittedList.length - 1]]
    );

  return (
    <div className='flex flex-col w-full justify-center items-center'>
      <Title
        Icon={sidebarRoute.icon!}
        title={sidebarRoute.name}
        classNames={`${
          isAssessmentActive && 'text-blue-01'
        } ${key}-item text-black-02  
          ${
            disabled
              ? 'cursor-default opacity-50 '
              : 'hover:text-blue-01 cursor-pointer hover:font-medium'
          }`}
        onClick={() => {
          if (!disabled) {
            setItemSelected(!itemSelected);
            setSubmenuOpen(!submenuOpen);
            navigate(sidebarRoute.key);
          }
        }}
      />
      {sidebarRoute.childRoutes &&
        submenuOpen &&
        sidebarRoute.childRoutes?.map((childRoute) => (
          <div key={childRoute.key}>
            <SidebarItem
              sidebarRoute={childRoute}
              disabled={!childRoute.enabled}
            />
          </div>
        ))}
    </div>
  );
};

export default SidebarItem;
