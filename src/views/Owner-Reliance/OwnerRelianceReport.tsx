import React, { useEffect, useState } from 'react';
import { FaDownload } from 'react-icons/fa6';
import { IoArrowBackSharp } from 'react-icons/io5';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import NavigateContainer from 'shared-resources/components/NavigateContainer';
import {
  fetchAssessmentReport,
  fetchAssessmentReportByBusinessOwner,
} from 'store/actions/assessment-report.action';
import {
  getAssessmentReportLoading,
  getAssessmentReportResponse,
} from 'store/selectors/assessment-report.selector';
import { getUserData } from 'store/selectors/user.selector';
import {
  AssessmentTools,
  OwnerRelianceTabs,
  RouteKey,
  UserRouteType,
  UserType,
} from 'types/enum';

import DarkThemeReportWrapper from 'HOC/DarkThemeReportWrapper';
import LightThemeReportWrapper from 'HOC/LightThemeReportWrapper';
import _ from 'lodash';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import ReportGenerationModal from 'shared-resources/components/BusinessOwner/Modals/ReportGenerationModal';
import Button from 'shared-resources/components/Button/Button';
import Modal from 'shared-resources/components/Modal/Modal';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import { generatePdf } from 'utils/generate-pdf/generatePdf-utils';
import { getOrderedAwarenessAssessmentData } from 'utils/helpers/Helpers';
import CoverPage from 'views/ReadinessAssessment/ReadinessAssessmentReport/CoverPage';
import IntroductionPage from 'views/ReadinessAssessment/ReadinessAssessmentReport/IntrodunctionPage';
import OverallScorePage from 'views/ReadinessAssessment/ReadinessAssessmentReport/OverallScorePage';
import { resetAssessmentReportData } from 'store/reducers/assessment-report.reducer';
import { getTitle } from './OwnerRelianceConfig';
import {
  calculateOwnerRelianceOverallScore,
  calculateOwnerReliancePercentageScore,
} from './OwnerRelianceReportHelper';
import TableComponent from './TableComponent';
import DisclaimerPage from '../Common/Disclaimer';

const OwnerRelianceReport = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const response: any = useSelector(getAssessmentReportResponse);
  const loading = useSelector(getAssessmentReportLoading);
  const loggedInUserData = useSelector(getUserData);

  useEffect(() => {
    if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      const orderedAwarenessData = getOrderedAwarenessAssessmentData(
        loggedInUserData as BusinessOwner
      );
      const canSeeReport = orderedAwarenessData.find(
        (tool) => tool.toolName === AssessmentTools.OWNER_RELIANCE
      )?.toolData.is_report_sent_to_owner;

      if (!canSeeReport) {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.OWNER_RELIANCE_DASHBOARD}`
        );
      }
    }
  }, [loggedInUserData]);

  useEffect(() => {
    if (id && loggedInUserData?.type === UserType.ADVISOR) {
      dispatch(
        fetchAssessmentReport({
          id,
          tool: AssessmentTools.OWNER_RELIANCE,
          onError: () =>
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`),
        })
      );
    }
    if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchAssessmentReportByBusinessOwner({
          tool: AssessmentTools.OWNER_RELIANCE,
          onError: () =>
            navigate(
              `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.OWNER_RELIANCE_DASHBOARD}`
            ),
        })
      );
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  // cleanup function to reset the assessment report data
  useEffect(
    () => () => {
      dispatch(resetAssessmentReportData());
    },
    []
  );

  const [companyName, setCompanyName] = useState<string>();

  const [ownerRelianceScore, setOwnerRelianceScore] = useState<
    | {
        name: OwnerRelianceTabs;
        score: number;
      }[]
    | undefined
  >();

  const overallScore = calculateOwnerRelianceOverallScore(
    response.assessment_response
  );

  const scores = [
    {
      name: OwnerRelianceTabs.PRESIDENT_CEO,
      score: calculateOwnerReliancePercentageScore(
        response.assessment_response[OwnerRelianceTabs.PRESIDENT_CEO]
      ).scorePercentage,
    },
    {
      name: OwnerRelianceTabs.SALES_MARKETING,
      score: calculateOwnerReliancePercentageScore(
        response.assessment_response[OwnerRelianceTabs.SALES_MARKETING]
      ).scorePercentage,
    },
    {
      name: OwnerRelianceTabs.FINANCE,
      score: calculateOwnerReliancePercentageScore(
        response.assessment_response[OwnerRelianceTabs.FINANCE]
      ).scorePercentage,
    },
    {
      name: OwnerRelianceTabs.OPERATIONS,
      score: calculateOwnerReliancePercentageScore(
        response.assessment_response[OwnerRelianceTabs.OPERATIONS]
      ).scorePercentage,
    },
    {
      name: OwnerRelianceTabs.HUMAN_RESOURCES,
      score: calculateOwnerReliancePercentageScore(
        response.assessment_response[OwnerRelianceTabs.HUMAN_RESOURCES]
      ).scorePercentage,
    },
    {
      name: OwnerRelianceTabs.RESEARCH_AND_DEVELOPMENT,
      score: calculateOwnerReliancePercentageScore(
        response.assessment_response[OwnerRelianceTabs.RESEARCH_AND_DEVELOPMENT]
      ).scorePercentage,
    },
    {
      name: OwnerRelianceTabs.INFORMATION_TECHNOLOGY,
      score: calculateOwnerReliancePercentageScore(
        response.assessment_response[OwnerRelianceTabs.INFORMATION_TECHNOLOGY]
      ).scorePercentage,
    },
    {
      name: OwnerRelianceTabs.OTHERS,
      score: calculateOwnerReliancePercentageScore(
        response.assessment_response[OwnerRelianceTabs.OTHERS]
      ).scorePercentage,
    },
  ];

  useEffect(() => {
    if (!_.isEmpty(response.assessment_response)) {
      setOwnerRelianceScore(scores.slice(0, 7));
      setCompanyName(
        response?.assessment_response?.management_team?.business_name
      );
    }
  }, [response]);

  const [isDownloading, setIsDownloading] = useState<boolean>(false);

  const dividedOthersTabData: any = []; // divided rows in 16 group each
  let subArray = [];

  for (let i = 0; i < response.assessment_response.others?.length; i += 1) {
    const element = {
      ...response.assessment_response.others[i],
    };
    subArray.push(element);

    // If subArray size reaches 16 or end of originalArray is reached
    if (
      subArray.length === 16 ||
      i === (response.assessment_response.others?.length ?? 0) - 1
    ) {
      dividedOthersTabData.push(subArray);
      subArray = []; // Reset subArray for the next group
    }
  }

  const downloadReport = async () => {
    setIsDownloading(true);
    const reportPages: any[] = [
      document.getElementById(`cover-page`),
      document.getElementById(`introduction-page`),
      document.getElementById(`overall-score-page`),
    ];

    ownerRelianceScore?.forEach((_s, index) =>
      reportPages.push(document.getElementById(`score-result-${index}`))
    );

    dividedOthersTabData.forEach((_: any, index: number) =>
      reportPages.push(document.getElementById(`others-score-result-${index}`))
    );
    reportPages.push(document.getElementById(`disclaimer-page`));
    const reportName = `OwnerRelianceReport.pdf`;
    await generatePdf(
      reportPages,
      reportName,
      () => setIsDownloading(false),
      'portrait'
    );
  };

  const pageBreak = <div className='my-9' />;

  if (loading) {
    return <Spinner customClassName='' spinnerTheme='overlaySpinner' />;
  }

  return (
    <div className='w-full h-[calc(100vh-9.6875rem)]'>
      <div className='flex items-center justify-between w-full mb-5'>
        <NavigateContainer
          isEnableToolRoute
          className='min-w-7.5 text-black-02'
        >
          <IoArrowBackSharp size={24} />
        </NavigateContainer>
        <div>
          <Button
            className='py-2 px-6'
            LeadingIcon={FaDownload}
            leadingIconClassName='mr-2'
            onClick={downloadReport}
          >
            Download
          </Button>
        </div>
      </div>
      <div className='flex flex-col items-center h-[calc(100%-2.75rem)] overflow-y-auto pr-2 scrollbar '>
        <div className='m-auto'>
          <DarkThemeReportWrapper id='cover-page'>
            <CoverPage
              pageTitle='Owner Reliance Report'
              presentedBy={response?.advisor_name}
              preparedFor={response?.business_owner_data?.business_owner_name}
              reportDate={response?.assessment_completion_date}
              phone={response?.business_owner_data?.business_owner_phone}
              email={response?.business_owner_data?.business_owner_email}
              companyName={companyName}
            />
          </DarkThemeReportWrapper>
        </div>
        {pageBreak}
        <div className='m-auto'>
          <LightThemeReportWrapper id='introduction-page'>
            <IntroductionPage
              pageContent='This Owner Reliance report is extremely important to you in terms of understanding the number of decisions you make in your business. You can use it to identify which of the decisions that you make can be delegated to other Leadership Team members. One of the findings may be that no one on the current Leadership Team is qualified to take on ownership of some of the decisions that you make. This information then helps you to identify gaps in your Leadership Team that need to be addressed – as an integral part of increasing the value of the business.'
              titleClassName='text-blue-01'
              userName={response?.business_owner_data?.business_owner_name}
            />
          </LightThemeReportWrapper>
        </div>
        {pageBreak}
        <div className='m-auto'>
          <LightThemeReportWrapper id='overall-score-page'>
            <OverallScorePage
              isRendedForOwnerReliance
              description={
                <p className='font-[500]'>
                  Your overall Owner Reliance Score of
                  <span className='text-blue-01 font-semibold'>
                    {' '}
                    {overallScore}%{' '}
                  </span>
                  indicates you are making a high percentage of key decisions in the management of
                  your company.
                </p>
              }
              scores={scores.map((s) => ({
                name: getTitle(s.name),
                score: s.score,
              }))}
              overAllScore={overallScore}
            />
          </LightThemeReportWrapper>
        </div>
        {pageBreak}
        {ownerRelianceScore?.map((s, index) => (
          <div key={s.name}>
            <div className='m-auto'>
              <LightThemeReportWrapper id={`score-result-${index}`}>
                <div className='pl-10 pr-5'>
                  <TableComponent
                    showReportTitle
                    currentTab={s.name}
                    response={response?.assessment_response}
                    isRenderedForReport
                    scorePercentage={s.score}
                  />
                </div>
              </LightThemeReportWrapper>
            </div>
            {pageBreak}
          </div>
        ))}
        {dividedOthersTabData?.map((others: any, index: number) => (
          <div key={others?.selected_option}>
            <div>
              <LightThemeReportWrapper id={`others-score-result-${index}`}>
                <div className='pl-14 pr-5'>
                  <TableComponent
                    showReportTitle={index === 0}
                    currentTab={OwnerRelianceTabs.OTHERS}
                    response={{
                      others,
                      management_team:
                        response?.assessment_response.management_team,
                    }}
                    isRenderedForReport
                    scorePercentage={
                      calculateOwnerReliancePercentageScore(
                        response.assessment_response[OwnerRelianceTabs.OTHERS]
                      ).scorePercentage
                    }
                  />
                </div>
              </LightThemeReportWrapper>
            </div>
            {pageBreak}
          </div>
        ))}
        <div className='m-auto'>
          <LightThemeReportWrapper id='disclaimer-page'>
            <DisclaimerPage />
          </LightThemeReportWrapper>
        </div>
      </div>
      <Modal visible={isDownloading} handleVisibility={() => {}}>
        <ReportGenerationModal />
      </Modal>
    </div>
  );
};

export default OwnerRelianceReport;
