import React from 'react';
import { UserType } from 'types/enum';

export interface BaseRouteConfigType {
  name: string;
  key: string;
  path: string;
  allowedRoles: UserType[];
  childPathKey?: string;
}

export interface SidebarRoutesConfigType extends BaseRouteConfigType {
  icon?: any;
  component?: React.FC;
  isBottomItem?: boolean;
  isHover?: boolean;
}

export type WebRoutesType = Record<
  string,
  { [x: string]: (...args: any) => string }
>;
