import React from 'react';
import { Formik, Form } from 'formik';
import { useSelector } from 'react-redux';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import cx from 'classnames';
import * as yup from 'yup';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import { BuyerTypeScreens } from './BuyerTypeConfig';

interface Props {
  data: any;
  handleNextClick: (values: any) => void;
  handlebackClick: (values: any) => void;
  handleSaveAsDraftClick: (values: any) => void;
}

const BuyerNotIdentified: React.FC<Props> = ({
  data,
  handleNextClick,
  handleSaveAsDraftClick,
  handlebackClick,
}) => {
  const isLoading = useSelector(getAssessmentToolLoading);

  const validationSchema = yup.object().shape({
    buyer_type_choice: yup.string(),
    deal_details: yup.string(),
  });

  const initialValues = {
    buyer_type_choice:
      data?.[BuyerTypeScreens.NOT_IDENTIFIED]?.buyer_type_choice || undefined,
    deal_details:
      data?.[BuyerTypeScreens.NOT_IDENTIFIED]?.deal_details || undefined,
  };

  if (isLoading) {
    return <Spinner spinnerTheme='overlaySpinner' />;
  }

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={(values) => {
        handleNextClick(values);
      }}
      enableReinitialize
    >
      {({ values, isValid }) => (
        <Form
          className={cx(
            'flex flex-col justify-between h-[calc(100vh-14rem)] pb-5'
          )}
        >
          <div className='overflow-auto scrollbar rounded-lg px-10 py-8 flex flex-col space-y-6 pb-8'>
            <FormikInput
              name='buyer_type_choice'
              key='buyer_type_choice'
              label='Briefly describe your choices of buyer type:'
              labelClassName='mb-1 font-medium'
              fieldType='textarea'
              className='h-44'
            />

            <FormikInput
              name='deal_details'
              key='deal_details'
              label='Please briefly explain any deal details you might envision:'
              labelClassName='mb-1  font-medium'
              fieldType='textarea'
              className='h-44'
            />
          </div>
          <div className='px-10 bg-white'>
            <BackNextComponent
              backStep={() => handlebackClick(values)}
              buttonType='submit'
              nextStep={() => handleNextClick(values)}
              isLoading={isLoading}
              isNextDisable={!isValid}
              onSaveToDraftClick={() => handleSaveAsDraftClick(values)}
            />
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default BuyerNotIdentified;
