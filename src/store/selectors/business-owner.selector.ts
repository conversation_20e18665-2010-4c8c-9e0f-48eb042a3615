import { get } from 'lodash';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import { createSelector } from 'reselect';
import { AppState } from 'store';
import { createParamSelector } from './base.selectors';

const businessOwnerStore = (store: AppState) => store.businessOwner;

const getBusinessOwnerId = createParamSelector(
  (params: { id: number }) => params.id
);

export const allBusinessOwners = createSelector(
  [businessOwnerStore],
  (businessOwners) => {
    const ownersData = businessOwners.entities ?? {};
    if (businessOwners.ids) {
      return businessOwners.ids.map((id) => ownersData[id] as BusinessOwner);
    }
    return [];
  }
);

export const getBusinessOwner = createSelector(
  [allBusinessOwners, getBusinessOwnerId],
  (businessOwners, id) => get(businessOwners, [id])
);

export const businessOwnerListLoadingState = createSelector(
  [businessOwnerStore],
  (businessOwner) => businessOwner.list?.loading
);

export const businessOwnerPaginationMeta = createSelector(
  [businessOwnerStore],
  (businessOwner) => businessOwner.paginationMeta
);

export const businessOwnerListErrorState = createSelector(
  [businessOwnerStore],
  (businessOwner) => businessOwner.list?.error
);

export const getBusinessOwnerDetails = createSelector(
  [businessOwnerStore, getBusinessOwnerId],
  (businessOwnerData, ownerId) => {
    const { entities, assessmentDataMapping } = businessOwnerData;
    const businessOwner = entities[ownerId];

    if (businessOwner) {
      return {
        ...businessOwner,
        assessment_data: assessmentDataMapping[ownerId],
      };
    }
    return undefined;
  }
);

export const getBusinessOwnerDetailsLoading = createSelector(
  [businessOwnerStore],
  (businessOwnerData) => {
    const businessOwnerLoading = businessOwnerData.get?.loading;
    if (businessOwnerLoading) {
      return businessOwnerLoading;
    }
    return false;
  }
);

export const getCreateBusinessOwnerLoading = createSelector(
  [businessOwnerStore],
  (businessOwnerData) => {
    const createBusinessOwnerLoading = businessOwnerData.create?.loading;
    if (createBusinessOwnerLoading) {
      return createBusinessOwnerLoading;
    }
    return false;
  }
);

export const getBusinessOwnerIndustries = createSelector(
  [businessOwnerStore],
  (businessOwnerData) => businessOwnerData.industries.items
);

export const getBusinessOwnerIndustriesLoading = createSelector(
  [businessOwnerStore],
  (businessOwnerData) => businessOwnerData.industries.loading
);

export const createBusinessOwnerErrors = createSelector(
  [businessOwnerStore],
  (businessOwners) => businessOwners.create.errors
);

export const updateBusinessOwnerErrors = createSelector(
  [businessOwnerStore],
  (businessOwners) => businessOwners.update.errors
);

export const updateBusinessOwnerLoading = createSelector(
  [businessOwnerStore],
  (businessOwners) => businessOwners.update.loading
);

export const getBusinessOwnerLearnMoreLoading = createSelector(
  [businessOwnerStore],
  (businessOwnerData) => businessOwnerData.learnMoreLoading
);
