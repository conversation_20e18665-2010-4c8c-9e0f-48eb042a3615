import { BusinessOwner } from 'models/entities/BusinessOwner';
import React, { useState } from 'react';
import { FaCalendarCheck } from 'react-icons/fa';
import { FaSquarePhone } from 'react-icons/fa6';
import { useNavigate } from 'react-router';
import {
  AssessmentStage,
  AssessmentToolProgressStatus,
  AssessmentToolStatus,
  AssessmentTools,
  UserRouteType,
} from 'types/enum';
import {
  convertToTitleCase,
  getFormattedDate,
  getInitials,
  paymentNeededTabs,
  planDevelopmentTabs,
  titleCase,
} from 'utils/helpers/Helpers';
import { useSelector } from 'react-redux';
import { getUserData } from 'store/selectors/user.selector';
import TooltipComponent from '../Tooltip/TooltipComponent';
import Modal from '../Modal/Modal';
import ManageAccessModal from './ManageAccessModal';

interface BusinessOwnerCardProps {
  businessOwner: BusinessOwner;
  disableDetailView?: boolean;
}
const BusinessOwnerCard: React.FC<BusinessOwnerCardProps> = (props) => {
  const { businessOwner, disableDetailView } = props;
  const navigate = useNavigate();
  const userData = useSelector(getUserData);
  const [showSecondaryAdvisor, setShowSecondaryAdvisorPopUp] = useState(false);
  const tabsData = Object.values(AssessmentTools).map((tool) => {
    const awarenessData = Object.keys(
      businessOwner?.assessment_data?.awareness ?? {}
    );
    const planDevelopmentData = Object.keys(
      businessOwner?.assessment_data?.plan_development ?? {}
    );
    const isDisabled =
      businessOwner?.primary_advisor_id !== userData?.id &&
      ![...awarenessData, ...planDevelopmentData].includes(tool);
    return {
      name: tool.replace('_', ' '),
      value: tool,
      disabled: isDisabled,
    };
  });

  const getToolName = (tool: AssessmentTools) => {
    if (tool === AssessmentTools.FINANCIAL_GAP_ANALYSIS) {
      return 'Wealth Gap Analysis';
    }
    if (tool === AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE) {
      return 'Buyer Type-Deal Structure';
    }
    return convertToTitleCase(tool);
  };

  const progressIcons = () => {
    const icons: JSX.Element[] = [];
    const paymentIncompleteTabs = paymentNeededTabs.filter(
      (tab) => !businessOwner?.payment_details?.[tab]
    );
    const filtersTabsData = tabsData.filter(
      (tab) => !paymentIncompleteTabs.includes(tab.value)
    );
    filtersTabsData.forEach((tab) => {
      const stage = planDevelopmentTabs.includes(tab.value)
        ? AssessmentStage.PLAN_DEVELOPMENT
        : AssessmentStage.AWARENESS;
      const isTabEnabled =
        businessOwner?.assessment_data?.[stage]?.[tab.value]?.status ===
        AssessmentToolStatus.ENABLE;
      const isCompleted =
        businessOwner?.assessment_data?.[stage]?.[tab.value]
          ?.progress_status === AssessmentToolProgressStatus.COMPLETED;

      const progressStatus = isTabEnabled
        ? convertToTitleCase(
            businessOwner?.assessment_data?.[stage]?.[
              tab.value
            ]?.progress_status?.replace('_', ' ') as string
          )
        : 'Disabled';
      if (!tab.disabled) {
        icons.push(
          <div key={tab.value}>
            <TooltipComponent
              offset={20}
              anchorSelect={`.progress-icon-${tab.value}-${businessOwner.id}`}
              place='top-start'
              key={tab.value}
            >
              <div className='flex flex-col gap-1'>
                <span>Tool: {getToolName(tab.value)}</span>
                <span>Status: {progressStatus}</span>
              </div>
            </TooltipComponent>
            <img
              key={tab.name}
              src={
                isTabEnabled
                  ? '/icons/brand-icon.svg'
                  : '/icons/brand-icon-black.svg'
              }
              alt='brand-icon'
              className={`flex progress-icon-${tab.value}-${businessOwner.id} ${
                isTabEnabled && isCompleted ? 'opacity-100' : 'opacity-30'
              } w-5 h-5 mr-5`}
            />
          </div>
        );
      }
    });

    return icons;
  };

  const getAssessmentStatus = (): JSX.Element => {
    const paymentIncompleteTabs = [AssessmentTools.BUSINESS_CONTINUITY].filter(
      (tab) => !businessOwner?.payment_details?.[tab]
    );

    const filteredTabsData = tabsData.filter(
      (tab) => !paymentIncompleteTabs.includes(tab.value)
    );

    let hasEnabled = false;
    let allCompleted = true;

    filteredTabsData.forEach((tab) => {
      const assessmentData =
        businessOwner?.assessment_data?.awareness?.[tab.value];
      const isTabEnabled =
        assessmentData?.status === AssessmentToolStatus.ENABLE;
      const isCompleted =
        assessmentData?.progress_status ===
        AssessmentToolProgressStatus.COMPLETED;

      if (isTabEnabled) {
        hasEnabled = true;
        if (!isCompleted) {
          allCompleted = false;
        }
      } else {
        allCompleted = false;
      }
    });

    let statusText = '';
    let statusColor = '';

    if (!hasEnabled) {
      statusText = 'Not Started';
      statusColor = 'text-red-01';
    } else if (allCompleted) {
      statusText = 'Completed';
      statusColor = 'text-green-01';
    } else {
      statusText = 'In Progress';
      statusColor = 'text-yellow-01';
    }

    return <div className={statusColor}>{statusText}</div>;
  };

  const stage = planDevelopmentTabs.every(
    (tab) => businessOwner?.payment_details?.[tab]
  )
    ? AssessmentStage.PLAN_DEVELOPMENT.replace('_', ' ')
    : AssessmentStage.AWARENESS;
  return (
    <div>
      <button
        style={{
          pointerEvents: 'auto',
          cursor: disableDetailView ? 'not-allowed' : 'pointer',
        }}
        onClick={
          disableDetailView
            ? undefined
            : () => {
                navigate(
                  `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${businessOwner?.id}`
                );
              }
        }
        className='owner-card w-fit h-full p-2 bg-transparent hover:bg-blue-02 rounded-[2.5rem] border-2 border-gray-01 hover:border-blue-01'
      >
        <div className='inner-card w-full max-w-[35.75rem] hover:cursor-pointer border-2 border-blue-01 h-full max-h-[26.75rem] rounded-[2.5rem] hover'>
          <div className='w-full flex space-y-4 flex-col items-start bg-white rounded-[2.5rem] p-4 h-full border-gray-01'>
            <div className='flex items-center'>
              {businessOwner && (
                <div className='flex items-center w-12 h-12 border border-blue-01 justify-center font-semibold rounded-full text-blue-01'>
                  {getInitials(businessOwner)}
                </div>
              )}
              <div className='flex flex-col items-start ml-3'>
                <span className='font-semibold'>{businessOwner?.name}</span>
                <span className='font-medium text-blue-01'>
                  {businessOwner?.business_name}
                </span>
              </div>
            </div>
            <div className='flex flex-wrap w-full justify-between gap-4.5'>
              <div className='flex gap-4 items-center'>
                <img
                  src='/icons/email.svg'
                  alt='email-icon'
                  className='h-5 min-w-5'
                />
                <span className='text-start  min-w-64 text-blue-01 text-sm font-medium'>
                  {businessOwner.email}
                </span>
              </div>
              {businessOwner.phone && (
                <div className='flex gap-4 items-center min-w-28'>
                  <FaSquarePhone size={20} className='min-w-5 h-5' />
                  <span className='text-blue-01 text-sm font-medium'>
                    {businessOwner.phone}
                  </span>
                </div>
              )}
              <div className='flex flex-col gap-4 w-full lg:w-4/5 3xl:w-[90%]'>
                <div className='flex items-center justify-between'>
                  <div className='flex gap-4 items-center'>
                    <FaCalendarCheck size={20} className='min-w-5 h-5' />
                    <span className='text-gray-04 text-sm font-medium'>
                      Created On
                    </span>
                  </div>
                  <span className='text-blue-01 text-sm font-medium'>
                    {getFormattedDate(businessOwner?.created_at)}
                  </span>
                </div>
                <div className='flex items-center justify-between'>
                  <div className='flex gap-4 items-center'>
                    <img
                      src='/icons/calendar-clock.svg'
                      alt='calendar-clock-icon'
                      className='h-5 min-w-5'
                    />
                    <span className='text-gray-04 text-sm font-medium'>
                      Last Activity Date
                    </span>
                  </div>
                  <span className='text-blue-01 text-sm font-medium'>
                    {getFormattedDate(businessOwner?.last_activity_date)}
                  </span>
                </div>
                <div className='flex items-center justify-between'>
                  <div className='flex gap-4 items-center'>
                    <img
                      src='/icons/people.svg'
                      alt='people-icon'
                      className='h-5 min-w-5'
                    />
                    <span className='text-gray-04 text-sm font-medium'>
                      Secondary Advisors Count
                    </span>
                  </div>
                  <span
                    className='text-blue-500 hover:underline pointer-events-auto'
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent the outer button's onClick from being triggered
                      setShowSecondaryAdvisorPopUp(true);
                    }}
                  >
                    {businessOwner?.secondary_advisor_count}
                  </span>
                </div>
              </div>
            </div>
            <div className='flex text-sm mt-4.5'>
              <span className='text-black-02 font-medium'>Stage:</span>
              <div className='flex ml-6 items-center'>
                <span className='text-blue-01 mr-[0.375rem] font-medium'>
                  {titleCase(stage)}
                </span>
                {/* <FaCircleCheck size={16} color='green' /> */}
                {/* <FaHourglassHalf size={16} color='gray' /> */}
              </div>
            </div>
            <div className='flex text-sm mt-4.5'>
              <span className='text-black-02 font-medium'>Status:</span>
              <span className='ml-5 font-medium'>{getAssessmentStatus()}</span>
            </div>
            <div className='flex mt-3'>{progressIcons()}</div>
          </div>
        </div>
      </button>
      {showSecondaryAdvisor &&
        (businessOwner?.secondary_advisor_count ?? 0) > 0 && (
          <Modal
            title='Secondary Advisor Details'
            visible={showSecondaryAdvisor}
            handleVisibility={setShowSecondaryAdvisorPopUp}
            classname='w-[45%] h-[80%] max-w-[45rem] max-h-[40rem]'
          >
            <ManageAccessModal
              showForm={false}
              id={Number(businessOwner.id)}
              advisorId={businessOwner.primary_advisor_id}
            />
          </Modal>
        )}
      ;
    </div>
  );
};

export default BusinessOwnerCard;
