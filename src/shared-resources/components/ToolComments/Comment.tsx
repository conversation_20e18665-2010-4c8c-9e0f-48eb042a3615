import React, { useState } from 'react';
import { formatDistanceToNow } from 'date-fns';
import { ToolCommentResponse } from 'types/tool-comment.type';
import { MdModeEdit, MdDelete } from 'react-icons/md';
import { enUS } from 'date-fns/locale';
import AddCommentPopup from './AddCommentPopup';
import DeleteCommentPopup from './DeleteCommentPopup';

interface CommentProps {
  comment: ToolCommentResponse;
}

const Comment: React.FC<CommentProps> = ({ comment }) => {
  const [isEditPopupOpen, setIsEditPopupOpen] = useState(false);
  const [isDeletePopupOpen, setIsDeletePopupOpen] = useState(false);

  const getTimeAgo = (date: string) =>
    formatDistanceToNow(new Date(date), {
      addSuffix: true,
      locale: enUS,
    });

  const handleEditClick = () => {
    setIsEditPopupOpen(true);
  };

  const handleDeleteClick = () => {
    setIsDeletePopupOpen(true);
  };

  const handleCloseEditPopup = () => {
    setIsEditPopupOpen(false);
  };

  const handleCloseDeletePopup = () => {
    setIsDeletePopupOpen(false);
  };

  return (
    <div className='py-4 px-3 border-t border-b border-gray-200 relative bg-gray-08 hover:bg-white hover:border-l-4 hover:border-l-blue-01 transition-all duration-200'>
      <div className='absolute top-4 right-3 flex gap-2'>
        <button
          className='text-gray-500 hover:text-red-500'
          aria-label='Delete comment'
          onClick={handleDeleteClick}
        >
          <MdDelete />
        </button>
        <button
          className='text-gray-500 hover:text-blue-500'
          aria-label='Edit comment'
          onClick={handleEditClick}
        >
          <MdModeEdit />
        </button>
      </div>
      <div className='text-base font-semibold'>
        {comment.updated_by ?? comment.created_by}
      </div>
      <div className='text-xs text-gray-500 mb-2'>
        {getTimeAgo(comment.updated_at ?? comment.created_at)}
      </div>
      <div className='text-sm text-gray-700'>{comment.comment}</div>

      <AddCommentPopup
        isOpen={isEditPopupOpen}
        onClose={handleCloseEditPopup}
        isEdit
        initialComment={comment.comment}
        commentId={comment.id}
      />

      <DeleteCommentPopup
        isOpen={isDeletePopupOpen}
        onClose={handleCloseDeletePopup}
        commentId={comment.id}
      />
    </div>
  );
};

export default Comment;
