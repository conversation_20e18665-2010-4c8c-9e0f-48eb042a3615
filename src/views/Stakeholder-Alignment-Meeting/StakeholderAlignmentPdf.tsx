import React, { useCallback, useEffect, useRef } from 'react';
import PageWrapper from 'views/BusinessContinuity/shared-components/PageWrapper';
import { getKey } from 'utils/helpers/Helpers';
import { TableValueType, shareHoldersQuestions } from './Questions';
import StakeHoldersPdfTableComponent from './StakeHoldersPdfTableComponent';

interface StakeholderAlignmentPdfProps {
  attendeeTableValues: TableValueType[];
  pages: any[];
  setPages: (value: any[]) => void;
}

const StakeholderAlignmentPdf: React.FC<StakeholderAlignmentPdfProps> = (
  props
) => {
  const { attendeeTableValues, pages, setPages } = props;

  const rowRefs = useRef<any[]>([]);

  const createPages = (
    rowHeights: any,
    components: JSX.Element[],
    index: number
  ) => {
    const pageHeight = 1250;
    const rowsPerPage = [];
    let currentHeight = 0;
    let currentPageRows: any[] = [];

    components.forEach((component, i) => {
      if (component) {
        const rowHeight = rowHeights[i + index];

        if (currentHeight + rowHeight > pageHeight) {
          if (currentPageRows.length) rowsPerPage.push([...currentPageRows]);
          currentPageRows = [component];
          currentHeight = rowHeight;
        } else {
          currentPageRows.push(component);
          currentHeight += rowHeight;
        }
      }
    });

    if (currentPageRows.length > 0) {
      rowsPerPage.push([...currentPageRows]);
    }
    return rowsPerPage;
  };

  const components = shareHoldersQuestions.map((questionObj, index) => {
    if (questionObj.questionType === 'textArea') {
      return (
        <div className='flex flex-col gap-4 w-full' key={questionObj?.id}>
          {index === 0 && (
            <h1 className='font-bold text-2xl'>
              Stakeholder Alignment Meeting
            </h1>
          )}
          <div
            ref={(el) => {
              rowRefs.current[index] = el;
            }}
            className='w-full mb-4'
          >
            <h1>
              <strong>{`${index + 1}.  ${questionObj?.question} : `}</strong>
              {questionObj?.subText}
            </h1>
            <div className='mt-2 w-full min-h-64 border-2 border-gray-300 focus:outline-none rounded-md p-4 resize-none' />
          </div>
        </div>
      );
    }

    return (
      <div
        key={questionObj?.id}
        className='flex flex-col'
        ref={(el) => {
          rowRefs.current[index] = el;
        }}
      >
        <h1 className='font-bold'>{`${index + 1}.  ${
          questionObj?.question
        } :`}</h1>
        <div className='mt-6'>
          <StakeHoldersPdfTableComponent tableValues={attendeeTableValues} />
        </div>
      </div>
    );
  });

  const measureRowHeights = useCallback(() => {
    const rowHeights = rowRefs.current.map(
      (ref) => ref?.getBoundingClientRect()?.height
    );
    const rowsPerPage = components?.length
      ? createPages(rowHeights, components as JSX.Element[], 0)
      : [];
    setPages(rowsPerPage);
  }, [components, rowRefs]);

  useEffect(() => {
    measureRowHeights();
  }, [attendeeTableValues, rowRefs]);

  return (
    <div
      className='mr-4 flex flex-col gap-10 relative p-32'
      id='stakeholder-pdf'
    >
      <div className='absolute -left-[200%]'>{components}</div>
      {pages.map((pageRows, index) => (
        <PageWrapper id={`stakeholder-pdf-${index}`} key={getKey(index)}>
          {pageRows}
        </PageWrapper>
      ))}
    </div>
  );
};

export default StakeholderAlignmentPdf;
