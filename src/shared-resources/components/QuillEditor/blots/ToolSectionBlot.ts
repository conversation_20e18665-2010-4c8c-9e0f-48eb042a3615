import Quill from 'quill';

const BlockEmbed = Quill.import('blots/block/embed') as any;

class ToolSectionBlot extends BlockEmbed {
  static create(value: any) {
    const node = super.create();
    node.innerHTML = value.toolName;
    node.setAttribute('data-tool', value.toolName);
    node.className = 'tool-section-header';
    return node;
  }

  static value(node: any) {
    return {
      toolName: node.getAttribute('data-tool'),
    };
  }
}

export default ToolSectionBlot;
