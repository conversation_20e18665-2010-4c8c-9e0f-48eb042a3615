import { AssessmentTools } from 'types/enum';
import {
  AssessmentToolActionType,
  DocumentActionType,
} from './actions.constants';
import {
  AssessmentReEvaluatePayload,
  FetchBusinessOwnerAssessmentDTO,
  FetchOwnAssessmentDTO,
  FetchPublicAssessmentByTokenDTO,
  UpdateBusinessOwnerAssessmentDTO,
  UpdateOwnAssessmentDTO,
  UpdatePublicAssessmentByTokenDTO,
  UploadDocumentPayload,
} from '../../dtos/assessment-tools.dto';

export interface FetchOwnAssessmentPayload extends FetchOwnAssessmentDTO {
  onError?: () => void;
}

export interface FetchBusinessOwnerAssessmentPayload
  extends FetchBusinessOwnerAssessmentDTO {
  onError?: () => void;
}

export interface FetchPublicAssessmentByTokenPayload
  extends FetchPublicAssessmentByTokenDTO {
  onError?: () => void;
}

export const fetchOwnAssessment = (payload: FetchOwnAssessmentPayload) => ({
  type: AssessmentToolActionType.GET_OWN_ASSESSMENT,
  payload,
});

export const updateOwnAssessment = (payload: UpdateOwnAssessmentDTO) => ({
  type: AssessmentToolActionType.UPDATE_OWN_ASSESSMENT,
  payload,
});

export const fetchBusinessOwnerAssessment = (
  payload: FetchBusinessOwnerAssessmentPayload
) => ({
  type: AssessmentToolActionType.GET_BUSINESS_OWNER_ASSESSMENT,
  payload,
});

export const updateBusinessOwnerAssessment = (
  payload: UpdateBusinessOwnerAssessmentDTO
) => ({
  type: AssessmentToolActionType.UPDATE_BUSINESS_OWNER_ASSESSMENT,
  payload,
});

export const fetchPublicAssessmentByToken = (
  payload: FetchPublicAssessmentByTokenPayload
) => ({
  type: AssessmentToolActionType.GET_PUBLIC_ASSESSMENT_BY_TOKEN,
  payload,
});
export const updatePublicAssessmentByToken = (
  payload: UpdatePublicAssessmentByTokenDTO
) => ({
  type: AssessmentToolActionType.UPDATE_PUBLIC_ASSESSMENT_BY_TOKEN,
  payload,
});

export const updateAssessmentReEvaluate = (
  payload: AssessmentReEvaluatePayload
) => ({
  type: AssessmentToolActionType.UPDATE_ASSESSMENT_RE_EVALUATE,
  payload,
});

export const uploadContinuityDocument = (payload: UploadDocumentPayload) => ({
  type: DocumentActionType.UPLOAD_DOCUMENT,
  payload,
});

export const openCountIncrement = (tool: AssessmentTools) => ({
  type: AssessmentToolActionType.OPEN_COUNT_INCREMENT,
  payload: { tool },
});
