import { get } from 'lodash';
import { all, call, put, select, takeLatest } from 'redux-saga/effects';

import {
  AdvisorDetailResponse,
  AdvisorDTO as advisorDTO,
  AdvisorListResponse,
} from 'dtos/advisor.dto';
import { toast } from 'react-toastify';
import { advisorService } from 'services/api-services/Advisor';
import { AdvisorActionType } from 'store/actions/actions.constants';

import {
  AdvisorCreateActionPayloadType
} from 'store/actions/advisor.action';

import {
  UserRouteType,
} from 'types/enum';
import { SagaPayloadType } from 'types/SagaPayload.type';

import { convertToTitleCase } from 'utils/helpers/Helpers';
import {
  addAdvisor,
  getAdvisors,
  setAdvisors,
  setAdvisorsError,
  setAdvisorsLoading,
  setAdvisorsPageData,
  setCreateAdvisorError,
  setCreateAdvisorLoading,
  setCreatedAdvisor,
  setDeleteAdvisor,
  setGetAdvisorError,
  setGetAdvisorLoading,
  setUpdateAdvisor,
  setUpdateAdvisorError,
  setUpdateAdvisorLoading,
} from '../reducers/advisor.reducer';

export interface UpdateAdvisorSagaPayloadType {
  id: string;
  data: advisorDTO;
  onSucess: () => void;
}

interface AdvisorCreateSagaPayloadType extends SagaPayloadType {
  payload: AdvisorCreateActionPayloadType;
}

function* advisorsFetchSaga(action: any): any {
  const { filters } = action.payload;
  const page = get(filters, ['page']);

  const ownerData = yield select((state) => state.advisor);

  const isOwnersPageData = ownerData?.pages?.[page]?.ids?.length;
  try {
    if (!isOwnersPageData || filters.search !== undefined) {
      yield put(setAdvisorsLoading({ loading: true }));
      if (!filters.search) {
        delete filters.search;
      }
      const response: AdvisorListResponse = yield call(
        advisorService.fetchList,
        filters
      );
      yield put(getAdvisors(response.data));
      yield put(setAdvisorsPageData({ paginationMeta: response.meta }));
      yield put(
        setAdvisors({
          ids: response.data.map((owner) => owner.id),
          loadMore: filters.page,
        })
      );
      yield put(setAdvisorsLoading({ loading: false }));
    }
  } catch (e: any) {
    yield put(setAdvisorsError({ error: e?.message }));
  }
}

function* advisorCreateSaga(
  action: AdvisorCreateSagaPayloadType
): any {
  const { advisorCreatePayload, onSuccess } = action.payload;
  try {
    yield put(setCreateAdvisorLoading({ loading: true }));
    const response = yield call(
      advisorService.createAdvisor,
      advisorCreatePayload
    );
    const advisor = get(response, ['business_owner'], {});
    yield put(setCreatedAdvisor({ owner: advisor }));
    yield put(setCreateAdvisorLoading({ loading: false }));
    toast.success('Advisor created successfully!');
    onSuccess();
  } catch (e: any) {
    yield put(
      setCreateAdvisorError({
        error: true,
        message: e?.response?.data?.errors?.[0]?.message,
        errors: e?.response?.data?.errors,
      })
    );
  }
}
function* updateAdvisorSaga(action: any): any {
  const { onSuccess, data, id } = action.payload;
  try {
    yield put(setUpdateAdvisorLoading({ loading: true }));
    const response = yield call(
      advisorService.updateAdvisor,
      id,
      data
    );

    yield put(setUpdateAdvisor(response));
    yield put(setUpdateAdvisorLoading({ loading: false }));
    onSuccess();
    toast.success('Advisor updated successfully');
  } catch (e: any) {
    yield put(
      setUpdateAdvisorError({
        error: true,
        message: e?.response?.data?.errors?.[0]?.message,
        errors: e?.response?.data?.errors,
      })
    );
  }
}
function* deleteAdvisorSaga(action: any): any {
  const { id, onSuccess } = action.payload;
  try {
    yield put(setUpdateAdvisorLoading({ loading: true }));
    const response = yield call(advisorService.deleteAdvisor, id);
    yield put(setDeleteAdvisor(response));
    yield put(setUpdateAdvisorLoading({ loading: false }));
    onSuccess();
    toast.success('Advisor deleted successfully');
  } catch (e: any) {
    yield put(
      setUpdateAdvisorError({
        error: true,
        message: e?.response?.data?.errors?.[0]?.message,
        errors: e?.response?.data?.errors,
      })
    );
  }
}

function* advisorUpdateSaga(action: any): any {
  const advisorUpdatePayload: advisorDTO = action.payload.data;
  const advisorId = action.payload.id;
  const { user } = action.payload;
  const { callback, tool } = action.payload;
  try {
    let response;
    yield put(setUpdateAdvisorLoading({ loading: true }));
      response = yield call(
        advisorService.updateAdvisor,
        advisorId,
        advisorUpdatePayload
      );
    
   
    yield put(setUpdateAdvisorLoading({ loading: false }));
  } catch (e: any) {
    yield put(setUpdateAdvisorLoading({ loading: false }));
    yield put(
      setUpdateAdvisorError({
        error: true,
        message: e?.response?.data?.errors?.[0]?.message,
        errors: e?.response?.data?.errors,
      })
    );
  }
}

function* advisorUpdateAccessSaga(action: any): any {
  const advisorId = action.payload.id;
  const advisorAccess = action.payload.isActive;
  const onSuccess = action.payload.onSuccess;
  try {
    let response;
    yield put(setUpdateAdvisorLoading({ loading: true }));
      response = yield call(
        advisorService.updateAdvisorAccess,
        advisorId,
        advisorAccess
      );
    yield call(onSuccess);
    yield put(setUpdateAdvisorLoading({ loading: false }));
  } catch (e: any) {
    yield put(setUpdateAdvisorLoading({ loading: false }));
    yield put(
      setUpdateAdvisorError({
        error: true,
        message: e?.response?.data?.errors?.[0]?.message,
        errors: e?.response?.data?.errors,
      })
    );
  }
}

function* advisorFetchSaga(action: any): any {
  const id = Number(action.payload);
  try {
    yield put(setGetAdvisorLoading({ loading: true }));
    const response: AdvisorDetailResponse = yield call(
      advisorService.getAdvisor,
      id
    );

    yield put(addAdvisor(response.business_owner));
    yield put(setGetAdvisorLoading({ loading: false }));
  } catch (e: any) {
    yield put(setGetAdvisorError({ error: e, message: e.message }));
  }
}

export function* advisorSagaWatcher() {
  yield all([
    takeLatest(
      AdvisorActionType.GET_ADVISORS,
      advisorsFetchSaga
    ),
    takeLatest(
      AdvisorActionType.GET_ADVISOR,
      advisorFetchSaga
    ),
    takeLatest(
      AdvisorActionType.CREATE_ADVISOR,
      advisorCreateSaga
    ),
    takeLatest(
      AdvisorActionType.UPDATE_ADVISOR,
      advisorUpdateSaga
    ),
    takeLatest(
      AdvisorActionType.DELETE_ADVISOR,
      deleteAdvisorSaga
    ),
    takeLatest(
      AdvisorActionType.ACCESS_ADVISOR,
      advisorUpdateAccessSaga
    ),
  ]);
}
