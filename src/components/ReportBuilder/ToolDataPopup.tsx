import React from 'react';
import Modal from 'shared-resources/components/Modal/Modal';
import { AssessmentTools } from 'types/enum';
import Button from 'shared-resources/components/Button/Button';
import { getToolName } from 'utils/helpers/Helpers';

interface ToolDataPopupProps {
  tool: AssessmentTools | null;
  onClose: () => void;
  onSave: () => void;
  children: React.ReactNode;
}

const ToolDataPopup: React.FC<ToolDataPopupProps> = ({
  tool,
  onClose,
  onSave,
  children,
}) => (
  <Modal
    visible={!!tool}
    handleVisibility={onClose}
    title={tool ? getToolName(tool) : ''}
    closeOnOutsideClick={false}
    classname='w-2/3'
    theme='secondary'
  >
    <div className='flex flex-col h-full'>
      <div className='flex-grow p-6 overflow-y-auto'>{children}</div>
      <div className='flex justify-end gap-4 p-4 bg-blue-02'>
        <Button onClick={onSave} className='px-6 py-2'>
          Save
        </Button>
        <Button
          theme='secondary'
          onClick={onClose}
          className='px-6 py-2 bg-inherit'
        >
          Cancel
        </Button>
      </div>
    </div>
  </Modal>
);

export default ToolDataPopup;
