import React from 'react';

interface Props {
  categoryTitle?: string;
  categoryScore?: number;
  categoryDescription?: string;
  categoryScoreDescription?: React.ReactElement;
  subjectsInfo: { title?: string; description: string; insight?: string }[];
}

const CategoryResults: React.FC<Props> = ({
  categoryTitle,
  categoryScore,
  categoryDescription,
  categoryScoreDescription,
  subjectsInfo,
}) => (
  <div className='flex justify-center'>
    <div className='flex flex-col w-4/5 3xl:w-13/20 h-full'>
      <h1 className='text-blue-01 font-openSan font-semibold mt-[7.5rem] text-center'>
        Category Results
      </h1>
      {categoryTitle && categoryScore && (
        <div className='flex items-center justify-between font-semibold font-openSans my-9'>
          <span className='text-xl underline'>{categoryTitle}</span>
          <span className='text-xl underline text-blue-01'>
            {categoryScore}%
          </span>
        </div>
      )}
      {categoryDescription && categoryScoreDescription && (
        <div className='flex flex-col space-y-6 font-medium'>
          <div>{categoryDescription}</div>
          <div>{categoryScoreDescription}</div>
        </div>
      )}

      <div className='mt-6 flex flex-col space-y-12'>
        {subjectsInfo.map((subjectInfo) => (
          <div
            key={Math.random()}
            className='flex flex-col space-y-4 font-medium'
          >
            {subjectInfo.title && (
              <p className='text-blue-01 underline'>{subjectInfo.title}</p>
            )}
            <div>
              <h3 className='text-blue-01'>Question</h3>
              <p>{subjectInfo.description}</p>
            </div>
            {subjectInfo.insight && (
              <div>
                <h3 className='text-blue-01'>Insight</h3>
                <p>{subjectInfo.insight}</p>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  </div>
);

export default CategoryResults;
