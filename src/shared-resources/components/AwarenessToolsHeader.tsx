import React from 'react';
import { AwarenessTab } from 'types/business-owner.types';

import { getBusinessOwnerDetails } from 'store/selectors/business-owner.selector';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import { useParamSelector } from 'store/selectors/base.selectors';
import { useParams } from 'react-router';
import { AssessmentToolProgressStatus, AssessmentToolStatus } from 'types/enum';
import AssessmentTabButton from './TabButton/AssessmentTabButton';

interface Props {
  tabData: AwarenessTab[];
  activeTab?: AwarenessTab | null;
  onHandleTabChange?: (tab: AwarenessTab) => void;
}

const AwarenessToolsHeader: React.FC<Props> = (props) => {
  const { tabData, activeTab, onHandleTabChange } = props;
  const id = useParams();
  const businessOwner = useParamSelector(getBusinessOwnerDetails, {
    id: id.id,
  }) as BusinessOwner;
  const handleTabChange = (tab: AwarenessTab) => {
    if (onHandleTabChange) {
      onHandleTabChange(tab);
    }
  };
  const businessOwnerData = {
    ...businessOwner?.assessment_data?.awareness,
    ...businessOwner?.assessment_data?.plan_development,
  };

  return (
    <div
      className={`flex w-full relative ${
        tabData?.length > 3 ? 'justify-between' : 'justify-start'
      } gap-2 rounded-lg items-center p-1.5 bg-blue-02`}
    >
      {tabData?.map((a) => {
        const awarenessItem = businessOwnerData?.[a?.value];
        const completed =
          awarenessItem?.progress_status ===
            AssessmentToolProgressStatus.COMPLETED &&
          awarenessItem?.status === AssessmentToolStatus.ENABLE;

        return (
          <AssessmentTabButton
            key={a.value}
            tab={a}
            isSelected={a.value === activeTab?.value}
            onHandleTabChange={handleTabChange}
            completed={completed}
          />
        );
      })}
    </div>
  );
};
export default AwarenessToolsHeader;
