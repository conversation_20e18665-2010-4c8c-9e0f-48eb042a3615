import { SubscriptionType, UserType } from 'types/enum';
import { baseApiService } from './BaseApiService';

class PaymentService {
  static getInstance(): PaymentService {
    return new PaymentService();
  }

  public async businessOwnerPaymentLink(userType: UserType, id?: number) {
    if (userType === UserType.ADVISOR && id) {
      return baseApiService.get(
        `business-owner/${id}/payment-link/business-valuation`
      );
    }
    return baseApiService.get(`business-owner/payment-link/business-valuation`);
  }

  public async advisorPaymentLink(subscriptionType: SubscriptionType) {
    return baseApiService.get(`/advisor/subscription/${subscriptionType}`);
  }

  public async cancelAdvisorSubscription() {
    return baseApiService.post(`/advisor/subscription/cancel`);
  }

  public async retryAdvisorPayment() {
    return baseApiService.get(`/advisor/subscription/manual-pay`);
  }

  public async sendPaymentReminder(userId: number) {
      return baseApiService.post(`business-owner/${userId}/payment-reminder/business-valuation`);
   }
}

export const paymentService = PaymentService.getInstance();
