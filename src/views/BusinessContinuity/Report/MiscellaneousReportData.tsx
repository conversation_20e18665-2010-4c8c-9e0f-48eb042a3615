import React, { forwardRef } from 'react';

interface MiscellaneousReportDataProps {
  data: any;
}
const MiscellaneousReportData = forwardRef(
  (props: MiscellaneousReportDataProps, ref: React.Ref<HTMLDivElement>) => {
    const { data } = props;

    const dataMapping = [
      {
        key: 'user_listing',
        question: 'Where do you keep your listing of user names and passwords?',
        answer: data?.user_listing,
      },
      {
        key: 'properties_automobiles',
        question: 'Where do you keep keys to property/automobile(s)?',
        answer: data?.properties_automobiles,
      },
      {
        key: 'safes_combination',
        question:
          'If you have any safes, where are they and what are the combinations?',
        answer: data?.safes_combination,
      },
      {
        key: 'other_messages',
        question:
          'Do you have other messages that will be useful in the event of your incapacitation?',
        answer: data?.other_messages,
      },
    ];
    return (
      <div ref={ref} className='flex flex-col gap-4 mt-6 w-full'>
        <span className='font-semibold block text-lg text-center text-blue-01'>
          Miscellaneous
        </span>
        {dataMapping.map((item) => (
          <div key={item.key} className='flex flex-col gap-2'>
            <span className='font-semibold'>{item?.question}</span>
            <div
              className={`px-4 py-2 border border-gray-02 rounded-lg ${
                item?.key === 'other_messages' && 'min-h-28'
              }`}
            >
              {item?.answer}
            </div>
          </div>
        ))}
      </div>
    );
  }
);

MiscellaneousReportData.displayName = 'MiscellaneousReportData';
export default MiscellaneousReportData;
