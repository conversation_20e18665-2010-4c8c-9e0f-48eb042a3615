import React, { FC, memo, useState } from 'react';
import Button from 'shared-resources/components/Button/Button';
import { FieldArray, Form, Formik } from 'formik';
import * as yup from 'yup';
import { MdDeleteOutline } from 'react-icons/md';
import DetailItem from '../../shared-resources/components/BusinessOwner/DetailItems';
import { BusinessOwner } from '../../models/entities/BusinessOwner';
import FormikInput from '../../shared-resources/components/Input/FormikInput';

interface BasicInfoPageProps {
  setTab: () => void;
  businessOwner: BusinessOwner;
  assessmentResponse: {
    [key: number | string]: string | undefined;
  };
  setData: (res: any) => void;
}

const BasicInfoPage: FC<BasicInfoPageProps> = ({
  setTab,
  businessOwner,
  assessmentResponse,
  setData,
}) => {
  const defaultValue = JSON.stringify([
    {
      name: businessOwner?.name || '',
      ownership: 0,
    },
  ]);
  const initialValues = {
    owners: JSON.parse(assessmentResponse?.owners || defaultValue),
  };
  const validationSchema = yup.object().shape({
    owners: yup
      .array()
      .of(
        yup.object().shape({
          name: yup.string().required('Name is required'),
          ownership: yup
            .number()
            .typeError('Not a valid number')
            .required('Ownership Percentage is required')
            .moreThan(0, '0 and below not allowed')
            .max(100, 'Above 100 not allowed'),
        })
      )
      .required('Owners are required')
      .min(1, 'Minimum of 1 owner'),
  });
  const [error, setError] = useState('');

  return (
    <div className='flex flex-col gap-7'>
      <div className='flex p-7 bg-white'>
        <div className='flex flex-wrap justify-between gap-x-1 ml-10 max-w-[80rem]'>
          <DetailItem
            name='Business Name'
            value={businessOwner?.business_name}
          />
          <DetailItem
            name='Type of Business'
            value={businessOwner?.business_type || '-'}
          />
          <DetailItem name='Age of Business Owner' value={businessOwner?.age} />
          {businessOwner?.additional_advisor_email && (
            <DetailItem
              name='Additional Advisor'
              value={businessOwner?.additional_advisor_email}
            />
          )}
          <div className='inline-block w-full'>
            <DetailItem
              name='Email Address'
              width='w-full'
              value={businessOwner?.email}
            />
          </div>
          {businessOwner?.notes && (
            <div className='inline-block w-full'>
              <DetailItem
                name='Notes'
                width='w-full'
                value={businessOwner?.notes}
              />
            </div>
          )}
        </div>
      </div>
      <Formik
        initialValues={initialValues}
        enableReinitialize
        onSubmit={(values) => {
          if (
            values.owners
              .map(
                (o: { name: string; ownership: string | number }) =>
                  +o.ownership
              )
              .reduce((a: number, b: number) => a + b, 0) === 100
          ) {
            setError('');
            setData({
              ...assessmentResponse,
              owners: JSON.stringify(values.owners),
            });
            setTab();
          } else {
            setError('Percentage sum should be 100');
          }
        }}
        validationSchema={validationSchema}
      >
        {(formikProps) => (
          <Form
            onSubmit={formikProps.handleSubmit}
            className='flex flex-col items-start gap-6 rounded-xl p-7 bg-white mt-4 max-h-[calc(100vh-26.5rem)] min-h-[17.3rem]'
          >
            <FieldArray
              name='owners'
              render={(arrayHelpers) => (
                <>
                  <div className='flex justify-between w-full items-center'>
                    <h2 className='text-xl font-semibold'>
                      Ownership Structure
                    </h2>
                    <Button
                      className='px-6 py-2'
                      type='button'
                      onClick={() =>
                        arrayHelpers.push({ name: '', ownership: 0 })
                      }
                    >
                      Add Owner
                    </Button>
                  </div>
                  <div className='overflow-y-auto pr-5 min-h-24 scrollbar'>
                    {formikProps.values.owners.map(
                      (
                        owner: { name: string; ownership: string | number },
                        index: number
                      ) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <div key={index} className='flex items-center'>
                          {index === 0 ? (
                            <h3 className='text-lg font-medium text-blue-01 min-w-[25rem]'>
                              {owner.name}
                            </h3>
                          ) : (
                            <div className='min-w-[25rem]'>
                              <FormikInput
                                name={`owners[${index}].name`}
                                // eslint-disable-next-line react/no-array-index-key
                                key={`owners[${index}].name`}
                                label='Owner Name'
                                labelClassName='mb-1'
                                className='max-w-72'
                                asterisk
                              />
                            </div>
                          )}
                          <div className='flex justify-between items-center'>
                            <FormikInput
                              name={`owners[${index}].ownership`}
                              // eslint-disable-next-line react/no-array-index-key
                              key={`owners[${index}].ownership`}
                              label='Ownership Percentage (%)'
                              labelClassName='mb-1'
                              className='max-w-96'
                              asterisk
                            />
                            {index > 0 && (
                              <MdDeleteOutline
                                className='min-w-5 ml-5 text-red-500 cursor-pointer'
                                onClick={() => arrayHelpers.remove(index)}
                              />
                            )}
                          </div>
                        </div>
                      )
                    )}
                  </div>
                </>
              )}
            />
            <div className='flex justify-between w-full'>
              <span className='text-red-500'>{error}</span>
              <Button className='px-6 py-2' type='submit'>
                Next
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};
export default memo(BasicInfoPage);
