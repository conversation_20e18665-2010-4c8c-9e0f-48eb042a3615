import { FieldArray, Form, Formik, FormikErrors } from 'formik';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { FormikSelect } from 'shared-resources/components/Select/FormikSelect';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { AssessmentTools, BusinessContinuityScreens } from 'types/enum';
import {
  getKey,
  isRequiredFieldDependingOnValidationSchema,
  titleCaseAndRemoveUnderScoreOrHyphen,
} from 'utils/helpers/Helpers';
import * as yup from 'yup';
import { HiDocumentArrowUp, HiDocument } from 'react-icons/hi2';
import { uploadContinuityDocument } from 'store/actions/assessment-tool.action';
import { useParams } from 'react-router';
import { pick } from 'lodash';
import { BiLoaderCircle } from 'react-icons/bi';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import ContinuityQuestionHeader from './shared-components/ContinuityQuestionHeader';
import ContuinuityRowView from './shared-components/ContuinuityRowView';
import { formEmpty } from './Report/continuityReportHelper';

interface KeyDocumentProps {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  onSaveToDraftClick: (data: any) => void;
  data: any;
  question?: string;
  options: { label: string; value: string }[];
}
const KeyDocument: React.FC<KeyDocumentProps> = (props) => {
  const {
    nextStep,
    backStep,
    setData,
    onSaveToDraftClick,
    data,
    question,
    options,
  } = props;

  const screen = BusinessContinuityScreens.DOCUMENTS;
  const fileInputRef = React.useRef<HTMLInputElement | null>(null);
  const [fileName, setFileName] = useState<Record<number, string> | null>(null);
  const { id } = useParams();

  const handleInputClick = () => {
    fileInputRef.current?.click();
  };
  const isLoading = useSelector(getAssessmentToolLoading);
  const [isUploading, setIsUploading] = useState(false);

  const dispatch = useDispatch();
  const validationSchema = yup
    .object()
    .shape({
      [screen]: yup.array().of(
        yup.object({
          type: yup.string().required('Document type is required'),
          location: yup.string().required('Location is required'),
          specific_instructions: yup.string().nullable(),
          document: yup.string().nullable(),
        })
      ),
    })
    .required();

  const filteredInitialValues = (data?.[screen] || []).map((values: any) =>
    pick(
      values,
      Object.keys(values || {}).filter((key) => key !== 'url')
    )
  );

  const initialValues = {
    [screen]: data?.[screen]
      ? filteredInitialValues
      : [
          {
            type: '',
            location: '',
            specific_instructions: '',
            document: '',
            saved: false,
          },
        ],
  };

  const onFileUpload = (
    e: any,
    index: number,
    setFieldValue: (
      field: string,
      value: any,
      shouldValidate?: boolean | undefined
    ) => Promise<void | FormikErrors<{
      documents: any;
    }>>
  ) => {
    const file = e.target?.files?.[0] as File;
    if (file) {
      setIsUploading(true);
      dispatch(
        uploadContinuityDocument({
          file,
          payload: {
            content_type: file.type,
            file_name: file.name,
            image_category: AssessmentTools.BUSINESS_CONTINUITY,
            index,
          },
          ownerId: id ? +id : undefined,
          onSuccess: (value) => {
            setIsUploading(false);
            setFieldValue(`${screen}.${getKey(index)}.document`, value);
            setFileName({ ...fileName, [index]: file.name });
          },
        })
      );
    }
  };

  useEffect(() => {
    if (data?.[screen]) {
      const fileNameMap = (
        data[screen] as Record<string, string | boolean>[]
      ).reduce((acc: Record<number, string>, item: any, index: number) => {
        const documentValue = Object.entries(item).find(
          ([key]) => key === 'document'
        )?.[1] as string;
        if (documentValue) {
          const splittedList = documentValue.split('/');
          acc[index] = splittedList[splittedList.length - 1];
        }
        return acc;
      }, {});
      setFileName(fileNameMap);
    }
  }, []);
  return (
    <div className='flex flex-col rounded-lg h-full'>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          setData(values?.[screen]);
          nextStep();
        }}
        enableReinitialize
        validateOnMount
      >
        {({ values, setFieldValue, isValid, errors }) => {
          const isFormEmpty = formEmpty(values?.[screen]);
          return (
            <Form className='relative p-5 h-full flex flex-col gap-4 bg-white'>
              <FieldArray name={screen}>
                {({ push, remove }) => (
                  <div className='flex flex-col   h-full'>
                    <ContinuityQuestionHeader
                      fieldKey={screen}
                      push={push}
                      pushData={{
                        type: '',
                        location: '',
                        specific_instructions: '',
                        document: '',
                        saved: false,
                      }}
                      title={titleCaseAndRemoveUnderScoreOrHyphen(screen)}
                    >
                      {question && <h2 className='font-medium'>{question}</h2>}
                    </ContinuityQuestionHeader>
                    <div className=' flex flex-col max-h-[calc(100vh-25rem)]  pr-1 scrollbar overflow-auto'>
                      <ContuinuityRowView
                        fieldKey={screen}
                        formValues={values}
                        remove={remove}
                        initialValues={{
                          type: '',
                          location: '',
                          specific_instructions: '',
                          document: '',
                          saved: false,
                        }}
                      />
                      <div className='flex flex-col'>
                        {!!values?.[screen]?.length &&
                          values?.[screen]?.map(
                            (formValues: any, index: number) =>
                              !formValues.saved && (
                                <div
                                  key={`${getKey(index)}_form`}
                                  className='flex flex-col border border-gray-02 p-4 mt-1 rounded-b-lg'
                                >
                                  <div className='grid grid-cols-2 gap-y-2 gap-x-8 mt-5  overflow-auto scrollbar text-[1rem]'>
                                    <FormikSelect
                                      name={`${screen}.${getKey(index)}.type`}
                                      key={`${screen}.${getKey(index)}.type`}
                                      label='Type'
                                      options={options}
                                      outerClassName='!h-10'
                                      labelClassName='!text-base'
                                      optionClassname='!p-1 rounded-md'
                                      placeholder='Select Type'
                                      menuClassname='!p-2'
                                    />
                                    <FormikInput
                                      name={`${screen}.${getKey(
                                        index
                                      )}.location`}
                                      key={`${screen}.${getKey(
                                        index
                                      )}.location`}
                                      label='Location'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${screen}.${getKey(index)}.name`
                                      )}
                                    />

                                    <div className='col-span-2'>
                                      <FormikInput
                                        name={`${screen}.${getKey(
                                          index
                                        )}.specific_instructions`}
                                        key={`${screen}.${getKey(
                                          index
                                        )}.specific_instructions`}
                                        fieldType='textarea'
                                        label='Comment if any:'
                                        className='h-28'
                                        labelClassName='leading-6.5'
                                      />
                                    </div>

                                    <div className='col-span-2 mb-2'>
                                      <label
                                        htmlFor={`${screen}.${getKey(
                                          index
                                        )}.document`}
                                        className='leading-6.5 font-medium'
                                        aria-label={`${screen}.${getKey(
                                          index
                                        )}.document`}
                                      >
                                        Upload Document (optional)
                                        <input
                                          ref={fileInputRef}
                                          name={`${screen}.${getKey(
                                            index
                                          )}.document`}
                                          id={`${screen}.${getKey(
                                            index
                                          )}.document`}
                                          type='file'
                                          className='h-28  hidden'
                                          onChange={(e) => {
                                            onFileUpload(
                                              e,
                                              index,
                                              setFieldValue
                                            );
                                          }}
                                        />
                                      </label>
                                      <button
                                        className={`w-full border-2 ${
                                          fileName?.[index]
                                            ? 'border-blue-01'
                                            : 'border-gray-400'
                                        } rounded-lg  flex flex-col gap-3 items-center justify-center mt-1 py-6`}
                                        onClick={handleInputClick}
                                        aria-label='Upload document'
                                        type='button'
                                      >
                                        {isUploading ? (
                                          <BiLoaderCircle
                                            size={40}
                                            className='w-10 h-19 text-gray-400 animate-spin'
                                          />
                                        ) : (
                                          <div>
                                            {fileName?.[index] ? (
                                              <HiDocument
                                                className='text-blue-01'
                                                size={60}
                                              />
                                            ) : (
                                              <HiDocumentArrowUp
                                                className='text-gray-400'
                                                size={60}
                                              />
                                            )}
                                          </div>
                                        )}
                                        {isUploading ? (
                                          <span>Uploading</span>
                                        ) : (
                                          fileName?.[index] && (
                                            <span>{fileName?.[index]}</span>
                                          )
                                        )}
                                      </button>
                                    </div>
                                  </div>
                                  <div className='flex justify-end'>
                                    <Button
                                      onClick={() => {
                                        setFieldValue(
                                          `${screen}.${getKey(index)}.saved`,
                                          true
                                        );
                                      }}
                                      className='px-4 py-2'
                                      disabled={
                                        !!(
                                          errors?.[
                                            screen
                                          ] as FormikErrors<any>[]
                                        )?.[index] || isUploading
                                      }
                                    >
                                      Save
                                    </Button>
                                  </div>
                                </div>
                              )
                          )}
                      </div>
                    </div>
                  </div>
                )}
              </FieldArray>
              <BackNextComponent
                backStep={() => {
                  backStep();
                  setData(values?.[screen]);
                }}
                nextStep={() => {
                  nextStep();
                  setData(values?.[screen]);
                }}
                fieldKey={screen}
                formValues={values}
                isLoading={isLoading}
                isNextDisable={(isFormEmpty ? false : !isValid) || isUploading}
                onSaveToDraftClick={onSaveToDraftClick}
              />
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default KeyDocument;
