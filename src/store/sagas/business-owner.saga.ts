import { get } from 'lodash';
import { all, call, put, select, takeLatest } from 'redux-saga/effects';

import {
  BusinessOwnerDetailResponse,
  BusinessOwnerDTO,
  BusinessOwnerListResponse,
} from 'dtos/business-owner.dto';
import { toast } from 'react-toastify';
import { businessOwnerService } from 'services/api-services/BusinessOwnerService';
import { BusinessOwnerActionType } from 'store/actions/actions.constants';

import {
  BusinessOwnerCreateActionPayloadType,
  UpdateAssessmentToolActionPayloadType,
} from 'store/actions/business-owner.action';
import {
  addAssessmentLoading,
  updateBusinessOwnerProperty,
} from 'store/reducers/assessment-tool.reducer';
import {
  AssessmentTools,
  AssessmentToolStatus,
  BusinessContinuityScreens,
  BusinessValuationScreens,
  UserRouteType,
} from 'types/enum';
import { SagaPayloadType } from 'types/SagaPayload.type';

import { convertToTitleCase } from 'utils/helpers/Helpers';
import {
  addBusinessOwner,
  addBusinessOwnerIndustries,
  addBusinessOwners,
  SetBusinessOwnerAssessmentData,
  setBusinessOwnerIndustriesLoading,
  setBusinessOwners,
  setBusinessOwnersError,
  setBusinessOwnersLoading,
  setBusinessOwnersPageData,
  setCreateBusinessOwnerError,
  setCreateBusinessOwnerLoading,
  setCreatedBusinessOwner,
  setDeleteBusinessOwner,
  setGetBusinessOwnerError,
  setGetBusinessOwnerLoading,
  setLearnMoreLoading,
  setUpdateBusinessOwner,
  setUpdateBusinessOwnerError,
  setUpdateBusinessOwnerLoading,
  updateBusinessOwnerAssessmentData,
} from '../reducers/business-owner.reducer';

export interface UpdateBusinessOwnerSagaPayloadType {
  id: string;
  data: BusinessOwnerDTO;
  onSucess: () => void;
}

interface BusinessOwnerCreateSagaPayloadType extends SagaPayloadType {
  payload: BusinessOwnerCreateActionPayloadType;
}
interface UpdateAssessmentToolSagaPayloadType extends SagaPayloadType {
  payload: UpdateAssessmentToolActionPayloadType;
}
function* businessOwnersFetchSaga(action: any): any {
  const { filters } = action.payload;
  const page = get(filters, ['page']);

  const ownerData = yield select((state) => state.business_owner);

  const isOwnersPageData = ownerData?.pages?.[page]?.ids?.length;
  try {
    if (!isOwnersPageData || filters.search !== undefined) {
      yield put(setBusinessOwnersLoading({ loading: true }));
      if (!filters.search) {
        delete filters.search;
      }
      const response: BusinessOwnerListResponse = yield call(
        businessOwnerService.fetchList,
        filters
      );
      yield put(addBusinessOwners(response.business_owners));
      yield put(setBusinessOwnersPageData({ paginationMeta: response.meta }));
      yield put(
        setBusinessOwners({
          ids: response.business_owners.map((owner) => owner.id),
          loadMore: filters.page,
        })
      );
      yield put(setBusinessOwnersLoading({ loading: false }));
    }
  } catch (e: any) {
    yield put(setBusinessOwnersError({ error: e?.message }));
  }
}

function* businessOwnerCreateSaga(
  action: BusinessOwnerCreateSagaPayloadType
): any {
  const { businessOwnerCreatePayload, onSuccess } = action.payload;
  try {
    yield put(setCreateBusinessOwnerLoading({ loading: true }));
    const response = yield call(
      businessOwnerService.createBusinessOwner,
      businessOwnerCreatePayload
    );
    const businessOwner = get(response, ['business_owner'], {});
    yield put(setCreatedBusinessOwner({ owner: businessOwner }));
    yield put(setCreateBusinessOwnerLoading({ loading: false }));
    toast.success('Business Owner created successfully!');
    onSuccess();
  } catch (e: any) {
    yield put(
      setCreateBusinessOwnerError({
        error: true,
        message: e?.response?.data?.errors?.[0]?.message,
        errors: e?.response?.data?.errors,
      })
    );
  }
}
function* updateBusinessOwnerSaga(action: any): any {
  const { onSuccess, data, id } = action.payload;
  try {
    yield put(setUpdateBusinessOwnerLoading({ loading: true }));
    const response = yield call(
      businessOwnerService.updateBusinessOwner,
      id,
      data
    );

    yield put(setUpdateBusinessOwner(response));
    yield put(setUpdateBusinessOwnerLoading({ loading: false }));
    onSuccess();
    toast.success('Business Owner updated successfully');
  } catch (e: any) {
    yield put(
      setUpdateBusinessOwnerError({
        error: true,
        message: e?.response?.data?.errors?.[0]?.message,
        errors: e?.response?.data?.errors,
      })
    );
  }
}
function* deleteBusinessOwnerSaga(action: any): any {
  const { id, onSuccess } = action.payload;
  try {
    yield put(setUpdateBusinessOwnerLoading({ loading: true }));
    const response = yield call(businessOwnerService.deleteBusinessOwner, id);
    yield put(setDeleteBusinessOwner(response));
    yield put(setUpdateBusinessOwnerLoading({ loading: false }));
    onSuccess();
    toast.success('Business Owner deleted successfully');
  } catch (e: any) {
    yield put(
      setUpdateBusinessOwnerError({
        error: true,
        message: e?.response?.data?.errors?.[0]?.message,
        errors: e?.response?.data?.errors,
      })
    );
  }
}

function* transferBusinessOwnerSaga(action: any): any {
  const { id, transferRequest, onSuccess } = action.payload;
  try {
    yield put(setUpdateBusinessOwnerLoading({ loading: true }));
    const response = yield call(businessOwnerService.transferBusinessOwner, id, transferRequest);
    yield put(setDeleteBusinessOwner(response));
    yield put(setUpdateBusinessOwnerLoading({ loading: false }));
    onSuccess();
    toast.success('Business Owner transfered successfully');
  } catch (e: any) {
    yield put(
      setUpdateBusinessOwnerError({
        error: true,
        message: e?.response?.data?.errors?.[0]?.message,
        errors: e?.response?.data?.errors,
      })
    );
  }
}

function* businessOwnerUpdateSaga(action: any): any {
  const businessOwnerUpdatePayload: BusinessOwnerDTO = action.payload.data;
  const businessOwnerId = action.payload.id;
  const { user } = action.payload;
  const { callback, tool } = action.payload;
  try {
    let response;
    yield put(setUpdateBusinessOwnerLoading({ loading: true }));
    if (user.type === UserRouteType.ADVISOR) {
      response = yield call(
        businessOwnerService.updateBusinessOwnerByAdvisor,
        businessOwnerId,
        businessOwnerUpdatePayload
      );
    } else {
      response = yield call(
        businessOwnerService.updateBusinessOwnerByOwner,
        businessOwnerUpdatePayload
      );
    }
    const businessOwnerProperties = get(response, ['business_owner'], {});
    yield put(
      updateBusinessOwnerProperty({
        business_owner_properties: businessOwnerProperties,
      })
    );
    yield put(addAssessmentLoading({ loading: false }));
    callback({
      screen:
        tool === AssessmentTools.BUSINESS_VALUATION
          ? BusinessValuationScreens.INFO_SCREEN
          : BusinessContinuityScreens.SOLE_OWNERS,
    });
    yield put(setUpdateBusinessOwnerLoading({ loading: false }));
  } catch (e: any) {
    yield put(setUpdateBusinessOwnerLoading({ loading: false }));
    yield put(
      setUpdateBusinessOwnerError({
        error: true,
        message: e?.response?.data?.errors?.[0]?.message,
        errors: e?.response?.data?.errors,
      })
    );
  }
}

function* businessOwnerFetchSaga(action: any): any {
  const id = Number(action.payload);
  try {
    yield put(setGetBusinessOwnerLoading({ loading: true }));
    const response: BusinessOwnerDetailResponse = yield call(
      businessOwnerService.getBusinessOwner,
      id
    );

    yield put(addBusinessOwner(response.business_owner));
    yield put(
      SetBusinessOwnerAssessmentData({
        assessmentDataMapping: {
          [id]: response.business_owner.assessment_data,
        },
      })
    );
    yield put(setGetBusinessOwnerLoading({ loading: false }));
  } catch (e: any) {
    yield put(setGetBusinessOwnerError({ error: e, message: e.message }));
  }
}

function* updateBusinessAssessmentToolSaga(
  action: UpdateAssessmentToolSagaPayloadType
): any {
  const businessOwnerId = action.payload.id;
  const { assessmentTool, onSuccess, onError } = action.payload;
  const assessmentDataMapping = yield select(
    (state) => state.businessOwner.assessmentDataMapping
  );
  const ownerAssessmentData = assessmentDataMapping[businessOwnerId];
  try {
    yield call(
      businessOwnerService.updateAssessmentTool,
      businessOwnerId,
      assessmentTool
    );
    yield put(
      updateBusinessOwnerAssessmentData({
        tool: assessmentTool.tool,
        status: assessmentTool.status,
        id: businessOwnerId,
        assessmentData: ownerAssessmentData,
      })
    );
    onSuccess(
      `${convertToTitleCase(assessmentTool.tool)} ${
        assessmentTool.status === AssessmentToolStatus.DISABLE
          ? 'Disabled'
          : 'Enabled'
      } Successfully`
    );
  } catch (e: any) {
    onError(e?.message);
  }
}

function* businessOwnerIndustriesFetchSaga(action: any): any {
  const { search } = action.payload;
  try {
    yield put(setBusinessOwnerIndustriesLoading({ loading: true }));
    const response = yield call(
      businessOwnerService.getBusinessOwnerIndustries,
      search
    );

    yield put(addBusinessOwnerIndustries({ industries: response?.data }));
    yield put(setBusinessOwnerIndustriesLoading({ loading: false }));
  } catch (e: any) {
    toast.error(e?.response?.data?.message);
    yield put(setBusinessOwnerIndustriesLoading({ loading: false }));
  }
}

function* businessOwnerLearnMoreSaga(action: any): any {
  const { tool, learnMoreSuccess } = action.payload;
  try {
    yield put(setLearnMoreLoading({ loading: true }));
    const response = yield call(businessOwnerService.businessLearnMore, tool);
    toast.success(response.message);
    learnMoreSuccess();
    yield put(setLearnMoreLoading({ loading: false }));
  } catch (e: any) {
    yield put(setLearnMoreLoading({ loading: false }));
  }
}

export function* businessOwnerSagaWatcher() {
  yield all([
    takeLatest(
      BusinessOwnerActionType.GET_BUSINESS_OWNERS,
      businessOwnersFetchSaga
    ),
    takeLatest(
      BusinessOwnerActionType.GET_BUSINESS_OWNER,
      businessOwnerFetchSaga
    ),
    takeLatest(
      BusinessOwnerActionType.CREATE_BUSINESS_OWNER,
      businessOwnerCreateSaga
    ),
    takeLatest(
      BusinessOwnerActionType.UPDATE_BUSINESS_OWNER,
      businessOwnerUpdateSaga
    ),
    takeLatest(
      BusinessOwnerActionType.UPDATE_ASSESSMENT_TOOL,
      updateBusinessAssessmentToolSaga
    ),
    takeLatest(
      BusinessOwnerActionType.GET_BUSINESS_OWNER_INDUSTRIES,
      businessOwnerIndustriesFetchSaga
    ),
    takeLatest(
      BusinessOwnerActionType.UPDATE_BUSINESS_OWNER_ADVISOR,
      updateBusinessOwnerSaga
    ),
    takeLatest(
      BusinessOwnerActionType.DELETE_BUSINESS_OWNER,
      deleteBusinessOwnerSaga
    ),
    takeLatest(
      BusinessOwnerActionType.TRANSFER_BUSINESS_OWNER,
      transferBusinessOwnerSaga
    ),
    takeLatest(BusinessOwnerActionType.LEARN_MORE, businessOwnerLearnMoreSaga),
  ]);
}
