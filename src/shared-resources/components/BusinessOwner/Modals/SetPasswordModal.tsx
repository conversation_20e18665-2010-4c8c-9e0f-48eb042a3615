import { Form, Formik } from 'formik';
import * as yup from 'yup';
import React from 'react';
import Button from 'shared-resources/components/Button/Button';
import Input from 'shared-resources/components/Input/Input';
import { passwordSchema } from 'utils/helpers/Helpers';

const changePasswordSchema = yup.object().shape({
  newPassword: passwordSchema.required('New Password is required'),
  confirmNewPassword: yup
    .string()
    .oneOf([yup.ref('newPassword')], 'Confirm Password do not match')
    .required('Confirm Password is required'),
});
interface Props {
  handleSetPasswordModalVisibility: (value: boolean) => void;
  onSubmitSetPassword: (values: any) => void;
  businessOwnerProperties?: {
    [key: string]: any;
  };
}
const SetPasswordModal: React.FC<Props> = ({
  handleSetPasswordModalVisibility,
  onSubmitSetPassword,
  businessOwnerProperties,
}) => (
  <div className='bg-white rounded-xl px-12.5 py-8 mb-2'>
    <Formik
      initialValues={{
        newPassword: '',
        confirmNewPassword: '',
      }}
      onSubmit={(values) => {
        onSubmitSetPassword(values);
        handleSetPasswordModalVisibility(false);
      }}
      validationSchema={changePasswordSchema}
    >
      {(formikProps) => (
        <Form
          onSubmit={formikProps.handleSubmit}
          className='flex flex-col gap-7'
        >
          <div className='flex flex-col max-w-2/3 gap-6'>
            <div className='flex justify-between'>
              <h1 className='font-medium text-lg'>
                Setting password for
                <span className='ml-2 font-semibold'>
                  {businessOwnerProperties?.first_name}
                  <span> {businessOwnerProperties?.last_name}</span>
                </span>
                <span>
                  {' '}
                  (
                  <span className='font-semibold'>
                    {businessOwnerProperties?.email}
                  </span>
                  )
                </span>
              </h1>
            </div>
            <div className='w-[40rem]'>
              <Input
                asterisk
                name='newPassword'
                id='newPassword'
                type='password'
                className='h-[2.85rem] outline-none rounded-xl'
                label='New Password'
                labelClassName='font-medium'
                requiredLabel
                value={formikProps.values.newPassword}
                onChange={formikProps.handleChange}
                onBlur={formikProps.handleBlur}
                error={
                  formikProps.touched.newPassword &&
                  formikProps.errors.newPassword
                }
              />
            </div>
            <div className='w-[40rem]'>
              <Input
                asterisk
                name='confirmNewPassword'
                id='confirmNewPassword'
                type='password'
                className='h-[2.85rem] outline-none rounded-xl'
                label='Confirm New Password'
                labelClassName='font-medium'
                requiredLabel
                value={formikProps.values.confirmNewPassword}
                onChange={formikProps.handleChange}
                onBlur={formikProps.handleBlur}
                error={
                  formikProps.touched.confirmNewPassword &&
                  formikProps.errors.confirmNewPassword
                }
              />
            </div>
          </div>
          <div className='flex space-x-4 justify-end'>
            <Button className='px-6 py-2' disabled={!formikProps.dirty}>
              Confirm
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  </div>
);
export default SetPasswordModal;
