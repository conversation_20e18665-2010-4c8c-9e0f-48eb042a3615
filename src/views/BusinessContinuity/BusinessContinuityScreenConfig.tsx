import { User } from 'models/entities/User';
import { NavigateFunction } from 'react-router';
import {
  AssessmentResponseType,
  BusinessContinuityScreens,
  UserType,
} from 'types/enum';
import React from 'react';
import Bankers from './Banking/Bankers';
import Banking from './Banking/Banking';
import Insurance from './Banking/Insurance';
import Leases from './Banking/Leases';
import Loans from './Banking/Loans';
import BasicInformation from './BasicInformation';
import Board<PERSON>hairman from './BoardChairman';
import {
  continuityOptionsForScreen,
  continuityScreenToNumberObject,
  mapOptions,
} from './BusinessContinuityConfig';
import DataPrivacy from './DataPrivacy';
import GetStartedScreen from './GetStartedScreen';
import KeyAdvisors from './KeyAdvisors/KeyAdvisors';
import KeyDirector from './KeyAdvisors/KeyDirector';
import KeyDocument from './KeyDocument';
import MessageWishes from './MessageWishes';
import Miscellaneous from './Miscellaneous';
import PersonalGuarantees from './PersonalGuarantees';
import SoleOwners from './SoleOwners';
import ContinuityIncapacitatedView from './shared-components/ContinuityIncapacitatedView';
import OtherAdvisors from './OtherAdvisors';

export const getContinuityScreen = (
  currentScreen: {
    screen: BusinessContinuityScreens | undefined;
  },
  loggedInUser: User,
  businessContinuityData: any,
  setBusinessContinuityData: any,
  backNextClickHandler: (nextScreen: BusinessContinuityScreens) => void,
  setSubmitType: any,
  navigate: NavigateFunction,
  businessOwnerData: any,
  updateBusinessOwner: (values: any) => void,
  setSaveAsDraftCliked: (clicked: boolean) => void
) => {
  const onSaveToDraft = (data: any, screen: BusinessContinuityScreens) => {
    setSaveAsDraftCliked(true);
    const currentScreen = businessContinuityData?.saved_screen || 0;
    if (currentScreen < continuityScreenToNumberObject[screen]) {
      setBusinessContinuityData({
        ...businessContinuityData,
        [screen]: data,
        saved_screen: continuityScreenToNumberObject[screen],
      });
    } else {
      setBusinessContinuityData({
        ...businessContinuityData,
        [screen]: data,
      });
    }

    setSubmitType(AssessmentResponseType.DRAFT);
  };

  if (currentScreen?.screen === BusinessContinuityScreens.GET_STARTED) {
    return (
      <GetStartedScreen
        onNextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.BASIC_INFORMATION);
        }}
      />
    );
  }
  if (currentScreen?.screen === BusinessContinuityScreens.BASIC_INFORMATION) {
    return (
      <BasicInformation
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.GET_STARTED);
        }}
        businessOwnerProperties={businessOwnerData}
        nextStep={(values, shouldUpdate) => {
          if (shouldUpdate) {
            updateBusinessOwner(values);
          } else {
            backNextClickHandler(BusinessContinuityScreens.SOLE_OWNERS);
          }
        }}
        user={loggedInUser}
      />
    );
  }

  if (currentScreen?.screen === BusinessContinuityScreens.SOLE_OWNERS) {
    return (
      <SoleOwners
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            sole_owners: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.AUTHORIZED_OWNERS);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.BASIC_INFORMATION);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.SOLE_OWNERS);
        }}
      />
    );
  }
  if (currentScreen?.screen === BusinessContinuityScreens.AUTHORIZED_OWNERS) {
    return (
      <ContinuityIncapacitatedView
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            authorized_owners: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.FIRST_PEOPLE_1);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.SOLE_OWNERS);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.AUTHORIZED_OWNERS);
        }}
        screen={BusinessContinuityScreens.AUTHORIZED_OWNERS}
        question='In the event that your Coach ExitSmarts receives a request to provide these Business Continuity Instructions, to whom is the Coach or ExitSmarts authorized to give them?'
      />
    );
  }
  if (currentScreen?.screen === BusinessContinuityScreens.FIRST_PEOPLE_1) {
    return (
      <ContinuityIncapacitatedView
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            first_people_1: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.FIRST_PEOPLE_2);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.AUTHORIZED_OWNERS);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.FIRST_PEOPLE_1);
        }}
        screen={BusinessContinuityScreens.FIRST_PEOPLE_1}
        question='If you become incapaciated, who should be the first person (people) contacted?'
      />
    );
  }

  if (currentScreen?.screen === BusinessContinuityScreens.FIRST_PEOPLE_2) {
    return (
      <ContinuityIncapacitatedView
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            first_people_2: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.INTERIM_OWNERS);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.FIRST_PEOPLE_1);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.FIRST_PEOPLE_2);
        }}
        screen={BusinessContinuityScreens.FIRST_PEOPLE_2}
        question='If you become incapacitated, who should receive and manage these Business Continuity Instructions? (could be different from the person first contacted)'
      />
    );
  }
  if (currentScreen?.screen === BusinessContinuityScreens.INTERIM_OWNERS) {
    return (
      <ContinuityIncapacitatedView
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            interim_owners: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.CORPORATE_OFFICERS);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.FIRST_PEOPLE_2);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.INTERIM_OWNERS);
        }}
        screen={BusinessContinuityScreens.INTERIM_OWNERS}
        question='If you become incapacitated, who should run the business in the interim?'
      />
    );
  }

  if (currentScreen?.screen === BusinessContinuityScreens.CORPORATE_OFFICERS) {
    return (
      <ContinuityIncapacitatedView
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            corporate_officers: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(
            BusinessContinuityScreens.ADDRESS_EMPLOYEE_PEOPLE
          );
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.INTERIM_OWNERS);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.CORPORATE_OFFICERS);
        }}
        screen={BusinessContinuityScreens.CORPORATE_OFFICERS}
        question='Which Corporate Officer(s) can bind the Company to legal agreements - if any?'
      />
    );
  }
  if (
    currentScreen?.screen === BusinessContinuityScreens.ADDRESS_EMPLOYEE_PEOPLE
  ) {
    return (
      <ContinuityIncapacitatedView
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            address_employee_people: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(
            BusinessContinuityScreens.ADDRESS_CUSTOMERS_PEOPLE
          );
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.CORPORATE_OFFICERS);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(
            data,
            BusinessContinuityScreens.ADDRESS_EMPLOYEE_PEOPLE
          );
        }}
        screen={BusinessContinuityScreens.ADDRESS_EMPLOYEE_PEOPLE}
        question='If you become incapacitated who should address your employees?'
      />
    );
  }
  if (
    currentScreen?.screen === BusinessContinuityScreens.ADDRESS_CUSTOMERS_PEOPLE
  ) {
    return (
      <ContinuityIncapacitatedView
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            address_customers_people: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(
            BusinessContinuityScreens.ADDRESS_VENDORS_PEOPLE
          );
        }}
        backStep={() => {
          backNextClickHandler(
            BusinessContinuityScreens.ADDRESS_EMPLOYEE_PEOPLE
          );
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(
            data,
            BusinessContinuityScreens.ADDRESS_CUSTOMERS_PEOPLE
          );
        }}
        screen={BusinessContinuityScreens.ADDRESS_CUSTOMERS_PEOPLE}
        question='If you become incapacitated who should address your key customers?'
      />
    );
  }
  if (
    currentScreen?.screen === BusinessContinuityScreens.ADDRESS_VENDORS_PEOPLE
  ) {
    return (
      <ContinuityIncapacitatedView
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            address_vendors_people: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.BOARD_CHAIRMAN);
        }}
        backStep={() => {
          backNextClickHandler(
            BusinessContinuityScreens.ADDRESS_CUSTOMERS_PEOPLE
          );
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.ADDRESS_VENDORS_PEOPLE);
        }}
        screen={BusinessContinuityScreens.ADDRESS_VENDORS_PEOPLE}
        question='If you become incapacitated who should address your key vendors?'
      />
    );
  }

  if (currentScreen?.screen === BusinessContinuityScreens.BOARD_CHAIRMAN) {
    return (
      <BoardChairman
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            board_chairman: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.INTERIM_CHAIRMAN);
        }}
        backStep={() => {
          backNextClickHandler(
            BusinessContinuityScreens.ADDRESS_VENDORS_PEOPLE
          );
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.BOARD_CHAIRMAN);
        }}
      />
    );
  }
  if (currentScreen?.screen === BusinessContinuityScreens.INTERIM_CHAIRMAN) {
    return (
      <ContinuityIncapacitatedView
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            interim_chairman: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(
            BusinessContinuityScreens.ACCOUNTING_INFORMATION
          );
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.BOARD_CHAIRMAN);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.INTERIM_CHAIRMAN);
        }}
        screen={BusinessContinuityScreens.INTERIM_CHAIRMAN}
        question='If you become incapaticated, and you are the Chariman of the Board, who should take over as the interim Chairman of the Board? (assuming you have the authority to make this decision)'
      />
    );
  }
  if (
    currentScreen?.screen === BusinessContinuityScreens.ACCOUNTING_INFORMATION
  ) {
    return (
      <KeyAdvisors
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            accounting_information: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.ATTORNEYS_INFORMATION);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.INTERIM_CHAIRMAN);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.ACCOUNTING_INFORMATION);
        }}
        screen={BusinessContinuityScreens.ACCOUNTING_INFORMATION}
        question="This section provides a single location for keeping common business and personal information that will be useful in the event of the Business Owner's incapacitation. Completing and keeping this information current will provide significant assistance and clarity for those charged with managing business continuity."
        title='Accounting'
        options={mapOptions(
          continuityOptionsForScreen[
            BusinessContinuityScreens.ACCOUNTING_INFORMATION
          ]
        )}
      />
    );
  }
  if (
    currentScreen?.screen === BusinessContinuityScreens.ATTORNEYS_INFORMATION
  ) {
    return (
      <KeyAdvisors
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            attorneys_information: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.BUSINESS_INFORMATION);
        }}
        backStep={() => {
          backNextClickHandler(
            BusinessContinuityScreens.ACCOUNTING_INFORMATION
          );
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.ATTORNEYS_INFORMATION);
        }}
        screen={BusinessContinuityScreens.ATTORNEYS_INFORMATION}
        title='Attorneys'
        options={mapOptions(
          continuityOptionsForScreen[
            BusinessContinuityScreens.ATTORNEYS_INFORMATION
          ]
        )}
      />
    );
  }
  if (
    currentScreen?.screen === BusinessContinuityScreens.BUSINESS_INFORMATION
  ) {
    return (
      <KeyAdvisors
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            business_information: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.FINANCIAL_ADVISORS);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.ATTORNEYS_INFORMATION);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.BUSINESS_INFORMATION);
        }}
        screen={BusinessContinuityScreens.BUSINESS_INFORMATION}
        title='Business'
        options={mapOptions(
          continuityOptionsForScreen[
            BusinessContinuityScreens.BUSINESS_INFORMATION
          ]
        )}
      />
    );
  }
  if (currentScreen?.screen === BusinessContinuityScreens.FINANCIAL_ADVISORS) {
    return (
      <KeyAdvisors
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            financial_advisors: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.BOARD_OF_DIRECTORS);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.BUSINESS_INFORMATION);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.FINANCIAL_ADVISORS);
        }}
        screen={BusinessContinuityScreens.FINANCIAL_ADVISORS}
        title='Financial Advisors'
        options={mapOptions(
          continuityOptionsForScreen[
            BusinessContinuityScreens.FINANCIAL_ADVISORS
          ]
        )}
      />
    );
  }

  if (currentScreen?.screen === BusinessContinuityScreens.BOARD_OF_DIRECTORS) {
    return (
      <KeyDirector
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            board_of_directors: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.OTHER_ADVISORS);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.FINANCIAL_ADVISORS);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.BOARD_OF_DIRECTORS);
        }}
      />
    );
  }
  if (currentScreen?.screen === BusinessContinuityScreens.OTHER_ADVISORS) {
    return (
      <OtherAdvisors
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            other_advisors: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.DOCUMENTS);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.BOARD_OF_DIRECTORS);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.OTHER_ADVISORS);
        }}
        screen={BusinessContinuityScreens.OTHER_ADVISORS}
        title='Others'
      />
    );
  }
  if (currentScreen?.screen === BusinessContinuityScreens.DOCUMENTS) {
    return (
      <KeyDocument
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            documents: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.BANKERS);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.OTHER_ADVISORS);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.DOCUMENTS);
        }}
        options={mapOptions(
          continuityOptionsForScreen[BusinessContinuityScreens.DOCUMENTS]
        )}
      />
    );
  }
  if (currentScreen?.screen === BusinessContinuityScreens.BANKERS) {
    return (
      <Bankers
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            bankers: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.BANKING_INFORMATION);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.DOCUMENTS);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.BANKERS);
        }}
        options={mapOptions(
          continuityOptionsForScreen[BusinessContinuityScreens.BANKERS]
        )}
      />
    );
  }
  if (currentScreen?.screen === BusinessContinuityScreens.BANKING_INFORMATION) {
    return (
      <Banking
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            banking_information: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.LOANS_INFORMATION);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.BANKERS);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.BANKING_INFORMATION);
        }}
        options={mapOptions(
          continuityOptionsForScreen[
            BusinessContinuityScreens.BANKING_INFORMATION
          ]
        )}
      />
    );
  }
  if (currentScreen?.screen === BusinessContinuityScreens.LOANS_INFORMATION) {
    return (
      <Loans
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            loans_information: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.PERSONAL_GUARANTEES);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.BANKING_INFORMATION);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.LOANS_INFORMATION);
        }}
        options={mapOptions(
          continuityOptionsForScreen[
            BusinessContinuityScreens.LOANS_INFORMATION
          ]
        )}
      />
    );
  }

  if (currentScreen?.screen === BusinessContinuityScreens.PERSONAL_GUARANTEES) {
    return (
      <PersonalGuarantees
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            personal_guarantees: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.LEASES);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.LOANS_INFORMATION);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.PERSONAL_GUARANTEES);
        }}
      />
    );
  }
  if (currentScreen?.screen === BusinessContinuityScreens.LEASES) {
    return (
      <Leases
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            leases: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.INSURANCES);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.PERSONAL_GUARANTEES);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.LEASES);
        }}
        options={mapOptions(
          continuityOptionsForScreen[BusinessContinuityScreens.LEASES]
        )}
      />
    );
  }

  if (currentScreen?.screen === BusinessContinuityScreens.INSURANCES) {
    return (
      <Insurance
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            insurances: data,
          });
        }}
        nextStep={() => {
          if (
            (businessContinuityData?.[BusinessContinuityScreens.SHOULD_PRIVATE]
              ?.is_private === false &&
              loggedInUser.type === UserType.ADVISOR) ||
            loggedInUser.type === UserType.BUSINESS_OWNER
          ) {
            backNextClickHandler(BusinessContinuityScreens.MISCELLANEOUS);
          } else {
            backNextClickHandler(BusinessContinuityScreens.MESSAGE_WISHES);
          }
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.LEASES);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.INSURANCES);
        }}
        options={mapOptions(
          continuityOptionsForScreen[BusinessContinuityScreens.INSURANCES]
        )}
      />
    );
  }
  if (currentScreen?.screen === BusinessContinuityScreens.MISCELLANEOUS) {
    return (
      <Miscellaneous
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            miscellaneous: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessContinuityScreens.MESSAGE_WISHES);
        }}
        backStep={() => {
          backNextClickHandler(BusinessContinuityScreens.INSURANCES);
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.MISCELLANEOUS);
        }}
      />
    );
  }

  if (currentScreen?.screen === BusinessContinuityScreens.MESSAGE_WISHES) {
    return (
      <MessageWishes
        data={businessContinuityData}
        setData={(data: any) => {
          setBusinessContinuityData({
            ...businessContinuityData,
            message_wishes: data,
          });
        }}
        nextStep={() => {
          if (loggedInUser?.type === UserType.ADVISOR) {
            setSubmitType(AssessmentResponseType.COMPLETE);
          } else {
            backNextClickHandler(BusinessContinuityScreens.SHOULD_PRIVATE);
          }
        }}
        backStep={() => {
          if (
            (businessContinuityData?.[BusinessContinuityScreens.SHOULD_PRIVATE]
              ?.is_private === false &&
              loggedInUser.type === UserType.ADVISOR) ||
            loggedInUser.type === UserType.BUSINESS_OWNER
          ) {
            backNextClickHandler(BusinessContinuityScreens.MISCELLANEOUS);
          } else {
            backNextClickHandler(BusinessContinuityScreens.INSURANCES);
          }
        }}
        onSaveToDraftClick={(data: any) => {
          onSaveToDraft(data, BusinessContinuityScreens.MESSAGE_WISHES);
        }}
      />
    );
  }
  return (
    <DataPrivacy
      data={businessContinuityData}
      setData={(data: any) => {
        setBusinessContinuityData({
          ...businessContinuityData,
          should_private: data,
        });
      }}
      nextStep={() => {
        backNextClickHandler(BusinessContinuityScreens.GET_STARTED);
        setSubmitType(AssessmentResponseType.COMPLETE);
      }}
      backStep={() => {
        backNextClickHandler(BusinessContinuityScreens.MESSAGE_WISHES);
      }}
      onSaveToDraftClick={(data: any) => {
        onSaveToDraft(data, BusinessContinuityScreens.SHOULD_PRIVATE);
      }}
    />
  );
};
