import React, { useCallback, useEffect, useRef, useState } from 'react';
import { FaDownload } from 'react-icons/fa6';
import { IoArrowBackSharp } from 'react-icons/io5';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import NavigateContainer from 'shared-resources/components/NavigateContainer';
import {
  fetchAssessmentReport,
  fetchAssessmentReportByBusinessOwner,
} from 'store/actions/assessment-report.action';
import {
  getAssessmentReportLoading,
  getAssessmentReportResponse,
} from 'store/selectors/assessment-report.selector';
import { getUserData } from 'store/selectors/user.selector';
import {
  AssessmentTools,
  BusinessContinuityScreens,
  RouteKey,
  UserRouteType,
  UserType,
} from 'types/enum';

import DarkThemeReportWrapper from 'HOC/DarkThemeReportWrapper';
import LightThemeReportWrapper from 'HOC/LightThemeReportWrapper';
import _, { pick } from 'lodash';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import Button from 'shared-resources/components/Button/Button';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import { generatePdf } from 'utils/generate-pdf/generatePdf-utils';
import {
  getKey,
  getOrderedAwarenessAssessmentData,
} from 'utils/helpers/Helpers';
import CoverPage from 'views/ReadinessAssessment/ReadinessAssessmentReport/CoverPage';
import IntroductionPage from 'views/ReadinessAssessment/ReadinessAssessmentReport/IntrodunctionPage';
import ReportGenerationModal from 'shared-resources/components/BusinessOwner/Modals/ReportGenerationModal';
import Modal from 'shared-resources/components/Modal/Modal';
import { resetAssessmentReportData } from 'store/reducers/assessment-report.reducer';
import ContinuityReportRowData from './ContinuityReportRowData';
import PageWrapper from '../shared-components/PageWrapper';

import MessageWishesReport from './MessageWishesReport';
import MiscellaneousReportData from './MiscellaneousReportData';
import {
  screenToQuestion,
  screenToQuestionSecondary,
  screenToQuestionTertiary,
} from './continuityReportHelper';
import DisclaimerPage from '../../Common/Disclaimer';

const BusinessContinuityReport = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const response: any = useSelector(getAssessmentReportResponse);
  const loading = useSelector(getAssessmentReportLoading);
  const loggedInUserData = useSelector(getUserData);
  const [pages, setPages] = useState<any[]>([]);
  const rowRefs = useRef<any[]>([]);
  const filterEmptyArrayObjects = (data: any) =>
    Object.entries(data).reduce<any>((filteredData, [key, value]) => {
      if (Array.isArray(value)) {
        const nonEmptyObjects = value.filter((obj) => {
          const filteredObj = pick(
            obj,
            Object.keys(obj).filter(
              (key: string) => key !== 'priority' && key !== 'saved'
            )
          );
          return Object.values(filteredObj).some(
            (val) => val != null && String(val).trim() !== ''
          );
        });

        if (nonEmptyObjects.length > 0) {
          filteredData[key] = nonEmptyObjects;
        }
      } else {
        filteredData[key] = value;
      }
      return filteredData;
    }, {});
  const filteredResponse = filterEmptyArrayObjects(
    response?.assessment_response
  );

  useEffect(() => {
    if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      const orderedAwarenessData = getOrderedAwarenessAssessmentData(
        loggedInUserData as BusinessOwner
      );
      const canSeeReport = orderedAwarenessData.find(
        (tool) => tool.toolName === AssessmentTools.BUSINESS_CONTINUITY
      )?.toolData.is_report_sent_to_owner;

      if (!canSeeReport) {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.BUSINESS_CONTINUITY_DASHBOARD}`
        );
      }
    }
  }, [loggedInUserData]);

  useEffect(() => {
    if (id && loggedInUserData?.type === UserType.ADVISOR) {
      dispatch(
        fetchAssessmentReport({
          id,
          tool: AssessmentTools.BUSINESS_CONTINUITY,
          onError: () =>
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`),
        })
      );
    }
    if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchAssessmentReportByBusinessOwner({
          tool: AssessmentTools.BUSINESS_CONTINUITY,
          onError: () =>
            navigate(
              `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.BUSINESS_CONTINUITY}`
            ),
        })
      );
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  const [companyName, setCompanyName] = useState<string>();

  // cleanup function to reset the assessment report data
  useEffect(
    () => () => {
      dispatch(resetAssessmentReportData());
    },
    []
  );

  useEffect(() => {
    if (!_.isEmpty(response.assessment_response)) {
      setCompanyName(filteredResponse?.management_team?.business_name);
    }
  }, [response]);

  const [isDownloading, setIsDownloading] = useState<boolean>(false);

  const downloadReport = async () => {
    setIsDownloading(true);
    const reportPages: any[] = [
      document.getElementById(`cover-page`),
      document.getElementById(`introduction-page`),
    ];

    pages.forEach((page, index) => {
      reportPages.push(
        document.getElementById(`business-continuity-page-${index}`)
      );
    });
    reportPages.push(document.getElementById(`disclaimer-page`));
    const reportName = `BusinessContinuityReport.pdf`;
    await generatePdf(
      reportPages,
      reportName,
      () => setIsDownloading(false),
      'portrait'
    );
  };

  const pageBreak = <div className='my-9' />;

  const secondaryStartIndex = Object.keys(screenToQuestion).length;
  const tertiaryStartIndex =
    Object.keys(screenToQuestion).length +
    Object.keys(screenToQuestionSecondary).length;

  const components = Object.keys(screenToQuestion).map((screen, index) => {
    if (
      screen === BusinessContinuityScreens.SOLE_OWNERS &&
      filteredResponse?.sole_owners &&
      Object.keys(filteredResponse?.sole_owners)?.length
    ) {
      const soleOwnerData = filteredResponse?.sole_owners;

      return (
        <div
          ref={(el) => {
            rowRefs.current[index] = el;
          }}
          key={screen}
          className='flex flex-col gap-4 mt-1 w-full'
        >
          <span className='font-semibold'>{screenToQuestion[screen]}</span>
          <div className='px-4 py-3 rounded-lg border text-blue-01 border-gray-02'>
            {soleOwnerData?.is_sole_owner ? 'Sole Owner' : 'Partners'}
          </div>
          {!!soleOwnerData?.partners?.length && (
            <ContinuityReportRowData
              fieldKey='partners'
              responseData={soleOwnerData}
            />
          )}
        </div>
      );
    }
    if (
      screen === BusinessContinuityScreens.BOARD_CHAIRMAN &&
      filteredResponse?.board_chairman &&
      Object.keys(filteredResponse?.board_chairman)?.length
    ) {
      const chairmanData = filteredResponse?.board_chairman;
      if (Object.keys(chairmanData).length) {
        return (
          <div
            ref={(el) => {
              rowRefs.current[index] = el;
            }}
            key={screen}
            className='flex flex-col gap-4 mt-4 w-full'
          >
            <span className='font-semibold'>
              {screenToQuestion[BusinessContinuityScreens.BOARD_CHAIRMAN]}
            </span>
            {chairmanData?.is_chairman && (
              <div className='px-4 py-3 rounded-lg border text-blue-01 border-gray-02'>
                Yes
              </div>
            )}
            {chairmanData?.is_chairman === false && (
              <div className='px-4 py-3 rounded-lg border text-blue-01 border-gray-02'>
                No
              </div>
            )}
            {chairmanData?.name_of_director && (
              <div className='flex flex-col gap-4'>
                <span className='font-semibold'>
                  Who is the Chairman of the Board?
                </span>
                <div className='px-4 py-3 rounded-lg text-blue-01 border border-gray-02'>
                  {chairmanData?.name_of_director}
                </div>
              </div>
            )}
          </div>
        );
      }
    }
    return (
      !!filteredResponse?.[screen]?.length && (
        <div
          ref={(el) => {
            rowRefs.current[index] = el;
          }}
          className='mt-4 w-full'
          key={screen}
        >
          <ContinuityReportRowData
            fieldKey={screen}
            question={screenToQuestion[screen]}
            responseData={filteredResponse}
          />
        </div>
      )
    );
  });

  const secondaryPageComponents = Object.keys(screenToQuestionSecondary).map(
    (screen, index) =>
      !!filteredResponse?.[screen]?.length && (
        <div
          ref={(el) => {
            rowRefs.current[index + secondaryStartIndex] = el;
          }}
          className='mt-4 w-full'
          key={screen}
        >
          {screen === BusinessContinuityScreens.ACCOUNTING_INFORMATION && (
            <div className='flex flex-col gap-5 my-6 items-center'>
              <h1 className='text-lg text-blue-01 font-semibold'>
                Key Advisors
              </h1>
              <p className='text-center'>
                This section provides a single location for keeping common
                business and personal information that will be useful in the
                event of the Business Owner&apos;s incapacitation. Completing
                and keeping this information current will provide significant
                assistance and clarity for those charged with managing business
                continuity.
              </p>
            </div>
          )}
          {screen === BusinessContinuityScreens.BANKERS && (
            <h1 className='text-lg text-center text-blue-01 my-4 font-semibold'>
              Bankers / Banking / Loans
            </h1>
          )}
          <ContinuityReportRowData
            fieldKey={screen}
            question={screenToQuestionSecondary[screen]}
            responseData={filteredResponse}
            questionClassname='text-blue-01'
          />
        </div>
      )
  );

  const tertiaryPageComponents = Object.keys(screenToQuestionTertiary).map(
    (screen, index) => {
      const showMiscellaneous =
        (loggedInUserData?.type === UserType.ADVISOR &&
          !filteredResponse?.should_private?.is_private) ||
        loggedInUserData?.type === UserType.BUSINESS_OWNER;

      if (
        screen === BusinessContinuityScreens.MISCELLANEOUS &&
        filteredResponse?.miscellaneous &&
        Object.keys(filteredResponse?.miscellaneous)?.length &&
        showMiscellaneous
      ) {
        return (
          <MiscellaneousReportData
            ref={(el) => {
              rowRefs.current[index + tertiaryStartIndex] = el;
            }}
            key={screen}
            data={filteredResponse?.miscellaneous}
          />
        );
      }
      if (
        screen === BusinessContinuityScreens.MESSAGE_WISHES &&
        filteredResponse?.message_wishes &&
        Object.keys(filteredResponse?.message_wishes)?.length
      ) {
        return (
          <MessageWishesReport
            key={screen}
            data={filteredResponse?.message_wishes}
            ref={(el) => {
              rowRefs.current[index + tertiaryStartIndex] = el;
            }}
          />
        );
      }
      if (
        screen === BusinessContinuityScreens.SHOULD_PRIVATE &&
        filteredResponse?.should_private &&
        Object.keys(filteredResponse?.should_private)?.length
      ) {
        return (
          <div
            ref={(el) => {
              rowRefs.current[index + tertiaryStartIndex] = el;
            }}
            key={screen}
            className='flex flex-col gap-4 mt-4 w-full'
          >
            <span className='font-semibold'>
              {
                screenToQuestionTertiary[
                  BusinessContinuityScreens.SHOULD_PRIVATE
                ]
              }
            </span>
            <div className='px-4 py-3 rounded-lg text-blue-01 border border-gray-02'>
              {filteredResponse?.should_private?.is_private ? 'Yes' : 'No'}
            </div>
          </div>
        );
      }
      return null;
    }
  );

  const createPages = (
    rowHeights: any,
    components: JSX.Element[],
    index: number
  ) => {
    const pageHeight = 1000;
    const rowsPerPage = [];
    let currentHeight = 0;
    let currentPageRows: any[] = [];

    components.forEach((component, i) => {
      if (component) {
        const rowHeight = rowHeights[i + index];

        if (currentHeight + rowHeight > pageHeight) {
          if (currentPageRows.length) rowsPerPage.push([...currentPageRows]);
          currentPageRows = [component];
          currentHeight = rowHeight;
        } else {
          currentPageRows.push(component);
          currentHeight += rowHeight;
        }
      }
    });

    if (currentPageRows.length > 0) {
      rowsPerPage.push([...currentPageRows]);
    }
    return rowsPerPage;
  };

  const measureRowHeights = useCallback(() => {
    const rowHeights = rowRefs.current.map(
      (ref) => ref?.getBoundingClientRect()?.height
    );
    const rowsPerPagePrimary = components?.length
      ? createPages(rowHeights, components as JSX.Element[], 0)
      : [];
    const rowsPerPageSecondary = secondaryPageComponents?.length
      ? createPages(
          rowHeights,
          secondaryPageComponents as JSX.Element[],
          secondaryStartIndex
        )
      : [];
    const rowsPerPageTertiary = tertiaryPageComponents?.length
      ? createPages(
          rowHeights,
          tertiaryPageComponents as JSX.Element[],
          tertiaryStartIndex
        )
      : [];

    setPages([
      ...rowsPerPagePrimary,
      ...rowsPerPageSecondary,
      ...rowsPerPageTertiary,
    ]);
  }, [components, secondaryPageComponents, tertiaryPageComponents, rowRefs]);

  useEffect(() => {
    measureRowHeights();
  }, [response, rowRefs]);

  if (loading) {
    return <Spinner customClassName='mx-auto' spinnerTheme='overlaySpinner' />;
  }

  return (
    <div className='w-full h-[calc(100vh-9.6875rem)]'>
      <div className='flex items-center justify-between w-full mb-5'>
        <NavigateContainer
          isEnableToolRoute
          className='min-w-7.5 text-black-02'
        >
          <IoArrowBackSharp size={24} />
        </NavigateContainer>
        <div>
          <Button
            className='py-2 px-6'
            LeadingIcon={FaDownload}
            leadingIconClassName='mr-2'
            onClick={downloadReport}
          >
            Download
          </Button>
        </div>
      </div>
      <div className='flex flex-col items-center h-[calc(100%-2.75rem)] overflow-y-auto pr-2 scrollbar '>
        <div className='m-auto'>
          <DarkThemeReportWrapper id='cover-page'>
            <CoverPage
              pageTitle='Business Continuity Report'
              presentedBy={response?.advisor_name}
              preparedFor={response?.business_owner_data?.business_owner_name}
              reportDate={response?.assessment_completion_date}
              phone={response?.business_owner_data?.business_owner_phone}
              email={response?.business_owner_data?.business_owner_email}
              companyName={companyName}
            />
          </DarkThemeReportWrapper>
        </div>

        {pageBreak}
        <div className='m-auto'>
          <LightThemeReportWrapper id='introduction-page'>
            <IntroductionPage
              pageContent={
                <div className='flex flex-col justify-center space-y-6'>
                  <p>
                    Congratulations, by completing these Business Continuity
                    Instructions you have taken an important step in protecting
                    what you have built and in helping your stakeholders to
                    continue the operation of your business as you would want
                    them to. Your stakeholders’ ability to react, with minimized
                    confusion, upon your incapacitation is a tribute to your
                    foresight and your planning wisdom.{' '}
                  </p>
                  <p>
                    Now that you have completed these instructions, make sure
                    that they are readily available, if and when needed, to
                    guide your stakeholders. This means informing all of your
                    stakeholders that these instructions exist and that they can
                    be accessed through your advisor. Now that you have put
                    these instructions into place, it is even more important
                    that you keep them current. Your advisor will remind you
                    from time to time to review this document to be sure that if
                    always reflects your most current thinking.
                  </p>
                </div>
              }
              userName={response?.business_owner_data?.business_owner_name}
            />
          </LightThemeReportWrapper>
        </div>
        {pageBreak}
        <div id='continuity-report' className='m-auto'>
          {pages.length === 0
            ? [
                ...components,
                ...secondaryPageComponents,
                ...tertiaryPageComponents,
              ]
            : pages.map((pageRows, index) => (
                <PageWrapper
                  id={`business-continuity-page-${index}`}
                  key={getKey(index)}
                >
                  {pageRows}
                </PageWrapper>
              ))}
        </div>
        <div className='m-auto'>
          <LightThemeReportWrapper id='disclaimer-page'>
            <DisclaimerPage />
          </LightThemeReportWrapper>
        </div>
      </div>
      <Modal visible={isDownloading} handleVisibility={() => {}}>
        <ReportGenerationModal />
      </Modal>
    </div>
  );
};

export default BusinessContinuityReport;
