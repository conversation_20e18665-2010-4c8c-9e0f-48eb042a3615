import { Form, Formik } from 'formik';
import React from 'react';
import { IoInformationCircle } from 'react-icons/io5';
import { useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import * as yup from 'yup';
import {
  getTotalNormalizationExpenses,
  getYear,
} from './BusinessValuationConfig';

interface Props {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  data: any;
  onSaveToDraftClick: (data: any) => void;
}
const IncomeStatementData: React.FC<Props> = (props) => {
  const { nextStep, setData, backStep, data, onSaveToDraftClick } = props;
  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup.object().shape({
    [`revenue_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Revenue is required'),
    [`revenue_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Revenue is required'),
    [`revenue_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Revenue is required'),
    [`non_cash_expenses_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Non Cash Expense is required'),
    [`non_cash_expenses_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Non Cash Expense is required'),
    [`non_cash_expenses_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Non Cash Expense is required'),
    [`pretax_income_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Pretax Income is required'),
    [`pretax_income_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Pretax Income is required'),
    [`pretax_income_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Pretax Income is required'),
    [`interest_expense_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Interest Expense is required'),
    [`interest_expense_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Interest Expense is required'),
    [`interest_expense_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Interest Expense is required'),
    [`onetime_gains_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('One Time Gains is required'),
    [`onetime_gains_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('One Time Gains is required'),
    [`onetime_gains_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('One Time Gains is required'),
    [`onetime_losses_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('OneTime Loss is required'),
    [`onetime_losses_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('OneTime Loss is required'),
    [`onetime_losses_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('OneTime Loss is required'),
    [`normalization_${getYear(1)}`]: yup.number(),
    [`normalization_${getYear(2)}`]: yup.number(),
    [`normalization_${getYear(3)}`]: yup.number(),
  });

  const handleSubmit = (values: any) => {
    setData(values);
    nextStep();
  };

  const totalNormalizationExpenses = getTotalNormalizationExpenses(data);

  return (
    <div className='flex flex-col '>
      <Formik
        initialValues={{
          [`revenue_${getYear(1)}`]:
            data?.income_statement_data?.[`revenue_${getYear(1)}`] || '',
          [`revenue_${getYear(2)}`]:
            data?.income_statement_data?.[`revenue_${getYear(2)}`] || '',
          [`revenue_${getYear(3)}`]:
            data?.income_statement_data?.[`revenue_${getYear(3)}`] || '',
          [`non_cash_expenses_${getYear(1)}`]:
            data?.income_statement_data?.[`non_cash_expenses_${getYear(1)}`] ||
            '',
          [`non_cash_expenses_${getYear(2)}`]:
            data?.income_statement_data?.[`non_cash_expenses_${getYear(2)}`] ||
            '',
          [`non_cash_expenses_${getYear(3)}`]:
            data?.income_statement_data?.[`non_cash_expenses_${getYear(3)}`] ||
            '',
          [`pretax_income_${getYear(1)}`]:
            data?.income_statement_data?.[`pretax_income_${getYear(1)}`] || '',
          [`pretax_income_${getYear(2)}`]:
            data?.income_statement_data?.[`pretax_income_${getYear(2)}`] || '',
          [`pretax_income_${getYear(3)}`]:
            data?.income_statement_data?.[`pretax_income_${getYear(3)}`] || '',
          [`interest_expense_${getYear(1)}`]:
            data?.income_statement_data?.[`interest_expense_${getYear(1)}`] ||
            '',
          [`interest_expense_${getYear(2)}`]:
            data?.income_statement_data?.[`interest_expense_${getYear(2)}`] ||
            '',
          [`interest_expense_${getYear(3)}`]:
            data?.income_statement_data?.[`interest_expense_${getYear(3)}`] ||
            '',
          [`onetime_gains_${getYear(1)}`]:
            data?.income_statement_data?.[`onetime_gains_${getYear(1)}`] || '',
          [`onetime_gains_${getYear(2)}`]:
            data?.income_statement_data?.[`onetime_gains_${getYear(2)}`] || '',
          [`onetime_gains_${getYear(3)}`]:
            data?.income_statement_data?.[`onetime_gains_${getYear(3)}`] || '',
          [`onetime_losses_${getYear(1)}`]:
            data?.income_statement_data?.[`onetime_losses_${getYear(1)}`] || '',
          [`onetime_losses_${getYear(2)}`]:
            data?.income_statement_data?.[`onetime_losses_${getYear(2)}`] || '',
          [`onetime_losses_${getYear(3)}`]:
            data?.income_statement_data?.[`onetime_losses_${getYear(3)}`] || '',
          [`normalization_${getYear(1)}`]:
            totalNormalizationExpenses.totalNormalizationExpensesFirst || 0,
          [`normalization_${getYear(2)}`]:
            totalNormalizationExpenses.totalNormalizationExpensesSecond || 0,
          [`normalization_${getYear(3)}`]:
            totalNormalizationExpenses.totalNormalizationExpensesThird || 0,
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnMount
      >
        {({ values }) => (
          <Form className='pl-5 py-3 relative flex flex-col gap-4 bg-white'>
            <div className='relative z-10 pb-5 mt-3 text-xl items-center font-semibold'>
              <span className='absolute left-[26%]'>{getYear(1)}</span>
              <span className='absolute left-[52%]'>{getYear(2)}</span>
              <span className='absolute left-[77.5%]'>{getYear(3)}</span>
            </div>
            <div className='h-[calc(100vh-22.5rem)] mt-4 overflow-auto pr-4 scrollbar '>
              <div className='grid grid-cols-4  gap-x-16  gap-y-1 font-[500]'>
                <div className='flex  items-center gap-2'>
                  <IoInformationCircle id='Revenue' className='text-blue-01' />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='#Revenue'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      The amount of money your business receives for selling
                      goods or services.
                    </Tooltip>
                    <span className='w-full'>Revenue</span>
                  </div>
                </div>

                <FormikInput
                  className='!h-10'
                  name={`revenue_${getYear(1)}`}
                  key={`revenue_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`revenue_${getYear(2)}`}
                  key={`revenue_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`revenue_${getYear(3)}`}
                  key={`revenue_${getYear(3)}`}
                />
                <div className='flex  items-center gap-2'>
                  <IoInformationCircle
                    id='NonCashExpenses'
                    className='text-blue-01'
                  />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='#NonCashExpenses'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      These are expenses which did not require the use of cash
                      in the current period. Customarily these items include
                      only amortizaiton and depreciation expense.
                    </Tooltip>
                    <span className='w-full'>Non Cash Expenses</span>
                  </div>
                </div>

                <FormikInput
                  className='!h-10'
                  name={`non_cash_expenses_${getYear(1)}`}
                  key={`non_cash_expenses_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`non_cash_expenses_${getYear(2)}`}
                  key={`non_cash_expenses_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`non_cash_expenses_${getYear(3)}`}
                  key={`non_cash_expenses_${getYear(3)}`}
                />
                <div className='flex  items-center gap-2'>
                  <IoInformationCircle
                    id='PretaxIncome'
                    className='text-blue-01'
                  />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='#PretaxIncome'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      The amount of money your business earns (revenue minus
                      expenses) before deducting income taxes. Also known as
                      pretax profit.
                    </Tooltip>
                    <span className='w-full'>Pretax Income</span>
                  </div>
                </div>

                <FormikInput
                  className='!h-10'
                  name={`pretax_income_${getYear(1)}`}
                  key={`pretax_income_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`pretax_income_${getYear(2)}`}
                  key={`pretax_income_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`pretax_income_${getYear(3)}`}
                  key={`pretax_income_${getYear(3)}`}
                />
                <div className='flex  items-center gap-2'>
                  <IoInformationCircle
                    id='InterestExpense'
                    className='text-blue-01'
                  />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='#InterestExpense'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      The amount of interest expense paid by your company on
                      various types of loans, credit carts, etc.
                    </Tooltip>
                    <span className='w-full'>
                      <span className='w-full'>Interest Expense</span>
                    </span>
                  </div>
                </div>

                <FormikInput
                  className='!h-10'
                  name={`interest_expense_${getYear(1)}`}
                  key={`interest_expense_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`interest_expense_${getYear(2)}`}
                  key={`interest_expense_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`interest_expense_${getYear(3)}`}
                  key={`interest_expense_${getYear(3)}`}
                />
                <div className='flex  items-center gap-2'>
                  <IoInformationCircle
                    id='onetimegains'
                    className='text-blue-01'
                  />
                  <div className='relative w-full'>
                    <Tooltip
                      anchorSelect='#onetimegains'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      This does NOT include typical operations, sales &
                      administrative expenses. Characterized by being
                      &quot;unusual in nature and infrequent in occurance&quot;.
                      Any amount enteted will raise the amount of earnings and
                      increase the company&quot; value.
                    </Tooltip>
                    <span className='w-full'>
                      <span className='w-full'>
                        One-time/non-operating revenues/gains
                      </span>
                    </span>
                  </div>
                </div>

                <FormikInput
                  className='!h-10'
                  name={`onetime_gains_${getYear(1)}`}
                  key={`onetime_gains_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`onetime_gains_${getYear(2)}`}
                  key={`onetime_gains_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`onetime_gains_${getYear(3)}`}
                  key={`onetime_gains_${getYear(3)}`}
                />
                <div className='flex  items-center gap-2'>
                  <IoInformationCircle
                    id='onetimelosses'
                    className='text-blue-01'
                  />
                  <div className='w-full relative'>
                    <Tooltip
                      anchorSelect='#onetimelosses'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      This does NOT include gains from typical operations and
                      sales. Characterized by being &quot;unusual in nature and
                      infrequent in occurance&quot;. Any amount enteted will
                      decrease the amount of earnings and lower the company
                      value.
                    </Tooltip>

                    <span className='w-full'>
                      One-time/non-operating expenses/losses
                    </span>
                  </div>
                </div>

                <FormikInput
                  className='!h-10'
                  name={`onetime_losses_${getYear(1)}`}
                  key={`onetime_losses_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`onetime_losses_${getYear(2)}`}
                  key={`onetime_losses_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`onetime_losses_${getYear(3)}`}
                  key={`onetime_losses_${getYear(3)}`}
                />
                <div className='flex 4xl:relative items-center gap-2'>
                  <IoInformationCircle
                    id='Normalization'
                    className='text-blue-01 '
                  />
                  <div className='w-full'>
                    <Tooltip
                      anchorSelect='#Normalization'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500] !left-24  4xl:!left-8 !top-[75%] border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      This does NOT include gains from typical operations and
                      sales. Characterized by being &quot;unusual in nature and
                      infrequent in occurance&quot;. Any amount enteted will
                      decrease the amount of earnings and lower the company
                      value.
                    </Tooltip>

                    <span>
                      This is the Total Sellers Discretionary Earnings figure
                      that are computed using the SDE Worksheets
                    </span>
                  </div>
                </div>

                <FormikInput
                  disabled
                  className='!h-10'
                  name={`normalization_${getYear(1)}`}
                  key={`normalization_${getYear(1)}`}
                />
                <FormikInput
                  disabled
                  className='!h-10'
                  name={`normalization_${getYear(2)}`}
                  key={`normalization_${getYear(2)}`}
                />
                <FormikInput
                  disabled
                  className='!h-10'
                  name={`normalization_${getYear(3)}`}
                  key={`normalization_${getYear(3)}`}
                />
              </div>
            </div>

            <div className='pr-7'>
              <BackNextComponent
                backStep={backStep}
                buttonType='submit'
                isLoading={isLoading}
                isNextDisable={false}
                onSaveToDraftClick={() => {
                  onSaveToDraftClick(values);
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default IncomeStatementData;
