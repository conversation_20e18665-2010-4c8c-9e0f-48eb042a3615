import classNames from 'classnames';
import React, { memo } from 'react';
import { FormattedToolResponse } from '../FinancialGapAnalysisConfig';

interface Props {
  response: FormattedToolResponse[];
  isReportBuilder?: boolean;
}
const ToolTableComponent: React.FC<Props> = ({ response, isReportBuilder }) => (
  <>
    <div>
      {isReportBuilder || (
        <span className='text-2xl font-bold text-26 text-blue-01'>
          Wealth Gap Analysis
        </span>
      )}
    </div>

    <div className='flex flex-col w-full space-y-7 mt-5'>
      {response?.map((toolResponse) => (
        <div
          className='relative scrollbar overflow-auto w-full rounded-t-md'
          key={toolResponse.heading}
        >
          <div
            className={`bg-blue-01 z-10 tracking-[0.07rem] sticky top-0 overflow-hidden text-white 
       rounded-t-md px-10 py-6`}
          >
            <span className='w-full text-2xl font-semibold'>
              {toolResponse.heading}
            </span>
          </div>
          <div>
            {(toolResponse?.rows || []).map((businessFunction: any) => (
              <div
                className={classNames(
                  'grid grid-cols-6 font-[500]  text-[0.9rem]'
                )}
                key={businessFunction.heading}
              >
                <div className='px-1  2xl:px-3 col-span-4  flex items-center border-b border-l py-1 border-r border-gray-02'>
                  <span className='text-xs xl:text-sm '>
                    {businessFunction.heading}
                  </span>
                </div>
                <div className='px-1 2xl:px-3 col-span-2 text-center border-b border-l py-1 border-r border-gray-02'>
                  <span className='text-xs xl:text-sm break-all'>
                    {businessFunction.value}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  </>
);

export default memo(ToolTableComponent);
