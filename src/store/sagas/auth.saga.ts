import { toast } from 'react-toastify';
import { all, call, put, takeLatest } from 'redux-saga/effects';
import { localStorageService } from 'services/LocalStorageService';
import { authService } from 'services/api-services/AuthService';
import { AuthActionType } from 'store/actions/actions.constants';
import {
  AuthLoginActionPayloadType,
  authFetchMeErrorAction,
} from 'store/actions/auth.action';
import {
  loginCompletedAction,
  loginErrorAction,
  loginLoading,
} from 'store/reducers/auth.reducer';
import {
  AddAdvisorSubscriptionData,
  MFADetails,
  userAdd,
} from 'store/reducers/user.reducer';
import { SagaPayloadType } from 'types/SagaPayload.type';
import { UserRouteType, UserType } from 'types/enum';

interface LoginSagaPayloadType extends SagaPayloadType {
  payload: AuthLoginActionPayloadType;
}
interface LoginWithCodeSagaPayloadType extends SagaPayloadType {
  payload: { code: string; userType: UserType; onError: () => void };
}
interface ResendCodeSagaPayloadType extends SagaPayloadType {
  payload: { userType: UserType; onSuccess: () => void; onError: () => void };
}
interface FetcheMeSagaPayloadType extends SagaPayloadType {
  payload: { userType: UserRouteType };
}

function* getMfaCode(data: LoginSagaPayloadType): any {
  try {
    yield put(loginLoading({ loading: true }));
    const response = yield call(authService.fetchCode, data.payload);
    localStorageService.setLocalStorageValue('mfa_token', response?.mfa_token);
    yield put(MFADetails({ data: { ...response } }));
    data?.payload?.onSuccess?.();
    yield put(loginLoading({ loading: false }));
    toast.success('Login Validation Successful!');
  } catch (e: any) {
    yield put(
      loginErrorAction(
        (e?.errors && e.errors[0]?.message) || e?.response?.data?.message
      )
    );
    yield put(loginLoading({ loading: false }));
  }
}
function* resendMfaCode(data: ResendCodeSagaPayloadType): any {
  try {
    yield call(authService.resendCode, data.payload);
    data?.payload?.onSuccess();
    toast.success('Code Successfully Sent to Your Email');
  } catch (e: any) {
    yield put(
      loginErrorAction(
        (e?.errors && e.errors[0]?.message) || e?.response?.data?.message
      )
    );
    data?.payload?.onError?.();
  }
}

function* loginSaga(data: LoginWithCodeSagaPayloadType): any {
  try {
    const response = yield call(authService.login, data.payload);
    yield put(loginCompletedAction({ id: response?.user?.id }));
    yield put(userAdd(response?.user));
    yield put(
      AddAdvisorSubscriptionData({
        data: {
          ...response?.subscription,
          onboardingFee: response?.onboardingFee,
          primaryAdvisor: response?.primaryAdvisor,
          secondaryAdvisor: response?.secondaryAdvisor,
        },
      })
    );

    localStorageService.setAuthToken(response?.token);
    localStorageService.removeLocalStorageValue('mfa_token');
  } catch (e: any) {
    yield put(
      loginErrorAction(
        (e?.errors && e.errors[0]?.message) || e?.response?.data?.message
      )
    );
    yield put(loginLoading({ loading: false }));
    data?.payload?.onError?.();
  }
}

function* fetchLoggedInUserSaga(data: FetcheMeSagaPayloadType): any {
  try {
    yield put(loginLoading({ loading: true }));
    const response = yield call(authService.fetchMe, data.payload.userType);
    yield put(loginCompletedAction({ id: response?.user?.id }));
    yield put(userAdd(response.user));
    yield put(
      AddAdvisorSubscriptionData({
        data: {
          ...response?.subscription,
          onboardingFee: response?.onboardingFee,
          primaryAdvisor: response?.primaryAdvisor,
          secondaryAdvisor: response?.secondaryAdvisor,
        },
      })
    );
  } catch (e: any) {
    localStorageService.removeAuthToken();
    yield put(authFetchMeErrorAction(e?.message));
    yield put(loginLoading({ loading: false }));
  }
}

export function* authSaga() {
  yield all([
    takeLatest(AuthActionType.LOGIN, loginSaga),
    takeLatest(AuthActionType.FETCH_ME, fetchLoggedInUserSaga),
    takeLatest(AuthActionType.GET_MFA_CODE, getMfaCode),
    takeLatest(AuthActionType.RESEND_MFA_CODE, resendMfaCode),
  ]);
}
