import cx from 'classnames';
import React, { useCallback } from 'react';
import { FaCaretDown } from 'react-icons/fa';
import ReactSelect, {
  ActionMeta,
  components,
  MultiValue,
  SingleValue,
  StylesConfig,
} from 'react-select';
import { MenuPlacement } from 'react-select/dist/declarations/src/types';

export type SelectOptionValue = string | number;

export interface SelectOption {
  value: SelectOptionValue;
  label: string;
  disabled?: boolean;
  status?: string; // for filtering published listings in add reservation
}

export interface SelectProps {
  options: SelectOption[];
  onChange?: any;
  selectedOption?: SelectOptionValue | null;
  selectedMultiOptions?: SelectOptionValue[];
  rounded?: 'xl' | 'md' | '4xl' | '2xl';
  label?: string;
  mandatory?: boolean;
  labelClasses?: string;
  errors?: string;
  isClearable?: boolean;
  touched?: boolean;
  onInputChange?: any;
  placeholder?: string;
  className?: string;
  isMulti?: boolean;
  hasSelectAll?: boolean;
  hasSelectNone?: boolean;
  disabled?: boolean;
  menuPlacement?: MenuPlacement;
  errorClassName?: string;
  selectClassName?: string;
  optionTextAlignment?: string;
  showSelectAllSelectNone?: boolean;
  disableInternalSearch?: boolean;
  searchable?: boolean;
  controlHeight?: string;
  asterisk?: boolean;
  menuIsOpen?: boolean;
  showDropdownIcon?: boolean;
  isLoading?: boolean;
  optionPadding?: string;
  menuPadding?: string;
  menuListPadding?: string;
}

const DropdownIndicator = (props: any) => (
  <components.DropdownIndicator {...props}>
    <FaCaretDown size={20} className='text-black-02 text-base' />
  </components.DropdownIndicator>
);

const Select: React.FC<SelectProps> = (props: SelectProps) => {
  const {
    options,
    selectedOption,
    selectedMultiOptions,
    rounded = 'md',
    label,
    asterisk,
    labelClasses = 'font-medium mb-2 leading-none',
    errors = '',
    touched,
    placeholder = 'Please select an option',
    onChange,
    className,
    searchable = true,
    isMulti = false,
    hasSelectAll = true,
    hasSelectNone = true,
    isClearable,
    disabled = false,
    menuPlacement = 'auto',
    onInputChange,
    errorClassName,
    showSelectAllSelectNone = true,
    disableInternalSearch = false,
    selectClassName,
    controlHeight = '3.125rem',
    menuIsOpen,
    showDropdownIcon = true,
    isLoading,
    optionPadding,
    menuPadding,
    menuListPadding,
  } = props;

  const customStyles: StylesConfig<SelectOption> = {
    control: () => ({
      borderWidth: '1px',
      borderColor: '#666666',
      borderRadius: '.75rem',
      background: 'white',
      padding: '.25rem .5rem',
      height: controlHeight,
      display: 'flex',
      alignItems: 'center',
      opacity: disabled ? 0.5 : 1, // Adjust opacity when disabled
    }),
    indicatorSeparator: () => ({
      display: 'none',
    }),
    valueContainer: (base: any) => ({
      ...base,
      color: '#544D4D',
      position: 'relative',
    }),
    singleValue: (base: any) => ({
      ...base,
      color: '#544D4D',
      fontWeight: 500,
    }),
    option: (base) => ({
      ...base,
      color: '#544D4D',
      fontWeight: 500,
      borderRadius: '.75rem',
      paddingLeft: '1rem',
      padding: optionPadding || '1rem .625rem',
    }),
    menu: (base) => ({
      ...base,
      borderRadius: '.75rem',
      padding: menuPadding || '.75rem',
      borderWidth: '1px',
      borderColor: '#666666',
    }),
    menuList: (base) => ({
      ...base,
      '::-webkit-scrollbar': {
        width: '.5rem',
        height: '.5rem',
      },
      '::-webkit-scrollbar-track': {
        borderRadius: '.5rem',
        backgroundColor: '#E1F0F6',
      },
      '::-webkit-scrollbar-thumb': {
        borderRadius: '.5rem',
        backgroundColor: '#93C5FD',
      },
      '::-webkit-scrollbar-thumb:hover': {
        backgroundColor: '#20A0D6',
      },
      display: 'flex',
      flexDirection: 'column',
      gap: '.25rem',
      paddingRight: '.5rem',
      padding: menuListPadding,
    }),
    placeholder: (base) => ({
      ...base,
      color: '#544D4D',
      fontWeight: 500,
    }),
    menuPortal: (base: any) => ({ ...base, zIndex: 9999 }),
  };

  const multiValueContainer = (multiValueContainerProps: any) => {
    const { selectProps, data } = multiValueContainerProps;
    const { label: multiValueLabel } = data;
    const allSelected = selectProps.value;
    const index = allSelected.findIndex(
      (selected: any) => selected.label === multiValueLabel
    );
    const isLastSelected = index === allSelected.length - 1;
    let labelSuffix = ',';
    if (multiValueLabel === 'Select All') {
      labelSuffix = '';
    } else if (isLastSelected) {
      labelSuffix = `(${allSelected.length})`;
    }
    // eslint-disable-next-line react/jsx-no-useless-fragment
    return <>{`${multiValueLabel}${labelSuffix}`}</>;
  };

  const selectAllOption = {
    value: '<SELECT_ALL>',
    label: 'Select All',
  };

  const selectNoneOption = {
    value: '<SELECT_NONE>',
    label: 'Select None',
  };

  const getOptions = useCallback(() => {
    if (isMulti) {
      const updatedOptions = [...options];
      if (options.length !== 0) {
        if (hasSelectNone) {
          updatedOptions.unshift(selectNoneOption);
        }
        if (hasSelectAll) {
          updatedOptions.unshift(selectAllOption);
        }
      }
      return updatedOptions;
    }
    return options;
  }, [options]);

  let borderRadius = 0;
  if (rounded === '4xl') {
    borderRadius = 20;
  } else if (rounded === '2xl') {
    borderRadius = 10;
  } else if (rounded === 'xl') {
    borderRadius = 5;
  } else {
    borderRadius = 3;
  }

  const isSelectAllSelected = () =>
    !!showSelectAllSelectNone &&
    selectedMultiOptions?.length === options.length;

  const isOptionSelected = (option: SelectOption) => {
    if (isMulti) {
      return (
        selectedMultiOptions?.some((value) => value === option.value) ||
        isSelectAllSelected()
      );
    }
    return selectedOption === option.value;
  };

  const getValue = useCallback(() => {
    if (isMulti) {
      return isSelectAllSelected()
        ? [selectAllOption]
        : options.filter((option: SelectOption) =>
            selectedMultiOptions?.includes(option.value)
          );
    }

    if (!options?.length && !!selectedOption) {
      return { label: selectedOption, value: selectedOption } as SelectOption;
    }
    return (
      options?.find(
        (option: SelectOption) => option.value === selectedOption
      ) || null
    );
  }, [options, selectedOption, selectedMultiOptions, isMulti]);

  const onChangeOption = (
    newValue: MultiValue<SelectOption> | SingleValue<SelectOption>,
    actionMeta: ActionMeta<SelectOption>
  ) => {
    const { action, option, removedValue } = actionMeta;
    let updatedOptions: SelectOption[] = [];
    if (action === 'clear') {
      updatedOptions = [];
      onChange(null, actionMeta);
      return;
    }

    if (isMulti) {
      if (
        action === 'select-option' &&
        option?.value === selectAllOption.value
      ) {
        updatedOptions = options;
      } else if (
        (action === 'deselect-option' &&
          option?.value === selectAllOption.value) ||
        (action === 'remove-value' &&
          removedValue?.value === selectAllOption.value) ||
        (action === 'select-option' &&
          option?.value === selectNoneOption.value) ||
        (action === 'deselect-option' &&
          option?.value === selectNoneOption.value)
      ) {
        updatedOptions = [];
      } else if (
        actionMeta.action === 'deselect-option' &&
        isSelectAllSelected()
      ) {
        updatedOptions = options.filter(({ value }) => value !== option?.value);
      } else {
        updatedOptions =
          (newValue as SelectOption[])?.filter(
            (value: any) => value.value !== selectNoneOption?.value
          ) || [];
      }
      const updatedOptionValues = updatedOptions.map(({ value }) => value);
      onChange(updatedOptionValues, actionMeta);
    } else {
      const updatedOptionValue = (newValue as SelectOption).value;
      onChange(updatedOptionValue, actionMeta);
    }
  };

  const customOptionContainer = (optionProps: any) => {
    const { label: optionLabel, isSelected } = optionProps;
    return (
      <div>
        <components.Option
          {...optionProps}
          className='flex flex-row items-center justify-start'
        >
          {!['Select None', 'Select All'].includes(optionLabel) && (
            <>
              <input
                type='checkbox'
                id='react-select-option'
                checked={isSelected}
                onChange={() => null}
                className='mr-2 border-none accent-primary-600'
              />{' '}
            </>
          )}
          <label htmlFor='react-select-option'>{optionLabel}</label>
        </components.Option>
      </div>
    );
  };

  return (
    <div className={cx('relative', className)}>
      {!!label && (
        <div className={cx('px-1', labelClasses)}>
          {label}
          {asterisk && <span className='text-red-500 ml-1'>*</span>}
        </div>
      )}
      <ReactSelect
        backspaceRemovesValue
        onInputChange={onInputChange}
        onChange={onChangeOption}
        isMulti={isMulti}
        menuShouldScrollIntoView={false}
        menuPortalTarget={document.body}
        isOptionSelected={isOptionSelected}
        closeMenuOnSelect={!isMulti}
        hideSelectedOptions={false}
        className={`w-full ${selectClassName}`}
        filterOption={disableInternalSearch ? (option) => !!option : undefined}
        isClearable={isClearable}
        placeholder={placeholder}
        styles={customStyles}
        value={getValue()}
        theme={(theme) => ({
          ...theme,
          borderRadius,
          colors: {
            ...theme.colors,
            primary: '#E1F0F6',
          },
        })}
        isSearchable={searchable}
        options={getOptions()}
        components={
          isMulti
            ? {
                MultiValueContainer: multiValueContainer,
                Option: customOptionContainer,
              }
            : {
                DropdownIndicator: showDropdownIcon ? DropdownIndicator : null,
              }
        }
        isDisabled={disabled}
        menuPlacement={menuPlacement}
        menuIsOpen={menuIsOpen && !isMulti}
        isLoading={isLoading}
      />
      <div className={cx('mt-1 px-1 text-xs text-red-600', errorClassName)}>
        {touched && <span>{errors}</span>}
      </div>
    </div>
  );
};

export default React.memo(Select);
