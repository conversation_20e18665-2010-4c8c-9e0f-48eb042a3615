import React, { useEffect } from 'react';
import { TiTick } from 'react-icons/ti';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router';
import Button from 'shared-resources/components/Button/Button';
import { getPaymentLinkAction, updateAdvisorForPayment } from 'store/actions/payment.action';
import { getUserData } from 'store/selectors/user.selector';
import { RouteKey, UserRouteType, UserType } from 'types/enum';
import { IoArrowBackSharp } from 'react-icons/io5';
import NavigateContainer from 'shared-resources/components/NavigateContainer';
import { getPaymentLoading } from '../../store/selectors/assessment-tool.selector';
import logo from '../../assets/ExitSmartsLogo.svg';

const PaymentPage: React.FC = () => {
  const dispatch = useDispatch();
  const loggedInUser = useSelector(getUserData);
  const loading = useSelector(getPaymentLoading);
  const params = useParams();
  const { id } = params;

  useEffect(() => {
    if (loggedInUser?.type === UserType.BUSINESS_OWNER) {
      dispatch(updateAdvisorForPayment(Number(loggedInUser.id)));
    }
  }, [loggedInUser, dispatch]);

  return (
    <div className='flex flex-col justify-center items-center relative bg-white rounded-xl h-full gap-3'>
      <NavigateContainer
        defaultPrevRoute={
          loggedInUser?.type === UserType.ADVISOR
            ? `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${id}`
            : `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.BUSINESS_VALUATION_DASHBOARD}`
        }
        className='min-w-7.5 absolute top-16 left-16 text-black-02'
      >
        <IoArrowBackSharp size={24} />
      </NavigateContainer>
      <div>
        <img
          src={logo}
          alt='exit-smart-logo'
          className='object-contain w-[10rem] h-[3rem]'
        />
      </div>

      <div className='flex flex-col items-center gap-1 mt-7'>
        <h2 className='text-[1.3rem] font-bold ml-3'>Business Valuation</h2>
        <h2 className='text-blue-01 font-[500] text-[1.1rem]'>
          Accessible And Affordable
        </h2>
      </div>
      <div className='flex flex-col justify-center text-center items-center text-gray-500 text-sm w-2/3 mt-4'>
        <p>
          Knowing the true value of your business - most likely your most
          valuable asset - is critical to proper business, personal and
          financial planning. The valuation process can help answer the
          questions that will lead you to make informed decisions for your
          future.
        </p>
      </div>
      <div className='bg-gray-100 w-2/3 p-7 flex justify-between rounded-md mt-3'>
        <div className='flex flex-col font-[450] gap-1'>
          <span>
            <h3 className='text-blue-01 font-bold'>
              Minimal Work On Your End:
            </h3>
            <div className='flex items-center gap-2 mt-2'>
              <TiTick className='text-xl text-blue-01' />
              <h3>Just provide financials</h3>
            </div>
          </span>
          <span className='mt-4 flex flex-col gap-4'>
            <h3 className='text-blue-01 font-bold'>You Will Recive:</h3>
            <div className='flex items-center gap-2'>
              <TiTick className='text-xl text-blue-01' />
              <h3>Today’s value of your business</h3>
            </div>
            <div className='flex  flex-row gap-2'>
              <TiTick className='text-xl flex-shrink-0 text-blue-01' />
              <h3>
                Key performance indicators to learn how your business compares
                to your industry .
              </h3>
            </div>
          </span>
        </div>

        {loggedInUser?.type !== UserType.BUSINESS_OWNER && (
          <div className='flex flex-col w-80 bg-white rounded-md px-5 py-3 font-[500]'>
            <div className='flex flex-col items-center text-center'>
              Invest Now and Learn the Value of Your Business
            </div>

            <div className='mt-6 flex gap-2 mx-auto'>
              <h3 className='font-bold text-3xl'>$2500</h3>
              <h3 className='mt-3'>USD</h3>
            </div>
            <Button
              onClick={() => {
                if (id && loggedInUser?.type === UserType.ADVISOR) {
                  dispatch(
                    getPaymentLinkAction({
                      userType: loggedInUser?.type as UserType,
                      userId: +id,
                    })
                  );
                } else {
                  dispatch(
                    getPaymentLinkAction({
                      userType: loggedInUser?.type as UserType,
                    })
                  );
                }
              }}
              isSubmitting={loading}
              className='py-4 mt-4 w-60 mx-auto'
            >
              Get Started
            </Button>
          </div>
        )}

        {loggedInUser?.type == UserType.BUSINESS_OWNER && (
          <div className='flex flex-col w-90 bg-white rounded-md px-5 py-3 font-[500]'>
            <h3 className='text-blue-01 font-bold'>
              Dear {loggedInUser?.first_name},
            </h3>
            {/* <div className='flex flex-col items-center text-center'> */}
            <div className='flex gap-2 mt-2'>
              <TiTick className='text-xl flex-shrink-0 text-blue-01' />
              <h3>
                We are working with your Advisor to get the Business Valuation
                Tool Ready
              </h3>
            </div>
            <div className='mt-2 pt-2 text-center'>
              <h3>please check back later.</h3>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentPage;
