import React, { useEffect, useState } from 'react';
import Modal from 'shared-resources/components/Modal/Modal';
import Button from 'shared-resources/components/Button/Button';
import Input from 'shared-resources/components/Input/Input';
import { useSelector } from 'react-redux';
import {
  getCurrentReport,
  getReportBuilderLoading,
} from 'store/selectors/report-builder.selector';

interface CreateReportModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (title: string) => void;
  loading?: boolean;
}

const CreateReportModal: React.FC<CreateReportModalProps> = ({
  visible,
  onClose,
  onSubmit,
  loading = false,
}) => {
  const [reportTitle, setReportTitle] = useState('');
  const currentReport = useSelector(getCurrentReport);
  const isReportLoading = useSelector(getReportBuilderLoading);

  useEffect(() => {
    if (!isReportLoading) {
      setReportTitle(currentReport?.title || '');
    }
  }, [currentReport, isReportLoading]);

  const handleSubmit = () => {
    onSubmit(reportTitle);
    setReportTitle('');
  };

  return (
    <Modal
      visible={visible}
      handleVisibility={onClose}
      title='Create Report'
      closeOnOutsideClick
      classname='w-[500px]'
    >
      <div className='p-6'>
        <div className='mb-2'>
          <Input
            id='reportTitle'
            name='reportTitle'
            type='text'
            value={reportTitle}
            onChange={(e) => setReportTitle(e.target.value)}
            label='Report Title'
            placeholder='Enter report title'
            asterisk
          />
        </div>
        <div className='flex justify-end gap-3'>
          <Button
            onClick={handleSubmit}
            disabled={!reportTitle.trim() || loading}
            theme='primary'
            className='py-2 px-6'
            isSubmitting={loading}
          >
            Create
          </Button>
          <Button onClick={onClose} theme='tertiary' className='py-2 px-6'>
            Cancel
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default CreateReportModal;
