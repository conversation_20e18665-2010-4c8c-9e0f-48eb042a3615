import { call, put, select, takeLatest } from 'redux-saga/effects';
import { reportService } from 'services/api-services/ReportBuilderService';
import { ReportListResponseData } from 'types/report-builder.type';
import { getUserData } from 'store/selectors/user.selector';
import { ReportType, UserType } from 'types/enum';
import { toast } from 'react-toastify';
import { documentsDataSelector } from 'store/selectors/document.selector';
import { setDocuments } from 'store/reducers/documents.reducer';
import {
  setReports,
  setLoading,
  setError,
  setCurrentReport,
  setIsReportCreated,
} from '../reducers/report-builder.reducer';
import { ReportActionType } from '../actions/actions.constants';

function* createReport(action: any): any {
  try {
    yield put(setLoading(true));
    const { businessOwnerId, fileName, content, reportTitle } = action.payload;
    const { url } = yield call(
      reportService.getReportPreSignedUrl,
      businessOwnerId,
      fileName,
      'text/plain',
      ReportType.BUILDER
    );
    const file = new File([content], fileName, { type: 'text/plain' });
    yield call(reportService.uploadFileToS3, file, url);
    yield call(
      reportService.saveReport,
      businessOwnerId,
      reportTitle,
      url,
      ReportType.BUILDER
    );
    yield put(setLoading(false));
    yield put(setIsReportCreated(true));
    toast.success('Report created successfully');
  } catch (error: any) {
    yield put(setError(error?.response?.data?.message));
    yield put(setLoading(false));
  }
}

function* fetchReports(action: any): any {
  try {
    yield put(setLoading(true));
    const { businessOwnerId } = action.payload;
    const user = yield select(getUserData);
    let response: ReportListResponseData;
    if (user.type === UserType.ADVISOR) {
      response = yield call(
        reportService.listReportsToAdvisor,
        businessOwnerId,
        ReportType.BUILDER
      );
    } else {
      response = yield call(reportService.listReportsToBO, ReportType.BUILDER);
    }
    yield put(setReports(response.reports));
    yield put(setLoading(false));
  } catch (error: any) {
    yield put(setError(error.message));
    yield put(setLoading(false));
  }
}

function* fetchReport(action: any): any {
  try {
    yield put(setLoading(true));
    const { reportId, businessOwnerId } = action.payload;
    const user = yield select(getUserData);
    let response;

    if (user.type === UserType.BUSINESS_OWNER) {
      response = yield call(reportService.getReportByBO, reportId);
    } else {
      response = yield call(
        reportService.getReportByAdvisor,
        businessOwnerId,
        reportId
      );
    }

    if (response.report.url) {
      const fileResponse = yield call(
        reportService.getReportFileFromS3,
        response.report.url
      );
      yield put(setCurrentReport({ ...response.report, file: fileResponse }));
    } else {
      yield put(setCurrentReport(response.report));
    }

    yield put(setLoading(false));
    yield put(setIsReportCreated(true));
    toast.success('Report fetched successfully');
  } catch (error: any) {
    yield put(setError(error?.response?.data?.message));
    yield put(setLoading(false));
  }
}

function* sendReportToBO(action: any): any {
  try {
    yield put(setLoading(true));
    const { reportId, status } = action.payload;
    yield call(reportService.sendReportToBO, reportId, status);

    const currentDocuments = yield select(documentsDataSelector);

    const updatedDocuments = currentDocuments.map((doc: any) =>
      doc.id === reportId ? { ...doc, is_report_sent_to_owner: true } : doc
    );

    yield put(setDocuments(updatedDocuments));

    yield put(setLoading(false));
    toast.success('Report sent to BO successfully');
  } catch (error: any) {
    yield put(setError(error?.response?.data?.message));
    yield put(setLoading(false));
  }
}

export function* reportBuilderSagaWatcher() {
  yield takeLatest(ReportActionType.CREATE_REPORT, createReport);
  yield takeLatest(ReportActionType.FETCH_REPORTS, fetchReports);
  yield takeLatest(ReportActionType.FETCH_REPORT, fetchReport);
  yield takeLatest(ReportActionType.SEND_REPORT_TO_BO, sendReportToBO);
}
