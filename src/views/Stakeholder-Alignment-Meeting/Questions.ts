export const shareHoldersQuestions = [
  {
    id: 'boStateObjectives',
    question: 'Business Owner(s) state objectives:',
    options: [
      'Sale Or Material Transition Of The Business',
      'Financial Dynamics Of A Transition/Sale',
      'Desired Timing Of A Transition/Sale',
      'Acceptable Buyer Criteria (If A Sale)',
    ],
    questionType: 'textArea',
  },
  {
    id: 'attendeeTable',
    question:
      'Each attendee gives reaction to stated objectives – offering as much input as desired',
    questionType: 'table',
  },
  {
    id: 'summaryMessage',
    question: ' Composite summary of all participant input',
    questionType: 'textArea',
    placeholder: 'Record Summary here:',
  },
  {
    id: 'nextStepsMessage',
    question: 'Next steps',
    questionType: 'textArea',
    placeholder: 'Record next steps here:',
  },

  {
    id: 'cascadingMessage',
    question: 'Cascading messages',
    subText:
      'The Stakeholder Alignment Meeting may contain some discussion that is confidential and should not be communicated outside of the meeting. Immediate external communication of some of the concepts discussed could damage employee morale, send "for sale" messages into the marketplace prematurely, potentially hurt the selling price. According, it is important to discuss, and gain alignment among the Stakeholder Alignment Meeting participants about what will be shared and what is not to be shared.',
    questionType: 'textArea',
    placeholder: 'Cascading messages recorded here:',
  },
  {
    id: 'adjournMessage',
    question: 'Adjourn meeting',
    questionType: 'textArea',
  },
];

export type TableValueType = {
  id: number;
  name: string;
  equityOwned: number;
  relationship: string;
  invite?: boolean;
  aligned?: boolean;
  comment: string;
};

export interface TextAreaAnswers {
  [id: number]: string;
}
