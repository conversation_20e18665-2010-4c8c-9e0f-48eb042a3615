import React from 'react';
import ProfileDropdown from 'shared-resources/components/ProfileDropdown';
import { Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { getUserData } from 'store/selectors/user.selector';
import { getUserRouteType } from 'utils/helpers/Helpers';
import { UserType } from 'types/enum';
import logo from '../../assets/ExitSmartsLogo.svg';
import linkedinIcon from '../../assets/linkedin.svg';

const Header: React.FC = () => {
  const user = useSelector(getUserData);

  return (
    <div className='relative flex justify-between w-full px-11 py-4.5'>
      <Link
        to={
          user ? `${getUserRouteType(user?.type as UserType)}/dashboard` : '/'
        }
      >
        <img
          src={logo}
          alt='exit-smart-logo'
          width={228}
          height={55}
          className='object-contain w-[14.25rem] h-[3.4375rem]'
        />
      </Link>
      <div className='flex flex-col items-center w-full'>
        <ProfileDropdown />
      </div>
      <div className='flex justify-end items-center w-20 gap-4'>
      {/* <a href="https://www.facebook.com/people/EZDocTracker/61556572380636/?mibextid=ZbWKwL" target="_blank" rel="noopener noreferrer">
          <img src={facebookIcon} alt="Facebook" className="w-8 h-8" />
        </a>
        <a href="https://www.youtube.com/channel/UC3AxjYzt1UABgqQ9xa7CytQ" target="_blank" rel="noopener noreferrer">
          <img src={youtubeIcon} alt="YouTube" className="w-8 h-8" />
        </a> */}
        <a href="https://www.linkedin.com/company/exitsmartsinc/about/" target="_blank" rel="noopener noreferrer">
          <img src={linkedinIcon} alt="LinkedIn" className="w-8 h-8" />
        </a>
      </div>
    </div>
  );
};

export default Header;
