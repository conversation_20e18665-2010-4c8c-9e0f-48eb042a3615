import { pick } from 'lodash';
import { BusinessContinuityScreens } from 'types/enum';

export const screenToQuestion: { [screen: string]: string } = {
  [BusinessContinuityScreens.SOLE_OWNERS]:
    'Are you sole owner of your business or do you have partners ?',
  [BusinessContinuityScreens.AUTHORIZED_OWNERS]:
    'In the event that your Coach ExitSmarts receives a request to provide these Business Continuity Instructions, to whom is the Coach or ExitSmarts authorized to give them?',
  [BusinessContinuityScreens.FIRST_PEOPLE_1]:
    'If you become incapacitated, who should be the first person (people) contacted?',
  [BusinessContinuityScreens.FIRST_PEOPLE_2]:
    'If you become incapacitated, who should receive and manage these Business Continuity Instructions? (could be different from the person first contacted)',
  [BusinessContinuityScreens.INTERIM_OWNERS]:
    'If you become incapacitated, who should run the business in the interim?',
  [BusinessContinuityScreens.CORPORATE_OFFICERS]:
    'Which Corporate Officer(s) can bind the Company to legal agreements - if any?',
  [BusinessContinuityScreens.ADDRESS_EMPLOYEE_PEOPLE]:
    'If you become incapacitated who should address your employees?',
  [BusinessContinuityScreens.ADDRESS_CUSTOMERS_PEOPLE]:
    'If you become incapacitated who should address your key customers?',
  [BusinessContinuityScreens.ADDRESS_VENDORS_PEOPLE]:
    'If you become incapacitated who should address your key customers?',
  [BusinessContinuityScreens.BOARD_CHAIRMAN]:
    'Are you the Chairman of the Board of Directors of the business?',
  [BusinessContinuityScreens.INTERIM_CHAIRMAN]:
    'If you become incapacitated, and you are the Chairman of the Board, who should take over as the interim Chairman of the Board? (assuming you have the authority to make this decision)',
};

export const screenToQuestionSecondary: { [screen: string]: string } = {
  [BusinessContinuityScreens.ACCOUNTING_INFORMATION]: 'Accounting',
  [BusinessContinuityScreens.ATTORNEYS_INFORMATION]: 'Attorneys',
  [BusinessContinuityScreens.BUSINESS_INFORMATION]: 'Business',
  [BusinessContinuityScreens.FINANCIAL_ADVISORS]: 'Financial Advisors',
  [BusinessContinuityScreens.BOARD_OF_DIRECTORS]: 'Board Of Directors',
  [BusinessContinuityScreens.OTHER_ADVISORS]: 'Others',
  [BusinessContinuityScreens.DOCUMENTS]: 'Documents',
  [BusinessContinuityScreens.BANKERS]: 'Bankers',
  [BusinessContinuityScreens.BANKING_INFORMATION]: 'Banking',
  [BusinessContinuityScreens.LOANS_INFORMATION]: 'Loans',
  [BusinessContinuityScreens.PERSONAL_GUARANTEES]: 'Personal Guarantees',
  [BusinessContinuityScreens.LEASES]: 'Leases',
};

export const screenToQuestionTertiary: { [screen: string]: string } = {
  [BusinessContinuityScreens.MISCELLANEOUS]: '',
  [BusinessContinuityScreens.MESSAGE_WISHES]: '',
  [BusinessContinuityScreens.SHOULD_PRIVATE]:
    'Do you want to keep this data private and that is it will not be accessible to the advisor then ?',
};

export const formEmpty = (values: any[]): boolean => {
  const statusMap: Record<number, boolean> = {};

  const filteredValues = values?.reduce((acc, item) => {
    const filteredKeys = Object.keys(item)?.filter((key) => key !== 'priority');
    acc.push(pick(item, filteredKeys));
    return acc;
  }, []);
  filteredValues?.forEach((value: any, index: number) => {
    const allFieldsEmpty = Object.values(value).every(
      (fieldValue) =>
        !fieldValue ||
        (typeof fieldValue === 'string' && fieldValue.trim() === '')
    );

    statusMap[index] = allFieldsEmpty || (value.saved && !allFieldsEmpty);
  });

  return !Object.values(statusMap).some((item) => !item);
};
