import { useField } from 'formik';
import React, { useState } from 'react';
import { IoCloudUploadOutline } from 'react-icons/io5';
import { FaFile } from 'react-icons/fa';
import { TiDelete } from 'react-icons/ti';
import InputLabel from '../InputLabel/InputLabel';
import InputHelper from '../Inputhelper/InputHelper';

interface FormikFileInputProps {
  name: string;
  label?: string;
  asterisk?: boolean;
  accept?: string;
  multiple?: boolean;
  className?: string;
  labelClassName?: string;
  disabled?: boolean;
  helperText?: string;
  placeholder?: string;
}

const FormikFileInput: React.FC<FormikFileInputProps> = ({
  name,
  label,
  asterisk,
  accept = '*/*',
  multiple = false,
  className = '',
  labelClassName = '',
  disabled = false,
  helperText,
  placeholder = 'Click to upload or drag and drop',
}) => {
  const [, meta, helpers] = useField(name);
  const { value, error, touched } = meta;
  const { setValue, setTouched } = helpers;
  
  const [dragActive, setDragActive] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      const fileArray = Array.from(files);
      setValue(multiple ? fileArray : fileArray[0]);
      setTouched(true);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && !disabled) {
      const files = Array.from(e.dataTransfer.files);
      setValue(multiple ? files : files[0]);
      setTouched(true);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragEnter = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setDragActive(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  };

  const removeFile = (indexToRemove?: number) => {
    if (multiple && Array.isArray(value)) {
      const newFiles = value.filter((_, index) => index !== indexToRemove);
      setValue(newFiles.length > 0 ? newFiles : null);
    } else {
      setValue(null);
    }
    setTouched(true);
  };

  const renderFilePreview = () => {
    if (!value) return null;

    if (multiple && Array.isArray(value)) {
      return (
        <div className='flex flex-wrap gap-2 mt-2'>
          {value.map((file: File, index: number) => (
            <div key={index} className='flex items-center bg-gray-100 rounded p-2'>
              <FaFile className='text-gray-500 mr-2' />
              <span className='text-sm truncate max-w-32'>{file.name}</span>
              <TiDelete
                className='text-red-500 cursor-pointer ml-2'
                onClick={() => removeFile(index)}
              />
            </div>
          ))}
        </div>
      );
    } else if (value instanceof File) {
      return (
        <div className='flex items-center bg-gray-100 rounded p-2 mt-2'>
          <FaFile className='text-gray-500 mr-2' />
          <span className='text-sm truncate max-w-32'>{value.name}</span>
          <TiDelete
            className='text-red-500 cursor-pointer ml-2'
            onClick={() => removeFile()}
          />
        </div>
      );
    }

    return null;
  };

  return (
    <div className={`flex flex-col ${className}`}>
      {label && (
        <InputLabel
          label={label}
          asterisk={asterisk}
          className={labelClassName}
        />
      )}
      
      <div className='mt-2'>
        <label
          htmlFor={`file-input-${name}`}
          className={`
            flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer
            ${dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 bg-gray-50'}
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'}
            ${touched && error ? 'border-red-500' : ''}
          `}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
        >
          <div className='flex flex-col items-center justify-center pt-5 pb-6'>
            <IoCloudUploadOutline
              size={32}
              className={`mb-3 ${dragActive ? 'text-blue-500' : 'text-gray-400'}`}
            />
            <p className='mb-2 text-sm text-gray-500'>
              <span className='font-semibold'>{placeholder}</span>
            </p>
            {accept && (
              <p className='text-xs text-gray-500'>
                Accepted formats: {accept}
              </p>
            )}
          </div>
          <input
            id={`file-input-${name}`}
            type='file'
            accept={accept}
            multiple={multiple}
            className='hidden'
            onChange={handleFileChange}
            disabled={disabled}
          />
        </label>
        
        {renderFilePreview()}
        
        {touched && error && (
          <InputHelper text={error} type='error' />
        )}
        
        {helperText && !error && (
          <InputHelper text={helperText} type='info' />
        )}
      </div>
    </div>
  );
};

export default FormikFileInput;
