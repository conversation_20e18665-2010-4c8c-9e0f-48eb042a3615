import { Form, Formik } from 'formik';
import React from 'react';
import { IoInformationCircle } from 'react-icons/io5';
import { useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import * as yup from 'yup';
import { getYear } from '../BusinessValuationConfig';

interface Props {
  nextStep: () => void;
  setData: (data: any) => void;
  backStep: () => void;
  data: any;
  onSaveToDraftClick: (data: any) => void;
}
const BalanceSheetData1: React.FC<Props> = (props) => {
  const { nextStep, setData, backStep, data, onSaveToDraftClick } = props;
  const isLoading = useSelector(getAssessmentToolLoading);

  const validationSchema = yup.object().shape({
    [`cash_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Cash is required'),
    [`cash_${getYear(2)}`]: yup
      .string()
      .required('Cash for Second Year is required')
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Cash is required'),
    [`cash_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Cash is required'),
    [`accounts_receivable_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Accounts Receivable is required'),
    [`accounts_receivable_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Accounts Receivable is required'),
    [`accounts_receivable_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Accounts Receivable is required'),
    [`inventory_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Inventory is required'),
    [`inventory_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Inventory is required'),
    [`inventory_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Inventory is required'),
    [`other_current_assets_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Other current assets is required'),
    [`other_current_assets_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Other current assets is required'),
    [`other_current_assets_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Other current assets is required'),
    [`fixed_assets_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Fixed assets is required'),
    [`fixed_assets_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Fixed assets is required'),
    [`fixed_assets_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Fixed assets is required'),
    [`intengible_assets_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Intengible assets is required'),
    [`intengible_assets_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Intengible assets is required'),
    [`intengible_assets_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Intengible assets is required'),
    [`accounts_payable_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Accounts payable is required'),
    [`accounts_payable_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Accounts payable is required'),
    [`accounts_payable_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Accounts payable is required'),
    [`other_short_term_liabilities_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Short term Liabilities is required'),
    [`other_short_term_liabilities_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Short term Liabilities is required'),
    [`other_short_term_liabilities_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Short term Liabilities is required'),
    [`bank_loans_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Bank loans is required'),
    [`bank_loans_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Bank loans is required'),
    [`bank_loans_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Bank loans is required'),
    [`other_long_term_liabilities_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Other long term liabilities is required'),
    [`other_long_term_liabilities_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Other long term liabilities is required'),
    [`other_long_term_liabilities_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Other long term liabilities is required'),
    [`contingent_liabilities_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Contingent liabilities is required'),
    [`contingent_liabilities_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Contingent liabilities is required'),
    [`contingent_liabilities_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Contingent liabilities is required'),
  });

  const handleSubmit = (values: any) => {
    setData(values);
    nextStep();
  };

  return (
    <div className='flex flex-col '>
      <Formik
        initialValues={
          data?.balance_sheet_data_1 || {
            [`cash_${getYear(1)}`]: '',
            [`cash_${getYear(2)}`]: '',
            [`cash_${getYear(3)}`]: '',
            [`accounts_receivable_${getYear(1)}`]: '',
            [`accounts_receivable_${getYear(2)}`]: '',
            [`accounts_receivable_${getYear(3)}`]: '',
            [`inventory_${getYear(1)}`]: '',
            [`inventory_${getYear(2)}`]: '',
            [`inventory_${getYear(3)}`]: '',
            [`other_current_assets_${getYear(1)}`]: '',
            [`other_current_assets_${getYear(2)}`]: '',
            [`other_current_assets_${getYear(3)}`]: '',
            [`fixed_assets_${getYear(1)}`]: '',
            [`fixed_assets_${getYear(2)}`]: '',
            [`fixed_assets_${getYear(3)}`]: '',
            [`intengible_assets_${getYear(1)}`]: '',
            [`intengible_assets_${getYear(2)}`]: '',
            [`intengible_assets_${getYear(3)}`]: '',
            [`accounts_payable_${getYear(1)}`]: '',
            [`accounts_payable_${getYear(2)}`]: '',
            [`accounts_payable_${getYear(3)}`]: '',
            [`other_short_term_liabilities_${getYear(1)}`]: '',
            [`other_short_term_liabilities_${getYear(2)}`]: '',
            [`other_short_term_liabilities_${getYear(3)}`]: '',
            [`bank_loans_${getYear(1)}`]: '',
            [`bank_loans_${getYear(2)}`]: '',
            [`bank_loans_${getYear(3)}`]: '',
            [`other_long_term_liabilities_${getYear(1)}`]: '',
            [`other_long_term_liabilities_${getYear(2)}`]: '',
            [`other_long_term_liabilities_${getYear(3)}`]: '',
            [`contingent_liabilities_${getYear(1)}`]: '',
            [`contingent_liabilities_${getYear(2)}`]: '',
            [`contingent_liabilities_${getYear(3)}`]: '',
          }
        }
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values }) => (
          <>
            <div className='relative z-10 bg-green-100 text-xl items-center font-semibold'>
              <span className='absolute left-[27.5%] mt-8'>{getYear(1)}</span>
              <span className='absolute left-[52%] mt-8'>{getYear(2)}</span>
              <span className='absolute left-[76.5%] mt-8'>{getYear(3)}</span>
            </div>

            <Form className='pl-5 py-3 relative flex flex-col gap-4 bg-white'>
              <div className='h-[calc(100vh-23.5rem)] mt-20 overflow-auto scrollbar pr-4'>
                <div className='grid grid-cols-4 gap-x-16  gap-y-1  text-[1.1rem] font-[500]'>
                  <div className='flex  items-center gap-2'>
                    <IoInformationCircle id='cash' className='text-blue-01' />
                    <div className='relative w-full'>
                      <Tooltip
                        anchorSelect='#cash'
                        place='right'
                        offset={0}
                        className=' !bg-gray-100 border !z-50  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                        classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                      >
                        This does NOT include typical operations, sales &
                        administrative expenses. Characterized by being unusual
                        in nature and infrequent in occurance. Any amount
                        enteted will raise the amount of earnings and increase
                        the company value.
                      </Tooltip>
                      <span className='w-full'>Cash</span>
                    </div>
                  </div>
                  <FormikInput
                    className='!h-10'
                    name={`cash_${getYear(1)}`}
                    key={`cash_${getYear(1)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`cash_${getYear(2)}`}
                    key={`cash_${getYear(2)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`cash_${getYear(3)}`}
                    key={`cash_${getYear(3)}`}
                  />
                  <div className='flex  items-center gap-2'>
                    <IoInformationCircle
                      id='AccountsReceivable'
                      className='text-blue-01'
                    />
                    <div className='relative w-full'>
                      <Tooltip
                        anchorSelect='#AccountsReceivable'
                        place='right'
                        offset={0}
                        className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                        classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                      >
                        This is the amount of money owed to your company by
                        clients who have been provided goods or services on
                        credit.
                      </Tooltip>
                      <span className='w-full'>Accounts Receivable</span>
                    </div>
                  </div>

                  <FormikInput
                    className='!h-10'
                    name={`accounts_receivable_${getYear(1)}`}
                    key={`accounts_receivable_${getYear(1)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`accounts_receivable_${getYear(2)}`}
                    key={`accounts_receivable_${getYear(2)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`accounts_receivable_${getYear(3)}`}
                    key={`accounts_receivable_${getYear(3)}`}
                  />
                  <div className='flex  items-center gap-2'>
                    <IoInformationCircle
                      id='Inventory'
                      className='text-blue-01'
                    />
                    <div className='relative w-full'>
                      <Tooltip
                        anchorSelect='#Inventory'
                        place='right'
                        offset={0}
                        className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                        classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                      >
                        Typically measured at original cost, this account should
                        reflect office supplies, raw materials, work in progress
                        and finished goods.
                      </Tooltip>
                      <span className='w-full'>Inventory</span>
                    </div>
                  </div>
                  <FormikInput
                    className='!h-10'
                    name={`inventory_${getYear(1)}`}
                    key={`inventory_${getYear(1)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`inventory_${getYear(2)}`}
                    key={`inventory_${getYear(2)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`inventory_${getYear(3)}`}
                    key={`inventory_${getYear(3)}`}
                  />
                  <div className='flex  items-center gap-2'>
                    <IoInformationCircle
                      id='OtherCurrentAssets'
                      className='text-blue-01'
                    />
                    <div className='relative w-full'>
                      <Tooltip
                        anchorSelect='#OtherCurrentAssets'
                        place='right'
                        offset={0}
                        className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                        classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                      >
                        The ‘catchall’ account typically includes items such as
                        lease deposits, prepaid expenses and employee/officer
                        loan receivables but may include accounts such as
                        accrued revenue and deferred income tax.
                      </Tooltip>
                      <span className='w-full'>Other Current Assets</span>
                    </div>
                  </div>
                  <FormikInput
                    className='!h-10'
                    name={`other_current_assets_${getYear(1)}`}
                    key={`other_current_assets_${getYear(1)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`other_current_assets_${getYear(2)}`}
                    key={`other_current_assets_${getYear(2)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`other_current_assets_${getYear(3)}`}
                    key={`other_current_assets_${getYear(3)}`}
                  />
                  <div className='flex  items-center gap-2'>
                    <IoInformationCircle
                      id='FixedAssets'
                      className='text-blue-01'
                    />
                    <div className='relative  w-full'>
                      <Tooltip
                        anchorSelect='#FixedAssets'
                        place='right'
                        offset={0}
                        className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                        classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                      >
                        Commonly referred to as ‘furniture, fixtures and
                        equipment’ or ‘property, plant balance sheets will
                        presenttheir ‘original cost’ followed by ‘accumulated
                        depreciation’ with the difference being ‘book value’.
                        For financial analysis purposes, book value should be
                        entered in this line item.
                      </Tooltip>
                      <span className='w-full'>Fixed Assets</span>
                    </div>
                  </div>
                  <FormikInput
                    className='!h-10'
                    name={`fixed_assets_${getYear(1)}`}
                    key={`fixed_assets_${getYear(1)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`fixed_assets_${getYear(2)}`}
                    key={`fixed_assets_${getYear(2)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`fixed_assets_${getYear(3)}`}
                    key={`fixed_assets_${getYear(3)}`}
                  />
                  <div className='flex  items-center gap-2'>
                    <IoInformationCircle
                      id='IntangibleAssets'
                      className='text-blue-01'
                    />
                    <div className='relative w-full'>
                      <Tooltip
                        anchorSelect='#IntangibleAssets'
                        place='right'
                        offset={0}
                        className=' !bg-gray-100 border !z-10  !w-[26rem] !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                        classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                      >
                        Most balance sheets will NOT include intangible assets
                        based on GAAP rules. The common exception is when the
                        subject company has been purchased via the ‘asset sale’
                        deal structure which requires the buyer and seller to
                        agree on the ‘allocation of purchase price’. Most such
                        allocations will include value attributed to intangible
                        assets such as goodwill, customer base, tradename or
                        covenant not to compete. Similar to fixed assets, the
                        balance entered here should be the ‘book value’ figure
                        (original cost less accumulated amortization). Certain
                        ‘internally-generated’ intangible assets may be
                        capitalized and amortized but this generally applies to
                        advanced, high-tech-oriented businesses.
                      </Tooltip>
                      <span className='w-full'>Intangible Assets</span>
                    </div>
                  </div>
                  <FormikInput
                    className='!h-10'
                    name={`intengible_assets_${getYear(1)}`}
                    key={`intengible_assets_${getYear(1)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`intengible_assets_${getYear(2)}`}
                    key={`intengible_assets_${getYear(2)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`intengible_assets_${getYear(3)}`}
                    key={`intengible_assets_${getYear(3)}`}
                  />
                  <div className='flex  items-center gap-2'>
                    <IoInformationCircle
                      id='AccountsPayable'
                      className='text-blue-01'
                    />
                    <div className='relative w-full'>
                      <Tooltip
                        anchorSelect='#AccountsPayable'
                        place='right'
                        offset={0}
                        className=' !bg-gray-100 border !z-10  !w-[60rem] !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                        classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                      >
                        Commonly referred to as ‘furniture, fixtures and
                        equipment’ or ‘property, plant balance sheets will
                        presenttheir ‘original cost’ followed by ‘accumulated
                        depreciation’ with the difference being ‘book value’.
                        For financial analysis purposes, book value should be
                        entered in this line item.
                      </Tooltip>
                      <span className='w-full'>Accounts Payable</span>
                    </div>
                  </div>
                  <FormikInput
                    className='!h-10'
                    name={`accounts_payable_${getYear(1)}`}
                    key={`accounts_payable_${getYear(1)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`accounts_payable_${getYear(2)}`}
                    key={`accounts_payable_${getYear(2)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`accounts_payable_${getYear(3)}`}
                    key={`accounts_payable_${getYear(3)}`}
                  />
                  <div className='flex  items-center gap-2'>
                    <IoInformationCircle
                      id='OtherShortTermLiabilities'
                      className='text-blue-01'
                    />
                    <div className='relative w-full'>
                      <Tooltip
                        anchorSelect='#OtherShortTermLiabilities'
                        place='right'
                        offset={0}
                        className=' !bg-gray-100 border !z-10  !w-[60rem] !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                        classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                      >
                        Any Financial obligation due within the next year which
                        is not include in accounts payable. Common examples
                        include creditcard payables, line of credit balances and
                        accured liabilitiesrelated to payroll and taxes. This
                        line item could also include current amounts due tolong
                        term creditors, e.g. current portion (due over the next
                        12 months) of 10 years term loan from bank
                      </Tooltip>
                      <span className='w-full'>
                        Other Short-Term Liabilities
                      </span>
                    </div>
                  </div>

                  <FormikInput
                    className='!h-10'
                    name={`other_short_term_liabilities_${getYear(1)}`}
                    key={`other_short_term_liabilities_${getYear(1)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`other_short_term_liabilities_${getYear(2)}`}
                    key={`other_short_term_liabilities_${getYear(2)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`other_short_term_liabilities_${getYear(3)}`}
                    key={`other_short_term_liabilities_${getYear(3)}`}
                  />
                  <div className='flex  items-center gap-2'>
                    <IoInformationCircle
                      id='BankLoans'
                      className='text-blue-01'
                    />
                    <div className='relative w-full'>
                      <Tooltip
                        anchorSelect='#BankLoans'
                        place='right'
                        offset={0}
                        className=' !bg-gray-100 border !z-10  !w-[60rem] !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                        classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                      >
                        The account represents the total principal balance
                        outstanding on all loans made by banks or other
                        financial institutions.which possess a term of longer
                        than one year.Any real state to debt , such as mortgage
                        balance should be excluded here. It will be asked later
                        in the Operation step.
                      </Tooltip>
                      <span className='w-full'>Bank Loans</span>
                    </div>
                  </div>

                  <FormikInput
                    className='!h-10'
                    name={`bank_loans_${getYear(1)}`}
                    key={`bank_loans_${getYear(1)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`bank_loans_${getYear(2)}`}
                    key={`bank_loans_${getYear(2)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`bank_loans_${getYear(3)}`}
                    key={`bank_loans_${getYear(3)}`}
                  />

                  <div className='flex  items-center gap-2'>
                    <IoInformationCircle
                      id='OtherLongTermLiabilities'
                      className='text-blue-01'
                    />
                    <div className='w-full'>
                      <Tooltip
                        anchorSelect='#OtherLongTermLiabilities'
                        place='right'
                        offset={0}
                        className=' !bg-gray-100 border !z-10  !w-[60rem] !font-[500] !left-24  4xl:!left-8 !top-[70%] border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                        classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                      >
                        Other Long-Term Liabilities may include real property
                        mortgages with non-banks, obligations under long term
                        capital leases, bonds payable and deferred income taxes.
                        In general this category should include all long term
                        liablities other than bank loans. Any real estate
                        related debt, such as mortgage balance. should be
                        EXCLUDED here. It will be asked later in the operations
                        step.
                      </Tooltip>
                      <span className='w-full'>
                        Other Long-Term Liabilities
                      </span>
                    </div>
                  </div>

                  <FormikInput
                    className='!h-10'
                    name={`other_long_term_liabilities_${getYear(1)}`}
                    key={`other_long_term_liabilities_${getYear(1)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`other_long_term_liabilities_${getYear(2)}`}
                    key={`other_long_term_liabilities_${getYear(2)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`other_long_term_liabilities_${getYear(3)}`}
                    key={`other_long_term_liabilities_${getYear(3)}`}
                  />

                  <div className='flex  4xl:relative items-center gap-2'>
                    <IoInformationCircle
                      id='ContingentLiabilities'
                      className='text-blue-01'
                    />
                    <div className='w-full'>
                      <Tooltip
                        anchorSelect='#ContingentLiabilities'
                        place='right'
                        offset={0}
                        className=' !bg-gray-100 border  !z-10  !w-[60rem] !font-[500]  !left-24  4xl:!left-8 !top-[78%] border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                        classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                      >
                        There are potential obligations which are dependent on
                        the occurence/non-occurence of a certain event in the
                        future. A common example would be a pending lawsuit
                        which is likely to lead to legallyenforcable financial
                        payments owed by the company in future. Other examples
                        include potential warrenty payments or payments related
                        to the guarantee of third party loan
                      </Tooltip>
                      <span className='w-full'>Contingent Liabilities</span>
                    </div>
                  </div>

                  <FormikInput
                    className='!h-10'
                    name={`contingent_liabilities_${getYear(1)}`}
                    key={`contingent_liabilities_${getYear(1)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`contingent_liabilities_${getYear(2)}`}
                    key={`contingent_liabilities_${getYear(2)}`}
                  />
                  <FormikInput
                    className='!h-10'
                    name={`contingent_liabilities_${getYear(3)}`}
                    key={`contingent_liabilities_${getYear(3)}`}
                  />
                </div>
              </div>

              <div className='pr-7'>
                <BackNextComponent
                  backStep={backStep}
                  buttonType='submit'
                  isLoading={isLoading}
                  isNextDisable={false}
                  onSaveToDraftClick={() => {
                    onSaveToDraftClick(values);
                  }}
                />
              </div>
            </Form>
          </>
        )}
      </Formik>
    </div>
  );
};

export default BalanceSheetData1;
