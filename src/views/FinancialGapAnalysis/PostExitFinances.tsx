import { Form, Formik } from 'formik';
import React, { memo } from 'react';
import { IoInformationCircle } from 'react-icons/io5';
import { useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { Tooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { getUserData } from 'store/selectors/user.selector';
import { RouteKey, UserRouteType, UserType } from 'types/enum';
import * as yup from 'yup';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import {
  InvestmentsKeys,
  PostExitFinancesKeys,
  getKeyTitle,
} from './FinancialGapAnalysisConfig';
import { formatCurrency } from './ExpenseCalculator/ExpenseCalculatorConfig';

interface Props {
  nextStep: () => void;
  setData: (data: any) => void;
  backStep: () => void;
  data: any;
  onSaveToDraftClick: (data: any, callback?: () => void) => void;
}

const PostExitFinances: React.FC<Props> = (props) => {
  const { id } = useParams();
  const { nextStep, setData, backStep, data, onSaveToDraftClick } = props;
  const isLoading = useSelector(getAssessmentToolLoading);
  const navigate = useNavigate();

  const getInitialNetAdditionalAnnualAmount = () => {
    if (data?.post_exit_finances?.[PostExitFinancesKeys.ANNUAL_EXPENSES]) {
      return formatCurrency(
        Number(
          data?.post_exit_finances?.[
            PostExitFinancesKeys.ANNUAL_EXPENSES
          ]?.replaceAll(',', '')
        ) -
          Number(
            data?.post_exit_finances?.[
              PostExitFinancesKeys.NET_ANUAL_RECURRING_REVENUE
            ]?.replaceAll(',', '') || 0
          )
      )?.replace('$', '');
    }
    if (
      data?.post_exit_finances?.[
        PostExitFinancesKeys.NET_ADDITIONAL_ANUAL_AMOUNT
      ]
    ) {
      return Number(
        data?.post_exit_finances?.[
          PostExitFinancesKeys.NET_ADDITIONAL_ANUAL_AMOUNT
        ]
      );
    }
    return '';
  };

  const validationSchema = yup.object().shape({
    [PostExitFinancesKeys.ANNUAL_EXPENSES]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [PostExitFinancesKeys.RECURRING_REVENUE_SOCIAL_SECURITY]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number'),
    [PostExitFinancesKeys.RECURRING_REVENUE_OTHER]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number'),
    [PostExitFinancesKeys.TOTAL_RECURRING_REVENUE]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number'),
    [PostExitFinancesKeys.FEDERAL_TAX_ON_RECURRING_REVENUE]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [PostExitFinancesKeys.STATE_TAX_ON_RECURRING_REVENUE]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [PostExitFinancesKeys.NET_ANUAL_RECURRING_REVENUE]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number'),
    [PostExitFinancesKeys.NET_ADDITIONAL_ANUAL_AMOUNT]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [PostExitFinancesKeys.YEARS_TO_FUND_AGE_99]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [PostExitFinancesKeys.TOTAL_ADDITIONAL_AMOUNT]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [PostExitFinancesKeys.LESS_AVAILABLE_INVESTMENTS]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [PostExitFinancesKeys.NET_AMOUNT_NEEDED_TO_FUND]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
  });
  const initialValue = {
    [PostExitFinancesKeys.ANNUAL_EXPENSES]:
      data?.post_exit_finances?.[PostExitFinancesKeys.ANNUAL_EXPENSES] || '',
    [PostExitFinancesKeys.RECURRING_REVENUE_SOCIAL_SECURITY]:
      data?.post_exit_finances?.[
        PostExitFinancesKeys.RECURRING_REVENUE_SOCIAL_SECURITY
      ] || '',
    [PostExitFinancesKeys.RECURRING_REVENUE_OTHER]:
      data?.post_exit_finances?.[
        PostExitFinancesKeys.RECURRING_REVENUE_OTHER
      ] || '',
    [PostExitFinancesKeys.TOTAL_RECURRING_REVENUE]:
      data?.post_exit_finances?.[
        PostExitFinancesKeys.TOTAL_RECURRING_REVENUE
      ] || '',
    [PostExitFinancesKeys.FEDERAL_TAX_ON_RECURRING_REVENUE]:
      data?.post_exit_finances?.[
        PostExitFinancesKeys.FEDERAL_TAX_ON_RECURRING_REVENUE
      ] || '',
    [PostExitFinancesKeys.STATE_TAX_ON_RECURRING_REVENUE]:
      data?.post_exit_finances?.[
        PostExitFinancesKeys.STATE_TAX_ON_RECURRING_REVENUE
      ] || '',
    [PostExitFinancesKeys.NET_ANUAL_RECURRING_REVENUE]:
      data?.post_exit_finances?.[
        PostExitFinancesKeys.NET_ANUAL_RECURRING_REVENUE
      ] || '',
    [PostExitFinancesKeys.NET_ADDITIONAL_ANUAL_AMOUNT]:
      getInitialNetAdditionalAnnualAmount(),
    [PostExitFinancesKeys.YEARS_TO_FUND_AGE_99]:
      data?.post_exit_finances?.[PostExitFinancesKeys.YEARS_TO_FUND_AGE_99] ||
      99 - (data?.investments?.expect_to_exit || 0) ||
      '',
    [PostExitFinancesKeys.TOTAL_ADDITIONAL_AMOUNT]:
      data?.post_exit_finances?.[
        PostExitFinancesKeys.TOTAL_ADDITIONAL_AMOUNT
      ] || '',
    [PostExitFinancesKeys.LESS_AVAILABLE_INVESTMENTS]:
      data?.investments[InvestmentsKeys.COMPOUNDED_INVESTMENTS] ||
      data?.post_exit_finances?.[
        PostExitFinancesKeys.LESS_AVAILABLE_INVESTMENTS
      ] ||
      '',
    [PostExitFinancesKeys.NET_AMOUNT_NEEDED_TO_FUND]:
      data?.post_exit_finances?.[
        PostExitFinancesKeys.NET_AMOUNT_NEEDED_TO_FUND
      ] || '',
  };

  const handleSubmit = (values: any) => {
    nextStep();
    setData(values);
  };

  const loggedInUser = useSelector(getUserData);

  const setAutoCalculateField = (
    event: React.ChangeEvent<HTMLInputElement>,
    setFieldValue: (
      field: string,
      value: any,
      shouldValidate?: boolean
    ) => void,
    values: any
  ) => {
    const { name, value } = event.target;

    // Update the current field in the values object
    const updatedValues = {
      ...values,
      [name]: value,
    };

    // Perform calculations or updates based on the updatedValues
    const totalTax =
      Number(
        updatedValues[PostExitFinancesKeys.STATE_TAX_ON_RECURRING_REVENUE]
      ) +
      Number(
        updatedValues[PostExitFinancesKeys.FEDERAL_TAX_ON_RECURRING_REVENUE]
      );

    const totalPostExitRevenue =
      Number(
        updatedValues[
          PostExitFinancesKeys.RECURRING_REVENUE_SOCIAL_SECURITY
        ].replaceAll(',', '')
      ) +
      Number(
        updatedValues[PostExitFinancesKeys.RECURRING_REVENUE_OTHER].replaceAll(
          ',',
          ''
        )
      );

    const afterTaxRevenue =
      totalPostExitRevenue - (totalTax / 100) * totalPostExitRevenue;

    const netAdditionalAnnualAmount =
      Number(
        updatedValues[PostExitFinancesKeys.ANNUAL_EXPENSES].replaceAll(',', '')
      ) - afterTaxRevenue;

    const totalAdditionalAmount =
      netAdditionalAnnualAmount *
      Number(updatedValues[PostExitFinancesKeys.YEARS_TO_FUND_AGE_99]);

    const netAmountNeededToFundPostExitLife =
      totalAdditionalAmount -
      Number(
        updatedValues[
          PostExitFinancesKeys.LESS_AVAILABLE_INVESTMENTS
        ].replaceAll(',', '')
      );

    // Update relevant fields using setFieldValue
    setFieldValue(name, value); // Update the current field first

    // Update other dependent fields
    setFieldValue(
      PostExitFinancesKeys.NET_AMOUNT_NEEDED_TO_FUND,
      formatCurrency(netAmountNeededToFundPostExitLife)
        .toString()
        .replace('$', '')
    );
    setFieldValue(
      PostExitFinancesKeys.TOTAL_ADDITIONAL_AMOUNT,
      formatCurrency(totalAdditionalAmount).toString().replace('$', '')
    );
    setFieldValue(
      PostExitFinancesKeys.NET_ANUAL_RECURRING_REVENUE,
      formatCurrency(afterTaxRevenue).toString().replace('$', '')
    );
    setFieldValue(
      PostExitFinancesKeys.TOTAL_RECURRING_REVENUE,
      formatCurrency(totalPostExitRevenue).toString().replace('$', '')
    );
    setFieldValue(
      PostExitFinancesKeys.NET_ADDITIONAL_ANUAL_AMOUNT,
      formatCurrency(netAdditionalAnnualAmount).toString().replace('$', '')
    );
  };

  return (
    <div className='flex flex-col'>
      <Formik
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values, setFieldValue }) => (
          <Form className='pl-5 py-3 bg-white'>
            <h2 className='text-xl font-semibold mt-7 underline'>
              Post Exit Finances
            </h2>
            <div className='flex flex-col gap-6 h-[calc(100vh-25rem)] mt-12 overflow-auto scrollbar pr-4 text-[1rem]'>
              <div className='grid grid-cols-2 gap-x-10 items-end'>
                <FormikInput
                  leadingIcon='$'
                  labelIcon2={
                    <div className='flex items-center gap-2'>
                      <IoInformationCircle
                        id='annualExpenses'
                        className='text-blue-01'
                      />
                      <div className='relative'>
                        <Tooltip
                          clickable
                          anchorSelect='#annualExpenses'
                          place='left-start'
                          offset={0}
                          className='!p-4 !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-4  !text-black-01 !text-[0.9rem] !rounded-lg'
                          classNameArrow='border-r border-b  !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                        >
                          <p>
                            Don&#39;t know your annual expenses?{' '}
                            <Button
                              className='!bg-transparent !text-blue-01 underline !border-0'
                              onClick={(e) => {
                                e.preventDefault();
                                onSaveToDraftClick(values, () => {
                                  if (loggedInUser?.type === UserType.ADVISOR) {
                                    navigate(
                                      `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${id}/${RouteKey.EXPENSE_CALCULATOR}`
                                    );
                                  } else if (
                                    loggedInUser?.type ===
                                    UserType.BUSINESS_OWNER
                                  ) {
                                    navigate(
                                      `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.EXPENSE_CALCULATOR}`
                                    );
                                  }
                                });
                              }}
                            >
                              Click here
                            </Button>{' '}
                            to go to annual expenses calculator or make a manual
                            entry
                          </p>
                        </Tooltip>
                      </div>
                    </div>
                  }
                  valueChanged={(event) => {
                    setAutoCalculateField(event, setFieldValue, values);
                    setFieldValue(
                      PostExitFinancesKeys.ANNUAL_EXPENSES,
                      formatCurrency(
                        Number(event.target.value.replaceAll(',', ''))
                      )
                        .toString()
                        .replace('$', '')
                    );
                  }}
                  name={`${PostExitFinancesKeys.ANNUAL_EXPENSES}`}
                  key={`${PostExitFinancesKeys.ANNUAL_EXPENSES}`}
                  label={getKeyTitle(PostExitFinancesKeys.ANNUAL_EXPENSES)}
                  labelClassName='!text-[1rem]'
                />
                <FormikInput
                  valueChanged={(event) => {
                    setAutoCalculateField(event, setFieldValue, values);
                    setFieldValue(
                      PostExitFinancesKeys.RECURRING_REVENUE_SOCIAL_SECURITY,
                      formatCurrency(
                        Number(event.target.value.replaceAll(',', ''))
                      )
                        .toString()
                        .replace('$', '')
                    );
                  }}
                  leadingIcon='$'
                  name={`${PostExitFinancesKeys.RECURRING_REVENUE_SOCIAL_SECURITY}`}
                  key={`${PostExitFinancesKeys.RECURRING_REVENUE_SOCIAL_SECURITY}`}
                  label={getKeyTitle(
                    PostExitFinancesKeys.RECURRING_REVENUE_SOCIAL_SECURITY
                  )}
                  labelClassName='!text-[1rem]'
                />
              </div>
              <div className='grid grid-cols-2 gap-x-10 items-end'>
                <FormikInput
                  valueChanged={(event) => {
                    setAutoCalculateField(event, setFieldValue, values);
                    setFieldValue(
                      PostExitFinancesKeys.RECURRING_REVENUE_OTHER,
                      formatCurrency(
                        Number(event.target.value.replaceAll(',', ''))
                      )
                        .toString()
                        .replace('$', '')
                    );
                  }}
                  leadingIcon='$'
                  className='!my-auto'
                  name={`${PostExitFinancesKeys.RECURRING_REVENUE_OTHER}`}
                  key={`${PostExitFinancesKeys.RECURRING_REVENUE_OTHER}`}
                  label={getKeyTitle(
                    PostExitFinancesKeys.RECURRING_REVENUE_OTHER
                  )}
                  labelClassName='!text-[1rem]'
                />
                <FormikInput
                  disabled
                  valueChanged={(event) => {
                    setAutoCalculateField(event, setFieldValue, values);
                  }}
                  leadingIcon='$'
                  name={`${PostExitFinancesKeys.TOTAL_RECURRING_REVENUE}`}
                  key={`${PostExitFinancesKeys.TOTAL_RECURRING_REVENUE}`}
                  label={getKeyTitle(
                    PostExitFinancesKeys.TOTAL_RECURRING_REVENUE
                  )}
                  labelClassName='!text-[1rem]'
                />
              </div>
              <div className='grid grid-cols-2 gap-x-10 items-end'>
                <FormikInput
                  valueChanged={(event) => {
                    setAutoCalculateField(event, setFieldValue, values);
                  }}
                  className='!mb-0.5'
                  name={`${PostExitFinancesKeys.FEDERAL_TAX_ON_RECURRING_REVENUE}`}
                  key={`${PostExitFinancesKeys.FEDERAL_TAX_ON_RECURRING_REVENUE}`}
                  label={`${getKeyTitle(
                    PostExitFinancesKeys.FEDERAL_TAX_ON_RECURRING_REVENUE
                  )} %`}
                  labelClassName='!text-[1rem]'
                />
                <FormikInput
                  valueChanged={(event) => {
                    setAutoCalculateField(event, setFieldValue, values);
                  }}
                  className='!mb-0.5'
                  name={`${PostExitFinancesKeys.STATE_TAX_ON_RECURRING_REVENUE}`}
                  key={`${PostExitFinancesKeys.STATE_TAX_ON_RECURRING_REVENUE}`}
                  label={`${getKeyTitle(
                    PostExitFinancesKeys.STATE_TAX_ON_RECURRING_REVENUE
                  )} %`}
                  labelClassName='!text-[1rem]'
                />
              </div>
              <div className='grid grid-cols-2 gap-x-10 items-end'>
                <FormikInput
                  disabled
                  valueChanged={(event) => {
                    setAutoCalculateField(event, setFieldValue, values);
                  }}
                  leadingIcon='$'
                  className='!mb-0.5'
                  name={`${PostExitFinancesKeys.NET_ANUAL_RECURRING_REVENUE}`}
                  key={`${PostExitFinancesKeys.NET_ANUAL_RECURRING_REVENUE}`}
                  label={getKeyTitle(
                    PostExitFinancesKeys.NET_ANUAL_RECURRING_REVENUE
                  )}
                  labelClassName='!text-[1rem]'
                />
                <FormikInput
                  disabled
                  valueChanged={(event) => {
                    setAutoCalculateField(event, setFieldValue, values);
                  }}
                  leadingIcon='$'
                  className='!mb-0.5'
                  name={`${PostExitFinancesKeys.NET_ADDITIONAL_ANUAL_AMOUNT}`}
                  key={`${PostExitFinancesKeys.NET_ADDITIONAL_ANUAL_AMOUNT}`}
                  label={getKeyTitle(
                    PostExitFinancesKeys.NET_ADDITIONAL_ANUAL_AMOUNT
                  )}
                  labelClassName='!text-[1rem]'
                />
              </div>
              <div className='grid grid-cols-2 gap-x-10 items-end'>
                <FormikInput
                  valueChanged={(event) => {
                    setAutoCalculateField(event, setFieldValue, values);
                  }}
                  labelIcon2={
                    <div className='flex  items-center gap-2'>
                      <IoInformationCircle
                        id='age99'
                        className='text-blue-01'
                      />
                      <div className='relative'>
                        <Tooltip
                          anchorSelect='#age99'
                          place='left-start'
                          offset={0}
                          clickable
                          className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-5  !text-black-01 !text-[0.9rem] !rounded-lg'
                          classNameArrow='border-r  border-b  !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                        >
                          Override and enter your own number if you don&#39;t
                          like 99 as your life expectancy.
                        </Tooltip>
                      </div>
                    </div>
                  }
                  className='!mb-0.5'
                  name={`${PostExitFinancesKeys.YEARS_TO_FUND_AGE_99}`}
                  key={`${PostExitFinancesKeys.YEARS_TO_FUND_AGE_99}`}
                  label={getKeyTitle(PostExitFinancesKeys.YEARS_TO_FUND_AGE_99)}
                  labelClassName='!text-[1rem]'
                />
                <FormikInput
                  leadingIcon='$'
                  disabled
                  className='!mb-0.5'
                  name={`${PostExitFinancesKeys.TOTAL_ADDITIONAL_AMOUNT}`}
                  key={`${PostExitFinancesKeys.TOTAL_ADDITIONAL_AMOUNT}`}
                  label={getKeyTitle(
                    PostExitFinancesKeys.TOTAL_ADDITIONAL_AMOUNT
                  )}
                  labelClassName='!text-[1rem]'
                />
              </div>
              <div className='grid grid-cols-2 gap-x-10 items-end'>
                <FormikInput
                  disabled
                  leadingIcon='$'
                  className='!mb-0.5'
                  name={`${PostExitFinancesKeys.LESS_AVAILABLE_INVESTMENTS}`}
                  key={`${PostExitFinancesKeys.LESS_AVAILABLE_INVESTMENTS}`}
                  label={getKeyTitle(
                    PostExitFinancesKeys.LESS_AVAILABLE_INVESTMENTS
                  )}
                  labelClassName='!text-[1rem]'
                />
                <FormikInput
                  disabled
                  leadingIcon='$'
                  className='!mb-0.5'
                  name={`${PostExitFinancesKeys.NET_AMOUNT_NEEDED_TO_FUND}`}
                  key={`${PostExitFinancesKeys.NET_AMOUNT_NEEDED_TO_FUND}`}
                  label={getKeyTitle(
                    PostExitFinancesKeys.NET_AMOUNT_NEEDED_TO_FUND
                  )}
                  labelClassName='!text-[1rem]'
                />
              </div>
            </div>

            <div className='pr-7'>
              <BackNextComponent
                backStep={backStep}
                buttonType='submit'
                isLoading={isLoading}
                isNextDisable={false}
                onSaveToDraftClick={() => onSaveToDraftClick(values)}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default memo(PostExitFinances);
