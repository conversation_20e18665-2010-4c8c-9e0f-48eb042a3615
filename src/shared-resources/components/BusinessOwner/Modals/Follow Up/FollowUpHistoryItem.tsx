import React, { useState } from 'react';

interface FollowUpHistoryItemProps {
  followUpNumber: number;
  followUpText: string;
  date: string;
  description: string;
}

const FollowUpHistoryItem: React.FC<FollowUpHistoryItemProps> = (props) => {
  const { followUpNumber, followUpText, date, description } = props;
  const [showContent, setShowContent] = useState(false);

  return (
    <button
      onClick={() => {
        setShowContent(!showContent);
      }}
      className='flex gap-5 text-sm p-3 border border-gray-02 rounded-lg'
    >
      <div className='flex relative'>
        <div className='flex text-sm max-h-fit whitespace-nowrap text-white py-4 pl-5 pr-2 bg-blue-01'>
          Follow Up {}
          {followUpNumber}
        </div>
        <div className='border-l-[1rem] border-t-[1.6rem] border-b-[1.6rem] border-l-blue-01 border-b-transparent border-t-transparent' />
      </div>

      <div className='flex text-start flex-col overflow-auto scrollbar justify-start gap-1'>
        <p>{description}</p>
        <p
          className={`${
            showContent ? 'break-words' : 'hidden'
          } transition-all text-black-02 duration-300`}
        >
          {followUpText}
        </p>
        <span className='text-start text-gray-07'>{date}</span>
      </div>
    </button>
  );
};

export default FollowUpHistoryItem;
