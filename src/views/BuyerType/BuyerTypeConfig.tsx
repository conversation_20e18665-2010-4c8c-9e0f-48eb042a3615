/* eslint-disable @typescript-eslint/naming-convention */
import React from 'react';
import { AssessmentToolProgressStatus } from 'types/enum';
import GetStartedPage from './BuyerTypeScreens/GetStartedPage';
import NotIndentifiedTable from './BuyerTypeScreens/NotIndentifiedTable';
import BuyerAssessmentContainer from './BuyerAssessmentContainer';
import BuyerTypeIdentified from './BuyerTypeIdentified';
import BuyerNotIdentified from './BuyerNotIdentified';
import DealStructure2 from './DealStructure2';

export enum BuyerTypeScreens {
  GET_STARTED = 'get_started',
  TYPE_OF_BUYER = 'type_of_buyer',
  ALREADY_IDENTIFIED = 'already_identified',
  NOT_IDENTIFIED_TABLE = 'not_identified_table',
  NOT_IDENTIFIED = 'not_identified',
  DEAL_STRUCTURE = 'deal_structure',
  DEAL_STRUCTURE_2 = 'deal_structure_2',
}

export const numberToScreenObject: { [key: number]: BuyerTypeScreens } = {
  1: BuyerTypeScreens.GET_STARTED,
  2: BuyerTypeScreens.TYPE_OF_BUYER,
  3: BuyerTypeScreens.ALREADY_IDENTIFIED,
  4: BuyerTypeScreens.NOT_IDENTIFIED_TABLE,
  5: BuyerTypeScreens.NOT_IDENTIFIED,
  6: BuyerTypeScreens.DEAL_STRUCTURE,
  7: BuyerTypeScreens.DEAL_STRUCTURE_2,
};

export const screenToNumberObject: Record<string, number> = {
  [BuyerTypeScreens.GET_STARTED]: 1,
  [BuyerTypeScreens.TYPE_OF_BUYER]: 2,
  [BuyerTypeScreens.ALREADY_IDENTIFIED]: 3,
  [BuyerTypeScreens.NOT_IDENTIFIED_TABLE]: 4,
  [BuyerTypeScreens.NOT_IDENTIFIED]: 5,
  [BuyerTypeScreens.DEAL_STRUCTURE]: 6,
  [BuyerTypeScreens.DEAL_STRUCTURE_2]: 7,
};
export const NotIdentifiedTableHeaderOptions = [
  'Buyer Type',
  'First Choice',
  'Second Choice',
  'Third Choice',
];
export enum DealStructure {
  STOCK_VS_ASSET_SALE = 'stock_vs_asset_sale',
  TAX_IMPLICATIONS = 'tax_implications',
  PAYMENT_METHODS = 'payment_methods',
  PURCHASE_OPTIONS = 'purchase_options',
}
export enum BuyerReportPageEnum {
  SCREEN_1 = 'screen_1',
  SCREEN_2 = 'screen_2',
  SCREEN_3 = 'screen_3',
}
export const BuyerReportPageArray = [
  BuyerReportPageEnum.SCREEN_1,
  BuyerReportPageEnum.SCREEN_2,
  BuyerReportPageEnum.SCREEN_3,
];
export const ChoiceOptions = NotIdentifiedTableHeaderOptions.slice(1).map(
  (key) => ({
    label: key,
    value: key.toLowerCase().replace(/ /g, '_'),
  })
);
export const DEAL_STRUCTURE_QUESTIONS_DATA = [
  {
    ques: 'Do you understand the difference between a Stock Sale and an Asset Sale?',
    id: DealStructure.STOCK_VS_ASSET_SALE,
  },
  {
    ques: 'Do you understand the tax implications of a Stock Sale versus an Asset Sale?',
    id: DealStructure.TAX_IMPLICATIONS,
  },
  {
    ques: 'Relative to the purchase price, do you understand different payment methods like cash, installments, and earnout?',
    id: DealStructure.PAYMENT_METHODS,
  },
  {
    ques: 'Are you aware of purchase options for a buyer who does not have enough money to buy the business?',
    id: DealStructure.PURCHASE_OPTIONS,
  },
];
export const getDealStructureQuestionById = (
  id: DealStructure
): string | undefined => {
  const questionData = DEAL_STRUCTURE_QUESTIONS_DATA.find(
    (item) => item.id === id
  );
  return questionData ? questionData.ques : undefined;
};
export const convertObjectToFormattedResponse = (
  obj: any,
  mainHeading: string[],
  isDealStructure?: boolean
) => {
  // Map over the object keys and values to create the formatted rows
  const rows = Object?.keys(obj || {}).map((key) => ({
    heading: !isDealStructure
      ? `${key.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}`
      : `${getDealStructureQuestionById(key as DealStructure)}`, // Capitalize the first letter of each word and add index starting from 19
    value: obj[key], // Use the original value
  }));

  // Return the final formatted structure
  return [
    {
      headingArray: mainHeading, // Use the provided main heading
      rows, // The generated rows from the object
    },
  ];
};

export interface ThirdPartyBuyerTypeResponse {
  choice: 'first_choice' | 'second_choice' | 'third_choice' | undefined;
  checked: boolean | undefined;
}

export interface NotIdentifiedTableResponseType {
  'Existing Owners': ThirdPartyBuyerTypeResponse;
  'Key Employees': ThirdPartyBuyerTypeResponse;
  'Family Member in the business': ThirdPartyBuyerTypeResponse;
  'Family Member not in the business': ThirdPartyBuyerTypeResponse;
  'Someone I will bring in and train': ThirdPartyBuyerTypeResponse;
  Competitor: ThirdPartyBuyerTypeResponse;
  Vendor: ThirdPartyBuyerTypeResponse;
  Customer: ThirdPartyBuyerTypeResponse;
  Strategic: ThirdPartyBuyerTypeResponse;
  ESOP: ThirdPartyBuyerTypeResponse;
  'I am indifferent regarding Buyer Type': ThirdPartyBuyerTypeResponse;
}

export const notIdentifiedTableResponse: NotIdentifiedTableResponseType = {
  'Existing Owners': {
    choice: undefined,
    checked: undefined,
  },
  'Key Employees': {
    choice: undefined,
    checked: undefined,
  },
  'Family Member in the business': {
    choice: undefined,
    checked: undefined,
  },
  'Family Member not in the business': {
    choice: undefined,
    checked: undefined,
  },
  'Someone I will bring in and train': {
    choice: undefined,
    checked: undefined,
  },
  Competitor: {
    choice: undefined,
    checked: undefined,
  },
  Vendor: {
    choice: undefined,
    checked: undefined,
  },
  Customer: {
    choice: undefined,
    checked: undefined,
  },
  Strategic: {
    choice: undefined,
    checked: undefined,
  },
  ESOP: {
    choice: undefined,
    checked: undefined,
  },
  'I am indifferent regarding Buyer Type': {
    choice: undefined,
    checked: undefined,
  },
};

// Find errors in not identified table, In case of choosing same option and not choosing any option
export const findErrors = (
  values: NotIdentifiedTableResponseType,
  selectedTypes: string[]
) => {
  let keyMap: { [key: string]: string } = {};
  let duplicateKey = '';
  const errors: string[] = [];

  for (let i = 0; i < selectedTypes?.length; i += 1) {
    const key = selectedTypes[i];
    if (values[key as keyof NotIdentifiedTableResponseType].choice) {
      if (
        keyMap[
          values[key as keyof NotIdentifiedTableResponseType].choice as string
        ] === values[key as keyof NotIdentifiedTableResponseType].choice
      ) {
        duplicateKey = values[key as keyof NotIdentifiedTableResponseType]
          .choice as string; // for choosing same option
        break;
      } else {
        const value =
          values[key as keyof NotIdentifiedTableResponseType].choice;
        keyMap = { ...keyMap, [value as string]: value as string };
      }
    } else {
      errors.push(key); // for not choosing any option
    }
  }

  return {
    duplicateKey,
    errors,
  };
};

export const screenTitles: { [key in BuyerTypeScreens]: string } = {
  [BuyerTypeScreens.GET_STARTED]: 'Buyer Type',
  [BuyerTypeScreens.TYPE_OF_BUYER]: 'Buyer Type',
  [BuyerTypeScreens.ALREADY_IDENTIFIED]: 'Buyer Type',
  [BuyerTypeScreens.NOT_IDENTIFIED_TABLE]: 'Buyer Type',
  [BuyerTypeScreens.NOT_IDENTIFIED]: 'Buyer Type',
  [BuyerTypeScreens.DEAL_STRUCTURE]: 'Deal Structure',
  [BuyerTypeScreens.DEAL_STRUCTURE_2]: 'Deal Structure',
};

export enum BuyerAssessmentTitle {
  BUYER_TYPE = 'Buyer Type',
  DEAL_STRUCTURE = 'Deal Structure',
}

export enum BuyerType {
  STRATEGIC_BUYER = 'strategic_buyer',
  FINANCIAL_BUYER = 'financial_buyer',
  INDIVIDUAL_BUYER = 'individual_buyer',
  INSTITUTIONAL_BUYER = 'institutional_buyer',
  TURNAROUND_BUYER = 'turnaround_buyer',
  COMPETITOR_BUYER = 'competitor_buyer',
  FAMILY_BUYER = 'family_buyer',
  EMPLOYEE_BUYER = 'employee_buyer',
  INSIDE_BUYER = 'inside_buyer',
}

export enum BuyerTypeOther {
  STRATEGIC_BUYER = 'strategic_buyer',
  FINANCIAL_BUYER = 'financial_buyer',
  INDIVIDUAL_BUYER = 'individual_buyer',
  INSTITUTIONAL_BUYER = 'institutional_buyer',
  TURNAROUND_BUYER = 'turnaround_buyer',
  COMPETITOR_BUYER = 'competitor_buyer',
  FAMILY_BUYER = 'family_buyer',
  EMPLOYEE_BUYER = 'employee_buyer',
  INSIDE_BUYER = 'inside_buyer',
  OTHER = 'other',
}
// Arrow function to count all keys in an object (including nested ones)
const getTotalKeyCount = (obj: any): number => {
  let totalCount = 0;

  Object.keys(obj).forEach((section) => {
    const sectionKeys = obj[section];
    // Add the number of keys in each section to the total count
    totalCount += Object.keys(sectionKeys || {}).length;
  });

  return totalCount;
};
type AssessmentResponse = {
  saved_screen: number;
  type_of_buyer: Record<string, number>;
  already_identified: Record<string, string>;
  not_identified_table: Record<string, any>;
  not_identified: Record<string, string>;
  deal_structure: Record<string, number>;
  deal_structure_2: Record<string, string>;
  progress_status: string;
  reevaluate: boolean;
};

// Arrow function to get the total key count and selected screen count

export function calculateBuyerTypeResponsePercentage(
  assessmentResponse: any,
  status: AssessmentToolProgressStatus
) {
  const totalQuestions = 30; // Set total questions to 30

  // If status is "completed", return 100%
  if (status === AssessmentToolProgressStatus.COMPLETED) {
    return 100;
  }

  // Helper function to count non-empty responses
  const getDataLength = (response: AssessmentResponse): number => {
    if (!response || Object.keys(response).length === 0) {
      return 0; // Return 0 if data is null, undefined, or an empty object
    }
    const {
      saved_screen,
      progress_status,
      reevaluate,
      not_identified_table,
      ...rest
    } = response;

    // Count all keys in the remaining data (excluding saved_screen, progress_status, reevaluate)
    const totalKeyCount = getTotalKeyCount(rest); // Adding 1 for saved_screen

    // Get the number of selected items in 'not_identified_table' under 'selectedTypes'
    const selectedScreenCount =
      not_identified_table?.selectedTypes?.length || 0;

    // Return the sum of totalKeyCount and selectedScreenCount
    return totalKeyCount + selectedScreenCount;
  };

  // Calculate the percentage of answered questions
  const responsePercentage =
    (getDataLength(assessmentResponse) / totalQuestions) * 100;

  // Return the percentage as a number
  return parseFloat(responsePercentage.toFixed(2));
}

export const BUYER_TYPE_QUESTIONS_DATA = [
  {
    ques: 'Strategic Buyer',
    id: BuyerType.STRATEGIC_BUYER,
  },
  {
    ques: 'Financial Buyer',
    id: BuyerType.FINANCIAL_BUYER,
  },
  {
    ques: 'Individual Buyer',
    id: BuyerType.INDIVIDUAL_BUYER,
  },
  {
    ques: 'Institutional Buyer',
    id: BuyerType.INSTITUTIONAL_BUYER,
  },
  {
    ques: 'Turnaround Buyer',
    id: BuyerType.TURNAROUND_BUYER,
  },
  {
    ques: 'Competitor Buyer',
    id: BuyerType.COMPETITOR_BUYER,
  },
  {
    ques: 'Family Buyer',
    id: BuyerType.FAMILY_BUYER,
  },
  {
    ques: 'Employee Buyer',
    id: BuyerType.EMPLOYEE_BUYER,
  },
  {
    ques: 'Inside Buyer Not Yet Known',
    id: BuyerType.INSIDE_BUYER,
  },
];

export const getScreen = (
  currentScreen: BuyerTypeScreens,
  buyerTypeData: any,
  handleBack: (data: any, key?: string, screen?: BuyerTypeScreens) => void,
  handleNext: (data: any, key?: string, screen?: BuyerTypeScreens) => void,
  handleSubmit: (
    data: any,
    key?: string,
    submitType?: boolean,
    saved_screen?: number
  ) => void
) => {
  if (currentScreen === BuyerTypeScreens.GET_STARTED) {
    return (
      <GetStartedPage
        onGestartedClick={() =>
          handleNext(buyerTypeData, undefined, BuyerTypeScreens.TYPE_OF_BUYER)
        }
      />
    );
  }

  if (currentScreen === BuyerTypeScreens.TYPE_OF_BUYER) {
    return (
      <BuyerAssessmentContainer
        questions={BUYER_TYPE_QUESTIONS_DATA}
        data={buyerTypeData}
        handleNextClick={(values) => {
          handleNext(
            values,
            BuyerTypeScreens.TYPE_OF_BUYER,
            BuyerTypeScreens.ALREADY_IDENTIFIED
          );
        }}
        handlebackClick={(values) => {
          handleBack(
            values,
            BuyerTypeScreens.TYPE_OF_BUYER,
            BuyerTypeScreens.GET_STARTED
          );
        }}
        handleSaveAsDraftClick={(values) => {
          handleSubmit(
            values,
            BuyerTypeScreens.TYPE_OF_BUYER,
            false,
            screenToNumberObject[BuyerTypeScreens.TYPE_OF_BUYER]
          );
        }}
      />
    );
  }
  if (currentScreen === BuyerTypeScreens.DEAL_STRUCTURE) {
    return (
      <BuyerAssessmentContainer
        isDealStructure
        questions={DEAL_STRUCTURE_QUESTIONS_DATA}
        data={buyerTypeData}
        handleNextClick={(values) => {
          handleNext(
            values,
            BuyerTypeScreens.DEAL_STRUCTURE,
            BuyerTypeScreens.DEAL_STRUCTURE_2
          );
        }}
        handlebackClick={(values) => {
          handleBack(
            values,
            BuyerTypeScreens.DEAL_STRUCTURE,
            BuyerTypeScreens.NOT_IDENTIFIED
          );
        }}
        handleSaveAsDraftClick={(values) => {
          handleSubmit(
            values,
            BuyerTypeScreens.DEAL_STRUCTURE,
            false,
            screenToNumberObject[BuyerTypeScreens.DEAL_STRUCTURE]
          );
        }}
      />
    );
  }
  if (currentScreen === BuyerTypeScreens.NOT_IDENTIFIED) {
    return (
      <BuyerNotIdentified
        data={buyerTypeData}
        handleNextClick={(values) => {
          handleNext(
            values,
            BuyerTypeScreens.NOT_IDENTIFIED,
            BuyerTypeScreens.DEAL_STRUCTURE
          );
        }}
        handlebackClick={(values) => {
          handleBack(
            values,
            BuyerTypeScreens.NOT_IDENTIFIED,
            BuyerTypeScreens.NOT_IDENTIFIED_TABLE
          );
        }}
        handleSaveAsDraftClick={(values) => {
          handleSubmit(
            values,
            BuyerTypeScreens.NOT_IDENTIFIED,
            false,
            screenToNumberObject[BuyerTypeScreens.NOT_IDENTIFIED]
          );
        }}
      />
    );
  }
  if (currentScreen === BuyerTypeScreens.DEAL_STRUCTURE_2) {
    return (
      <DealStructure2
        data={buyerTypeData}
        handleNextClick={(values) => {
          handleSubmit(
            values,
            BuyerTypeScreens.DEAL_STRUCTURE_2,
            true,
            screenToNumberObject[BuyerTypeScreens.DEAL_STRUCTURE_2]
          );
        }}
        handlebackClick={(values) => {
          handleBack(
            values,
            BuyerTypeScreens.DEAL_STRUCTURE_2,
            BuyerTypeScreens.DEAL_STRUCTURE
          );
        }}
        handleSaveAsDraftClick={(values) => {
          handleSubmit(
            values,
            BuyerTypeScreens.DEAL_STRUCTURE_2,
            false,
            screenToNumberObject[BuyerTypeScreens.DEAL_STRUCTURE_2]
          );
        }}
      />
    );
  }

  if (currentScreen === BuyerTypeScreens.ALREADY_IDENTIFIED) {
    return (
      <BuyerTypeIdentified
        data={buyerTypeData}
        handleNextClick={(values) => {
          handleNext(
            values,
            BuyerTypeScreens.ALREADY_IDENTIFIED,
            BuyerTypeScreens.NOT_IDENTIFIED_TABLE
          );
        }}
        handleBackClick={(values) => {
          handleBack(
            values,
            BuyerTypeScreens.ALREADY_IDENTIFIED,
            BuyerTypeScreens.TYPE_OF_BUYER
          );
        }}
        handleSaveAsDraftClick={(values) => {
          handleSubmit(
            values,
            BuyerTypeScreens.ALREADY_IDENTIFIED,
            false,
            screenToNumberObject[BuyerTypeScreens.ALREADY_IDENTIFIED]
          );
        }}
      />
    );
  }

  if (currentScreen === BuyerTypeScreens.NOT_IDENTIFIED_TABLE) {
    return (
      <NotIndentifiedTable
        data={buyerTypeData}
        handleNextClick={(values) => {
          handleNext(
            values,
            BuyerTypeScreens.NOT_IDENTIFIED_TABLE,
            BuyerTypeScreens.NOT_IDENTIFIED
          );
        }}
        handleBackClick={(values) => {
          handleBack(
            values,
            BuyerTypeScreens.NOT_IDENTIFIED_TABLE,
            BuyerTypeScreens.ALREADY_IDENTIFIED
          );
        }}
        handleSaveAsDraftClick={(values) => {
          handleSubmit(
            values,
            BuyerTypeScreens.NOT_IDENTIFIED_TABLE,
            false,
            screenToNumberObject[BuyerTypeScreens.NOT_IDENTIFIED_TABLE]
          );
        }}
      />
    );
  }
  return (
    <BuyerAssessmentContainer
      questions={BUYER_TYPE_QUESTIONS_DATA}
      data={buyerTypeData}
      handleNextClick={(values) => {
        handleNext(
          values,
          BuyerTypeScreens.TYPE_OF_BUYER,
          BuyerTypeScreens.ALREADY_IDENTIFIED
        );
      }}
      handlebackClick={(values) => {
        handleBack(
          values,
          BuyerTypeScreens.TYPE_OF_BUYER,
          BuyerTypeScreens.GET_STARTED
        );
      }}
      handleSaveAsDraftClick={(values) => {
        handleSubmit(
          values,
          BuyerTypeScreens.TYPE_OF_BUYER,
          false,
          screenToNumberObject[BuyerTypeScreens.TYPE_OF_BUYER]
        );
      }}
    />
  );
};

export enum BuyerReportScreens {
  SCREEN_1 = 'screen_1',
  SCREEN_2 = 'screen_2',
  SCREEN_3 = 'screen_3',
}

export const screenToQuestionMapping: Record<BuyerReportScreens, string[]> = {
  [BuyerReportScreens.SCREEN_1]: [
    BuyerTypeScreens.TYPE_OF_BUYER,
    BuyerTypeScreens.ALREADY_IDENTIFIED,
  ],
  [BuyerReportScreens.SCREEN_2]: [
    BuyerTypeScreens.NOT_IDENTIFIED_TABLE,
    BuyerTypeScreens.NOT_IDENTIFIED,
  ],
  [BuyerReportScreens.SCREEN_3]: [
    BuyerTypeScreens.DEAL_STRUCTURE,
    BuyerTypeScreens.DEAL_STRUCTURE_2,
  ],
};

interface QuestionField {
  key: string;
  question: string;
}

export const ALREADY_IDENTIFIED_FIELDS: QuestionField[] = [
  {
    key: 'potential_buyer',
    question: 'If you have identified a potential buyer, please name the buyer',
  },
  {
    key: 'buyer_type',
    question: 'Which buyer type is the buyer you have identified?',
  },
  {
    key: 'conversation',
    question:
      'Briefly explain any conversation you have had with this potential buyer about buying your business.',
  },
  {
    key: 'reason_for_choice',
    question: 'Briefly explain why you chose this buyer.',
  },
  {
    key: 'envisioned_deal',
    question: 'Briefly describe the deal you envision with this buyer.',
  },
];

export const NOT_IDENTIFIED_FIELDS: QuestionField[] = [
  {
    key: 'buyer_type_choice',
    question: 'Briefly describe your choices of buyer type:',
  },
  {
    key: 'deal_details',
    question: 'Please briefly explain any deal details you might envision:',
  },
];

export const DEAL_STRUCTURE_QUESTIONS: Record<string, string> = {
  engaged_after_sale:
    'Are you willing to stay engaged in the business for a period of time after the sale?',
  pending_legal_issues:
    'Are there any pending legal issues that could affect the sale?',
  quick_sale_reason:
    'Are there any reasons why you need to sell the business quickly?',
  assets_not_for_sale:
    'Are there any assets that you do not want to sell with the business?',
  liabilities_at_sale:
    'What is your expectation regarding any business liabilities at the time of sale?',
  willing_to_finance:
    'Are you willing to finance any part of the purchase price?',
  deal_structure: 'Do you have a particular deal structure in mind?',
};

export const SECTION_TITLES: Record<BuyerReportScreens, string> = {
  [BuyerReportScreens.SCREEN_1]: 'Buyer Type Information',
  [BuyerReportScreens.SCREEN_2]: 'Not Identified Information',
  [BuyerReportScreens.SCREEN_3]: 'Deal Structure Information',
};

export const TABLE_COLUMNS = {
  BUYER_TYPE: ['Buyer Type', 'Value'],
  DEAL_STRUCTURE: ['Questions', 'Response'],
};
