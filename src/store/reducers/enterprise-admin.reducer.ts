import {
  createEntityAdapter,
  createSlice,
  EntityId,
  EntityState,
  PayloadAction,
} from '@reduxjs/toolkit';
import { uniq } from 'lodash';
import { EnterpriseAdmin } from 'models/entities/EnterpriseAdmin';
import { BaseEntityStore } from 'store/types/BaseEntityStoreType';
import { PaginationMeta } from '../../types/pagination.types';

const enterpriseAdminAdapter = createEntityAdapter<EnterpriseAdmin>();

const initialState: EntityState<EnterpriseAdmin> &
  BaseEntityStore &
  PaginationMeta = {
  ...enterpriseAdminAdapter.getInitialState(),
  create: {},
  update: {},
  get: {},
  list: {},
  paginationMeta: {},
};

export const enterpriseAdminSlice = createSlice({
  name: 'enterpriseAdmin',
  initialState,
  reducers: {
    fetch_enterprise_admin_list: enterpriseAdminAdapter.addMany,
    add_one: enterpriseAdminAdapter.setOne,
    set_enterprise_admins: (
      state,
      action: PayloadAction<{
        ids: EntityId[];
        loadMore: boolean;
      }>
    ) => {
      const { ids, loadMore } = action.payload;
      state.ids = loadMore ? uniq([...state.ids, ...ids]) : ids;
    },
    set_created_enterprise_admin: (
      state,
      action: PayloadAction<{ owner: EnterpriseAdmin }>
    ) => {
      const { owner } = action.payload;
      const entity = { [owner.id]: owner };
      state.entities = { ...state.entities, ...entity };
      state.ids = [owner.id, ...state.ids];
    },
    set_update_enterprise_admin: (state, action: any) => {
      const payload = action?.payload;
      const enterpriseAdmin = payload?.enterpriseAdmin;

      if (payload && enterpriseAdmin) {
        const { id } = enterpriseAdmin;
        const entity = { [id]: enterpriseAdmin };
        state.entities = { ...state.entities, ...entity };
        state.ids = state.ids.map((id) =>
          id === enterpriseAdmin.id ? enterpriseAdmin.id : id
        );
      }
    },
    set_delete_enterprise_admin: (state, action: any) => {
      const payload = action?.payload;
      const enterpriseAdmin = payload?.enterpriseAdmin;
      if (payload && enterpriseAdmin) {
        const { id } = enterpriseAdmin;
        const entity = { [id]: enterpriseAdmin };
        state.entities = { ...state.entities, ...entity };
        state.ids = state.ids.filter((i) => i !== id);
      }
    },

    set_create_enterprise_admin_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.create.loading = loading;
    },
    set_update_enterprise_admin_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.update.loading = loading;
    },
    set_create_enterprise_admin_error: (
      state,
      action: PayloadAction<{ error: boolean; message?: string; errors?: any }>
    ) => {
      const { error, message, errors } = action.payload;
      state.create.error = error;
      state.create.errors = errors;
      state.create.message = message;
      state.create.loading = false;
    },
    set_update_enterprise_admin_error: (
      state,
      action: PayloadAction<{ error: boolean; message?: string; errors?: any }>
    ) => {
      const { error, message, errors } = action.payload;
      state.update.error = error;
      state.update.errors = errors;
      state.update.message = message;
      state.update.loading = false;
    },
    set_get_enterprise_admin_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.get = { ...state.get, loading };
    },
    set_get_enterprise_admin_error: (
      state,
      action: PayloadAction<{ error: boolean; message?: string }>
    ) => {
      const { error, message } = action.payload;
      state.get = { ...state.get, loading: false, error, message };
    },
    set_enterprise_admins_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.list = { ...state.list, loading };
    },
    set_enterprise_admins_error: (
      state,
      action: PayloadAction<{ error: boolean; message?: string }>
    ) => {
      const { error, message } = action.payload;
      state.list = { ...state.list, error, message };
    },

    set_page_data: (state, action: PayloadAction<PaginationMeta>) => {
      state.paginationMeta = action.payload.paginationMeta;
    },

  },
});

export const {
  add_one: addEnterpriseAdmin,
  fetch_enterprise_admin_list: getEnterpriseAdmins,
  set_created_enterprise_admin: setCreatedEnterpriseAdmin,
  set_create_enterprise_admin_loading: setCreateEnterpriseAdminLoading,
  set_create_enterprise_admin_error: setCreateEnterpriseAdminError,
  set_update_enterprise_admin_loading: setUpdateEnterpriseAdminLoading,
  set_update_enterprise_admin_error: setUpdateEnterpriseAdminError,
  set_get_enterprise_admin_loading: setGetEnterpriseAdminLoading,
  set_get_enterprise_admin_error: setGetEnterpriseAdminError,

  set_enterprise_admins_loading: setEnterpriseAdminsLoading,
  set_enterprise_admins_error: setEnterpriseAdminsError,
  set_page_data: setEnterpriseAdminsPageData,
  set_enterprise_admins: setEnterpriseAdmins,

  set_update_enterprise_admin: setUpdateEnterpriseAdmin,
  set_delete_enterprise_admin: setDeleteEnterpriseAdmin,
} = enterpriseAdminSlice.actions;

export default enterpriseAdminSlice.reducer;
