import { BusinessOwner } from 'models/entities/BusinessOwner';
import React, { useEffect, useState } from 'react';
import { IoArrowBackSharp } from 'react-icons/io5';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router';
import {
  fetchOwnAssessment,
  openCountIncrement,
} from 'store/actions/assessment-tool.action';
import {
  getAssessmentToolResponse,
  getAssessmentToolStatus,
  getBusinessOwnerProperties,
  getReevaluate,
} from 'store/selectors/assessment-tool.selector';
import { getBusinessOwnerDetailsLoading } from 'store/selectors/business-owner.selector';
import { getUserData } from 'store/selectors/user.selector';
import { documentsLoading } from 'store/selectors/document.selector';
import { deleteDocument } from 'store/actions/document.action';
import {
  AssessmentToolProgressStatus,
  AssessmentTools,
  AssessmentToolStatus,
  NonAssessmentToolTabs,
  <PERSON><PERSON><PERSON>,
  UserRouteType,
  UserType,
} from 'types/enum';
import {
  getInitials,
  getOrderedAllStageAssessmentData,
  traceQuestionPath,
  getReportType,
} from 'utils/helpers/Helpers';
import { calculateValuationCompletedPercentage } from 'views/BusinessValuation/BusinessValuationConfig';
import { getToolProgressPercentage } from 'views/FinancialGapAnalysis/FinancialGapAnalysisConfig';
import { calculateOwnerRelianceCompletedPercentage } from 'views/Owner-Reliance/OwnerRelianceConfig';
import { calculateCompletedPercentage } from 'views/Readiness-Assesment/readinessReportHelper';
import { getQuestions } from 'views/TransitionObjectives/Questions';
import { calculateBusinessContinuityPercentage } from 'views/BusinessContinuity/BusinessContinuityConfig';
import { calculatePercentageOfNonNullOptions } from 'views/Value-Enhancement/ValueEnhancementConfig';
import { calculateStakeholderAlignmentPercentage } from 'views/Stakeholder-Alignment-Meeting/StakeHolderConfig';
import {
  OwnerType,
  getAssetProtectionToolProgressPercentage,
} from 'views/AssetProtection/AssetProtectionConfig';
import { calculateBuyerTypeResponsePercentage } from 'views/BuyerType/BuyerTypeConfig';
import NavigateContainer from '../NavigateContainer';
import Spinner from '../Spinner/Spinner';
import DetailItem from './DetailItems';
import IntermediateToolView from './IntermediateToolView';
import Button from '../Button/Button';
import Modal from '../Modal/Modal';
import DocumentsModal from './Modals/DocumentModal';
import DeleteDocumentModal from './Modals/Follow Up/DeleteDocumentModal';

const BusinessOwnerAssessment: React.FC = () => {
  const dispatch = useDispatch();

  const location = useLocation();
  const lastIndex = location.pathname.lastIndexOf('/');
  const tab = location.pathname.substring(lastIndex + 1);
  const navigate = useNavigate();

  const businessOwnerLoading = useSelector(getBusinessOwnerDetailsLoading);
  const businessOwner = useSelector(getUserData) as BusinessOwner;
  const progressStatus = useSelector(getAssessmentToolStatus);
  const [canSeeReport, setCanSeeReport] = React.useState<boolean>();
  const response: any = useSelector(getAssessmentToolResponse);
  const businessOwnerProperties = useSelector(
    getBusinessOwnerProperties
  ) as BusinessOwner;

  const reevaluate = useSelector(getReevaluate);

  const [documentModalVisible, setDocumentModalVisible] =
    useState<boolean>(false);
  const isBusinessContinuityCompleted = () =>
    !!businessOwnerProperties?.metadata?.business_continuity_updated_at;

  const getInitialOwnerType = () => {
    if (response?.screen3?.owner_type) {
      return response?.screen3?.owner_type as OwnerType;
    }
    if (businessOwnerProperties?.metadata?.ownership) {
      return businessOwnerProperties?.metadata?.ownership as OwnerType;
    }
    return null;
  };

  const getReportUrl = () => {
    switch (tab) {
      case RouteKey.READINESS_ASSESSMENT_DASHBOARD:
        return `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.READINESS_REPORT}`;
      case RouteKey.OWNER_RELIANCE_DASHBOARD:
        return `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.OWNER_RELIANCE_REPORT}`;
      case RouteKey.BUSINESS_CONTINUITY_DASHBOARD:
        return `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.BUSINESS_CONTINUITY_REPORT}`;
      case RouteKey.TRANSITION_OBJECTIVES_DASHBOARD:
        return `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.TRANSITION_REPORT}`;
      case RouteKey.FINANCIAL_GAP_ANALYSIS_DASHBOARD:
        return `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.FINANCIAL_GAP_REPORT}`;
      case RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES_DASHBOARD:
        return `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES_REPORT}`;
      case RouteKey.ASSET_PROTECTION_DASHBOARD:
        return `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.ASSET_PROTECTION_REPORT}`;
      case RouteKey.BUYER_TYPE_DEAL_STRUCTURE_DASHBOARD:
        return `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.BUYER_TYPE_DEAL_STRUCTURE_REPORT}`;
      default:
        return ``;
    }
  };

  useEffect(() => {
    const orderedAwarenessData = getOrderedAllStageAssessmentData(
      businessOwner as BusinessOwner
    );
    let isAssessmentEnabled;
    let shouldSeeReport;
    if (tab === RouteKey.READINESS_ASSESSMENT_DASHBOARD) {
      isAssessmentEnabled =
        orderedAwarenessData.find(
          (tool) => tool.toolName === AssessmentTools.READINESS_ASSESSMENT
        )?.toolData?.status === AssessmentToolStatus.ENABLE;

      shouldSeeReport = orderedAwarenessData.find(
        (tool) => tool.toolName === AssessmentTools.READINESS_ASSESSMENT
      )?.toolData?.is_report_sent_to_owner;
    } else if (tab === RouteKey.BUSINESS_VALUATION_DASHBOARD) {
      isAssessmentEnabled =
        orderedAwarenessData.find(
          (tool) => tool.toolName === AssessmentTools.BUSINESS_VALUATION
        )?.toolData?.status === AssessmentToolStatus.ENABLE;

      shouldSeeReport = orderedAwarenessData.find(
        (tool) => tool.toolName === AssessmentTools.BUSINESS_VALUATION
      )?.toolData?.is_report_sent_to_owner;
    } else if (tab === RouteKey.OWNER_RELIANCE_DASHBOARD) {
      isAssessmentEnabled =
        orderedAwarenessData.find(
          (tool) => tool.toolName === AssessmentTools.OWNER_RELIANCE
        )?.toolData?.status === AssessmentToolStatus.ENABLE;

      shouldSeeReport = orderedAwarenessData.find(
        (tool) => tool.toolName === AssessmentTools.OWNER_RELIANCE
      )?.toolData?.is_report_sent_to_owner;
    } else if (tab === RouteKey.TRANSITION_OBJECTIVES_DASHBOARD) {
      isAssessmentEnabled =
        orderedAwarenessData.find(
          (tool) => tool.toolName === AssessmentTools.TRANSITION_OBJECTIVES
        )?.toolData?.status === AssessmentToolStatus.ENABLE;

      shouldSeeReport = orderedAwarenessData.find(
        (tool) => tool.toolName === AssessmentTools.TRANSITION_OBJECTIVES
      )?.toolData?.is_report_sent_to_owner;
    } else if (tab === RouteKey.BUSINESS_CONTINUITY_DASHBOARD) {
      isAssessmentEnabled =
        orderedAwarenessData.find(
          (tool) => tool.toolName === AssessmentTools.BUSINESS_CONTINUITY
        )?.toolData?.status === AssessmentToolStatus.ENABLE;

      shouldSeeReport = orderedAwarenessData.find(
        (tool) => tool.toolName === AssessmentTools.BUSINESS_CONTINUITY
      )?.toolData.is_report_sent_to_owner;
    } else if (tab === RouteKey.FINANCIAL_GAP_ANALYSIS_DASHBOARD) {
      isAssessmentEnabled =
        orderedAwarenessData.find(
          (tool) => tool.toolName === AssessmentTools.FINANCIAL_GAP_ANALYSIS
        )?.toolData?.status === AssessmentToolStatus.ENABLE;

      shouldSeeReport = orderedAwarenessData.find(
        (tool) => tool.toolName === AssessmentTools.FINANCIAL_GAP_ANALYSIS
      )?.toolData?.is_report_sent_to_owner;
    } else if (tab === RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES_DASHBOARD) {
      isAssessmentEnabled =
        orderedAwarenessData.find(
          (tool) =>
            tool.toolName === AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES
        )?.toolData?.status === AssessmentToolStatus.ENABLE;

      shouldSeeReport = orderedAwarenessData.find(
        (tool) =>
          tool.toolName === AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES
      )?.toolData?.is_report_sent_to_owner;
    } else if (tab === RouteKey.STAKEHOLDER_ALIGNMENT_MEETING_DASHBOARD) {
      isAssessmentEnabled =
        orderedAwarenessData.find(
          (tool) =>
            tool.toolName === AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING
        )?.toolData?.status === AssessmentToolStatus.ENABLE;
    } else if (tab === RouteKey.ASSET_PROTECTION_DASHBOARD) {
      isAssessmentEnabled =
        orderedAwarenessData.find(
          (tool) => tool.toolName === AssessmentTools.ASSET_PROTECTION
        )?.toolData?.status === AssessmentToolStatus.ENABLE;

      shouldSeeReport = orderedAwarenessData.find(
        (tool) => tool.toolName === AssessmentTools.ASSET_PROTECTION
      )?.toolData?.is_report_sent_to_owner;
    } else if (tab === RouteKey.BUYER_TYPE_DEAL_STRUCTURE_DASHBOARD) {
      isAssessmentEnabled =
        orderedAwarenessData.find(
          (tool) => tool.toolName === AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE
        )?.toolData?.status === AssessmentToolStatus.ENABLE;

      shouldSeeReport = orderedAwarenessData.find(
        (tool) => tool.toolName === AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE
      )?.toolData?.is_report_sent_to_owner;
    }

    setCanSeeReport(shouldSeeReport);

    if (isAssessmentEnabled) {
      if (tab === RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES_DASHBOARD) {
        dispatch(
          fetchOwnAssessment({
            tool: AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES,
          })
        );
      } else if (tab === RouteKey.READINESS_ASSESSMENT_DASHBOARD) {
        dispatch(
          fetchOwnAssessment({ tool: AssessmentTools.READINESS_ASSESSMENT })
        );
      } else if (tab === RouteKey.BUSINESS_VALUATION_DASHBOARD) {
        dispatch(
          fetchOwnAssessment({ tool: AssessmentTools.BUSINESS_VALUATION })
        );
      } else if (tab === RouteKey.OWNER_RELIANCE_DASHBOARD) {
        dispatch(fetchOwnAssessment({ tool: AssessmentTools.OWNER_RELIANCE }));
      } else if (tab === RouteKey.BUSINESS_CONTINUITY_DASHBOARD) {
        dispatch(
          fetchOwnAssessment({ tool: AssessmentTools.BUSINESS_CONTINUITY })
        );
      } else if (tab === RouteKey.TRANSITION_OBJECTIVES_DASHBOARD) {
        dispatch(
          fetchOwnAssessment({ tool: AssessmentTools.TRANSITION_OBJECTIVES })
        );
      } else if (tab === RouteKey.FINANCIAL_GAP_ANALYSIS_DASHBOARD) {
        dispatch(
          fetchOwnAssessment({ tool: AssessmentTools.FINANCIAL_GAP_ANALYSIS })
        );
      } else if (tab === RouteKey.BUYER_TYPE_DEAL_STRUCTURE_DASHBOARD) {
        dispatch(
          fetchOwnAssessment({
            tool: AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE,
          })
        );
      } else if (tab === RouteKey.STAKEHOLDER_ALIGNMENT_MEETING_DASHBOARD) {
        dispatch(
          fetchOwnAssessment({
            tool: AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING,
          })
        );
      } else if (tab === RouteKey.ASSET_PROTECTION_DASHBOARD) {
        dispatch(
          fetchOwnAssessment({
            tool: AssessmentTools.ASSET_PROTECTION,
          })
        );
      }
    }
  }, [businessOwner?.assessment_data, dispatch]);

  const [questionNumber, setQuestionNumber] = useState(1);
  const [isCompleteOwner, setIsCompleteOwner] = useState(false);
  const Questions = getQuestions(isCompleteOwner);
  const [questions, setQuestions] = useState(Questions);

  function getInitialQuestions() {
    if (
      response &&
      typeof response === 'object' &&
      Object.keys(response).length
    ) {
      if (response.lastID !== undefined) {
        setQuestionNumber(+response.lastID);
      }
      return questions.map((question) => ({
        ...question,
        answer: response[question.id],
      }));
    }

    return Questions;
  }

  useEffect(() => {
    if (tab === RouteKey.TRANSITION_OBJECTIVES_DASHBOARD) {
      if (progressStatus === AssessmentToolProgressStatus.IN_PROGRESS) {
        setQuestions(getInitialQuestions());
      } else {
        setQuestions(Questions);
      }
    }
  }, [response, progressStatus, isCompleteOwner]);

  useEffect(() => {
    if (tab === RouteKey.TRANSITION_OBJECTIVES_DASHBOARD && response?.owners) {
      const ownershipData = JSON.parse(response?.owners).find(
        (owner: { name: string; ownership: string }) =>
          owner.name === businessOwner?.name
      );

      if (Number(ownershipData?.ownership) === 100) {
        setIsCompleteOwner(true);
      } else {
        setIsCompleteOwner(false);
      }
    }
  }, [response]);
  const getProgressPercentage = () => {
    let percentage = 0;
    if (reevaluate) {
      return percentage;
    }
    if (tab === RouteKey.READINESS_ASSESSMENT_DASHBOARD) {
      percentage = calculateCompletedPercentage(response);
      return percentage;
    }
    if (tab === RouteKey.BUSINESS_VALUATION_DASHBOARD) {
      percentage = calculateValuationCompletedPercentage(
        response,
        progressStatus
      );
    } else if (tab === RouteKey.OWNER_RELIANCE_DASHBOARD) {
      percentage = calculateOwnerRelianceCompletedPercentage(response);
    } else if (tab === RouteKey.TRANSITION_OBJECTIVES_DASHBOARD) {
      percentage =
        progressStatus === AssessmentToolProgressStatus.COMPLETED
          ? 100
          : traceQuestionPath(
              Questions,
              questions.filter((ques) => ques?.id === questionNumber)[0],
              response?.orderedQuestionIds || []
            );
    } else if (tab === RouteKey.BUSINESS_CONTINUITY_DASHBOARD) {
      percentage = calculateBusinessContinuityPercentage(
        response,
        progressStatus,
        businessOwner?.type as UserType
      );
    } else if (tab === RouteKey.FINANCIAL_GAP_ANALYSIS_DASHBOARD) {
      percentage = getToolProgressPercentage(response);
    } else if (tab === RouteKey.BUYER_TYPE_DEAL_STRUCTURE_DASHBOARD) {
      percentage = calculateBuyerTypeResponsePercentage(
        response,
        progressStatus as AssessmentToolProgressStatus
      );
    } else if (tab === RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES_DASHBOARD) {
      percentage = calculatePercentageOfNonNullOptions(response);
    } else if (tab === RouteKey.STAKEHOLDER_ALIGNMENT_MEETING_DASHBOARD) {
      percentage = calculateStakeholderAlignmentPercentage(
        response,
        progressStatus
      );
    } else if (
      tab === RouteKey.ASSET_PROTECTION_DASHBOARD &&
      !businessOwnerLoading
    ) {
      percentage = getAssetProtectionToolProgressPercentage(
        response,
        isBusinessContinuityCompleted(),
        getInitialOwnerType()
      );
    }
    return percentage;
  };

  const getAssessmentTitle = () => {
    if (tab === RouteKey.BUSINESS_VALUATION_DASHBOARD) {
      return 'Business Valuation';
    }
    if (tab === RouteKey.OWNER_RELIANCE_DASHBOARD) {
      return 'Owner Reliance';
    }
    if (tab === RouteKey.TRANSITION_OBJECTIVES_DASHBOARD) {
      return 'Transition Objectives';
    }
    if (tab === RouteKey.BUSINESS_CONTINUITY_DASHBOARD) {
      return 'Business Continuity';
    }
    if (tab === RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES_DASHBOARD) {
      return 'Value Enhancement Opportunities';
    }
    if (tab === RouteKey.STAKEHOLDER_ALIGNMENT_MEETING_DASHBOARD) {
      return 'Stakeholder Alignment Meeting';
    }
    if (tab === RouteKey.FINANCIAL_GAP_ANALYSIS_DASHBOARD) {
      return 'Wealth Gap Analysis';
    }
    if (tab === RouteKey.ASSET_PROTECTION_DASHBOARD) {
      return 'Asset Protection';
    }
    if (tab === RouteKey.BUYER_TYPE_DEAL_STRUCTURE_DASHBOARD) {
      return 'Buyer Type-Deal Structure';
    }
    if (tab === RouteKey.BUSINESS_FINANCIAL_ANALYSIS) {
      return 'Business Financial Analysis';
    }
    return 'Readiness Assessment';
  };

  const [showDeleteDocumentPopUp, setShowDeleteDocumentPopUp] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<any>(null);
  const loading = useSelector(documentsLoading);

  const handleDocumentModalVisibility = (value: boolean) => {
    setDocumentModalVisible(value);
  };

  const handleDelete = (item: any) => {
    if (businessOwner?.id) {
      dispatch(
        deleteDocument({
          id: businessOwner.id as any,
          type: getReportType(tab as AssessmentTools | NonAssessmentToolTabs),
          reportId: item.id,
          onSuccess: () => setShowDeleteDocumentPopUp(false),
        })
      );
    }
  };

  if (businessOwnerLoading) {
    return (
      <div className='flex items-center justify-center h-32'>
        <Spinner size='sm' />
      </div>
    );
  }

  const onOpenAssessment = () => {
    if (tab === RouteKey.BUSINESS_VALUATION_DASHBOARD) {
      if (progressStatus !== AssessmentToolProgressStatus.COMPLETED) {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.BUSINESS_VALUATION}`
        );
        dispatch(openCountIncrement(AssessmentTools.BUSINESS_VALUATION));
      } else {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.BUSINESS_VALUATION}`
        );
      }
    } else if (tab === RouteKey.OWNER_RELIANCE_DASHBOARD) {
      if (progressStatus !== AssessmentToolProgressStatus.COMPLETED) {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.OWNER_RELIANCE}`
        );
        dispatch(openCountIncrement(AssessmentTools.OWNER_RELIANCE));
      } else {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.OWNER_RELIANCE}`
        );
      }
    } else if (tab === RouteKey.READINESS_ASSESSMENT_DASHBOARD) {
      if (progressStatus !== AssessmentToolProgressStatus.COMPLETED) {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.READINESS_ASSESSMENT}`
        );
        dispatch(openCountIncrement(AssessmentTools.READINESS_ASSESSMENT));
      } else {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.READINESS_ASSESSMENT}`
        );
      }
    } else if (tab === RouteKey.BUSINESS_CONTINUITY_DASHBOARD) {
      if (progressStatus !== AssessmentToolProgressStatus.COMPLETED) {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.BUSINESS_CONTINUITY}`
        );
        dispatch(openCountIncrement(AssessmentTools.BUSINESS_CONTINUITY));
      } else {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.BUSINESS_CONTINUITY}`
        );
      }
    } else if (tab === RouteKey.TRANSITION_OBJECTIVES_DASHBOARD) {
      if (progressStatus !== AssessmentToolProgressStatus.COMPLETED) {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.TRANSITION_OBJECTIVES}`
        );
        dispatch(openCountIncrement(AssessmentTools.TRANSITION_OBJECTIVES));
      } else {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.TRANSITION_OBJECTIVES}`
        );
      }
    } else if (tab === RouteKey.FINANCIAL_GAP_ANALYSIS_DASHBOARD) {
      if (progressStatus !== AssessmentToolProgressStatus.COMPLETED) {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.FINANCIAL_GAP_ANALYSIS}`
        );
        dispatch(openCountIncrement(AssessmentTools.FINANCIAL_GAP_ANALYSIS));
      } else {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.FINANCIAL_GAP_ANALYSIS}`
        );
      }
    } else if (tab === RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES_DASHBOARD) {
      if (progressStatus !== AssessmentToolProgressStatus.COMPLETED) {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES}`
        );
        dispatch(
          openCountIncrement(AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES)
        );
      } else {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES}`
        );
      }
    } else if (tab === RouteKey.STAKEHOLDER_ALIGNMENT_MEETING_DASHBOARD) {
      if (progressStatus !== AssessmentToolProgressStatus.COMPLETED) {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.STAKEHOLDER_ALIGNMENT_MEETING}`
        );
        dispatch(
          openCountIncrement(AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING)
        );
      } else {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.STAKEHOLDER_ALIGNMENT_MEETING}`
        );
      }
    } else if (tab === RouteKey.ASSET_PROTECTION_DASHBOARD) {
      if (progressStatus !== AssessmentToolProgressStatus.COMPLETED) {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.ASSET_PROTECTION}`
        );
        dispatch(openCountIncrement(AssessmentTools.ASSET_PROTECTION));
      } else {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.ASSET_PROTECTION}`
        );
      }
    } else if (tab === RouteKey.BUYER_TYPE_DEAL_STRUCTURE_DASHBOARD) {
      if (progressStatus !== AssessmentToolProgressStatus.COMPLETED) {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.BUYER_TYPE_DEAL_STRUCTURE}`
        );
        dispatch(openCountIncrement(AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE));
      } else {
        navigate(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.BUYER_TYPE_DEAL_STRUCTURE}`
        );
      }
    }
  };

  const getDocumentTool = () => {
    if (tab === RouteKey.BUSINESS_FINANCIAL_ANALYSIS) {
      return NonAssessmentToolTabs.BUSINESS_FINANCIAL_ANALYSIS;
    }
    if (tab === RouteKey.ASSET_PROTECTION_DASHBOARD) {
      return AssessmentTools.ASSET_PROTECTION;
    }
    if (tab === RouteKey.BUSINESS_VALUATION_DASHBOARD) {
      return AssessmentTools.BUSINESS_VALUATION;
    }
    return null;
  };

  return (
    <div className='w-full flex flex-col'>
      <div className='flex rounded-xl p-7 bg-white'>
        <NavigateContainer className='min-w-7.5 text-black-02 mr-4 mt-2'>
          <IoArrowBackSharp size={24} />
        </NavigateContainer>
        <div className='w-fit flex items-center'>
          <div className='flex flex-col justify-center items-center'>
            <div className='rounded-full w-[7.5rem] h-[7.5rem] border-2 border-blue-01'>
              {businessOwner && (
                <div className='flex items-center font-medium w-full h-full text-3xl justify-center border border-blue-01 rounded-full text-blue-01'>
                  {getInitials(businessOwner)}
                </div>
              )}
            </div>
            <span className='text-center mt-4 font-medium whitespace-nowrap'>
              {businessOwner?.name}
            </span>
          </div>
        </div>
        <div className='flex flex-wrap justify-between gap-x-1 ml-10 max-w-[80rem]'>
          <DetailItem
            name='Business Name'
            value={businessOwner?.business_name}
          />
          <DetailItem
            name='Type of Business'
            value={businessOwner?.business_type || '-'}
          />
          <DetailItem name='Age of Business Owner' value={businessOwner?.age} />
          {businessOwner?.additional_advisor_email && (
            <DetailItem
              name='Additional Advisor'
              value={businessOwner?.additional_advisor_email}
            />
          )}
          <div className='inline-block w-full'>
            <DetailItem
              name='Email Address'
              width='w-full'
              value={businessOwner?.email}
            />
          </div>
        </div>
      </div>
      <div className='flex mt-5 rounded-xl pt-10 pl-12.5 pr-[4.7rem] pb-[6.6875rem] bg-white'>
        {tab !== RouteKey.BUSINESS_FINANCIAL_ANALYSIS && (
          <IntermediateToolView
            onOpenAssessment={onOpenAssessment}
            title={getAssessmentTitle()}
            percentage={getProgressPercentage()}
            showReport={
              tab !== RouteKey.BUSINESS_VALUATION_DASHBOARD &&
              tab !== RouteKey.STAKEHOLDER_ALIGNMENT_MEETING &&
              canSeeReport
            }
            reportUrl={getReportUrl()}
            showDocuments={
              tab === RouteKey.BUSINESS_VALUATION_DASHBOARD ||
              tab === RouteKey.ASSET_PROTECTION_DASHBOARD
            }
            onDocumentsClick={() => handleDocumentModalVisibility(true)}
          />
        )}
        {tab === RouteKey.BUSINESS_FINANCIAL_ANALYSIS && (
          <div className='flex flex-col w-full font-medium'>
            <h1 className='text-xl font-semibold mb-5'>
              Business Financial Analysis
            </h1>
            <Button
              className='px-6 py-2 w-fit'
              onClick={() => handleDocumentModalVisibility(true)}
            >
              Documents
            </Button>
          </div>
        )}
      </div>
      {documentModalVisible && (
        <Modal
          title='Documents'
          visible={documentModalVisible}
          handleVisibility={handleDocumentModalVisibility}
          classname='w-[66%]'
        >
          <DocumentsModal
            onCancelClick={handleDocumentModalVisibility}
            setSelectedDocument={setSelectedDocument}
            setShowDeleteDocumentPopUp={setShowDeleteDocumentPopUp}
            tool={getDocumentTool() as AssessmentTools | NonAssessmentToolTabs}
          />
        </Modal>
      )}

      {showDeleteDocumentPopUp && (
        <Modal
          title='Delete Document'
          visible={showDeleteDocumentPopUp}
          handleVisibility={setShowDeleteDocumentPopUp}
        >
          <DeleteDocumentModal
            loading={loading}
            onDeleteClick={() => handleDelete(selectedDocument)}
            onCancelClick={() => setShowDeleteDocumentPopUp(false)}
          />
        </Modal>
      )}
    </div>
  );
};

export default BusinessOwnerAssessment;
