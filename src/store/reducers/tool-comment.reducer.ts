import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ToolCommentResponse } from 'types/tool-comment.type';

interface ToolCommentState {
  comments: ToolCommentResponse[];
  loading: boolean;
  error: string | null;
}

const initialState: ToolCommentState = {
  comments: [],
  loading: false,
  error: null,
};

export const toolCommentSlice = createSlice({
  name: 'tool-comment',
  initialState,
  reducers: {
    setComments: (state, action: PayloadAction<ToolCommentResponse[]>) => {
      state.comments = action.payload;
      state.loading = false;
      state.error = null;
    },
    addComment: (state, action: PayloadAction<ToolCommentResponse>) => {
      state.comments.push(action.payload);
      state.loading = false;
      state.error = null;
    },
    updateComment: (state, action: PayloadAction<ToolCommentResponse>) => {
      const index = state.comments.findIndex(
        (comment) => comment.id === action.payload.id
      );
      if (index !== -1) {
        state.comments[index] = action.payload;
      }
      state.loading = false;
      state.error = null;
    },
    removeComment: (state, action: PayloadAction<number>) => {
      state.comments = state.comments.filter(
        (comment) => comment.id !== action.payload
      );
      state.loading = false;
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },
    resetComments: (state) => {
      state.comments = [];
      state.loading = false;
      state.error = null;
    },
  },
});

export const {
  setComments,
  addComment,
  updateComment,
  removeComment,
  setLoading,
  setError,
  resetComments,
} = toolCommentSlice.actions;

export default toolCommentSlice.reducer;
