import {
  AssessmentToolProgressStatus,
  BusinessValuationScreens,
} from 'types/enum';

const currDate = new Date();
const currentYear =
  currDate.getMonth() > 5 ? currDate.getFullYear() : currDate.getFullYear() - 1;

export const getYear = (yearNumber: number) => {
  if (yearNumber === 1) {
    return currentYear;
  }
  if (yearNumber === 2) {
    return currentYear - 1;
  }
  return currentYear - 2;
};

// screen name against its screen number
export const numberToScreenObject: { [key: number]: BusinessValuationScreens } =
  {
    1: BusinessValuationScreens.BASIC_INFORMATION,
    2: BusinessValuationScreens.INFO_SCREEN,
    3: BusinessValuationScreens.OWNER_P_AND_L,
    4: BusinessValuationScreens.SPOUSE_OR_FAMILY_P_AND_L,
    5: BusinessValuationScreens.OWNER_INSURANCE_P_AND_L,
    6: BusinessValuationScreens.VEHICLE_P_AND_L,
    7: BusinessValuationScreens.RENT_ADJUSTMENTS_P_AND_L,
    8: BusinessValuationScreens.NON_ACTIVE_FAMILY_BENEFITS_P_AND_L,
    9: BusinessValuationScreens.PERSONAL_EXPENSES_P_AND_L,
    10: BusinessValuationScreens.TOTAL_NORMALIZATION_EXPENSES,
    11: BusinessValuationScreens.INCOME_STATEMENT_DATA,
    12: BusinessValuationScreens.BALANCE_SHEET_DATA_1,
    13: BusinessValuationScreens.BALANCE_SHEET_DATA_2,
    14: BusinessValuationScreens.BALANCE_SHEET_DATA_3,
  };

// screen number against its screen name
export const screenToNumberObject: Record<string, number> = {
  [BusinessValuationScreens.BASIC_INFORMATION]: 1,
  [BusinessValuationScreens.INFO_SCREEN]: 2,
  [BusinessValuationScreens.OWNER_P_AND_L]: 3,
  [BusinessValuationScreens.SPOUSE_OR_FAMILY_P_AND_L]: 4,
  [BusinessValuationScreens.OWNER_INSURANCE_P_AND_L]: 5,
  [BusinessValuationScreens.VEHICLE_P_AND_L]: 6,
  [BusinessValuationScreens.RENT_ADJUSTMENTS_P_AND_L]: 7,
  [BusinessValuationScreens.NON_ACTIVE_FAMILY_BENEFITS_P_AND_L]: 8,
  [BusinessValuationScreens.PERSONAL_EXPENSES_P_AND_L]: 9,
  [BusinessValuationScreens.TOTAL_NORMALIZATION_EXPENSES]: 10,
  [BusinessValuationScreens.INCOME_STATEMENT_DATA]: 11,
  [BusinessValuationScreens.BALANCE_SHEET_DATA_1]: 12,
  [BusinessValuationScreens.BALANCE_SHEET_DATA_2]: 13,
  [BusinessValuationScreens.BALANCE_SHEET_DATA_3]: 14,
};

export const getFirstName = (fullName?: string) =>
  fullName ? fullName.split(' ')[0] : '';

export const getLastName = (fullName?: string) =>
  fullName ? fullName.split(' ')[1] : '';

export const getDate = (dateObject: Date) => {
  const year = dateObject.getFullYear();
  const month = String(dateObject.getMonth() + 1).padStart(2, '0'); // Month is zero-based, so we add 1
  const day = String(dateObject.getDate()).padStart(2, '0');

  // Format the date string as "YYYY-MM-DD"
  return `${year}-${month}-${day}`;
};

export const getTotalNormalizationExpenses = (values: any) => {
  let totalNormalizationExpensesFirst = 0;
  let totalNormalizationExpensesSecond = 0;
  let totalNormalizationExpensesThird = 0;

  values?.owner_p_and_l?.forEach((element: any, i: number) => {
    if (i > 0) {
      totalNormalizationExpensesFirst +=
        (Number(element?.[`net_adjustments_${getYear(1)}_salary`]) || 0) +
        (Number(element?.[`net_adjustments_${getYear(1)}_bonus`]) || 0);

      totalNormalizationExpensesSecond =
        totalNormalizationExpensesSecond +
        (Number(element?.[`net_adjustments_${getYear(2)}_salary`]) || 0) +
        (Number(element?.[`net_adjustments_${getYear(2)}_bonus`]) || 0);

      totalNormalizationExpensesThird =
        totalNormalizationExpensesThird +
        (Number(element?.[`net_adjustments_${getYear(3)}_salary`]) || 0) +
        (Number(element?.[`net_adjustments_${getYear(3)}_bonus`]) || 0);
    } else {
      totalNormalizationExpensesFirst +=
        (Number(
          (element?.[`owner_${getYear(1)}_salary`] || '0').replace(/,/g, '')
        ) || 0) +
        (Number(
          (element?.[`owner_${getYear(1)}_bonus`] || '0').replace(/,/g, '')
        ) || 0);

      totalNormalizationExpensesSecond +=
        (Number(
          (element?.[`owner_${getYear(2)}_salary`] || '0').replace(/,/g, '')
        ) || 0) +
        (Number(
          (element?.[`owner_${getYear(2)}_bonus`] || '0').replace(/,/g, '')
        ) || 0);

      totalNormalizationExpensesThird +=
        (Number(
          (element?.[`owner_${getYear(3)}_salary`] || '0').replace(/,/g, '')
        ) || 0) +
        (Number(
          (element?.[`owner_${getYear(3)}_bonus`] || '0').replace(/,/g, '')
        ) || 0);
    }
  });

  values?.spouse_or_family_p_and_l?.forEach((element: any) => {
    totalNormalizationExpensesFirst +=
      (Number(element?.[`net_adjustments_${getYear(1)}_salary`]) || 0) +
      (Number(element?.[`net_adjustments_${getYear(1)}_bonus`]) || 0);

    totalNormalizationExpensesSecond =
      totalNormalizationExpensesSecond +
      (Number(element?.[`net_adjustments_${getYear(2)}_salary`]) || 0) +
      (Number(element?.[`net_adjustments_${getYear(2)}_bonus`]) || 0);

    totalNormalizationExpensesThird =
      totalNormalizationExpensesThird +
      (Number(element?.[`net_adjustments_${getYear(3)}_salary`]) || 0) +
      (Number(element?.[`net_adjustments_${getYear(3)}_bonus`]) || 0);
  });

  values?.vehicle_p_and_l?.forEach((element: any) => {
    totalNormalizationExpensesFirst +=
      Number(
        (element?.[`annual_finance_payments_${getYear(1)}`] &&
          element?.[`annual_finance_payments_${getYear(1)}`]?.replace(
            /,/g,
            ''
          )) ||
          0
      ) +
      Number(
        (element?.[`fuel_${getYear(1)}`] &&
          element?.[`fuel_${getYear(1)}`]?.replace(/,/g, '')) ||
          0
      ) +
      Number(
        (element?.[`insurance_${getYear(1)}`] &&
          element?.[`insurance_${getYear(1)}`]?.replace(/,/g, '')) ||
          0
      ) +
      Number(
        (element?.[`maintenance_and_repairs_${getYear(1)}`] &&
          element?.[`maintenance_and_repairs_${getYear(1)}`]?.replace(
            /,/g,
            ''
          )) ||
          0
      );

    totalNormalizationExpensesSecond +=
      Number(
        (element?.[`annual_finance_payments_${getYear(2)}`] &&
          element?.[`annual_finance_payments_${getYear(2)}`]?.replace(
            /,/g,
            ''
          )) ||
          0
      ) +
      Number(
        (element?.[`fuel_${getYear(2)}`] &&
          element?.[`fuel_${getYear(2)}`]?.replace(/,/g, '')) ||
          0
      ) +
      Number(
        (element?.[`insurance_${getYear(2)}`] &&
          element?.[`insurance_${getYear(2)}`]?.replace(/,/g, '')) ||
          0
      ) +
      Number(
        (element?.[`maintenance_and_repairs_${getYear(2)}`] &&
          element?.[`maintenance_and_repairs_${getYear(2)}`]?.replace(
            /,/g,
            ''
          )) ||
          0
      );

    totalNormalizationExpensesThird +=
      Number(
        (element?.[`annual_finance_payments_${getYear(3)}`] &&
          element?.[`annual_finance_payments_${getYear(3)}`]?.replace(
            /,/g,
            ''
          )) ||
          0
      ) +
      Number(
        (element?.[`fuel_${getYear(3)}`] &&
          element?.[`fuel_${getYear(3)}`]?.replace(/,/g, '')) ||
          0
      ) +
      Number(
        (element?.[`insurance_${getYear(3)}`] &&
          element?.[`insurance_${getYear(3)}`]?.replace(/,/g, '')) ||
          0
      ) +
      Number(
        (element?.[`maintenance_and_repairs_${getYear(3)}`] &&
          element?.[`maintenance_and_repairs_${getYear(3)}`]?.replace(
            /,/g,
            ''
          )) ||
          0
      );
  });

  values?.rent_adjustments?.forEach((element: any) => {
    totalNormalizationExpensesFirst += Number(
      (element?.[`actual_rent_paid_minus_market_${getYear(1)}`] &&
        element?.[`actual_rent_paid_minus_market_${getYear(1)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    );

    totalNormalizationExpensesSecond += Number(
      (element?.[`actual_rent_paid_minus_market_${getYear(2)}`] &&
        element?.[`actual_rent_paid_minus_market_${getYear(2)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    );

    totalNormalizationExpensesThird += Number(
      (element?.[`actual_rent_paid_minus_market_${getYear(3)}`] &&
        element?.[`actual_rent_paid_minus_market_${getYear(3)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    );
  });

  totalNormalizationExpensesFirst +=
    Number(
      (values?.owner_insurance?.[`health_insurance_${getYear(1)}`] &&
        values?.owner_insurance?.[`health_insurance_${getYear(1)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.owner_insurance?.[`life_insurance_${getYear(1)}`] &&
        values?.owner_insurance?.[`life_insurance_${getYear(1)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.owner_insurance?.[`non_auto_insurance_${getYear(1)}`] &&
        values?.owner_insurance?.[`non_auto_insurance_${getYear(1)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    );

  totalNormalizationExpensesSecond +=
    Number(
      (values?.owner_insurance?.[`health_insurance_${getYear(2)}`] &&
        values?.owner_insurance?.[`health_insurance_${getYear(2)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.owner_insurance?.[`life_insurance_${getYear(2)}`] &&
        values?.owner_insurance?.[`life_insurance_${getYear(2)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.owner_insurance?.[`non_auto_insurance_${getYear(2)}`] &&
        values?.owner_insurance?.[`non_auto_insurance_${getYear(2)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    );

  totalNormalizationExpensesThird +=
    Number(
      (values?.owner_insurance?.[`health_insurance_${getYear(3)}`] &&
        values?.owner_insurance?.[`health_insurance_${getYear(3)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.owner_insurance?.[`life_insurance_${getYear(3)}`] &&
        values?.owner_insurance?.[`life_insurance_${getYear(3)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.owner_insurance?.[`non_auto_insurance_${getYear(3)}`] &&
        values?.owner_insurance?.[`non_auto_insurance_${getYear(3)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    );

  values?.owner_insurance?.other_insurance?.forEach((element: any) => {
    totalNormalizationExpensesFirst += Number(
      (element?.[`other_insurance_${getYear(1)}`] &&
        element?.[`other_insurance_${getYear(1)}`]?.replace(/,/g, '')) ||
        0
    );

    totalNormalizationExpensesSecond += Number(
      (element?.[`other_insurance_${getYear(2)}`] &&
        element?.[`other_insurance_${getYear(2)}`]?.replace(/,/g, '')) ||
        0
    );

    totalNormalizationExpensesThird += Number(
      (element?.[`other_insurance_${getYear(3)}`] &&
        element?.[`other_insurance_${getYear(3)}`]?.replace(/,/g, '')) ||
        0
    );
  });

  totalNormalizationExpensesFirst +=
    Number(
      (values?.non_active_family_benefits?.[`education_${getYear(1)}`] &&
        values?.non_active_family_benefits?.[
          `education_${getYear(1)}`
        ]?.replace(/,/g, '')) ||
        0
    ) +
    Number(
      (values?.non_active_family_benefits?.[`housing_${getYear(1)}`] &&
        values?.non_active_family_benefits?.[`housing_${getYear(1)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.non_active_family_benefits?.[`insurance_${getYear(1)}`] &&
        values?.non_active_family_benefits?.[
          `insurance_${getYear(1)}`
        ]?.replace(/,/g, '')) ||
        0
    ) +
    Number(
      (values?.non_active_family_benefits?.[`vehicles_${getYear(1)}`] &&
        values?.non_active_family_benefits?.[`vehicles_${getYear(1)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    );

  totalNormalizationExpensesSecond +=
    Number(
      (values?.non_active_family_benefits?.[`education_${getYear(2)}`] &&
        values?.non_active_family_benefits?.[
          `education_${getYear(2)}`
        ]?.replace(/,/g, '')) ||
        0
    ) +
    Number(
      (values?.non_active_family_benefits?.[`housing_${getYear(2)}`] &&
        values?.non_active_family_benefits?.[`housing_${getYear(2)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.non_active_family_benefits?.[`insurance_${getYear(2)}`] &&
        values?.non_active_family_benefits?.[
          `insurance_${getYear(2)}`
        ]?.replace(/,/g, '')) ||
        0
    ) +
    Number(
      (values?.non_active_family_benefits?.[`vehicles_${getYear(2)}`] &&
        values?.non_active_family_benefits?.[`vehicles_${getYear(2)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    );

  totalNormalizationExpensesThird +=
    Number(
      (values?.non_active_family_benefits?.[`education_${getYear(3)}`] &&
        values?.non_active_family_benefits?.[
          `education_${getYear(3)}`
        ]?.replace(/,/g, '')) ||
        0
    ) +
    Number(
      (values?.non_active_family_benefits?.[`housing_${getYear(3)}`] &&
        values?.non_active_family_benefits?.[`housing_${getYear(3)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.non_active_family_benefits?.[`insurance_${getYear(3)}`] &&
        values?.non_active_family_benefits?.[
          `insurance_${getYear(3)}`
        ]?.replace(/,/g, '')) ||
        0
    ) +
    Number(
      (values?.non_active_family_benefits?.[`vehicles_${getYear(3)}`] &&
        values?.non_active_family_benefits?.[`vehicles_${getYear(3)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    );

  values?.non_active_family_benefits?.other?.forEach((element: any) => {
    totalNormalizationExpensesFirst += Number(
      (element?.[`other_${getYear(1)}`] &&
        element?.[`other_${getYear(1)}`]?.replace(/,/g, '')) ||
        0
    );

    totalNormalizationExpensesSecond += Number(
      (element?.[`other_${getYear(2)}`] &&
        element?.[`other_${getYear(2)}`]?.replace(/,/g, '')) ||
        0
    );

    totalNormalizationExpensesThird += Number(
      (element?.[`other_${getYear(3)}`] &&
        element?.[`other_${getYear(3)}`]?.replace(/,/g, '')) ||
        0
    );
  });

  totalNormalizationExpensesFirst +=
    Number(
      (values?.personal_expenses?.[`entertainment_${getYear(1)}`] &&
        values?.personal_expenses?.[`entertainment_${getYear(1)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.personal_expenses?.[`memberships_${getYear(1)}`] &&
        values?.personal_expenses?.[`memberships_${getYear(1)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.personal_expenses?.[`travel_${getYear(1)}`] &&
        values?.personal_expenses?.[`travel_${getYear(1)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.personal_expenses?.[`charitable_contribution_${getYear(1)}`] &&
        values?.personal_expenses?.[
          `charitable_contribution_${getYear(1)}`
        ]?.replace(/,/g, '')) ||
        0
    );

  totalNormalizationExpensesSecond +=
    Number(
      (values?.personal_expenses?.[`entertainment_${getYear(2)}`] &&
        values?.personal_expenses?.[`entertainment_${getYear(2)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.personal_expenses?.[`memberships_${getYear(2)}`] &&
        values?.personal_expenses?.[`memberships_${getYear(2)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.personal_expenses?.[`travel_${getYear(2)}`] &&
        values?.personal_expenses?.[`travel_${getYear(2)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.personal_expenses?.[`charitable_contribution_${getYear(2)}`] &&
        values?.personal_expenses?.[
          `charitable_contribution_${getYear(2)}`
        ]?.replace(/,/g, '')) ||
        0
    );

  totalNormalizationExpensesThird +=
    Number(
      (values?.personal_expenses?.[`entertainment_${getYear(3)}`] &&
        values?.personal_expenses?.[`entertainment_${getYear(3)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.personal_expenses?.[`memberships_${getYear(3)}`] &&
        values?.personal_expenses?.[`memberships_${getYear(3)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.personal_expenses?.[`travel_${getYear(3)}`] &&
        values?.personal_expenses?.[`travel_${getYear(3)}`]?.replace(
          /,/g,
          ''
        )) ||
        0
    ) +
    Number(
      (values?.personal_expenses?.[`charitable_contribution_${getYear(3)}`] &&
        values?.personal_expenses?.[
          `charitable_contribution_${getYear(3)}`
        ]?.replace(/,/g, '')) ||
        0
    );

  values?.personal_expenses?.other.forEach((element: any) => {
    totalNormalizationExpensesFirst += Number(
      (element[`other_${getYear(1)}`] &&
        element[`other_${getYear(1)}`]?.replace(/,/g, '')) ||
        0
    );
    totalNormalizationExpensesSecond += Number(
      (element[`other_${getYear(2)}`] &&
        element[`other_${getYear(2)}`]?.replace(/,/g, '')) ||
        0
    );
    totalNormalizationExpensesThird += Number(
      (element[`other_${getYear(3)}`] &&
        element?.[`other_${getYear(3)}`]?.replace(/,/g, '')) ||
        0
    );
  });

  return {
    totalNormalizationExpensesFirst,
    totalNormalizationExpensesSecond,
    totalNormalizationExpensesThird,
  };
};

export const calculateValuationCompletedPercentage = (
  response: any,
  progressStatus: string
) => {
  if (progressStatus === AssessmentToolProgressStatus.COMPLETED) {
    return 100;
  }
  if (!response || Object.keys(response).length === 0) {
    return 0; // Return 0 if data is null, undefined, or an empty object
  }
  if (!response?.saved_screen && response?.basic_information) {
    return (2 / 14) * 100;
  }
  const savedScreen = response?.saved_screen ?? 0;
  const percentage = (savedScreen / 14) * 100;
  return percentage;
};

const downloadCSV = (csvString: string, filename: string): void => {
  const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

const processData = (data: any) => {
  const result: any[] = [];
  const years = ['2024', '2023', '2022'];

  const getOrdinal = (n: number): string => {
    const s = ['th', 'st', 'nd', 'rd'];
    const v = n % 100;
    return n + (s[(v - 20) % 10] || s[v] || s[0]);
  };

  const formatTitle = (title: string) =>
    title
      .split('_')
      .map((word) => {
        // Check if the word is an ordinal number (e.g., "1st", "2nd", etc.)
        const match = word.match(/^(\d+)(st|nd|rd|th)$/);
        if (match) {
          return `${match[1]}${match[2]}`;
        }
        return word.charAt(0).toUpperCase() + word.slice(1);
      })
      .join(' ');

  const formatValue = (value: string) => {
    if (value.includes('_')) {
      return value
        .split('_')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    }
    return value;
  };

  const processObject = (obj: any, prefix = '') => {
    Object.entries(obj).forEach(([key, value]) => {
      if (
        typeof value === 'object' &&
        value !== null &&
        !Array.isArray(value)
      ) {
        processObject(value, prefix ? `${prefix}_${key}` : key);
      } else if (Array.isArray(value)) {
        value.forEach((item, index) => {
          processObject(
            item,
            `${prefix ? `${prefix}_` : ''}${key}_${getOrdinal(index + 1)}`
          );
        });
      } else {
        let title = prefix ? `${prefix}_${key}` : key;
        const year = years.find((y) => title.includes(y));
        if (year) {
          title = title.replace(`_${year}`, '');
        }
        const formattedTitle = formatTitle(title);
        const formattedValue =
          typeof value === 'string' ? formatValue(value) : value;

        const existingRow = result.find((row) => row.title === formattedTitle);
        if (existingRow) {
          if (year) {
            existingRow[year] = formattedValue;
          } else {
            existingRow.value = formattedValue;
          }
        } else {
          const newRow: any = { title: formattedTitle, value: '' };
          years.forEach((y) => {
            newRow[y] = '';
          });
          if (year) {
            newRow[year] = formattedValue;
          } else {
            newRow.value = formattedValue;
          }
          result.push(newRow);
        }
      }
    });
  };

  processObject(data);
  return result;
};

const convertToCSV = (data: any) => {
  const processedData = processData(data);
  const headers = ['Title', 'Value', '2024', '2023', '2022'];
  const csvRows = [headers.join(',')];

  processedData.forEach((row) => {
    const values = headers.map((header) => {
      let value;
      if (header === 'Title') {
        value = row.title;
      } else if (header === 'Value') {
        value = row.value;
      } else if (row[header] === 0) {
        value = row[header];
      } else {
        value = row[header] || '';
      }
      return typeof value === 'string'
        ? `"${value.replace(/"/g, '""')}"`
        : value;
    });
    csvRows.push(values.join(','));
  });

  return csvRows.join('\n');
};

export const handleDownloadAsCSV = (businessValuationData: any): void => {
  const data = { ...businessValuationData };
  delete data.saved_screen;
  const csvString = convertToCSV(data);
  downloadCSV(csvString, 'businessValuation.csv');
};

export const transformOwnerPAndLData = (data: {
  [x: string]: any;
  owner_name?: any;
  other_owners?: any;
}) => {
  // Extract keys related to the owner
  const ownerKeys = Object.keys(data).filter((key) => key.startsWith('owner_'));

  // Get the last 7 keys for the first Owner
  const firstElement = ownerKeys
    .slice(-7)
    .reduce((acc, key) => ({ ...acc, [key]: data[key] }), {});

  // Combine firstElement with other_owners
  return [firstElement, ...(data.other_owners || [])];
};

export const getHeaderTitle = (screen?: BusinessValuationScreens) => {
  const sdeCalculationScreens = [
    BusinessValuationScreens.OWNER_P_AND_L,
    BusinessValuationScreens.PERSONAL_EXPENSES_P_AND_L,
    BusinessValuationScreens.VEHICLE_P_AND_L,
    BusinessValuationScreens.TOTAL_NORMALIZATION_EXPENSES,
    BusinessValuationScreens.SPOUSE_OR_FAMILY_P_AND_L,
    BusinessValuationScreens.RENT_ADJUSTMENTS_P_AND_L,
    BusinessValuationScreens.NON_ACTIVE_FAMILY_BENEFITS_P_AND_L,
    BusinessValuationScreens.OWNER_INSURANCE_P_AND_L,
  ];
  const balanceSheetScreens = [
    BusinessValuationScreens.BALANCE_SHEET_DATA_1,
    BusinessValuationScreens.BALANCE_SHEET_DATA_2,
    BusinessValuationScreens.BALANCE_SHEET_DATA_3,
  ];

  const incomeStatementScreens = [
    BusinessValuationScreens.INCOME_STATEMENT_DATA,
  ];

  if (screen) {
    if (sdeCalculationScreens.includes(screen)) return 'SDE Calculation';
    if (balanceSheetScreens.includes(screen)) return 'Balance Sheet Data';
    if (incomeStatementScreens.includes(screen)) return 'Income Statement Data';
    return '';
  }
  return '';
};
