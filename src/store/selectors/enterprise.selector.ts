import { createSelector } from '@reduxjs/toolkit';
import { Enterprise } from 'services/api-services/EnterpriseService';
import { AppState } from 'store';
import { createParamSelector } from './base.selectors';

// Base selector
const selectEnterpriseState = (state: AppState) => state.enterprise;

const getEnterpriseId = createParamSelector(
  (params: { id: string }) => params.id
);

// Enterprises List
export const allEnterprises = createSelector(
  [selectEnterpriseState],
  (enterprises) => {
    const enterprisesData = enterprises.entities ?? {};
    if (enterprises.ids) {
      return enterprises.ids.map((id) => enterprisesData[id] as Enterprise);
    }
    return [];
  }
);

// Get enterprise by ID
export const getEnterpriseById = createSelector(
  [selectEnterpriseState, getEnterpriseId],
  (enterpriseData, enterpriseId) => {
    const { entities } = enterpriseData;
    return entities[enterpriseId];
  }
);

// Get enterprises count
export const enterprisesCount = createSelector(
  [allEnterprises],
  (enterprises) => enterprises.length
);

// Check if enterprises list is empty
export const isEnterprisesListEmpty = createSelector(
  [allEnterprises],
  (enterprises) => enterprises.length === 0
);

// Loading states
export const enterpriseListLoading = createSelector(
  [selectEnterpriseState],
  (state) => state.list?.loading
);

export const enterpriseGetLoading = createSelector(
  [selectEnterpriseState],
  (state) => state.get?.loading
);

// Error states
export const enterpriseListError = createSelector(
  [selectEnterpriseState],
  (state) => state.list?.error
);

export const enterpriseGetError = createSelector(
  [selectEnterpriseState],
  (state) => state.get?.error
);

// Pagination info
export const enterprisePagination = createSelector(
  [selectEnterpriseState],
  (state) => state.paginationMeta
);