import { FieldArray, Form, Formik } from 'formik';
import React from 'react';
import { IoInformationCircle } from 'react-icons/io5';
import { useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { getKey } from 'utils/helpers/Helpers';
import * as yup from 'yup';
import { getYear } from '../BusinessValuationConfig';

interface Props {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  data: any;
  onSaveToDraftClick: (data: any) => void;
}
const PersonalExpensesPandL: React.FC<Props> = (props) => {
  const { nextStep, setData, backStep, data, onSaveToDraftClick } = props;
  const isLoading = useSelector(getAssessmentToolLoading);

  const validationSchema = yup.object().shape({
    [`entertainment_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`entertainment_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`entertainment_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`memberships_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`memberships_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`memberships_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`travel_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`travel_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`travel_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`charitable_contribution_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`charitable_contribution_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`charitable_contribution_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    other: yup.array().of(
      yup.object({
        [`other_${getYear(1)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`other_${getYear(2)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`other_${getYear(3)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
      })
    ),
  });

  const handleSubmit = (values: any) => {
    setData(values);
    nextStep();
  };

  return (
    <div className='flex flex-col '>
      <Formik
        initialValues={
          data?.personal_expenses || {
            [`entertainment_${getYear(1)}`]: '',
            [`entertainment_${getYear(2)}`]: '',
            [`entertainment_${getYear(3)}`]: '',
            [`memberships_${getYear(1)}`]: '',
            [`memberships_${getYear(2)}`]: '',
            [`memberships_${getYear(3)}`]: '',
            [`travel_${getYear(1)}`]: '',
            [`travel_${getYear(2)}`]: '',
            [`travel_${getYear(3)}`]: '',
            [`charitable_contribution_${getYear(1)}`]: '',
            [`charitable_contribution_${getYear(2)}`]: '',
            [`charitable_contribution_${getYear(3)}`]: '',
            other: [
              {
                [`other_${getYear(1)}`]: '',
                [`other_${getYear(2)}`]: '',
                [`other_${getYear(3)}`]: '',
              },
            ],
          }
        }
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values }) => (
          <Form className='pl-5 py-3 relative flex flex-col gap-4 bg-white'>
            <h2 className='text-xl font-bold  underline '>
              Personal Expenses :
            </h2>
            <div className='relative z-10 mb-4 text-xl items-center font-semibold '>
              <span className='absolute left-[26%]'>{getYear(1)}</span>
              <span className='absolute left-[52%]'>{getYear(2)}</span>
              <span className='absolute left-[78%]'>{getYear(3)}</span>
            </div>
            <div className='h-[calc(100vh-22.8rem)] mt-2 overflow-auto scrollbar pr-4'>
              <div className='grid grid-cols-4 gap-x-16 gap-y-1 text-[1.1rem]'>
                <div className='flex  items-center gap-2'>
                  <IoInformationCircle
                    id='Entertainment'
                    className='text-blue-01'
                  />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='#Entertainment'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      Excessive entertainment expenses.
                    </Tooltip>
                    <span>Entertainment</span>
                  </div>
                </div>
                <FormikInput
                  className='!h-10'
                  name={`entertainment_${getYear(1)}`}
                  key={`entertainment_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`entertainment_${getYear(2)}`}
                  key={`entertainment_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`entertainment_${getYear(3)}`}
                  key={`entertainment_${getYear(3)}`}
                />
                <div className='flex  items-center gap-2'>
                  <IoInformationCircle
                    id='Memberships'
                    className='text-blue-01'
                  />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='#Memberships'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      Non-business memberships - including club memberships.
                    </Tooltip>
                    <span>Memberships</span>
                  </div>
                </div>

                <FormikInput
                  className='!h-10'
                  name={`memberships_${getYear(1)}`}
                  key={`memberships_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`memberships_${getYear(2)}`}
                  key={`memberships_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`memberships_${getYear(3)}`}
                  key={`memberships_${getYear(3)}`}
                />
                <div className='flex  items-center gap-2'>
                  <IoInformationCircle id='Travel' className='text-blue-01' />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='#Travel'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      Travel expense beyond reasonable business travel.
                    </Tooltip>
                    <span>Travel</span>
                  </div>
                </div>
                <FormikInput
                  className='!h-10'
                  name={`travel_${getYear(1)}`}
                  key={`travel_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`travel_${getYear(2)}`}
                  key={`travel_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`travel_${getYear(3)}`}
                  key={`travel_${getYear(3)}`}
                />
                <div className='flex  items-center gap-2'>
                  <IoInformationCircle
                    id='Charitable'
                    className='text-blue-01'
                  />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='#Charitable'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      All charitable contributions.
                    </Tooltip>
                    <span>Charitable Contribution</span>
                  </div>
                </div>

                <FormikInput
                  className='!h-10'
                  name={`charitable_contribution_${getYear(1)}`}
                  key={`charitable_contribution_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`charitable_contribution_${getYear(2)}`}
                  key={`charitable_contribution_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`charitable_contribution_${getYear(3)}`}
                  key={`charitable_contribution_${getYear(3)}`}
                />
              </div>

              <FieldArray name='other'>
                {({ push }) => (
                  <div className=''>
                    {values.other &&
                      values.other.length > 0 &&
                      values.other.map((other: any, index: number) => (
                        <div
                          key={getKey(index)}
                          className='grid grid-cols-4 gap-x-16 gap-y-1  text-[1.1rem]'
                        >
                          <div
                            id={`other${index}`}
                            className='flex  items-center gap-2'
                          >
                            <IoInformationCircle className='text-blue-01' />
                            <div className='relative'>
                              <Tooltip
                                anchorSelect={`#other${index}`}
                                place='right'
                                offset={0}
                                className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !-top-1 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                              >
                                Other non-business expenses for owner(s) and
                                family members.
                              </Tooltip>
                              <span>Other</span>
                            </div>
                          </div>
                          <FormikInput
                            className='!h-10'
                            name={`other.${index}.other_${getYear(1)}`}
                            key={`other.${getKey(index)}.other_${getYear(1)}`}
                          />
                          <FormikInput
                            className='!h-10'
                            name={`other.${index}.other_${getYear(2)}`}
                            key={`other.${getKey(index)}.other_${getYear(2)}`}
                          />
                          <FormikInput
                            className='!h-10'
                            name={`other.${index}.other_${getYear(3)}`}
                            key={`other.${getKey(index)}.other_${getYear(3)}`}
                          />
                        </div>
                      ))}
                    <div className='absolute top-[3rem] right-[3rem]'>
                      <Button
                        type='button'
                        className='px-6 py-2 '
                        onClick={() =>
                          push({
                            [`other_${getYear(1)}`]: '',
                            [`other_${getYear(2)}`]: '',
                            [`other_${getYear(3)}`]: '',
                          })
                        }
                      >
                        Add
                      </Button>
                    </div>
                  </div>
                )}
              </FieldArray>
            </div>

            <div className='pr-7'>
              <BackNextComponent
                backStep={backStep}
                buttonType='submit'
                isLoading={isLoading}
                isNextDisable={false}
                onSaveToDraftClick={() => {
                  onSaveToDraftClick(values);
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default PersonalExpensesPandL;
