import { AdvisorInviteDTO } from 'dtos/invite-advisor.dto';
import { InviteStatus } from 'types/enum';
import { BaseApiService, baseApiService } from './BaseApiService';

class AdvisorInviteService {
  static getInstance(): AdvisorInviteService {
    return new AdvisorInviteService();
  }

  public async inviteSecondaryAdvisor(data: AdvisorInviteDTO) {
    return baseApiService.post(`/secondary-advisor/invite`, data);
  }

  public async getAdvisorInvites(id: number, advisor_id: number) {
    return baseApiService.get(`/${BaseApiService.loggedInUserType}/business-owner/${id}/${advisor_id}/sa/invites`);
  }

  public async cancelOrRevokeAdvisorInvite(data: {
    id: number;
    status: InviteStatus;
  }) {
    return baseApiService.put(`secondary-advisor/invite/${data.id}/update`, {
      status: data.status,
    });
  }
}

export const advisorInviteService = AdvisorInviteService.getInstance();
