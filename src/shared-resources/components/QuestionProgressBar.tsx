import React from 'react';
import classNames from 'classnames';
import { indexOf } from 'lodash';

interface ButtonData {
  isActive: boolean;
}

interface Props {
  buttonData: ButtonData[];
}

const QuestionProgressBar: React.FC<Props> = (props) => {
  const { buttonData } = props;

  return (
    <div className='flex gap-10 px-8'>
      {buttonData.map((a) => (
        <div
          key={indexOf(buttonData, a)}
          className={classNames(
            'h-4 w-full rounded-full',
            { 'bg-blue-02': !a.isActive },
            { 'bg-blue-01': a.isActive }
          )}
        />
      ))}
    </div>
  );
};
export default QuestionProgressBar;
