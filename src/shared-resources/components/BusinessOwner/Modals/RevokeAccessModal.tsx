import React from 'react';
import { IoWarning } from 'react-icons/io5';
import Button from 'shared-resources/components/Button/Button';

interface RevokeAccessModalProps {
  handleModalVisibility: (value: boolean) => void;
  isSecondaryAdvisor: boolean;
  handleToggle: () => void;
}

const RevokeAccessModal: React.FC<RevokeAccessModalProps> = ({
  handleModalVisibility,
  isSecondaryAdvisor,
  handleToggle,
}) => {
  const handleConfirm = () => {
    handleToggle();
    handleModalVisibility(false);
  };

  return (
    <div className='flex flex-col p-8'>
      <div className='flex items-start'>
        <IoWarning className='min-w-7 min-h-7' color='red' />
        <div className='flex flex-col ml-4'>
          <span className='font-medium'>
            Are you sure you want to {isSecondaryAdvisor ? 'revoke' : 'cancel'}{' '}
            the invite?
          </span>
        </div>
      </div>
      <div className='flex justify-end gap-6 mt-4'>
        <Button className='px-6 py-2' type='button' onClick={handleConfirm}>
          Yes
        </Button>
        <Button
          className='px-6 py-2'
          theme='secondary'
          type='button'
          onClick={() => handleModalVisibility(false)}
        >
          No
        </Button>
      </div>
    </div>
  );
};

export default React.memo(RevokeAccessModal);
