import { FC, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { PREV_LINK, localStorageService } from 'services/LocalStorageService';

interface RouteWrapperProps {
  children: any;
}

const RouteWrapper: FC<RouteWrapperProps> = ({ children }) => {
  const location = useLocation();

  useEffect(() => {
    if (!location.pathname.includes('login')) {
      localStorageService.setLocalStorageValue(
        PREV_LINK,
        `${location.pathname}${location.search}`
      );
    }
  }, [location]);

  return children;
};

export default RouteWrapper;
