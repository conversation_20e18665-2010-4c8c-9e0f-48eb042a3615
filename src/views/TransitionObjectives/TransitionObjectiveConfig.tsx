import { ObjectiveKeys } from 'shared-resources/types/TransitionObjectives.type';
import { QuestionType } from './Questions';

export const ObjectivesForStakeHoldertable: ObjectiveKeys[] = [
  'Spouse, Significant Other',
  'Key Employee(s)',
  'Child 1',
  'Child 2',
  'Child 3',
  'Lender(s)',
  'Other',
  'Others',
];

export const ObjectivesForAffectTable: ObjectiveKeys[] = [
  'Family',
  'Employees',
  'Community',
  'Charity',
  'Religious Affiliations',
  'Legacy',
];

export const ObjectivesForTimeFrameTable: ObjectiveKeys[] = [
  'Smoother Operations',
  'Business Growth',
  'Profitability Growth',
  'Value Growth',
  'Work Less',
];
export function getObjectives(questionObj: QuestionType) {
  switch (questionObj?.tableType) {
    case 'stakeHolders':
      return ObjectivesForStakeHoldertable;
    case 'affect':
      return ObjectivesForAffectTable;
    default:
      return ObjectivesForTimeFrameTable;
  }
}
