import { Form, Formik } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import * as yup from 'yup';
import { BusinessContinuityScreens } from 'types/enum';
import Checkbox from 'shared-resources/components/CheckBox/Checkbox';
import BackNextComponent from 'shared-resources/components/BackNextComponent';

interface DataPrivacyProps {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  onSaveToDraftClick: (data: any) => void;
  data: any;
}
const DataPrivacy: React.FC<DataPrivacyProps> = (props) => {
  const { nextStep, backStep, setData, onSaveToDraftClick, data } = props;

  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup.object().shape({
    [BusinessContinuityScreens.SHOULD_PRIVATE]: yup.object({
      is_private: yup.boolean().required(''),
    }),
  });

  const initialValues = {
    [BusinessContinuityScreens.SHOULD_PRIVATE]:
      data?.[BusinessContinuityScreens.SHOULD_PRIVATE] || {},
  };

  return (
    <div className='flex flex-col rounded-lg h-full'>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          setData(values?.[BusinessContinuityScreens.SHOULD_PRIVATE]);
          nextStep();
        }}
        enableReinitialize
        validateOnMount
      >
        {({ values, setFieldValue, isValid }) => (
          <Form className='relative p-5 h-[calc(100vh-15rem)] w-full flex flex-col items-center gap-4 bg-white'>
            <div className='flex flex-col gap-4 justify-start mt-28 w-full h-full  max-w-[70%]'>
              <h2 className='font-medium text-lg '>
                Do you want to keep this data private and that is it will not be
                accessible to the advisor then ?{' '}
              </h2>
              <div className='flex justify-between w-[40%]'>
                <Checkbox
                  className={`!h-4 !mt-0 ${
                    values?.[BusinessContinuityScreens.SHOULD_PRIVATE]
                      .is_private
                      ? 'pointer-events-none'
                      : ''
                  } `}
                  value={
                    values?.[BusinessContinuityScreens.SHOULD_PRIVATE]
                      ?.is_private
                  }
                  text={<span className='font-medium'>Yes</span>}
                  onChange={() => {
                    setFieldValue(
                      `${[
                        BusinessContinuityScreens.SHOULD_PRIVATE,
                      ]}.is_private`,
                      true
                    );
                  }}
                />
                <Checkbox
                  className={`!h-4 !mt-0 ${
                    values?.[BusinessContinuityScreens.SHOULD_PRIVATE]
                      .is_private === false
                      ? 'pointer-events-none'
                      : ''
                  } `}
                  value={
                    values?.[BusinessContinuityScreens.SHOULD_PRIVATE]
                      ?.is_private === false
                  }
                  text={<span className='font-medium'>No</span>}
                  onChange={() => {
                    setFieldValue(
                      `${[
                        BusinessContinuityScreens.SHOULD_PRIVATE,
                      ]}.is_private`,
                      false
                    );
                  }}
                />
              </div>
            </div>
            <div className='w-full'>
              <BackNextComponent
                backStep={() => {
                  backStep();
                  setData(values?.[BusinessContinuityScreens.SHOULD_PRIVATE]);
                }}
                fieldKey={BusinessContinuityScreens.SHOULD_PRIVATE}
                formValues={values}
                isLoading={isLoading}
                isNextDisable={!isValid}
                onSaveToDraftClick={onSaveToDraftClick}
                buttonText='Submit'
                buttonType='submit'
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default DataPrivacy;
