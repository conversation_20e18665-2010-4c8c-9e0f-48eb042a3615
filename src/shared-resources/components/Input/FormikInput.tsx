import { useField } from 'formik';
import React from 'react';
import { InputProps } from 'shared-resources/types/Input.type';
import Input from './Input';

interface FormikInputProps extends Omit<InputProps, 'onChange' | 'value'> {
  name: string;
  valueChanged?: (val: any) => void;
}

const FormikInput: React.FC<FormikInputProps> = (props) => {
  const { name, valueChanged } = props;
  const [, meta, helpers] = useField(name);

  const { value, error, touched } = meta;
  const { setValue, setTouched } = helpers;

  return (
    <Input
      value={value}
      onChange={(e) => {
        setValue(e.target.value);
        valueChanged?.(e);
      }}
      onBlur={() => {
        setTouched(true);
      }}
      error={touched && error}
      {...props}
    />
  );
};

export default React.memo(FormikInput);
