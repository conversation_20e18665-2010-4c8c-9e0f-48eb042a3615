import { ManualFollowUpDTO } from 'dtos/follow-up.dto';
import { FollowUpHistory, FollowUpSchedule } from 'models/entities/FollowUp';
import { AssessmentTools } from 'types/enum';
import { baseApiService } from './BaseApiService';

class FollowUpService {
  static getInstance(): FollowUpService {
    return new FollowUpService();
  }

  public async fetchList(
    businessOwnerId: number,
    assessmentTool: AssessmentTools
  ): Promise<{
    follow_up_record: FollowUpHistory[];
    follow_up_schedule: FollowUpSchedule;
  }> {
    return baseApiService.get(
      `/follow-up/${businessOwnerId}/${assessmentTool}/history`
    );
  }

  public async createFollowUp(followUp: ManualFollowUpDTO): Promise<{
    message: string;
    follow_up_text: string;
  }> {
    return baseApiService.post(
      `/follow-up/business-owner/${followUp.businessOwnertId}/send`,
      followUp
    );
  }

  public async cancelFollowUp(
    businessOwnerId: number,
    assessmentTool: AssessmentTools
  ): Promise<{ follow_up_schedule: FollowUpSchedule; message: string }> {
    return baseApiService.post(
      `/follow-up/${businessOwnerId}/${assessmentTool}/cancel`
    );
  }
}

export const followUpService = FollowUpService.getInstance();
