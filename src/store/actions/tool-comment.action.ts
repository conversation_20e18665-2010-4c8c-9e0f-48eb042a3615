import { AssessmentTools } from 'types/enum';
import { ToolCommentActionType } from './actions.constants';

export const fetchComments = (
  businessOwnerId: number,
  tool: AssessmentTools,
  search?: string
) => ({
  type: ToolCommentActionType.FETCH_COMMENTS,
  payload: { businessOwnerId, tool, search },
});

export const createComment = (
  businessOwnerId: number,
  tool: AssessmentTools,
  comment: string,
  metadata?: any
) => ({
  type: ToolCommentActionType.CREATE_COMMENT,
  payload: { businessOwnerId, tool, comment, metadata },
});

export const updateComment = (commentId: number, comment: string) => ({
  type: ToolCommentActionType.UPDATE_COMMENT,
  payload: { commentId, comment },
});

export const deleteComment = (commentId: number) => ({
  type: ToolCommentActionType.DELETE_COMMENT,
  payload: { commentId },
});
