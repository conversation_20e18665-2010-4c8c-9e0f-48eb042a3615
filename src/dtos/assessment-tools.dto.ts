import {
  AssessmentResponseType,
  AssessmentTools,
  AssessmentToolStatus,
  NonAssessmentToolTabs,
} from 'types/enum';

export interface AssessmentToolDTO {
  tool: AssessmentTools;
  status: AssessmentToolStatus;
  send_email?: boolean;
}

export interface FetchOwnAssessmentDTO {
  tool: AssessmentTools | NonAssessmentToolTabs;
}

export interface UpdateOwnAssessmentDTO {
  tool: AssessmentTools;
  assessment_response: any;
  submit_type: AssessmentResponseType;
  onSuccess?: () => void;
}

export interface FetchBusinessOwnerAssessmentDTO extends FetchOwnAssessmentDTO {
  businessOwnerId: number;
}

export interface UpdateBusinessOwnerAssessmentDTO
  extends UpdateOwnAssessmentDTO {
  businessOwnerId: number;
  onSuccess?: () => void;
}
export interface FetchPublicAssessmentByTokenDTO extends FetchOwnAssessmentDTO {
  token: string;
}

export interface UpdatePublicAssessmentByTokenDTO
  extends UpdateOwnAssessmentDTO {
  token: string;
}

export interface AssessmentReEvaluatePayload {
  ownerId: number;
  tool: AssessmentTools | NonAssessmentToolTabs;
  status: boolean;
}
export interface PresignedUrlPayload {
  content_type: string;
  file_name: string;
  image_category: AssessmentTools;
  index: number;
  image_caption?: string;
}

export interface UploadDocumentPayload {
  file: File;
  payload: PresignedUrlPayload;
  ownerId?: number;
  onSuccess?: (value: string) => void;
}
