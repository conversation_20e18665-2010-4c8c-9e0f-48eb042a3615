import BusinessOwnersList from "shared-resources/components/BusinessOwner/BusinessOwnersList";
import { RouteKey, UserRouteType } from "types/enum";
import { SidebarRoutesConfigType } from "types/routes/routes.type";
import ProfilePage from "views/Profile/ProfilePage";

export const adminDashboardRoutes: Array<SidebarRoutesConfigType> = [
  {
    name: 'Business Owner List',
    key: `/${UserRouteType.ADMIN}/${RouteKey.BO_LIST}`,
    component: BusinessOwnersList,
    props: {
    showSecondaryAdvisor: false,
    },
  },
  {
      name: 'Admin Profile',
      key: `/${UserRouteType.ADMIN}/${RouteKey.PROFILE}`,
      component: ProfilePage,
    },
  ];