import { UserRouteType } from 'types/enum';
import { SidebarRoutesConfigType } from 'types/routes/routes.type';
import MfaVerification from 'views/MfaVerification';
import ForgotPassword from 'views/ForgotPassword';
import ResetPassword from 'views/ResetPassword';
import SignIn from 'views/SignIn';
import MFASetupPage from 'views/MFASetupPage';

export const authRoutes: Array<SidebarRoutesConfigType> = [
  {
    name: 'Advisor Login',
    key: `/${UserRouteType.ADVISOR}/login`,
    component: SignIn,
  },
  {
    name: 'Business Owner Validate',
    key: `/${UserRouteType.BUSINESS_OWNER}/validate-code`,
    component: MfaVerification,
  },
  {
    name: 'Advisor Validate',
    key: `/${UserRouteType.ADVISOR}/validate-code`,
    component: MfaVerification,
  },
  {
    name: 'Advisor Reset Password',
    key: `/${UserRouteType.ADVISOR}/reset-password`,
    component: ResetPassword,
  },
  {
    name: 'Advisor Forgot Password',
    key: `/${UserRouteType.ADVISOR}/forgot-password`,
    component: ForgotPassword,
  },
  {
    name: 'Business Owner Login',
    key: `/${UserRouteType.BUSINESS_OWNER}/login`,
    component: SignIn,
  },
  {
    name: 'Business Owner Reset Password',
    key: `/${UserRouteType.BUSINESS_OWNER}/reset-password`,
    component: ResetPassword,
  },
  {
    name: 'Business Owner Forgot Password',
    key: `/${UserRouteType.BUSINESS_OWNER}/forgot-password`,
    component: ForgotPassword,
  },
  {
    name: 'Admin Login',
    key: `/${UserRouteType.ADMIN}/login`,
    component: SignIn,
  },
  {
    name: 'Admin Validate',
    key: `/${UserRouteType.ADMIN}/validate-code`,
    component: MfaVerification,
  },
  {
    name: 'Advisor MFA Setup',
    key: `/${UserRouteType.ADVISOR}/mfa-setup`,
    component: MFASetupPage,
  },
  {
    name: 'Business Owner MFA Setup',
    key: `/${UserRouteType.BUSINESS_OWNER}/mfa-setup`,
    component: MFASetupPage,
  },
  {
    name: 'Admin MFA Setup',
    key: `/${UserRouteType.ADMIN}/mfa-setup`,
    component: MFASetupPage,
  },
  {
    name: 'Enterprise Admin Login',
    key: `/${UserRouteType.ENT_ADMIN}/login`,
    component: SignIn,
  },
  {
    name: 'Enterprise Admin Validate',
    key: `/${UserRouteType.ENT_ADMIN}/validate-code`,
    component: MfaVerification,
  },
  {
    name: 'Enterprise Admin MFA Setup',
    key: `/${UserRouteType.ENT_ADMIN}/mfa-setup`,
    component: MFASetupPage,
  },
  {
    name: 'Enterprise Admin Forgot Password',
    key: `/${UserRouteType.ENT_ADMIN}/forgot-password`,
    component: ForgotPassword,
  },
  {
    name: 'Enterprise Admin Reset Password',
    key: `/${UserRouteType.ENT_ADMIN}/reset-password`,
    component: ResetPassword,
  },
];
