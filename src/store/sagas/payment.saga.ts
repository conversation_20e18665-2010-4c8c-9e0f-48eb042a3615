import { toast } from 'react-toastify';
import { all, call, put, takeLatest } from 'redux-saga/effects';
import { paymentService } from 'services/api-services/PaymentService';
import { PaymentActionType } from 'store/actions/actions.constants';
import { GetPaymentLinkActionPayloadType } from 'store/actions/payment.action';
import { AddAdvisorSubscriptionData } from 'store/reducers/user.reducer';
import { SagaPayloadType } from '../../types/SagaPayload.type';
import { setPaymentLoading } from '../reducers/assessment-tool.reducer';

interface GetPaymentLinkSagaPayloadType extends SagaPayloadType {
  payload: GetPaymentLinkActionPayloadType;
}

function* getPaymentLink(action: GetPaymentLinkSagaPayloadType): any {
  try {
    const { userType, userId } = action.payload;
    yield put(setPaymentLoading({ loading: true }));
    const response: { payment_link: string } = yield call(
      paymentService.businessOwnerPaymentLink,
      userType,
      userId
    );
    yield put(setPaymentLoading({ loading: false }));
    window.open(response.payment_link);
  } catch (e: any) {
    yield put(setPaymentLoading({ loading: false }));
    toast.error(e.response.data.message);
  }
}

function* getAdvisorPaymentLink(action: any): any {
  try {
    const type = action.payload;
    yield put(setPaymentLoading({ loading: true }));
    const response: { payment_link: string } = yield call(
      paymentService.advisorPaymentLink,
      type
    );
    yield put(setPaymentLoading({ loading: false }));
    window.open(response.payment_link);
  } catch (e: any) {
    yield put(setPaymentLoading({ loading: false }));
  }
}

function* cancelAdvisorSubscription(): any {
  try {
    yield put(setPaymentLoading({ loading: true }));
    const response = yield call(paymentService.cancelAdvisorSubscription);
    yield put(
      AddAdvisorSubscriptionData({
        data: {
          ...response?.subscription,
        },
      })
    );
    yield put(setPaymentLoading({ loading: false }));
  } catch (e: any) {
    yield put(setPaymentLoading({ loading: false }));
  }
}

function* retryAdvisorPayment(): any {
  try {
    yield put(setPaymentLoading({ loading: true }));
    const response = yield call(paymentService.retryAdvisorPayment);
    yield put(setPaymentLoading({ loading: false }));
    window.open(response.payment_link);
  } catch (e: any) {
    yield put(setPaymentLoading({ loading: false }));
  }
}

function* sendPaymentReminderSaga(action: any): any {
  try {
    yield call(paymentService.sendPaymentReminder, action.payload);
    console.log('Payment reminder sent to advisor successfully');
  } catch (e: any) {
    // Using console.error for AWS logging
    console.error('Failed to send payment reminder:', e.response?.data?.message || e.message);
  }
}

export function* paymentSagaWatcher() {
  yield all([takeLatest(PaymentActionType.GET_PAYMENT_LINK, getPaymentLink)]);
  yield all([
    takeLatest(
      PaymentActionType.GET_ADVISOR_PAYMENT_LINK,
      getAdvisorPaymentLink
    ),
  ]);
  yield all([
    takeLatest(
      PaymentActionType.CANCEL_ADVISOR_SUBSCRIPTION,
      cancelAdvisorSubscription
    ),
  ]);
  yield all([
    takeLatest(PaymentActionType.RETRY_ADVISOR_PAYMENT, retryAdvisorPayment),
  ]);
  yield all([
    takeLatest(PaymentActionType.SEND_PAYMENT_REMINDER, sendPaymentReminderSaga),
  ]);
}
