import React from 'react';
import { IoWarning } from 'react-icons/io5';
import Button from 'shared-resources/components/Button/Button';

interface DeleteBOModalProps {
  onCancelClick: () => void;
  onDeleteClick: () => void;
  loading: boolean | undefined;
}
const DeleteBOModal: React.FC<DeleteBOModalProps> = ({
  onCancelClick,
  onDeleteClick,
  loading,
}) => (
  <div className='flex flex-col p-8'>
    <div className='flex items-start'>
      <IoWarning className='min-w-7 min-h-7' color='red' />
      <div className='flex flex-col ml-4'>
        <span className='font-medium'>
          Please confirm that you want to delete this Business Owner.
          <br />
          Once deleted, that data is lost forever and can&apos;t be restored.
        </span>
      </div>
    </div>
    <div className='flex justify-end gap-6 mt-4'>
      <Button
        className='px-6 py-2'
        type='button'
        onClick={() => onDeleteClick()}
        isSubmitting={loading}
      >
        Yes
      </Button>
      <Button
        className='px-6 py-2'
        theme='secondary'
        type='button'
        onClick={() => onCancelClick()}
      >
        No
      </Button>
    </div>
  </div>
);

export default DeleteBOModal;
