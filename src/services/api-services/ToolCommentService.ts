import { ToolCommentResponse } from 'types/tool-comment.type';
import { AssessmentTools } from 'types/enum';
import { baseApiService } from './BaseApiService';

class ToolCommentService {
  static getInstance(): ToolCommentService {
    return new ToolCommentService();
  }

  public async fetchComments(
    businessOwnerId: number,
    tool: AssessmentTools,
    search?: string
  ): Promise<{ comments: ToolCommentResponse[] }> {
    const params = new URLSearchParams({ tool });
    if (search) {
      params.append('search', search);
    }
    return baseApiService.get(
      `/business-owner/${businessOwnerId}/assessment-tool/comment?${params.toString()}`
    );
  }

  public async createComment(
    businessOwnerId: number,
    tool: AssessmentTools,
    comment: string,
    metadata?: any
  ): Promise<{ comment: ToolCommentResponse; message: string }> {
    return baseApiService.post(
      `/business-owner/${businessOwnerId}/assessment-tool/comment`,
      { tool, comment, metadata }
    );
  }

  public async updateComment(
    commentId: number,
    comment: string
  ): Promise<{ comment: ToolCommentResponse; message: string }> {
    return baseApiService.put(`/comment/${commentId}`, { comment });
  }

  public async deleteComment(commentId: number): Promise<{ message: string }> {
    return baseApiService.delete(`/comment/${commentId}`);
  }
}

export const toolCommentService = ToolCommentService.getInstance();
