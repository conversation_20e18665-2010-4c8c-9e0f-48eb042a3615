import React from 'react';
import { User } from 'models/entities/User';
import { NavigateFunction } from 'react-router';
import {
  AssessmentResponseType,
  FinancialGapAnalysisScreens,
  RouteKey,
  UserRouteType,
  UserType,
} from 'types/enum';
import BusinessSaleCosts from './BusinessSaleCosts';
import GetStartedPage from './GetStartedPage';
import Investments from './Investments';
import PostExitFinances from './PostExitFinances';

export type FormattedToolResponse = {
  heading: string;
  rows: {
    heading: string;
    value: any;
  }[];
};

// screen name against its screen number
export const numberToScreenObject: {
  [key: number]: FinancialGapAnalysisScreens;
} = {
  1: FinancialGapAnalysisScreens.INVESTMENTS,
  2: FinancialGapAnalysisScreens.POST_EXIT_FINANCES,
  3: FinancialGapAnalysisScreens.BUSINESS_SALE_COSTS,
};

// screen number against its screen name
export const screenToNumberObject: Record<string, number> = {
  [FinancialGapAnalysisScreens.INVESTMENTS]: 1,
  [FinancialGapAnalysisScreens.POST_EXIT_FINANCES]: 2,
  [FinancialGapAnalysisScreens.BUSINESS_SALE_COSTS]: 3,
};

export enum InvestmentsKeys {
  TODAY_IN_INVESTMENTS = 'today_in_investments',
  EXPECTED_INVESTMENT_GROWTH_RATE = 'expected_investment_growth_rate',
  CURRENT_AGE = 'current_age',
  EXPECT_TO_EXIT = 'expect_to_exit',
  YEARS_TO_EXIT = 'years_to_exit',
  COMPOUNDED_INVESTMENTS = 'compounded_investments',
}

export enum PostExitFinancesKeys {
  ANNUAL_EXPENSES = 'annual_expenses',
  RECURRING_REVENUE_SOCIAL_SECURITY = 'recurring_revenue_social_security',
  RECURRING_REVENUE_OTHER = 'recurring_revenue_other',
  TOTAL_RECURRING_REVENUE = 'total_recurring_revenue',
  FEDERAL_TAX_ON_RECURRING_REVENUE = 'federal_tax_on_recurring_revenue',
  STATE_TAX_ON_RECURRING_REVENUE = 'state_tax_on_recurring_revenue',
  NET_ANUAL_RECURRING_REVENUE = 'net_anual_recurring_revenue',
  NET_ADDITIONAL_ANUAL_AMOUNT = 'net_additional_anual_amount',
  YEARS_TO_FUND_AGE_99 = 'years_to_fund_age_99',
  TOTAL_ADDITIONAL_AMOUNT = 'total_additional_amount',
  LESS_AVAILABLE_INVESTMENTS = 'less_available_investments',
  NET_AMOUNT_NEEDED_TO_FUND = 'net_amount_needed_to_fund',
}

export enum BusinessSaleCostskeys {
  FEDERAL_TAX_RATE = 'federal_tax_rate',
  STATE_TAX_RATE = 'state_tax_rate',
  TRANSACTION_EXPENSE_PERCENTAGE_COSTS = 'transaction_expense_percentage_costs',
  TRANSACTION_EXPENSE_DOLLARS = 'transaction_expense_dollars',
  GROSS_AMOUNT_NEEDED_FROM_BUSINESS_SALE = 'gross_amount_needed_from_business_sale',
  CURRENT_BUSINESS_VALUE = 'current_business_value',
  WEALTH_GAP = 'wealth_gap',
}

export const getKeyTitle = (key: string) => {
  switch (key) {
    case InvestmentsKeys.TODAY_IN_INVESTMENTS:
      return 'How much will you have today in investments that will be available for funding your post-exit life?';
    case InvestmentsKeys.EXPECTED_INVESTMENT_GROWTH_RATE:
      return 'Expected Investment Growth Rate (%)';
    case InvestmentsKeys.CURRENT_AGE:
      return 'What is Your Current Age?';
    case InvestmentsKeys.EXPECT_TO_EXIT:
      return 'At what age do you expect to exit your business?';
    case InvestmentsKeys.YEARS_TO_EXIT:
      return 'Years to exit (time for investments to grow)';
    case InvestmentsKeys.COMPOUNDED_INVESTMENTS:
      return 'Compounded Investments';
    case PostExitFinancesKeys.ANNUAL_EXPENSES:
      return 'What will your annual expenses be post-exit?';
    case PostExitFinancesKeys.RECURRING_REVENUE_SOCIAL_SECURITY:
      return 'Post-Exit Annual Recurring Revenue-Social Security';
    case PostExitFinancesKeys.RECURRING_REVENUE_OTHER:
      return 'Post-Exit Annual Recurring Revenue-Other';
    case PostExitFinancesKeys.TOTAL_RECURRING_REVENUE:
      return 'Total Post-Exit Annual Recurring Revenue';
    case PostExitFinancesKeys.FEDERAL_TAX_ON_RECURRING_REVENUE:
      return 'Federal tax on Annual Recurring Revenue (tax rate)';
    case PostExitFinancesKeys.STATE_TAX_ON_RECURRING_REVENUE:
      return 'State tax on Annual Recurring Revenue (tax rate)';
    case PostExitFinancesKeys.NET_ANUAL_RECURRING_REVENUE:
      return 'Net Annual Recurring Revenue (after tax)';
    case PostExitFinancesKeys.NET_ADDITIONAL_ANUAL_AMOUNT:
      return 'Net additional annual amount needed to fund post-exit life.';
    case PostExitFinancesKeys.YEARS_TO_FUND_AGE_99:
      return 'Years to fund to age 99';
    case PostExitFinancesKeys.TOTAL_ADDITIONAL_AMOUNT:
      return 'Total additional amount needed to fund post-exit life.';
    case PostExitFinancesKeys.NET_AMOUNT_NEEDED_TO_FUND:
      return 'Net amount needed to fund post-exit life.';
    case PostExitFinancesKeys.LESS_AVAILABLE_INVESTMENTS:
      return 'Less available investments';
    case BusinessSaleCostskeys.FEDERAL_TAX_RATE:
      return 'Federal Tax Rate (%)';
    case BusinessSaleCostskeys.STATE_TAX_RATE:
      return 'State Tax Rate (%)';
    case BusinessSaleCostskeys.TRANSACTION_EXPENSE_PERCENTAGE_COSTS:
      return 'Transaction Expense-Percentage Costs (like M&A)';
    case BusinessSaleCostskeys.TRANSACTION_EXPENSE_DOLLARS:
      return 'Transaction Expenses- Dollars (like Legal costs)';
    case BusinessSaleCostskeys.GROSS_AMOUNT_NEEDED_FROM_BUSINESS_SALE:
      return 'Gross amount needed from business sale (pre-tax and transaction costs)';
    case BusinessSaleCostskeys.CURRENT_BUSINESS_VALUE:
      return 'Current Business Value';
    case BusinessSaleCostskeys.WEALTH_GAP:
      return 'Wealth Gap (amount business needs to grow in value)';
    default:
      return '';
  }
};

export const getToolProgressPercentage = (response: { [key: string]: any }) => {
  if (!response || Object.keys(response).length === 0) {
    return 0; // Return 0 if data is null, undefined, or an empty object
  }
  const totalKeys =
    Object.keys(InvestmentsKeys).length +
    Object.keys(PostExitFinancesKeys).length +
    Object.keys(BusinessSaleCostskeys).length;
  let filledKeys = 0;
  Object.keys(response).forEach((key) => {
    if (key !== 'saved_screen' && key !== 'expense_calculator_response') {
      Object.keys(response[key] ?? {}).forEach((subKey) => {
        if (
          response[key][subKey] !== null &&
          response[key][subKey] !== undefined &&
          (Object.values(InvestmentsKeys).includes(subKey as InvestmentsKeys) ||
            Object.values(PostExitFinancesKeys).includes(
              subKey as PostExitFinancesKeys
            ) ||
            Object.values(BusinessSaleCostskeys).includes(
              subKey as BusinessSaleCostskeys
            ))
        ) {
          filledKeys += 1;
        }
      });
    }
  });
  return (filledKeys / totalKeys) * 100;
};

export const getCompundedInvestments = (
  years: number,
  investments: number,
  expectedReturn: number
) => {
  let totalCompoundedInvestments = investments;
  for (let i = 0; i < years; i += 1) {
    const totalReturn = (expectedReturn / 100) * totalCompoundedInvestments;
    totalCompoundedInvestments += totalReturn;
  }
  return totalCompoundedInvestments;
};

export const getScreen = (
  currentScreen: {
    screen: FinancialGapAnalysisScreens | undefined;
  },
  loggedInUser: User,
  financialGapData: any,
  setFinancialGapData: any,
  backNextClickHandler: (nextScreen: FinancialGapAnalysisScreens) => void,
  handleSubmit: (
    response: any,
    submitType: AssessmentResponseType,
    onSuccess?: () => void
  ) => void,
  navigate: NavigateFunction
) => {
  if (currentScreen?.screen === FinancialGapAnalysisScreens.GET_STARTED) {
    return (
      <GetStartedPage
        nextStep={() => {
          backNextClickHandler(FinancialGapAnalysisScreens.INVESTMENTS);
        }}
      />
    );
  }
  if (currentScreen?.screen === FinancialGapAnalysisScreens.INVESTMENTS) {
    return (
      <Investments
        data={financialGapData}
        setData={(data: any) => {
          setFinancialGapData({
            ...financialGapData,
            saved_screen: screenToNumberObject.post_exit_finances,
            [FinancialGapAnalysisScreens.INVESTMENTS]: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(FinancialGapAnalysisScreens.POST_EXIT_FINANCES);
        }}
        backStep={() => {
          if (loggedInUser?.type === UserType.ADVISOR) {
            navigate(`/${UserRouteType.ADVISOR}/${RouteKey.DASHBOARD}`);
          } else {
            navigate(`/${UserRouteType.BUSINESS_OWNER}/${RouteKey.DASHBOARD}`);
          }
        }}
        onSaveToDraftClick={(data: any) => {
          const currentScreen = financialGapData?.saved_screen || 0;
          let response = financialGapData;
          if (currentScreen < screenToNumberObject.investments) {
            response = {
              ...financialGapData,
              [FinancialGapAnalysisScreens.INVESTMENTS]: data,
              saved_screen:
                screenToNumberObject[FinancialGapAnalysisScreens.INVESTMENTS],
            };
          } else {
            response = {
              ...financialGapData,
              [FinancialGapAnalysisScreens.INVESTMENTS]: data,
            };
          }
          handleSubmit(response, AssessmentResponseType.DRAFT);
        }}
      />
    );
  }
  if (
    currentScreen?.screen === FinancialGapAnalysisScreens.POST_EXIT_FINANCES
  ) {
    return (
      <PostExitFinances
        data={financialGapData}
        setData={(data: any) => {
          setFinancialGapData({
            ...financialGapData,
            [FinancialGapAnalysisScreens.POST_EXIT_FINANCES]: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(FinancialGapAnalysisScreens.BUSINESS_SALE_COSTS);
        }}
        backStep={() => {
          backNextClickHandler(FinancialGapAnalysisScreens.INVESTMENTS);
        }}
        onSaveToDraftClick={(data: any, callback?: () => void) => {
          const currentScreen = financialGapData?.saved_screen || 0;
          let response = financialGapData;
          if (currentScreen < screenToNumberObject.post_exit_finances) {
            response = {
              ...financialGapData,
              [FinancialGapAnalysisScreens.POST_EXIT_FINANCES]: data,
              saved_screen:
                screenToNumberObject[
                  FinancialGapAnalysisScreens.POST_EXIT_FINANCES
                ],
            };
          } else {
            response = {
              ...financialGapData,
              [FinancialGapAnalysisScreens.POST_EXIT_FINANCES]: data,
            };
            if (callback) {
              response.saved_screen =
                screenToNumberObject[
                  FinancialGapAnalysisScreens.POST_EXIT_FINANCES
                ];
            }
          }
          handleSubmit(response, AssessmentResponseType.DRAFT, callback);
        }}
      />
    );
  }
  return (
    <BusinessSaleCosts
      data={financialGapData}
      nextStep={(data: any) => {
        const response = {
          ...financialGapData,
          [FinancialGapAnalysisScreens.BUSINESS_SALE_COSTS]: data,
        };
        if (loggedInUser.type === UserType.ADVISOR) {
          handleSubmit(response, AssessmentResponseType.COMPLETE);
        } else {
          handleSubmit(response, AssessmentResponseType.COMPLETE, () =>
            navigate(`${UserType.BUSINESS_OWNER}/${RouteKey.DASHBOARD}`)
          );
        }
      }}
      backStep={() => {
        backNextClickHandler(FinancialGapAnalysisScreens.POST_EXIT_FINANCES);
      }}
      onSaveToDraftClick={(data: any) => {
        const currentScreen = financialGapData?.saved_screen || 0;
        let response = financialGapData;
        if (
          currentScreen <
          screenToNumberObject[FinancialGapAnalysisScreens.BUSINESS_SALE_COSTS]
        ) {
          response = {
            ...financialGapData,
            [FinancialGapAnalysisScreens.BUSINESS_SALE_COSTS]: data,
            saved_screen:
              screenToNumberObject[
                FinancialGapAnalysisScreens.BUSINESS_SALE_COSTS
              ],
          };
        } else {
          response = {
            ...financialGapData,
            [FinancialGapAnalysisScreens.BUSINESS_SALE_COSTS]: data,
          };
        }
        handleSubmit(response, AssessmentResponseType.DRAFT);
      }}
    />
  );
};

export const formatFinancialToolResponseData = (response: {
  [key: string]: any;
}): FormattedToolResponse[][] => {
  let questionCounter = 1;

  const formatEntries = (entries: [string, any][]) =>
    entries.map(([key, value]) => {
      const title = `${questionCounter}. ${getKeyTitle(key)}`;
      questionCounter += 1;

      const priceQuestionKeys = [
        InvestmentsKeys.TODAY_IN_INVESTMENTS,
        InvestmentsKeys.COMPOUNDED_INVESTMENTS,
        PostExitFinancesKeys.ANNUAL_EXPENSES,
        PostExitFinancesKeys.RECURRING_REVENUE_SOCIAL_SECURITY,
        PostExitFinancesKeys.RECURRING_REVENUE_OTHER,
        PostExitFinancesKeys.TOTAL_RECURRING_REVENUE,
        PostExitFinancesKeys.NET_ANUAL_RECURRING_REVENUE,
        PostExitFinancesKeys.NET_ADDITIONAL_ANUAL_AMOUNT,
        PostExitFinancesKeys.TOTAL_ADDITIONAL_AMOUNT,
        PostExitFinancesKeys.LESS_AVAILABLE_INVESTMENTS,
        PostExitFinancesKeys.NET_AMOUNT_NEEDED_TO_FUND,
        BusinessSaleCostskeys.TRANSACTION_EXPENSE_DOLLARS,
        BusinessSaleCostskeys.GROSS_AMOUNT_NEEDED_FROM_BUSINESS_SALE,
        BusinessSaleCostskeys.CURRENT_BUSINESS_VALUE,
      ];
      const percentageQuestionKeys = [
        InvestmentsKeys.EXPECTED_INVESTMENT_GROWTH_RATE,
        PostExitFinancesKeys.FEDERAL_TAX_ON_RECURRING_REVENUE,
        PostExitFinancesKeys.STATE_TAX_ON_RECURRING_REVENUE,
        BusinessSaleCostskeys.FEDERAL_TAX_RATE,
        BusinessSaleCostskeys.STATE_TAX_RATE,
        BusinessSaleCostskeys.TRANSACTION_EXPENSE_PERCENTAGE_COSTS,
      ];

      let formattedValue: any;

      if (percentageQuestionKeys.includes(key as any)) {
        formattedValue = `${value}%`;
      } else if (priceQuestionKeys.includes(key as any)) {
        formattedValue = `$ ${value ?? '0'}`;
      } else {
        formattedValue = value;
      }

      if (key === BusinessSaleCostskeys.WEALTH_GAP) {
        formattedValue = (
          <span className='text-2xl text-blue-01 font-bold underline'>
            {`$ ${formattedValue ?? '0'}`}
          </span>
        );
      }

      return {
        heading: title,
        value: formattedValue,
      };
    });

  return [
    [
      {
        heading: 'Investments',
        rows: formatEntries(Object.entries(response.investments)),
      },
      {
        heading: 'Post Exit Finances',
        rows: formatEntries(Object.entries(response.post_exit_finances)),
      },
    ],
    [
      {
        heading: 'Business Sale Costs',
        rows: formatEntries(Object.entries(response.business_sale_costs)),
      },
    ],
  ];
};

export const financialGapAnalysisIntroText =
  'The Wealth Gap Analysis is used to determine any existing financial gap between what your post transition life will cost and how much you have (including business value).';
