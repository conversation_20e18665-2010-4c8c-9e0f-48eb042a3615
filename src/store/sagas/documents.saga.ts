import { toast } from 'react-toastify';
import { all, call, put, select, takeLatest } from 'redux-saga/effects';
import { reportService } from 'services/api-services/ReportBuilderService';
import {
  DocumentsActionType,
  ValuationAmountActionType,
} from 'store/actions/actions.constants';
import {
  deleteDocumentAction,
  setDocuments,
  setDocumentsListLoading,
  setDocumentsLoading,
  setValuationAmount,
  setValuationAmountError,
  setValuationAmountLoading,
} from 'store/reducers/documents.reducer';
import { documentsDataSelector } from 'store/selectors/document.selector';
import { getUserData } from 'store/selectors/user.selector';
import { UserType } from 'types/enum';

function* uploadDocumentsSaga(action: any): any {
  const { id, onSuccess, file, type }: any = action.payload;
  const documentsData = yield select(documentsDataSelector);

  try {
    yield put(setDocumentsLoading({ loading: true }));

    const documents = [];

    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < file.length; i++) {
      const presignedUrl = yield call(
        reportService.uploadFile,
        file[i],
        {
          fileName: file[i].name,
          contentType: file[i].type,
          type,
        },
        id
      );

      const saveReportResponse = yield call(
        reportService.saveReport,
        id,
        file[i].name,
        presignedUrl?.url,
        type
      );

      documents.push(saveReportResponse?.report);
      if (i === file.length - 1) {
        onSuccess?.();
        toast.success('Documents Uploaded Successfully');
      }
    }
    yield put(setDocuments([...documentsData, ...documents]));

    yield put(setDocumentsLoading({ loading: false }));
  } catch (e: any) {
    toast.error(e?.response?.data?.message);
    yield put(setDocumentsLoading({ loading: false }));
  }
}

function* fetchDocumentsSaga(action: any): any {
  const { id, type }: any = action.payload;
  const userData = yield select(getUserData);

  try {
    yield put(setDocumentsListLoading({ loading: true }));
    let response;

    if (userData?.type === UserType.BUSINESS_OWNER) {
      response = yield call(reportService.listReportsToBO, type);
    } else {
      response = yield call(reportService.listReportsToAdvisor, id, type);
    }

    yield put(setDocuments(response?.reports));
    yield put(setDocumentsListLoading({ loading: false }));
  } catch (e: any) {
    toast.error(e?.response?.data?.message);
    yield put(setDocumentsListLoading({ loading: false }));
  }
}

function* deleteDocumentSaga(action: any): any {
  const { id, onSuccess, reportId }: any = action.payload;

  try {
    yield put(setDocumentsLoading({ loading: true }));
    const response = yield call(reportService.deleteDocument, id, reportId);
    if (response) {
      yield put(deleteDocumentAction(reportId));
      onSuccess();
      yield put(setDocumentsLoading({ loading: false }));
      toast.success('Document Deleted Successfully');
    }
  } catch (e: any) {
    toast.error(e?.response?.data?.message);
    yield put(setDocumentsLoading({ loading: false }));
  }
}

function* getValuationAmountSaga(action: any): any {
  const { businessOwnerId } = action.payload;
  try {
    yield put(setValuationAmountLoading(true));
    const userData = yield select(getUserData);
    if (userData?.type === UserType.BUSINESS_OWNER) {
      const response = yield call(reportService.getValuationAmountForBO);
      yield put(setValuationAmount(response.valuation_amount));
    } else {
      const response = yield call(
        reportService.getValuationAmountForAdvisor,
        businessOwnerId
      );
      yield put(setValuationAmount(response.valuation_amount));
    }
  } catch (error: any) {
    yield put(setValuationAmountError(error.message));
  } finally {
    yield put(setValuationAmountLoading(false));
  }
}

function* updateValuationAmountSaga(action: any) {
  try {
    const { businessOwnerId, valuationAmount, onSuccess } = action.payload;
    yield put(setValuationAmountLoading(true));
    yield call(reportService.updateValuationAmount, businessOwnerId, {
      valuation_amount: valuationAmount,
    });
    yield put(setValuationAmount(valuationAmount));
    toast.success('Valuation amount updated successfully');
    if (onSuccess) {
      onSuccess();
    }
  } catch (error: any) {
    yield put(setValuationAmountError(error.message));
  } finally {
    yield put(setValuationAmountLoading(false));
  }
}

export function* documentsSagaWatcher() {
  yield all([
    takeLatest(DocumentsActionType.UPLOAD_DOCUMENT, uploadDocumentsSaga),
    takeLatest(DocumentsActionType.FETCH_DOCUMENTS, fetchDocumentsSaga),
    takeLatest(DocumentsActionType.DELETE_DOCUMENT, deleteDocumentSaga),
    takeLatest(
      ValuationAmountActionType.GET_VALUATION_AMOUNT,
      getValuationAmountSaga
    ),
    takeLatest(
      ValuationAmountActionType.UPDATE_VALUATION_AMOUNT,
      updateValuationAmountSaga
    ),
  ]);
}
