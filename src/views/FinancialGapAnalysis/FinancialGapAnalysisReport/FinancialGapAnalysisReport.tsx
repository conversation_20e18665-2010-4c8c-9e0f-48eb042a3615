import React, { useEffect, useState } from 'react';
import { FaDownload } from 'react-icons/fa6';
import { IoArrowBackSharp } from 'react-icons/io5';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import NavigateContainer from 'shared-resources/components/NavigateContainer';
import {
  fetchAssessmentReport,
  fetchAssessmentReportByBusinessOwner,
} from 'store/actions/assessment-report.action';
import {
  getAssessmentReportLoading,
  getAssessmentReportResponse,
} from 'store/selectors/assessment-report.selector';
import { getUserData } from 'store/selectors/user.selector';
import { AssessmentTools, RouteKey, UserRouteType, UserType } from 'types/enum';

import DarkThemeReportWrapper from 'HOC/DarkThemeReportWrapper';
import LightThemeReportWrapper from 'HOC/LightThemeReportWrapper';
import _ from 'lodash';
import ReportGenerationModal from 'shared-resources/components/BusinessOwner/Modals/ReportGenerationModal';
import Button from 'shared-resources/components/Button/Button';
import Modal from 'shared-resources/components/Modal/Modal';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import CoverPage from 'views/ReadinessAssessment/ReadinessAssessmentReport/CoverPage';
import { resetAssessmentReportData } from 'store/reducers/assessment-report.reducer';
import { generatePdf } from '../../../utils/generate-pdf/generatePdf-utils';
import {
  ExpensesReportResponse,
  paginateData,
} from '../ExpenseCalculator/ExpenseCalculatorConfig';
import {
  financialGapAnalysisIntroText,
  formatFinancialToolResponseData,
  FormattedToolResponse,
} from '../FinancialGapAnalysisConfig';
import ExpenseTableComponent from './ExpenseTableComponent';
import ToolTableComponent from './ToolTableComponent';
import DisclaimerPage from '../../Common/Disclaimer';

const FinancialGapAnalysisReport = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const response: any = useSelector(getAssessmentReportResponse);
  const loading = useSelector(getAssessmentReportLoading);
  const loggedInUserData = useSelector(getUserData);

  const [assessmentToolResponse, setAssessmentToolResponse] = useState<
    FormattedToolResponse[][]
  >([]);

  const [expenseResponse, setExpenseResponse] = useState<
    ExpensesReportResponse[]
  >([]);

  useEffect(() => {
    if (id && loggedInUserData?.type === UserType.ADVISOR) {
      dispatch(
        fetchAssessmentReport({
          id,
          tool: AssessmentTools.FINANCIAL_GAP_ANALYSIS,
          onError: () =>
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`),
        })
      );
    }
    if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchAssessmentReportByBusinessOwner({
          tool: AssessmentTools.FINANCIAL_GAP_ANALYSIS,
          onError: () =>
            navigate(
              `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.FINANCIAL_GAP_ANALYSIS_DASHBOARD}`
            ),
        })
      );
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  // cleanup function to reset the assessment report data
  useEffect(
    () => () => {
      dispatch(resetAssessmentReportData());
    },
    []
  );

  useEffect(() => {
    if (!_.isEmpty(response.assessment_response)) {
      setAssessmentToolResponse(
        formatFinancialToolResponseData(response.assessment_response)
      );
      if (
        !_.isEmpty(response.assessment_response.expense_calculator_response)
      ) {
        setExpenseResponse(
          paginateData(response.assessment_response.expense_calculator_response)
        );
      }
    }
  }, [response]);

  const [isDownloading, setIsDownloading] = useState<boolean>(false);

  const downloadReport = async () => {
    setIsDownloading(true);
    const reportPages: any[] = [
      document.getElementById(`cover-page`),
      document.getElementById(`introduction-page`),
    ];
    assessmentToolResponse?.forEach((expense, index) =>
      reportPages.push(document.getElementById(`tool-page-${index}`))
    );

    expenseResponse?.forEach((expense, index) =>
      reportPages.push(document.getElementById(`expense-page-${index}`))
    );
    reportPages.push(document.getElementById(`disclaimer-page`));
    const reportName = `FinancialGapAnalysisReport.pdf`;
    await generatePdf(
      reportPages,
      reportName,
      () => setIsDownloading(false),
      'portrait'
    );
  };

  const pageBreak = <div className='my-9' />;

  const wealthGap = Number(
    response?.assessment_response?.business_sale_costs?.wealth_gap.replaceAll(
      ',',
      ''
    )
  );

  const formatWealthGap = (gap: number) => {
    const absoluteValue = Math.abs(gap).toString();
    const formattedNumber = absoluteValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return gap < 0 ? `-${formattedNumber}` : formattedNumber;
  };

  if (loading) {
    return <Spinner customClassName='' spinnerTheme='overlaySpinner' />;
  }

  const getPagesKey = (prefix: string, i: number) => `${prefix}-${i}`;

  return (
    <div className='w-full h-[calc(100vh-9.6875rem)]'>
      <div className='flex items-center justify-between w-full mb-5'>
        <NavigateContainer
          isEnableToolRoute
          className='min-w-7.5 text-black-02'
        >
          <IoArrowBackSharp size={24} />
        </NavigateContainer>
        <div>
          <Button
            className='py-2 px-6'
            LeadingIcon={FaDownload}
            leadingIconClassName='mr-2'
            onClick={downloadReport}
          >
            Download
          </Button>
        </div>
      </div>
      <div className='flex flex-col items-center h-[calc(100%-2.75rem)] overflow-y-auto pr-2 scrollbar '>
        <div className='m-auto'>
          <DarkThemeReportWrapper id='cover-page'>
            <CoverPage
              pageTitle='Wealth Gap Analysis'
              presentedBy={response?.advisor_name}
              preparedFor={response?.business_owner_data?.business_owner_name}
              reportDate={response?.assessment_completion_date}
              phone={response?.business_owner_data?.business_owner_phone}
              email={response?.business_owner_data?.business_owner_email}
            />
          </DarkThemeReportWrapper>
        </div>
        {pageBreak}
        <div className='m-auto'>
          <LightThemeReportWrapper id='introduction-page'>
            <div className='flex flex-col gap-4 p-32'>
              <h1 className='text-xl font-semibold text-blue-01'>
                Congratulations{' '}
                {response?.business_owner_data?.business_owner_name}
              </h1>
              <p>{financialGapAnalysisIntroText}</p>
              {wealthGap < 0 && (
                <div className='flex flex-col gap-4'>
                  <p>
                    Your Wealth Gap is{' '}
                    <span className='text-lg font-medium'>
                      ${formatWealthGap(wealthGap)}
                    </span>{' '}
                    , meaning that you already have sufficient resources to
                    support the post-transition lifestyle you have described.
                    Your disciplined financial management has landed you in an
                    elite group where you can transition at any time without
                    needing to build additional business value.
                  </p>
                  <p>
                    However, it is important to consider that the business
                    climate can change at any time and affect the value of your
                    business. Accordingly, you should remain vigilant in
                    protecting/improving the value of your business up to the
                    time you transition from the business and harvest the
                    related financial rewards.
                  </p>
                </div>
              )}
              {wealthGap > 0 && (
                <div className='flex flex-col gap-4'>
                  <p>
                    Your Wealth Gap is{' '}
                    <span className='text-lg font-medium'>
                      ${formatWealthGap(wealthGap)}
                    </span>{' '}
                    , meaning that you will need to increase the total value of
                    your assets by this amount in order to live the
                    post-transition lifestyle you have described. You have
                    several options:
                  </p>

                  <ul className='list-disc pl-6 space-y-2'>
                    <li>
                      Amend your post-transition lifestyle plans – reduce the
                      cost of your future intended lifestyle
                    </li>
                    <li>
                      Extend the date of your intended transition, giving more
                      time for your assets to grow to a sufficient level to
                      support your intended post-transition lifestyle.
                    </li>
                    <li>
                      Increase the value of your assets
                      <ul className='list-disc pl-6 mt-2 space-y-1'>
                        <li>Growth of non-business assets/investments</li>
                        <li>Growth of business value</li>
                      </ul>
                    </li>
                  </ul>
                  <p>
                    For many Business Owners, the growth of the business is the
                    most logical approach to closing the Wealth Gap. Much of the
                    work done in this Plan highlights action items you will need
                    to address to improve business value. Talk with your Plan
                    Advisor about available services to assist you with building
                    your business value.
                  </p>
                  <p>
                    For growth of your non-business assets work closely with
                    your Financial Advisor.
                  </p>
                </div>
              )}
            </div>
          </LightThemeReportWrapper>
        </div>
        {pageBreak}
        <div className='m-auto'>
          {assessmentToolResponse.map((toolResponse, index: number) => (
            <div key={getPagesKey('tool-page', index)}>
              <LightThemeReportWrapper id={getPagesKey('tool-page', index)}>
                <div className='pt-16 w-4/5 mx-auto'>
                  <ToolTableComponent response={toolResponse} />
                </div>
              </LightThemeReportWrapper>
              {pageBreak}
            </div>
          ))}
        </div>
        <div className='m-auto'>
          {expenseResponse.map((res, index: number) => (
            <div key={getPagesKey('expense-page', index)}>
              <LightThemeReportWrapper id={getPagesKey('expense-page', index)}>
                <div className='pt-16 w-9/10 mx-auto'>
                  <ExpenseTableComponent response={res} />
                </div>
              </LightThemeReportWrapper>
              {pageBreak}
            </div>
          ))}
        </div>
        <div className='m-auto'>
          <LightThemeReportWrapper id='disclaimer-page'>
            <DisclaimerPage />
          </LightThemeReportWrapper>
        </div>
      </div>
      <Modal visible={isDownloading} handleVisibility={() => {}}>
        <ReportGenerationModal />
      </Modal>
    </div>
  );
};

export default FinancialGapAnalysisReport;
