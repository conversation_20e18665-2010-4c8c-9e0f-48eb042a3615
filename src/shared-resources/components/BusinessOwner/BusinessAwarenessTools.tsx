import { BusinessOwner } from 'models/entities/BusinessOwner';
import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import AwarenessToolsHeader from 'shared-resources/components/AwarenessToolsHeader';
import { fetchBusinessOwnerAssessment } from 'store/actions/assessment-tool.action';
import { updateAssessmentTool } from 'store/actions/business-owner.action';
import { AssessmentToolData, AwarenessTab } from 'types/business-owner.types';
import {
  AssessmentStage,
  AssessmentToolProgressStatus,
  AssessmentTools,
  AssessmentToolStatus,
  NonAssessmentToolTabs,
} from 'types/enum';
import {
  getReportType,
  paymentNeededTabs,
  planDevelopmentTabs,
} from 'utils/helpers/Helpers';
import { getUserData } from 'store/selectors/user.selector';
import { deleteDocument } from 'store/actions/document.action';
import { documentsLoading } from 'store/selectors/document.selector';
import Modal from '../Modal/Modal';
import Toggle from '../Toggle/Toggle';
import EnabledToolView from './EnabledToolView';
import EnableToolModal from './Modals/EnableToolModal';
import Button from '../Button/Button';
import DocumentsModal from './Modals/DocumentModal';
import DeleteDocumentModal from './Modals/Follow Up/DeleteDocumentModal';

interface BusinessAwarenessToolsProps {
  businessOwner: BusinessOwner;
}
const BusinessAwarenessTools: React.FC<BusinessAwarenessToolsProps> = (
  props
) => {
  const { businessOwner } = props;
  const [activeTab, setActiveTab] = useState<AwarenessTab | null>(null);
  const [modalVisible, setModalVisibility] = useState<boolean>(false);
  const [isEnablingTool, setIsEnablingTool] = useState(false);
  const [documentModalVisible, setDocumentModalVisible] =
    useState<boolean>(false);
  const [selectedDocument, setSelectedDocument] = useState<any>(null);
  const [showDeleteDocumentPopUp, setShowDeleteDocumentPopUp] =
    useState<boolean>(false);
  const userData = useSelector(getUserData);
  const isDocumentsLoading = useSelector(documentsLoading);
  const tabDataMap = Object.values(AssessmentTools).map((tool) => {
    const awarenessData = Object.keys(
      businessOwner?.assessment_data?.awareness ?? {}
    );
    const planDevelopmentData = Object.keys(
      businessOwner?.assessment_data?.plan_development ?? {}
    );

    const isDisabled =
      businessOwner?.primary_advisor_id !== userData?.id &&
      ![...awarenessData, ...planDevelopmentData].includes(tool);

    if (tool === AssessmentTools.FINANCIAL_GAP_ANALYSIS) {
      return {
        name: 'Wealth Gap Analysis',
        value: tool,
        disabled: isDisabled,
      };
    }
    if (tool === AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE) {
      return {
        name: 'Buyer Type-Deal Structure',
        value: tool,
        disabled: isDisabled,
      };
    }
    return {
      name: tool.replace(/_/g, ' '),
      value: tool,
      disabled: isDisabled,
    };
  });
  const [tabData, setTabData] = useState(tabDataMap);

  const handleModalVisibility = (value: boolean) => {
    setModalVisibility(value);
  };

  const handleDocumentModalVisibility = (value: boolean) => {
    setDocumentModalVisible(value);
  };

  const handleToggleChange = () => {
    setModalVisibility(true);
  };

  const dispatch = useDispatch();

  const progressStatus =
    businessOwner?.assessment_data?.awareness?.[activeTab?.value as string]
      ?.progress_status;

  const showDownloadAsCSVButton =
    activeTab?.value === AssessmentTools.BUSINESS_VALUATION &&
    progressStatus === AssessmentToolProgressStatus.COMPLETED;

  // calling this because we need data for business valuation to download as csv when it is completed
  useEffect(() => {
    if (showDownloadAsCSVButton) {
      dispatch(
        fetchBusinessOwnerAssessment({
          tool: AssessmentTools.BUSINESS_VALUATION,
          businessOwnerId: +businessOwner.id,
        })
      );
    }
  }, [activeTab]);

  const isPlanDevelopmentTab =
    activeTab && planDevelopmentTabs.includes(activeTab?.value);

  const currentTool =
    activeTab &&
    businessOwner?.assessment_data?.[
      isPlanDevelopmentTab
        ? AssessmentStage.PLAN_DEVELOPMENT
        : AssessmentStage.AWARENESS
    ]?.[activeTab?.value];

  const enabled = currentTool?.status === AssessmentToolStatus.ENABLE;

  const disableEnableText = `${
    enabled ? AssessmentToolStatus.DISABLE : AssessmentToolStatus.ENABLE
  } ${activeTab?.name}`;
  const primaryTabs = tabData.slice(0, 5);
  const planDevelopmentTabsData = tabData.slice(5);
  // const loadingList = useSelector(documentLoadinglistSelector);

  const handleDelete = (item: any) => {
    dispatch(
      deleteDocument({
        id: businessOwner?.id as any,
        type: getReportType(NonAssessmentToolTabs.BUSINESS_FINANCIAL_ANALYSIS),
        reportId: item.id,
        onSuccess: () => setShowDeleteDocumentPopUp(false),
      })
    );
  };

  const nonAssessmentPlanDevelopmentTabsData = [
    ...planDevelopmentTabsData.slice(0, 3),
    {
      name: NonAssessmentToolTabs.BUSINESS_FINANCIAL_ANALYSIS.replace(
        /_/g,
        ' '
      ) as AssessmentTools,
      value:
        NonAssessmentToolTabs.BUSINESS_FINANCIAL_ANALYSIS as string as AssessmentTools,
      disabled:
        businessOwner?.primary_advisor_id !== userData?.id &&
        !Object.keys(businessOwner?.assessment_data?.awareness ?? {}).includes(
          AssessmentTools.BUSINESS_VALUATION
        ),
    },
    ...planDevelopmentTabsData.slice(3),
  ];
  const showFirstEnabledTab = useCallback(() => {
    const allStagesData = {
      ...businessOwner?.assessment_data?.awareness,
      ...businessOwner?.assessment_data?.plan_development,
    };
    if (allStagesData) {
      const allToolsData = Object.entries(allStagesData);
      const orderedToolsData = Object.values(AssessmentTools).map((tool) => {
        const toolData = allToolsData?.find(([toolName]) => toolName === tool);
        if (toolData) {
          return {
            toolName: tool,
            toolData: toolData.at(1) as AssessmentToolData,
          };
        }
        return {};
      });
      const enabledTabValue = orderedToolsData?.find(
        (tool) => tool?.toolData?.status === AssessmentToolStatus.ENABLE
      )?.toolName;

      const assessmentTab = tabData.find(
        (tab) => tab.value === enabledTabValue
      );
      if (assessmentTab) {
        setActiveTab(assessmentTab);
      }
    }
  }, [businessOwner]);

  useEffect(() => {
    showFirstEnabledTab();
  }, []);

  useEffect(() => {
    const paymentIncompletedTabs = paymentNeededTabs.filter(
      (tab) => !businessOwner?.payment_details?.[tab]
    );
    const filteredTabsData = tabData.filter(
      (tab) => !paymentIncompletedTabs.includes(tab.value)
    );
    setTabData(filteredTabsData);
  }, [businessOwner]);

  const handleToggle = (value: boolean, send_email: boolean) => {
    const status = value
      ? AssessmentToolStatus.ENABLE
      : AssessmentToolStatus.DISABLE;

    if (activeTab) {
      setIsEnablingTool(true);
      dispatch(
        updateAssessmentTool({
          id: +businessOwner.id,
          assessmentTool: {
            status,
            tool: activeTab?.value,
            send_email,
          },
          onSuccess: (message: string) => {
            toast.success(message);
            handleModalVisibility(false);
            setIsEnablingTool(false);
          },
          onError: (error: string) => {
            toast.error(error);
            setIsEnablingTool(false);
          },
        })
      );
    }
  };

  return (
    <div className='flex flex-col w-full'>
      <h1 className='text-xl font-medium mb-4'>Tools</h1>
      <div className='flex flex-col gap-2'>
        <AwarenessToolsHeader
          tabData={primaryTabs}
          onHandleTabChange={(a) => setActiveTab(a)}
          activeTab={activeTab}
        />
        {!!planDevelopmentTabsData?.length && (
          <AwarenessToolsHeader
            tabData={nonAssessmentPlanDevelopmentTabsData}
            onHandleTabChange={(a) => setActiveTab(a)}
            activeTab={activeTab}
          />
        )}
      </div>
      {activeTab?.value &&
        activeTab.value !==
          (NonAssessmentToolTabs.BUSINESS_FINANCIAL_ANALYSIS as string) && (
          <>
            <div className='flex w-full mt-5 items-center justify-between '>
              <div className='flex gap-28 items-center'>
                <span className='font-medium capitalize'>
                  {disableEnableText}
                </span>
                <Toggle enabled={enabled} setEnabled={handleToggleChange} />
              </div>
            </div>
            {enabled && (
              <EnabledToolView
                tool={activeTab?.value}
                businessOwner={businessOwner}
                showDownload={showDownloadAsCSVButton}
              />
            )}
          </>
        )}

      {activeTab &&
        activeTab.value !==
          (NonAssessmentToolTabs.BUSINESS_FINANCIAL_ANALYSIS as string) && (
          <Modal
            visible={modalVisible}
            title={disableEnableText}
            handleVisibility={handleModalVisibility}
            classname='max-w-[40.625rem]'
          >
            <EnableToolModal
              enabled={enabled}
              handleModalVisibility={handleModalVisibility}
              tab={activeTab}
              isEnabling={isEnablingTool}
              handleToggle={handleToggle}
              pingOwner={activeTab.value !== AssessmentTools.BUSINESS_VALUATION}
            />
          </Modal>
        )}

      {activeTab?.value &&
        activeTab.value ===
          (NonAssessmentToolTabs.BUSINESS_FINANCIAL_ANALYSIS as string) && (
          <div className='flex flex-col items-center py-10'>
            <Button
              onClick={() => handleDocumentModalVisibility(true)}
              className='px-6 py-2'
              theme='tertiary'
              type='button'
            >
              Documents
            </Button>
            <Modal
              visible={documentModalVisible}
              title='Documents'
              handleVisibility={handleDocumentModalVisibility}
              classname='w-[66%]'
            >
              <DocumentsModal
                tool={NonAssessmentToolTabs.BUSINESS_FINANCIAL_ANALYSIS}
                onCancelClick={handleDocumentModalVisibility}
                setSelectedDocument={setSelectedDocument}
                setShowDeleteDocumentPopUp={setShowDeleteDocumentPopUp}
              />
            </Modal>
          </div>
        )}
      {showDeleteDocumentPopUp && (
        <Modal
          title='Delete Document'
          visible={showDeleteDocumentPopUp}
          handleVisibility={setShowDeleteDocumentPopUp}
        >
          <DeleteDocumentModal
            loading={isDocumentsLoading}
            onDeleteClick={() => handleDelete(selectedDocument)}
            onCancelClick={() => setShowDeleteDocumentPopUp(false)}
          />
        </Modal>
      )}
    </div>
  );
};

export default BusinessAwarenessTools;
