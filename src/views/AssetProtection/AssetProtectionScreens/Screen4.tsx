import React from 'react';
import { useSelector } from 'react-redux';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';

interface Props {
  handleNextClick: () => void;
  handlebackClick: () => void;
  handleSaveAsDraftClick: () => void;
}
const Screen4: React.FC<Props> = ({
  handleNextClick,
  handlebackClick,
  handleSaveAsDraftClick,
}) => {
  const isSubmitting = useSelector(getAssessmentToolLoading);
  return (
    // This Screen is for single owner
    <div className='flex flex-col h-full  gap-6 pl-10 py-10 pr-10 font-medium'>
      <div className='h-[calc(100vh-20rem)] flex justify-center items-center overflow-auto scrollbar'>
        <p className='text-xl text-blue-01'>
          Since you are a single owner of the business, you do not need to have
          a buy-sell agreement.
        </p>
      </div>
      <div className='pr-7'>
        <BackNextComponent
          backStep={() => handlebackClick()}
          buttonType='submit'
          nextStep={() => handleNextClick()}
          isLoading={isSubmitting}
          isNextDisable={false}
          onSaveToDraftClick={() => handleSaveAsDraftClick()}
        />
      </div>
    </div>
  );
};

export default Screen4;
