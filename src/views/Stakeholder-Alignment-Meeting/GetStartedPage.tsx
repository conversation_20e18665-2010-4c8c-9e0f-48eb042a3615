import React, { FC, memo } from 'react';
import Button from 'shared-resources/components/Button/Button';
import { definitions } from './Definitions';

type GetStartedPageProps = {
  onGetStartedClick: () => void;
};
type DefinitionTabProps = {
  heading: string;
  paragraph: string;
};
const DefinitionTab: FC<DefinitionTabProps> = ({ heading, paragraph }) => (
  <div className='flex'>
    <h1 className='text-base'>
      <strong className='underline'>{heading} :</strong> {paragraph}
    </h1>
  </div>
);

const GetStartedPage: FC<GetStartedPageProps> = ({ onGetStartedClick }) => (
  <div className='overflow-auto scrollbar flex flex-col items-center'>
    <div className='flex flex-col gap-10 items-start'>
      {definitions.map((definitionObj) => (
        <DefinitionTab
          key={Object.keys(definitionObj)[0]}
          heading={Object.keys(definitionObj)[0]}
          paragraph={Object.values(definitionObj)[0]}
        />
      ))}
    </div>
    <Button className='px-6 py-2 mt-10' onClick={onGetStartedClick}>
      Get Started
    </Button>
  </div>
);
export default memo(GetStartedPage);
