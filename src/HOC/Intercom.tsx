import React, { useEffect } from 'react';
import Intercom from '@intercom/messenger-js-sdk';
import { useSelector } from 'react-redux';
import ENV_CONFIG from '../constant/env.config';
import { getUserData } from '../store/selectors/user.selector';

interface Props {}

const IntercomHOC: React.FC<Props> = () => {
  const user = useSelector(getUserData);

  useEffect(() => {
    if (user?.id) {
      Intercom({
        app_id: ENV_CONFIG.INTERCOM_APP_ID,
        user_id: `${user.type}_${user.id}`,
        name: user.name,
        email: user.email,
        created_at: user.created_at_unix,
        user_hash: user.hash,
      });
    }
  }, [user?.id]);

  return <div />;
};

export default React.memo(IntercomHOC);
