import { Menu, Transition } from '@headlessui/react';
import React, { Fragment, useMemo } from 'react';
import { TbTriangleInvertedFilled } from 'react-icons/tb';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router';
import { localStorageService } from 'services/LocalStorageService';
import { getUserData } from 'store/selectors/user.selector';
import { UserRouteType, UserType } from 'types/enum';
import { getInitials, getUserRouteType } from 'utils/helpers/Helpers';
import { authLogoutAction } from '../../store/actions/auth.action';

const ProfileDropdown: React.FC<{}> = () => {
  const user = useSelector(getUserData);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const userType =
    user?.type === UserType.BUSINESS_OWNER
      ? UserRouteType.BUSINESS_OWNER
      : user?.type;
  const loggedInUserType = useMemo(
    () => localStorageService.getLoggedInUserType(),
    []
  );

  return (
    <div className='w-full relative'>
      <div className='absolute top-2 right-0 text-right'>
        <Menu as='div' className='relative text-left'>
          <Menu.Button className='flex w-full items-center justify-between rounded-md bg-black/20 font-medium text-black-01'>
            {user && (
              <div className='flex items-center w-12 h-12 justify-center border border-blue-01 rounded-full text-blue-01'>
                {getInitials(user)}
              </div>
            )}
            <span className='ml-[0.875rem] text-black-03 whitespace-nowrap leading-6'>
              {user?.name}
            </span>
            <TbTriangleInvertedFilled className='ml-[0.5rem]' size={12} />
          </Menu.Button>
          <Transition
            as={Fragment}
            enter='transition ease-out duration-100'
            enterFrom='transform opacity-0 scale-95'
            enterTo='transform opacity-100 scale-100'
            leave='transition ease-in duration-75'
            leaveFrom='transform opacity-100 scale-100'
            leaveTo='transform opacity-0 scale-95'
          >
            <Menu.Items className='absolute right-0 mt-2 w-[7.5rem] origin-top-right divide-y z-10 divide-gray-100 rounded-md bg-white shadow-lg'>
              <div className='px-1 py-1 '>
                <Menu.Item>
                  {({ active }) => (
                    <button
                      className={`${
                        active ? 'bg-blue-01 text-white' : 'text-black-01'
                      } group flex w-full items-center rounded-md px-2 py-2 text-sm`}
                      onClick={() =>
                        navigate(`/${userType ?? loggedInUserType}/profile`)
                      }
                    >
                      Your Profile
                    </button>
                  )}
                </Menu.Item>
                <Menu.Item>
                  {({ active }) => (
                    <button
                      className={`${
                        active ? 'bg-blue-01 text-white' : 'text-black-01'
                      } group flex w-full items-center rounded-md px-2 py-2 text-sm`}
                      onClick={() => {
                        dispatch(authLogoutAction());
                        navigate(
                          `/${getUserRouteType(loggedInUserType)}/login`
                        );
                      }}
                    >
                      Log Out
                    </button>
                  )}
                </Menu.Item>
              </div>
            </Menu.Items>
          </Transition>
        </Menu>
      </div>
    </div>
  );
};

export default ProfileDropdown;
