import { CSSProperties } from 'react';

export interface InputProps {
  id?: string;
  name?: string;
  value: any;
  onChange: (event: any) => void;
  onBlur?: (event: any) => void;
  placeholder?: string;
  label?: string;
  helperText?: string;
  type?: 'email' | 'password' | 'text' | 'date' | 'file' | 'number' | 'time';
  className?: string;
  leadingIcon?: any;
  trailingIcon?: any;
  trailingIcon2?: any;
  labelClassName?: string;
  style?: CSSProperties;
  disabled?: boolean;
  error?: any;
  showErrorText?: boolean;
  requiredLabel?: boolean;
  labelIcon?: string;
  labelIcon2?: any;
  containerClassName?: string;
  maxLength?: number;
  autoComplete?: string;
  onLabelIconClick?: () => void;
  passwordIconLeft?: boolean;
  fieldType?: 'textarea' | 'input';
  asterisk?: boolean;
}
