{"env": {"browser": true, "es2021": true}, "extends": ["airbnb", "airbnb-typescript", "prettier", "plugin:react/recommended", "plugin:react-redux/recommended", "plugin:react-hooks/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2020, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["react", "@typescript-eslint", "prettier", "import", "react-redux", "simple-import-sort"], "rules": {"prettier/prettier": "error", "no-use-before-define": "off", "@typescript-eslint/no-use-before-define": ["error"], "react/jsx-filename-extension": ["warn", {"extensions": [".tsx"]}], "import/no-cycle": "off", "import/extensions": ["error", "ignorePackages", {"ts": "never", "tsx": "never"}], "@typescript-eslint/explicit-function-return-type": ["off", {"allowExpressions": true}], "import/prefer-default-export": "off", "import/no-extraneous-dependencies": ["error", {"devDependencies": true}], "react/require-default-props": "off", "no-underscore-dangle": "off", "class-methods-use-this": "off", "react/function-component-definition": ["error", {"namedComponents": "arrow-function", "unnamedComponents": "arrow-function"}], "react/jsx-props-no-spreading": "off", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": ["error"], "no-shadow": "off", "react/button-has-type": "off", "default-param-last": "off", "func-names": ["error", "as-needed"], "no-param-reassign": ["error", {"props": false}], "@typescript-eslint/no-shadow": ["warn"], "jsx-a11y/click-events-have-key-events": ["warn"]}, "settings": {"import/resolver": {"typescript": {}}}}