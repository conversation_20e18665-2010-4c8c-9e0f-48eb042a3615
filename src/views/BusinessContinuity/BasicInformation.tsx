import { Form, Formik } from 'formik';
import { User } from 'models/entities/User';
import React from 'react';
import 'react-datepicker/dist/react-datepicker.css';
import { useSelector } from 'react-redux';
import 'react-tooltip/dist/react-tooltip.css';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { UserType } from 'types/enum';
import {
  isRequiredFieldDependingOnValidationSchema,
  valuesTobeUpdated,
} from 'utils/helpers/Helpers';
import * as yup from 'yup';

interface Props {
  nextStep: (values: any, shouldUpdate: boolean) => void;
  backStep: () => void;
  user?: User;
  businessOwnerProperties: any;
}
const BasicInformation: React.FC<Props> = (props) => {
  const { nextStep, backStep, user, businessOwnerProperties } = props;
  const userEmail =
    user?.type === UserType.BUSINESS_OWNER
      ? user?.email
      : businessOwnerProperties?.email;
  const isLoading = useSelector(getAssessmentToolLoading);

  const validationSchema = yup.object().shape({
    business_name: yup.string().required('Business name is required'),
    business_start_date: yup.string().nullable(),
    street_address: yup.string().nullable(),
    zip_code: yup
      .string()
      .matches(
        /^(\d{5}[-\s]?(?:\d{4})?|[A-Za-z]\d[A-Za-z][-\s]?\d[A-Za-z]\d)$/gm,
        'ZIP (Postal) Code not from US or Canadian region'
      )
      .required('Zip Code is required'),
    first_name: yup.string().required('Contact Person First Name is required'),
    last_name: yup.string().required('Contact Person Last Name is required'),
    phone: yup
      .string()
      .matches(
        /^(\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/,
        'Phone number not from US region'
      )
      .nullable(),
  });
  const initialValue = {
    business_name: businessOwnerProperties?.business_name,
    business_start_date: businessOwnerProperties?.business_start_date,
    street_address: businessOwnerProperties?.street_address,
    zip_code: businessOwnerProperties?.zip_code,
    first_name: businessOwnerProperties?.first_name,
    last_name: businessOwnerProperties?.last_name,
    email: userEmail,
    phone: businessOwnerProperties?.phone,
  };

  const handleSubmit = (values: any) => {
    const shouldUpdate = !!Object.keys(valuesTobeUpdated(initialValue, values))
      ?.length;

    nextStep(values, shouldUpdate);
  };

  return (
    <div className='flex flex-col rounded-lg  h-full'>
      <Formik
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        <Form className='h-full px-5 py-3 bg-white'>
          <h2 className='text-xl font-semibold'>Basic Information</h2>
          <div className=' h-[calc(100vh-20rem)] '>
            <div className='grid grid-cols-3 gap-y-10 gap-x-8 mt-5  overflow-auto scrollbar text-[1rem]'>
              <FormikInput
                name='first_name'
                key='first_name'
                label='Contact Person First Name'
                labelClassName='mb-1'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'first_name'
                )}
              />
              <FormikInput
                name='last_name'
                key='last_name'
                label='Contact Person Last Name'
                labelClassName='mb-1'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'last_name'
                )}
              />
              <FormikInput
                name='business_name'
                key='business_name'
                label='Business Name'
                labelClassName='mb-1'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'business_name'
                )}
              />

              <FormikInput
                name='street_address'
                key='street_address'
                label='Business Address'
                labelClassName='mb-1'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'street_address'
                )}
              />
              <FormikInput
                name='zip_code'
                key='zip_code'
                label='ZIP Code'
                labelClassName='mb-1'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'zip_code'
                )}
              />

              <FormikInput
                name='email'
                key='email'
                disabled
                label='Email Address'
                labelClassName='mb-1'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'email'
                )}
              />
              <FormikInput
                name='phone'
                key='phone'
                label='Phone Number'
                labelClassName='mb-1'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'phone'
                )}
              />
            </div>
          </div>

          <div className='flex justify-between'>
            <Button
              onClick={() => backStep()}
              className='rounded-xl px-7 py-2  font-medium'
              theme='secondary'
              type='button'
            >
              Back
            </Button>
            <Button
              isSubmitting={isLoading}
              disabled={isLoading}
              className='rounded-xl px-7 py-2  font-medium'
              type='submit'
            >
              Next
            </Button>
          </div>
        </Form>
      </Formik>
    </div>
  );
};

export default BasicInformation;
