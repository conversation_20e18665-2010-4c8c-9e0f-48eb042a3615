import React, { FC, memo, ReactElement } from 'react';
import cx from 'classnames';

type ProfileTabsProps = {
  selectedTab: string;
  setSelectedTab: (tab: string) => void;
  tabs: {
    title: string;
    icon: ReactElement;
  }[];
};

const ProfileTabs: FC<ProfileTabsProps> = ({
  tabs,
  selectedTab,
  setSelectedTab,
}) => (
  <div className='flex gap-10 border-b-2 border-gray-400 mt-6'>
    {tabs.map((tab) => (
      <div
        key={tab.title}
        role='button'
        tabIndex={0}
        className={cx(
          'flex items-center gap-2 cursor-pointer px-2 pb-2',
          tab.title === selectedTab && 'border-b-2 border-blue-01 -mb-0.5'
        )}
        onClick={() => {
          setSelectedTab(tab.title);
        }}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            setSelectedTab(tab.title);
          }
        }}
      >
        {tab.icon &&
          React.cloneElement(tab.icon, {
            className:
              tab.title === selectedTab ? 'text-blue-01' : 'text-gray-05',
          })}
        <div
          className={cx(
            'font-medium',
            tab.title === selectedTab ? 'text-blue-01' : 'text-gray-05'
          )}
        >
          {tab.title
            .toLowerCase()
            .replace(/_/g, ' ')
            .replace(/\b\w/g, (char) => char.toUpperCase())}
        </div>
      </div>
    ))}
  </div>
);

export default memo(ProfileTabs);
