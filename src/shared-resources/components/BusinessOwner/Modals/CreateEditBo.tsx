import { Formik, Form } from 'formik';
import React, { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import * as yup from 'yup';
import InputHelper from 'shared-resources/components/Inputhelper/InputHelper';
import { FormikSelect } from 'shared-resources/components/Select/FormikSelect';
import IndustrySearchableSelect from 'shared-resources/components/Select/IndustrySearchableSelect';
import { AgeRange, RevenueRange } from 'types/enum';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import {
  businessOwnerCreate,
  updateBusinessOwner,
} from 'store/actions/business-owner.action';
import {
  createBusinessOwnerErrors,
  getCreateBusinessOwnerLoading,
  updateBusinessOwnerLoading,
} from 'store/selectors/business-owner.selector';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import { BusinessOwnerDTO } from 'dtos/business-owner.dto';
import { allAdvisors } from 'store/selectors/advisor.selector';

interface CreateBusinessOwnerForm {
  first_name: string;
  last_name?: string;
  email: string;
  business_name: string;
  revenue?: string | RevenueRange;
  notes?: string;
  business_type?: string;
  age?: string | AgeRange;
  street_address?: string;
  zip_code?: string;
  additional_advisor_email?: string;
  advisor_id?: number;
}

interface CreateOrEditBusinessOwnerModalProps {
  handleModalVisibility: (value: boolean) => void;
  businessOwner?: BusinessOwner;
  onDeleteBOClick?: () => void;
  selectAdvisor?: boolean;
}

const CreateOrEditBusinessOwnerModal: React.FC<
  CreateOrEditBusinessOwnerModalProps
> = (props) => {
  const {
    handleModalVisibility,
    businessOwner,
    onDeleteBOClick,
    selectAdvisor,
  } = props;
  const dispatch = useDispatch();

  const createBOErrors = useSelector(createBusinessOwnerErrors);
  const ownerCreateLoading = useSelector(getCreateBusinessOwnerLoading);
  const ownerUpdateLoading = useSelector(updateBusinessOwnerLoading);

  // Validation schema
  const validationSchema = yup.object().shape({
    first_name: yup.string().required('First name is required'),
    last_name: yup.string().required('Last name is required'),
    business_name: yup.string().required('Business Name is required'),
    business_type: yup.string().required('Type of Business is required'),
    revenue: yup.string().required('Annual Revenue is required'),
    age: yup.string().required('Age of Business Owner is required'),
    email: yup
      .string()
      .required('Email is required')
      .email('This must be a valid e-mail'),
    additional_advisor_email: yup
      .string()
      .optional()
      .email('This must be a valid e-mail'),
    notes: yup.string(),
    zip_code: yup
      .string()
      .trim()
      .matches(
        /^(\d{5}[-\s]?(?:\d{4})?|[A-Za-z]\d[A-Za-z][-\s]?\d[A-Za-z]\d)$/gm,
        'ZIP (Postal) Code not from US or Canadian region'
      ),
    advisor_id: selectAdvisor ? yup.string().required('Advisor is required') : yup.string().optional(),
  });

  const formRef: any = useRef();
  const initialValue: CreateBusinessOwnerForm = {
    first_name: businessOwner?.first_name || '',
    last_name: businessOwner?.last_name || '',
    email: businessOwner?.email || '',
    business_name: businessOwner?.business_name || '',
    business_type: businessOwner?.business_type || '',
    revenue: businessOwner?.revenue || '',
    age: businessOwner?.age || '',
    notes: businessOwner?.notes || '',
    zip_code: businessOwner?.zip_code || '',
    additional_advisor_email: businessOwner?.additional_advisor_email || '',
    advisor_id: businessOwner?.primary_advisor_id || undefined,
  };

  const ageOptions = Object.values(AgeRange).map((age) => ({
    label: age,
    value: age,
  }));

  const revenueOptions = Object.values(RevenueRange).map((revenue) => ({
    label: revenue,
    value: revenue,
  }));

  const advisors = useSelector(allAdvisors);

  const advisorOptions = advisors.map((advisor) => ({
    label: `${advisor.email}`,
    value: String(advisor.id),
  }));

  // Handle form submission for create and edit
  const handleSubmit = (values: CreateBusinessOwnerForm) => {
    if (businessOwner) {
      dispatch(
        updateBusinessOwner({
          id: businessOwner.id.toString(),
          data: values as BusinessOwnerDTO,
          onSuccess: () => {
            handleModalVisibility(false);
          },
        })
      );
    } else {
      dispatch(
        businessOwnerCreate({
          businessOwnerCreatePayload: values,
          onSuccess: () => {
            handleModalVisibility(false);
          },
        })
      );
    }
  };

  // Handle validation errors from API
  useEffect(() => {
    if (createBOErrors) {
      createBOErrors.map((error: any) =>
        formRef.current.setFieldError(
          error.field,
          error.message.replace('_', ' ')
        )
      );
    }
  }, [createBOErrors]);

  return (
    <div className='flex flex-col py-9 px-10'>
      <Formik
        innerRef={formRef}
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values, setFieldValue, errors, touched }) => (
          <Form>
            <div className='grid grid-cols-2 gap-x-16 gap-y-1 max-w-[65.1875rem]'>
              <FormikInput
              asterisk
              name='first_name'
              key='first_name'
              label='First Name'
              labelClassName='font-medium leading-6.5'
              />
              <FormikInput
              asterisk
              name='last_name'
              key='last_name'
              label='Last Name'
              labelClassName='font-medium leading-6.5'
              />
              <FormikInput
              asterisk
              name='business_name'
              key='business_name'
              label='Business Name'
              labelClassName='font-medium leading-6.5'
              />
              <div className='flex flex-col'>
              <IndustrySearchableSelect
                asterisk
                key='business_type'
                placeHolder='Type to search industry'
                onChange={(value: string) => {
                setFieldValue('business_type', value);
                }}
                selectedIndustry={values.business_type}
                label='Type Of Business'
              />
              <div className='h-5'>
                {errors?.business_type && touched?.business_type && (
                <InputHelper type='error' text={errors?.business_type} />
                )}
              </div>
              </div>
              <FormikSelect
              asterisk
              name='revenue'
              label='Approximate Annual Revenue'
              options={revenueOptions}
              placeholder='Select Revenue'
              />
              <FormikSelect
              asterisk
              name='age'
              label='Age of Business Owner'
              options={ageOptions}
              placeholder='Select Age'
              />
              <FormikInput
              asterisk
              name='email'
              key='email'
              label='Email Address'
              labelClassName='font-medium leading-6.5'
              disabled={!!businessOwner}
              />
              <FormikInput
              name='additional_advisor_email'
              key='additional_advisor_email'
              label='Additional Advisor Email'
              labelClassName='font-medium leading-6.5'
              />
              <FormikInput
              name='zip_code'
              key='zip_code'
              label='ZIP (Postal) Code'
              labelClassName='font-medium leading-6.5'
              />
              {selectAdvisor && (
              <FormikSelect
                asterisk
                name='advisor_id'
                placeholder='Select Advisor'
                options={advisorOptions}
                classname='w-full'
                menuClassname='max-h-72 overflow-auto scrollbar border-r-0'
                outerClassName='!h-[3.125rem]'
                labelClassName='!text-lg'
                label='Advisor'
                errorClassname='mt-0'
                isSearch={true}
              />
              )}
            </div>
            <FormikInput
              name='notes'
              key='notes'
              fieldType='textarea'
              label='Notes (If Applicable)'
              className='h-28'
              labelClassName='font-medium leading-6.5'
            />
            <div className='flex justify-between mt-[5rem]'>
              {businessOwner && onDeleteBOClick ? (
                <Button
                  type='button'
                  className='px-6 py-2 bg-red-01 border-red-01'
                  onClick={onDeleteBOClick}
                >
                  Delete
                </Button>
              ) : (
                <div />
              )}
              <div className='flex gap-5'>
                <Button
                  disabled={
                    businessOwner ? ownerUpdateLoading : ownerCreateLoading
                  }
                  isSubmitting={
                    businessOwner ? ownerUpdateLoading : ownerCreateLoading
                  }
                  className='px-6 py-2 w-[7.55rem]'
                  type='submit'
                >
                  {businessOwner ? 'Update' : 'Create'}
                </Button>
                <Button
                  className='px-6 py-2'
                  theme='secondary'
                  type='button'
                  onClick={() => {
                    handleModalVisibility(false);
                  }}
                  disabled={
                    businessOwner ? ownerUpdateLoading : ownerCreateLoading
                  }
                >
                  Cancel
                </Button>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default CreateOrEditBusinessOwnerModal;
