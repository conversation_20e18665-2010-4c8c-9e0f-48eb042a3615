import { Form, Formik } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import * as yup from 'yup';
import { BusinessContinuityScreens } from 'types/enum';
import Checkbox from 'shared-resources/components/CheckBox/Checkbox';
import BackNextComponent from 'shared-resources/components/BackNextComponent';

interface BoardChairmanProps {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  onSaveToDraftClick: (data: any) => void;
  data: any;
}
const BoardChairman: React.FC<BoardChairmanProps> = (props) => {
  const { nextStep, backStep, setData, onSaveToDraftClick, data } = props;

  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup.object().shape({
    board_chairman: yup.object({
      is_chairman: yup.boolean().required(''),
      name_of_director: yup.string().when('is_chairman', {
        is: false,
        then: (schema) => schema.required('Name of Director is required'),
        otherwise: (schema) => schema.nullable(),
      }),
    }),
  });

  const initialValues = {
    board_chairman: data?.board_chairman || {
      is_chairman: null,
      name_of_director: '',
    },
  };

  return (
    <div className='flex flex-col rounded-lg h-full'>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          setData(values?.board_chairman);
          nextStep();
        }}
        enableReinitialize
        validateOnMount
      >
        {({ values, setFieldValue, isValid }) => (
          <Form className='relative p-5 h-full w-full flex flex-col items-center gap-4 bg-white'>
            <div className='flex flex-col gap-4 justify-start mt-28 w-full h-full  max-w-[70%]'>
              <h2 className='font-medium text-lg'>
                Are you the Chairman of the Board of Directors of the business?
              </h2>
              <div className='flex justify-between w-[40%]'>
                <Checkbox
                  value={values.board_chairman.is_chairman}
                  onChange={() => {
                    setFieldValue('board_chairman.is_chairman', true);
                  }}
                  text={<span className='font-medium'>Yes</span>}
                  className={`!h-4 !mt-0 ${
                    values.board_chairman.is_chairman
                      ? 'pointer-events-none'
                      : ''
                  } `}
                />
                <Checkbox
                  value={values.board_chairman.is_chairman === false}
                  onChange={() => {
                    setFieldValue('board_chairman.is_chairman', false);
                    setFieldValue('board_chairman.name_of_director', '');
                  }}
                  text={<span className='font-medium'>No</span>}
                  className={`!h-4 !mt-0 ${
                    values.board_chairman.is_chairman === false
                      ? 'pointer-events-none'
                      : ''
                  } `}
                />
              </div>
              {values?.board_chairman?.is_chairman === false && (
                <div className='flex justify-start w-full mt-24'>
                  <FormikInput
                    name='board_chairman.name_of_director'
                    key='board_chairman.name_of_director'
                    label='Who is the Chairman of the Board?'
                    labelClassName='mb-1 text-lg font-medium'
                  />
                </div>
              )}
            </div>

            <div className='w-full'>
              <BackNextComponent
                backStep={() => {
                  backStep();
                  setData(values?.[BusinessContinuityScreens.BOARD_CHAIRMAN]);
                }}
                fieldKey={BusinessContinuityScreens.BOARD_CHAIRMAN}
                formValues={values}
                isLoading={isLoading}
                isNextDisable={!isValid}
                onSaveToDraftClick={onSaveToDraftClick}
                buttonType='submit'
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default BoardChairman;
