import { Form, Formik, FormikValues } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import FormikRadio from 'shared-resources/components/Radio/FormikRadio';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import * as yup from 'yup';
import {
  Screen7Keys,
  getQuestionBykey,
  screen7HaveIncentiveAgreementsInPlaceOptions,
} from '../AssetProtectionConfig';

interface Props {
  data: any;
  handleNextClick: (values: FormikValues) => void;
  handlebackClick: (values: FormikValues) => void;
  handleSaveAsDraftClick: (values: FormikValues) => void;
}
const Screen7: React.FC<Props> = ({
  handleNextClick,
  handlebackClick,
  handleSaveAsDraftClick,
  data,
}) => {
  const validationSchema = yup.object().shape({
    [Screen7Keys.HAVE_INCENTIVE_AGREEMENTS_IN_PLACE]: yup
      .string()
      .required('value is required'),
  });
  const initialValue = data?.screen7 || {
    [Screen7Keys.HAVE_INCENTIVE_AGREEMENTS_IN_PLACE]: '',
  };

  const isSubmitting = useSelector(getAssessmentToolLoading);

  return (
    <div className='flex flex-col h-full  gap-6 pl-10 py-10 font-medium'>
      <Formik
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={(values: FormikValues) => {
          handleNextClick(values);
        }}
        enableReinitialize
      >
        {({ values }) => (
          <Form className='flex flex-col justify-between '>
            <div className='h-[calc(100vh-20rem)] overflow-auto scrollbar'>
              <div>
                <div className='mt-4'>
                  <span className='font-semibold'>
                    {getQuestionBykey(
                      Screen7Keys.HAVE_INCENTIVE_AGREEMENTS_IN_PLACE
                    )}
                  </span>

                  <div className='mt-5 xl:w-[80%]'>
                    <FormikRadio
                      labelClassName='!cursor-pointer'
                      className='grid grid-cols-2 gap-4'
                      name={Screen7Keys.HAVE_INCENTIVE_AGREEMENTS_IN_PLACE}
                      options={screen7HaveIncentiveAgreementsInPlaceOptions.map(
                        (option) => ({
                          value: option,
                          label: option,
                        })
                      )}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className='pr-5'>
              <BackNextComponent
                backStep={() => handlebackClick(values)}
                buttonType='submit'
                isLoading={isSubmitting}
                isNextDisable={false}
                onSaveToDraftClick={() => handleSaveAsDraftClick(values)}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Screen7;
