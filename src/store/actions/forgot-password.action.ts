import { UserRouteType } from 'types/enum';
import { ForgotPasswordActionType } from './actions.constants';

export interface ForgotPasswordActionPayloadType {
  email: string;
  userType: UserRouteType;
}

export interface ResetPasswordActionPayloadType {
  token: string;
  newPassword: string;
  userType: UserRouteType;
  onSuccess: () => void;
}

export interface SetPublicPasswordActionPayloadType {
  token: string;
  newPassword: string;
  onSuccess?: () => void;
}

export const forgotPasswordAction = (
  payload: ForgotPasswordActionPayloadType
) => ({
  type: ForgotPasswordActionType.FORGOT_PASSWORD,
  payload,
});

export const resetPasswordAction = (
  payload: ResetPasswordActionPayloadType
) => ({
  type: ForgotPasswordActionType.RESET_PASSWORD,
  payload,
});

export const setPublicPasswordAction = (
  payload: SetPublicPasswordActionPayloadType
) => ({
  type: ForgotPasswordActionType.SET_PUBLIC_PASSWORD,
  payload,
});
