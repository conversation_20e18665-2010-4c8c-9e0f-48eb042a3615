import React from 'react';
import SimpleBar from '../../../shared-resources/Bars/SimpleBar';
import BarChartComponent from '../../../shared-resources/charts/BarCharts/BarChartComponent';

interface CustomXAxisProps {
  x: number;
  y: number;
  width: number;
}
interface Props {
  overAllScore: number;
  scores: { name: string; score: number }[];
  description?: React.ReactElement;
  isRendedForOwnerReliance?: boolean;
}

const CustomXAxis: React.FC<CustomXAxisProps> = ({
  x,
  y,
  payload,
  width,
}: {
  x: number;
  y: number;
  payload?: { value: string };
  width: number;
}) => (
  <g transform={`translate(${x},${y})`}>
    <line
      x1={20}
      y1={y - 8}
      x2={x + width}
      y2={y - 8}
      stroke='#000'
      strokeWidth={1}
    />
    {payload!.value.split(' ').map((word, index) => (
      <text
        x={0}
        y={index * 15}
        dy={15}
        textAnchor='middle'
        fill='#666'
        key={Math.random()}
      >
        {word}
      </text>
    ))}
  </g>
);

const OverallScorePage: React.FC<Props> = ({
  scores,
  overAllScore,
  description,
  isRendedForOwnerReliance,
}) => (
  <div className='flex justify-center'>
    <div className='flex flex-col items-center w-4/5 3xl:w-1/2 h-full'>
      <h1
        className={`text-blue-01 font-openSan font-semibold mt-[7.5rem] text-center  ${
          isRendedForOwnerReliance ? 'text-3xl' : ''
        }`}
      >
        {`${
          isRendedForOwnerReliance
            ? 'Your Owner Reliance Score'
            : 'Your Overall Readiness Score'
        }`}
      </h1>
      <h2
        className={`font-openSans font-medium mt-7 mb-10 ${
          isRendedForOwnerReliance ? 'text-blue-01 font-semibold text-2xl' : ''
        }`}
      >
        Overall Score : {overAllScore}%
      </h2>

      <BarChartComponent
        referenceLine
        data={scores}
        bars={[{ key: 'score', color: '#20A0D6' }]}
        cartesianProps={{
          vertical: false,
          stroke: '#E4E4E4',
          x: 30,
          syncWithTicks: true,
        }}
        yAxisProps={{
          padding: {
            top: 20,
            bottom: 0,
          },
          tickLine: false,
          axisLine: { stroke: '#000', strokeWidth: 1 },
          tick: { dx: -25 },
          fontSize: 14,
          fontWeight: 500,
          tickFormatter: (tick) =>
            // Only render the tick label if it's not 0
            tick !== 0 ? tick : '',
        }}
        xAxisProps={{
          axisLine: {
            stroke: '#000',
            strokeWidth: 1,
          },
          // @ts-ignore
          tick: <CustomXAxis />,
          tickLine: true,
          padding: {
            left: 30,
            right: 0,
          },
        }}
        chartHeight={586}
        chartWidth={isRendedForOwnerReliance ? 1000 : 726}
        maxBarSize={70}
      />
      <div className='mt-24 w-full flex flex-col space-y-6'>
        {scores.map((score) => (
          <div
            className='flex items-center font-openSans font-semibold'
            key={Math.random()}
          >
            <h3 className='mr-12 flex-shrink-0 w-[17rem]'>{score.name} :</h3>
            <SimpleBar percentage={score.score} />
            <h3 className='ml-3 flex-shrink-0'>{score.score}%</h3>
          </div>
        ))}
      </div>

      <div className='mt-[4rem]'>
        {description || (
          <>
            <p>
              Your overall Readiness Score of
              <span className='font-semibold'> {overAllScore}%</span> indicates
              :
            </p>
            <p className='mt-6'>
              That you have some transition knowledge, but still have plenty of
              opportunity to advance your understanding of, and readiness for,
              your ultimate transition.
            </p>
          </>
        )}
      </div>
    </div>
  </div>
);

export default OverallScorePage;
