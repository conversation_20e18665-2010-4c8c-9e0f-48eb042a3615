import React from 'react';
import 'react-tooltip/dist/react-tooltip.css';
import Input from '../../../shared-resources/components/Input/Input';
import { formatCurrency } from './ExpenseCalculatorConfig';

interface Props {
  totalMonthlyExpense: number;
}
const TotalExpense: React.FC<Props> = ({ totalMonthlyExpense }) => (
  <div className='flex flex-col '>
    <h2 className='text-xl font-semibold mt-7 underline'>Expenses</h2>
    <div className='flex flex-col gap-6 h-[calc(100vh-25rem)] mt-12 overflow-auto scrollbar pr-4 text-[1rem]'>
      <div className='grid grid-cols-2 gap-x-10 items-end'>
        <Input
          label='Total Expenses (Monthly)'
          labelClassName='!text-[1rem]'
          onChange={() => null}
          value={formatCurrency(totalMonthlyExpense)}
          disabled
        />
        <Input
          label='Total Essential Expenses (Annually)'
          labelClassName='!text-[1rem]'
          onChange={() => null}
          value={formatCurrency(totalMonthlyExpense * 12)}
          disabled
        />
      </div>
    </div>
  </div>
);

export default TotalExpense;
