import React, { FC, memo } from 'react';
import { MdDelete } from 'react-icons/md';
import Select, {
  SelectOption,
} from 'shared-resources/components/Select/Select';
import { TableValueType } from './Questions';

type StakeHoldersTableCompProps = {
  tableValues: TableValueType[];
  handleInputChange: (
    index: number,
    field: string,
    value: string | boolean
  ) => void;
  handleDelete: (id: number) => void;
  options: SelectOption[];
  isStakeHolder?: boolean;
};

const StakeHoldersTableComp: FC<StakeHoldersTableCompProps> = ({
  tableValues,
  handleInputChange,
  handleDelete,
  options,
  isStakeHolder,
}) => {
  const getHighlightClassYes = (tableValObj: any) =>
    tableValObj?.invite === true ? 'bg-blue-01' : '';
  const getHighlightClassNo = (tableValObj: any) =>
    tableValObj?.invite === false ? 'bg-blue-01' : '';
  return (
    <div className='overflow-auto scrollbar relative mb-4'>
      <table>
        <thead className='sticky top-0 z-10'>
          <tr className='shadow-blue-border  rounded-t-lg border-t-0 sticky'>
            <th className='bg-blue-01 py-4 px-2 w-96 rounded-ss-lg border-r-2 border-gray-500 text-white'>
              StakeHolder Name
            </th>
            <th className='bg-blue-01 px-2 py-4 w-72 border-r-2  border-gray-500 text-white'>
              Equity Owned (if Any)
            </th>
            <th className='bg-blue-01 px-2 py-4 w-72 border-r-2  border-gray-500 text-white'>
              Relationship To Owner Or Business
            </th>
            <th className='bg-blue-01 px-2 py-4 w-72 border-r-2  border-gray-500 text-white'>
              {isStakeHolder
                ? 'Invite to Meeting (Yes/No)'
                : 'Aligned (Yes/No)'}
            </th>
            <th className='bg-blue-01 px-2 py-4 w-72 border-r-2  border-gray-500 text-white'>
              Comments
            </th>
            <th className='bg-blue-01 px-2 py-4 w-36 rounded-se-lg  border-gray-500 text-white'>
              Action
            </th>
          </tr>
        </thead>
        <tbody>
          {tableValues?.map((tableValObj: TableValueType) => (
            <tr key={tableValObj?.id} className='w-full'>
              <td
                className='p-2 justify-start
                 border-2 border-gray-500 h-full w-1/5'
              >
                <input
                  aria-label={tableValObj?.name}
                  value={tableValObj?.name}
                  onChange={(e) =>
                    handleInputChange(tableValObj?.id, 'name', e.target.value)
                  }
                  id='name'
                  type='text'
                  className='mx-auto focus:outline-none outline-black w-full '
                />
              </td>
              <td className='border-2 border-gray-500 border-l-0 p-2'>
                <input
                  value={tableValObj?.equityOwned}
                  onChange={(e) =>
                    handleInputChange(
                      tableValObj.id,
                      'equityOwned',
                      e.target.value
                    )
                  }
                  aria-label={tableValObj?.name}
                  id='equityOwned'
                  type='number'
                  className='mx-auto focus:outline-none outline-black w-full '
                />
              </td>
              <td className='border-2 border-gray-500 w-1/5 flexborder-l-0 p-2'>
                <Select
                  aria-label={tableValObj?.relationship}
                  selectedOption={
                    tableValObj?.relationship !== ''
                      ? tableValObj?.relationship
                      : undefined
                  }
                  optionPadding='0.2rem'
                  placeholder='Select'
                  controlHeight='2.125rem'
                  selectClassName='px-4'
                  menuListPadding='0'
                  menuPadding='0'
                  options={options}
                  onChange={(e: string) => {
                    handleInputChange(tableValObj?.id, 'relationship', e);
                  }}
                />
              </td>
              <td className='border-2 border-gray-500 border-l-0 p-2'>
                <div className='flex justify-center items-center'>
                  <button
                    className={`px-5  rounded-l-xl border-l-2 border-y-2 border-gray-300 ${getHighlightClassYes(
                      tableValObj
                    )}`}
                    onClick={() => {
                      handleInputChange(tableValObj?.id, 'invite', true);
                    }}
                  >
                    Yes
                  </button>
                  <button
                    className={` px-5 rounded-r-xl border-2 border-gray-300 ${getHighlightClassNo(
                      tableValObj
                    )}`}
                    onClick={() => {
                      handleInputChange(tableValObj?.id, 'invite', false);
                    }}
                  >
                    No
                  </button>
                </div>
              </td>
              <td className='border-2 border-gray-500 border-l-0 p-2 w-[30%]'>
                <input
                  aria-label={tableValObj?.comment}
                  value={tableValObj?.comment}
                  onChange={(e) =>
                    handleInputChange(
                      tableValObj?.id,
                      'comment',
                      e.target.value
                    )
                  }
                  id='comment'
                  type='text'
                  className='mx-auto focus:outline-none outline-black w-full '
                />
              </td>
              <td className='border-2 border-gray-500 border-l-0 p-2'>
                <MdDelete
                  aria-label='delete'
                  size={24}
                  color='red'
                  className='w-full h-6 cursor-pointer'
                  onClick={() => {
                    handleDelete(tableValObj?.id);
                  }}
                />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default memo(StakeHoldersTableComp);
