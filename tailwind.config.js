module.exports = {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  safelist: ['h-[3.75rem]', 'h-[6.25rem]', 'w-[3.75rem]', 'w-[6.25rem]'],
  theme: {
    fontFamily: {
      montserrat: ['Montserrat', 'sans-serif'],
      openSans: ['Open Sans', 'sans-serif'],
    },
    screens: {
      override: '0px', //0px - for overriding css
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '1.5xl': '1400px',
      '2xl': '1536px',
      '2.5xl': '1678px',
      '3xl': '1792px',
      '4xl': '2048px',
    },
    extend: {
      fontSize: {
        2.5: '0.625rem',
        26: '1.625rem',
        28: '1.75rem',
      },
      colors: {
        blue: {
          '01': '#20A0D6',
          '02': '#E1F0F6',
          '03': '#EDF2FC'
        },
        black: {
          '01': '#000000',
          '02': '#544D4D',
          '03': '#2C2A2A',
          '04': '#343434',
        },
        gray: {
          '01': '#E4E4E4',
          '02': '#707070',
          '03': '#666666',
          '04': '#898787',
          '05': '#A59F9F',
          '06': '#858282',
          '07': '#9C9494',
          '08': '#F6F6F6',
        },
        green: { '01': '#008000' },
        red: { '01': '#DC2626' },
        yellow: { '01': '#EAB308' },
      },
      height: { 13: '3.125rem' },
      width: {
        '13/20': '65%',
        68: '17rem',
        '11/20': '55%',
        '9/10': '90%',
      },
      spacing: {
        4.5: '1.125rem',
        8.5: '2.125rem',
        12.5: '3.125rem',
        6.5: '1.625rem',
        37.5: '9.375rem',
        135: '33.75rem',
        150: '37.5rem',
        7.5: '1.875rem',
        7.75: '1.9375rem',
        15: '3.75rem',
        1.25: '0.3125rem',
      },
      borderWidth: {
        6.25: '1.5625rem',
        32: '2rem',
        20: '1.25rem',
        15: '0.9375rem',
        37: '2.3125rem',
        38: '2.375rem',
      },
      boxShadow: {
        'blue-border': '1px 0 0 0 #20A0D6, -1px 0 0 0 #20A0D6',
      },
      gridTemplateColumns: {
        16: 'repeat(16, minmax(0, 1fr))',
      },
    },
  },
};
