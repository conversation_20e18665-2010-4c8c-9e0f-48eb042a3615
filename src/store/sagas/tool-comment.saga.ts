import { all, call, put, takeLatest } from 'redux-saga/effects';
import { toast } from 'react-toastify';
import { toolCommentService } from 'services/api-services/ToolCommentService';
import {
  setComments,
  addComment,
  updateComment as updateCommentReducer,
  removeComment,
  setLoading,
  setError,
} from 'store/reducers/tool-comment.reducer';
import { ToolCommentActionType } from '../actions/actions.constants';

function* fetchCommentsSaga(action: any): any {
  const { businessOwnerId, tool, search } = action.payload;
  try {
    yield put(setLoading(true));
    const response = yield call(
      toolCommentService.fetchComments,
      businessOwnerId,
      tool,
      search
    );
    yield put(setComments(response.comments));
  } catch (e: any) {
    yield put(setError(e?.response?.data?.message));
  } finally {
    yield put(setLoading(false));
  }
}

function* createCommentSaga(action: any): any {
  const { businessOwnerId, tool, comment, metadata } = action.payload;
  try {
    yield put(setLoading(true));
    const response = yield call(
      toolCommentService.createComment,
      businessOwnerId,
      tool,
      comment,
      metadata
    );
    yield put(addComment(response.comment));
    toast.success(response.message);
  } catch (e: any) {
    yield put(setError(e?.response?.data?.message));
  } finally {
    yield put(setLoading(false));
  }
}

function* updateCommentSaga(action: any): any {
  const { commentId, comment } = action.payload;
  try {
    yield put(setLoading(true));
    const response = yield call(
      toolCommentService.updateComment,
      commentId,
      comment
    );
    yield put(updateCommentReducer(response.comment));
    toast.success(response.message);
  } catch (e: any) {
    yield put(setError(e?.response?.data?.message));
  } finally {
    yield put(setLoading(false));
  }
}

function* deleteCommentSaga(action: any): any {
  const { commentId } = action.payload;
  try {
    yield put(setLoading(true));
    const response = yield call(toolCommentService.deleteComment, commentId);
    yield put(removeComment(commentId));
    toast.success(response.message);
  } catch (e: any) {
    yield put(setError(e?.response?.data?.message));
  } finally {
    yield put(setLoading(false));
  }
}

export function* toolCommentSagaWatcher() {
  yield all([
    takeLatest(ToolCommentActionType.FETCH_COMMENTS, fetchCommentsSaga),
    takeLatest(ToolCommentActionType.CREATE_COMMENT, createCommentSaga),
    takeLatest(ToolCommentActionType.UPDATE_COMMENT, updateCommentSaga),
    takeLatest(ToolCommentActionType.DELETE_COMMENT, deleteCommentSaga),
  ]);
}
