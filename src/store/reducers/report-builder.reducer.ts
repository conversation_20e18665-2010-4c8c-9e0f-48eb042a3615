import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ReportResponse } from 'types/report-builder.type';

interface ReportBuilderState {
  reports: ReportResponse[];
  currentReport: (ReportResponse & { file?: File }) | null;
  loading: boolean;
  error: string | null;
  isReportCreated: boolean;
}

const initialState: ReportBuilderState = {
  reports: [],
  currentReport: null,
  loading: false,
  error: null,
  isReportCreated: false,
};

const reportBuilderSlice = createSlice({
  name: 'reportBuilder',
  initialState,
  reducers: {
    setReports: (state, action: PayloadAction<ReportResponse[]>) => {
      state.reports = action.payload;
    },
    setCurrentReport: (
      state,
      action: PayloadAction<(ReportResponse & { file?: File }) | null>
    ) => {
      state.currentReport = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setIsReportCreated: (state, action: PayloadAction<boolean>) => {
      state.isReportCreated = action.payload;
    },
  },
});

export const {
  setReports,
  setCurrentReport,
  setLoading,
  setError,
  setIsReportCreated,
} = reportBuilderSlice.actions;

export default reportBuilderSlice.reducer;
