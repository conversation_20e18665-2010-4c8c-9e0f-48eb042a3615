import React from 'react';
import { IoWarning } from 'react-icons/io5';
import Button from 'shared-resources/components/Button/Button';

interface AdvisorAccessModalProps {
  onCancelClick: () => void;
  onDeleteClick: () => void;
  message: string[];
  loading: boolean | undefined;
}
const AdvisorAccessModal: React.FC<AdvisorAccessModalProps> = ({
  onCancelClick,
  onDeleteClick,
  message,
  loading,
}) => (
  <div className='flex flex-col p-8'>
    <div className='flex items-start'>
      <IoWarning className='min-w-7 min-h-7' color='red' />
      <div className='flex flex-col ml-4'>
        <span className='font-medium'>
          Please confirm that you want to {message[0]} this Advisor Access.
          <br />
          Once {message[1]}, that advisor {message[2]} able to login back to exit_smart.
          <br />
          This can be restored by toggling {message[3]}.
        </span>
      </div>
    </div>
    <div className='flex justify-end gap-6 mt-4'>
      <Button
        className='px-6 py-2'
        type='button'
        onClick={() => onDeleteClick()}
        isSubmitting={loading}
      >
        Yes
      </Button>
      <Button
        className='px-6 py-2'
        theme='secondary'
        type='button'
        onClick={() => onCancelClick()}
      >
        No
      </Button>
    </div>
  </div>
);

export default AdvisorAccessModal;
