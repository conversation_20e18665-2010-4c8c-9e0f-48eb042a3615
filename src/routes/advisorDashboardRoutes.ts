import BusinessOwnerDetails from 'shared-resources/components/BusinessOwner/BusinessOwnerDetails';
import { AssessmentTools, RouteKey, UserRouteType } from 'types/enum';
import { SidebarRoutesConfigType } from 'types/routes/routes.type';
import SubscriptionPage from 'views/AdvisorSubscription/SubscriptionPage';
import AssetProtection from 'views/AssetProtection/AssetProtection';
import BusinessContinuityContainer from 'views/BusinessContinuity/BusinessContinuityContainer';
import BusinessContinuityReport from 'views/BusinessContinuity/Report/BusinessContinuityReport';
import BusinessValuationScreenContainer from 'views/BusinessValuation/BusinessValuationScreenContainer';
import FinancialGapAnalysisScreenContainer from 'views/FinancialGapAnalysis/FinancialGapAnalysisScreenContainer';
import OwnerReliance from 'views/Owner-Reliance/OwnerReliance';
import OwnerRelianceReport from 'views/Owner-Reliance/OwnerRelianceReport';
import ReadinessAssessment from 'views/Readiness-Assesment/ReadinessAssessment';
import TransitionObjectivePage from 'views/TransitionObjectives/TransitionObjectivePage';
import TransitionObjectiveReport from 'views/TransitionObjectives/TransitionObjectiveReport';
import ValueEnhancement from 'views/Value-Enhancement/ValueEnhancement';
import ValueEnhancementReport from 'views/Value-Enhancement/report/ValueEnhancementReport';
import StakeHolderAlignmentMeetingPage from 'views/Stakeholder-Alignment-Meeting/StakeHolderAlignmentMeetingPage';
import AssetProtectionReport from 'views/AssetProtection/report/AssetProtectionReport';
import ReportBuilder from 'components/ReportBuilder/ReportBuilder';
import ProfilePage from 'views/Profile/ProfilePage';
import BuyerType from 'views/BuyerType/BuyerType';
import BuyerTypeReport from 'views/BuyerType/BuyerTypeReport';
import ExpenseCalculator from '../views/FinancialGapAnalysis/ExpenseCalculator/ExpenseCalculator';
import FinancialGapAnalysisReport from '../views/FinancialGapAnalysis/FinancialGapAnalysisReport/FinancialGapAnalysisReport';
import ReadinessAssessmentReport from '../views/ReadinessAssessment/ReadinessAssessmentReport/ReadinessAssessmentReport';

export const advisorDashboardRoute: Array<SidebarRoutesConfigType> = [
  {
    name: 'Adviser Profile',
    key: `/${UserRouteType.ADVISOR}/${RouteKey.PROFILE}`,
    component: ProfilePage,
  },
  {
    name: 'Business Owner Details',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id`,
    component: BusinessOwnerDetails,
  },
  {
    name: 'Business Valuation',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${AssessmentTools.BUSINESS_VALUATION}`,
    component: BusinessValuationScreenContainer,
  },
  {
    name: 'Readiness Assessment',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${AssessmentTools.READINESS_ASSESSMENT}`,
    component: ReadinessAssessment,
  },
  {
    name: 'Transition Objectives',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${AssessmentTools.TRANSITION_OBJECTIVES}`,
    component: TransitionObjectivePage,
  },
  {
    name: 'Value Enhancement Opportunities',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES}`,
    component: ValueEnhancement,
  },
  {
    name: 'Business Owner Reliance',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${AssessmentTools.OWNER_RELIANCE}`,
    component: OwnerReliance,
  },
  {
    name: 'Business Continuity',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${AssessmentTools.BUSINESS_CONTINUITY}`,
    component: BusinessContinuityContainer,
  },
  {
    name: 'Wealth Gap Analysis',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${AssessmentTools.FINANCIAL_GAP_ANALYSIS}`,
    component: FinancialGapAnalysisScreenContainer,
  },
  {
    name: 'Expense Calculator',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${RouteKey.EXPENSE_CALCULATOR}`,
    component: ExpenseCalculator,
  },
  {
    name: 'Wealth Gap Analysis Report',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${RouteKey.FINANCIAL_GAP_REPORT}`,
    component: FinancialGapAnalysisReport,
  },
  {
    name: 'Stakeholder Alignment Meeting',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING}`,
    component: StakeHolderAlignmentMeetingPage,
  },
  {
    name: 'Asset Protection',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${AssessmentTools.ASSET_PROTECTION}`,
    component: AssetProtection,
  },
  {
    name: 'Buyer Type-Deal Structure',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE}`,
    component: BuyerType,
  },
  {
    name: 'Advisor Subscription',
    key: `/${UserRouteType.ADVISOR}/${RouteKey.SUBSCRIPTION}`,
    component: SubscriptionPage,
  },
  {
    name: 'Readiness Assessment Report',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${RouteKey.READINESS_REPORT}`,
    component: ReadinessAssessmentReport,
  },
  {
    name: 'Transition Objectives Report',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${RouteKey.TRANSITION_REPORT}`,
    component: TransitionObjectiveReport,
  },
  {
    name: 'Owner Reliance Report',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${RouteKey.OWNER_RELIANCE_REPORT}`,
    component: OwnerRelianceReport,
  },
  {
    name: 'Business Continuity Report',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${RouteKey.BUSINESS_CONTINUITY_REPORT}`,
    component: BusinessContinuityReport,
  },
  {
    name: 'Value Enhancement Opportunities Report',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES_REPORT}`,
    component: ValueEnhancementReport,
  },
  {
    name: 'Asset Protection Report',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${RouteKey.ASSET_PROTECTION_REPORT}`,
    component: AssetProtectionReport,
  },
  {
    name: 'Buyer Type Report',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${RouteKey.BUYER_TYPE_DEAL_STRUCTURE_REPORT}`,
    component: BuyerTypeReport,
  },
  {
    name: 'Report Builder',
    key: `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/:id/${RouteKey.REPORT_BUILDER}`,
    component: ReportBuilder,
  },
];
