import classNames from 'classnames';
import React, { useCallback, useEffect, useState } from 'react';
import { renderToString } from 'react-dom/server';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import Checkbox from 'shared-resources/components/CheckBox/Checkbox';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import {
  fetchAssessmentReport,
  fetchAssessmentReportByBusinessOwner,
} from 'store/actions/assessment-report.action';
import { fetchComments } from 'store/actions/tool-comment.action';
import { resetAssessmentData } from 'store/reducers/assessment-tool.reducer';
import {
  getAssessmentReportLoading,
  getAssessmentReportResponse,
} from 'store/selectors/assessment-report.selector';
import { getToolComments } from 'store/selectors/tool-comment.selector';
import { getUserData } from 'store/selectors/user.selector';
import { AssessmentTools, RouteKey, UserRouteType, UserType } from 'types/enum';
import {
  InsuranceTypeheaderOptions,
  Screen2Keys,
  Screen3Keys,
  Screen8Keys,
  Screen8TableResponseType,
  getQuestionBykey,
  getScreenNumberByKey,
} from 'views/AssetProtection/AssetProtectionConfig';
import { formatCurrency } from 'views/FinancialGapAnalysis/ExpenseCalculator/ExpenseCalculatorConfig';

interface Props {
  onSelectionChange: (selectedData: {
    [key: string]: { content: string; type: string; comments?: any[] };
  }) => void;
}

const AssetProtectionBuilder: React.FC<Props> = ({ onSelectionChange }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const response = useSelector(getAssessmentReportResponse);
  const loggedInUserData = useSelector(getUserData);
  const assessmentLoading = useSelector(getAssessmentReportLoading);
  const [selectedQuestions, setSelectedQuestions] = useState<{
    [key: string]: boolean;
  }>({});
  const [selectedComments, setSelectedComments] = useState<{
    [key: string]: boolean;
  }>({});
  const comments = useSelector(getToolComments);

  const dispatch = useDispatch();

  useEffect(() => {
    if (id && loggedInUserData?.type === UserType.ADVISOR) {
      dispatch(
        fetchAssessmentReport({
          tool: AssessmentTools.ASSET_PROTECTION,
          id: id!,
          onError: () => {
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`);
          },
        })
      );
    }
    if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchAssessmentReportByBusinessOwner({
          tool: AssessmentTools.ASSET_PROTECTION,
          onError: () =>
            navigate(
              `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.ASSET_PROTECTION}`
            ),
        })
      );
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  useEffect(() => {
    if (loggedInUserData?.type === UserType.ADVISOR) {
      dispatch(fetchComments(Number(id), AssessmentTools.ASSET_PROTECTION));
    }
  }, [dispatch, id, loggedInUserData?.type]);

  const filterResponse = (obj: Record<string, any>): Record<string, string> =>
    Object.entries(obj).reduce<Record<string, string>>((acc, [key, value]) => {
      if (key !== 'screen8' && typeof value === 'object') {
        Object.entries(value).forEach(([k, v]) => {
          if (v && typeof v === 'string') acc[k] = v;
        });
      }
      return acc;
    }, {});

  const filteredResponse = filterResponse(response?.assessment_response);
  if (Object.keys(response?.assessment_response).includes('screen8')) {
    filteredResponse[Screen8Keys.INSURANCE_TYPE] = 'table';
  }
  const tableResponse = (response?.assessment_response as any)?.screen8;

  useEffect(
    () => () => {
      dispatch(resetAssessmentData());
    },
    []
  );

  const handleCheckboxChange = (key: string) => {
    setSelectedQuestions({
      ...selectedQuestions,
      [key]: !selectedQuestions[key],
    });
  };

  const handleCommentCheckboxChange = (commentId: string) => {
    setSelectedComments({
      ...selectedComments,
      [commentId]: !selectedComments[commentId],
    });
  };

  const renderComments = (key: string) => {
    const screenNumber = getScreenNumberByKey(key);
    const screenComments = comments.filter(
      (comment) => comment.metadata?.screen === `${screenNumber}`
    );

    if (screenComments.length > 0) {
      return (
        <div className='mt-4'>
          <span className='font-semibold mb-2'>Comments:</span>
          {screenComments.map((comment) => (
            <div key={comment.id} className='ml-4 mb-2'>
              <Checkbox
                onChange={() =>
                  handleCommentCheckboxChange(comment.id.toString())
                }
                value={selectedComments[comment.id.toString()] || false}
                text={comment.comment as any}
                className='!mt-0'
              />
            </div>
          ))}
        </div>
      );
    }
    return null;
  };

  const getRenderedData = (
    key: string,
    value: string,
    isRender: boolean = false
  ) => {
    if (key === Screen8Keys.INSURANCE_TYPE) {
      return (
        <div key={key} className='flex flex-col mt-2 gap-2'>
          <div className='flex gap-4'>
            {!isRender && (
              <Checkbox
                className='!cursor-pointer !mt-0'
                onChange={() => {
                  handleCheckboxChange(key);
                }}
                value={selectedQuestions[key] || false}
              />
            )}
            <span className='mt-1'>{getQuestionBykey(key)}</span>
          </div>

          <div className='pr-1 mt-3 pb-2'>
            <div
              className={`bg-blue-01 z-10 tracking-[0.07rem] grid grid-cols-4 text-white
            rounded-t-md  text-[0.9rem]`}
            >
              {InsuranceTypeheaderOptions.map((h, index) => (
                <div
                  key={h}
                  className={`px-5  flex items-center border-r border-gray-02 justify-center py-1 h-full ${
                    index === 0 && 'rounded-tl-md'
                  } ${index === 3 && ' border-r-0 rounded-t-md'} `}
                >
                  <span className='text-center'>{h}</span>
                </div>
              ))}
            </div>
            <div>
              {Object.keys(tableResponse).map((tableKey) => (
                <div
                  key={tableKey}
                  className={classNames('grid grid-cols-4  text-[0.9rem] ')}
                >
                  <div
                    className={`px-5
                 flex items-center border-b border-l py-1 border-r border-gray-02`}
                  >
                    <span className='text-xs xl:text-sm '>{tableKey}</span>
                  </div>

                  <div className='flex py-2 justify-center border-b border-r border-gray-02 '>
                    <button
                      className={`py-1 px-5 rounded-l-xl border-l-2 border-y-2 border-gray-300 ${
                        tableResponse[
                          tableKey as keyof Screen8TableResponseType
                        ]?.value === true && 'bg-blue-01'
                      }`}
                      onClick={() => {}}
                    >
                      Yes
                    </button>
                    <button
                      className={`py-1 px-5 rounded-r-xl border-2 border-gray-300 ${
                        tableResponse[
                          tableKey as keyof Screen8TableResponseType
                        ]?.value === false && 'bg-blue-01'
                      } `}
                      onClick={() => {}}
                    >
                      No
                    </button>
                  </div>

                  <div
                    className={`px-3 2xl:px-5 
                 flex items-center border-b py-1 border-r border-gray-02`}
                  >
                    <div className='flex gap-2 items-center w-full'>
                      <span>$</span>
                      {formatCurrency(
                        tableResponse[
                          tableKey as keyof Screen8TableResponseType
                        ].coverage_amount
                      ).replace('$', '')}
                    </div>
                  </div>

                  <div
                    className={`px-3 2xl:px-5 
                 flex items-center border-b py-1 border-r border-gray-02`}
                  >
                    <div className='flex gap-2 items-center w-full'>
                      <span>$</span>
                      {formatCurrency(
                        tableResponse[
                          tableKey as keyof Screen8TableResponseType
                        ].recommended_coverage
                      ).replace('$', '')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {!isRender && renderComments(key)}
        </div>
      );
    }

    if (key === Screen3Keys.owner_type) return null;

    return (
      <div key={key} className='flex flex-col gap-2 mt-4 w-full'>
        <div className='flex gap-4'>
          {!isRender && (
            <Checkbox
              className='!cursor-pointer !mt-0'
              onChange={() => {
                handleCheckboxChange(key);
              }}
              value={selectedQuestions[key] || false}
            />
          )}
          <span className='mt-1'>{getQuestionBykey(key)}</span>
        </div>

        <div className='px-4 py-3 rounded-lg text-blue-01 border border-gray-02'>
          {key === Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_IN_EXIT_SMARTS
            ? new Date(value).toLocaleDateString('en-US')
            : value}
        </div>
        {!isRender && renderComments(key)}
      </div>
    );
  };

  const getSelectedData = useCallback(() => {
    const renderDataForQuill: {
      [key: string]: { content: string; type: string; comments: any[] };
    } = {};

    Object.entries(filteredResponse).forEach(([key, value]) => {
      if (selectedQuestions[key]) {
        const screenNumber = getScreenNumberByKey(key);
        if (screenNumber !== undefined) {
          if (!renderDataForQuill[screenNumber]) {
            renderDataForQuill[screenNumber] = {
              content: '',
              type: 'asset-question',
              comments: [],
            };
          }
          renderDataForQuill[screenNumber].content += renderToString(
            getRenderedData(key, value, true)
          );

          // Add selected comments for this screen
          const screenComments = comments
            .filter(
              (comment) =>
                comment.metadata?.screen === `${screenNumber}` &&
                selectedComments[comment.id.toString()]
            )
            .map((comment) => ({
              id: comment.id,
              comment: comment.comment,
              metadata: comment.metadata,
            }));

          renderDataForQuill[screenNumber].comments.push(...screenComments);
        }
      }
    });

    return renderDataForQuill;
  }, [
    filteredResponse,
    selectedQuestions,
    selectedComments,
    comments,
    tableResponse,
  ]);

  useEffect(() => {
    if (Object.keys(getSelectedData()).length > 0) {
      onSelectionChange(getSelectedData());
    }
  }, [getSelectedData, onSelectionChange]);

  const displayPage = (res: Record<string, string>) => (
    <div>
      {Object.entries(res)?.map(([key, value]) => getRenderedData(key, value))}
    </div>
  );

  if (assessmentLoading) {
    return (
      <div className='h-[calc(100vh-18rem)]'>
        <Spinner />
      </div>
    );
  }

  return (
    <div className='w-full h-[calc(100vh-18rem)] overflow-y-auto pr-2 scrollbar'>
      {displayPage(filteredResponse)}
    </div>
  );
};

export default AssetProtectionBuilder;
