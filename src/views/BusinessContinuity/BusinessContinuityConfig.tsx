import { pick } from 'lodash';
import {
  AssessmentToolProgressStatus,
  BusinessContinuityScreens,
  UserType,
} from 'types/enum';

// screen name against its screen number
export const continuityNumberToScreenObject: {
  [key: number]: BusinessContinuityScreens;
} = Object.values(BusinessContinuityScreens).reduce(
  (acc: { [key: number]: BusinessContinuityScreens }, value, index: number) => {
    acc[index + 1] = value;
    return acc;
  },
  {}
);

// screen number against its screen name
export const continuityScreenToNumberObject = Object.values(
  BusinessContinuityScreens
).reduce<{ [screen: string]: number }>(
  (acc: { [screen: string]: number }, value, index: number) => {
    acc[value] = index + 1;
    return acc;
  },
  {}
);

export const mapOptions = (options: string[]) =>
  options.map((option) => ({ label: option, value: option }));

export const continuityOptionsForScreen = {
  [BusinessContinuityScreens.ACCOUNTING_INFORMATION]: [
    'Bookkeeper',
    'Payroll',
    'General',
    'Tax',
    'Advisory',
    'Audit',
    'Others',
  ],
  [BusinessContinuityScreens.ATTORNEYS_INFORMATION]: [
    'Business',
    'Estate',
    'Tax',
    'Personal',
    'Intellectual Property',
    'Other',
  ],
  [BusinessContinuityScreens.BUSINESS_INFORMATION]: [
    'Broker',
    'Consultant',
    'Transition Planning',
    'Valuation',
    'Coach',
    'Other',
  ],
  [BusinessContinuityScreens.FINANCIAL_ADVISORS]: [
    'Wealth Advisor',
    'Financial Planner',
    'Family Office',
    'Retirement Planning Specialist',
  ],
  [BusinessContinuityScreens.DOCUMENTS]: [
    'Company Bylaws',
    'Buy-Sell Agreement',
    'Employee Contracts',
    'Lease(s)',
    'Loan Documents',
    'Patents',
    'Franchise Agreement',
    'Customer Contracts',
    'Last Will',
    'Living Will',
    'Durable Financial Power of Attorney',
    'Durable Medical Power of Attorney',
    'Revocable Living Trust',
    'DNR Instructions',
    'Organ Donor Card',
    'HIPPA Release',
    'Funeral Arrangements',
    'Legal Documents',
    'Other',
  ],
  [BusinessContinuityScreens.BANKERS]: ['Commercial', 'Personal'],
  [BusinessContinuityScreens.BANKING_INFORMATION]: [
    'Checking Account',
    'Safe Deposit Account',
  ],
  [BusinessContinuityScreens.LOANS_INFORMATION]: [
    'Bank Loan',
    'Loan From Individual',
    'Equipment Loan',
    'Other Loan',
    'Line Of Credit',
  ],
  [BusinessContinuityScreens.LEASES]: [
    'Commercial Property',
    'Equipment',
    'Automobile',
  ],
  [BusinessContinuityScreens.INSURANCES]: [
    'Health Insurance',
    'Life Insurance',
    'Commercial Auto Insurance',
    'Business Liability Insurance',
    'Disability Insurance',
    'Commercial Property',
    'Business Income Insurance',
    'Professional Liability',
    'Commercial Umberalla',
  ],
};

const filterEmptyArrayObjects = (data: any) =>
  Object.entries(data).reduce<any>((filteredData, [key, value]) => {
    if (Array.isArray(value)) {
      const nonEmptyObjects = value.filter((obj) => {
        const filteredObj = pick(
          obj,
          Object.keys(obj).filter(
            (key: string) => key !== 'priority' && key !== 'saved'
          )
        );
        return Object.values(filteredObj).some(
          (val) => val != null && String(val).trim() !== ''
        );
      });

      if (nonEmptyObjects.length > 0) {
        filteredData[key] = nonEmptyObjects;
      }
    } else {
      filteredData[key] = value;
    }
    return filteredData;
  }, {});

const getTotalAnswered = (response: any) => {
  let count = 0;
  const filteredResponse = filterEmptyArrayObjects(response);

  Object.keys(filteredResponse || {})?.forEach((key) => {
    const value = Object.values(response?.[key] ?? {});
    if (value && key !== 'saved_screen') {
      count += 1;
    }
  });
  return count;
};
export const calculateBusinessContinuityPercentage = (
  response: any,
  progressStatus: string,
  userType: UserType
) => {
  if (!response || Object.keys(response).length === 0) {
    return 0; // Return 0 if data is null, undefined, or an empty object
  }
  let totalQuestions = Object.keys(BusinessContinuityScreens).length - 2;
  if (userType === UserType.ADVISOR) {
    if (response?.should_private?.is_private) {
      totalQuestions = Object.keys(BusinessContinuityScreens).length - 4;
    } else {
      totalQuestions = Object.keys(BusinessContinuityScreens).length - 3;
    }
  }
  const totalAnswered = getTotalAnswered(response);

  const completedPercentage = (totalAnswered / totalQuestions) * 100;
  if (
    completedPercentage === 100 &&
    progressStatus !== AssessmentToolProgressStatus.COMPLETED
  ) {
    return 99;
  }
  return completedPercentage;
};

export const getHeaders = (key: string) => {
  switch (key) {
    case 'key_advisors':
    case 'accounting_information':
    case 'attorneys_information':
    case 'business_information':
    case 'financial_advisors':
    case 'board_of_directors':
    case 'other_advisors':
      return ['Key Advisors'];
    case 'bankers':
      return ['Bankers'];
    case 'banking_information':
      return ['Bankers', 'Banking'];
    case 'loans_information':
    case 'personal_guarantees':
      return ['Bankers', 'Banking', 'Loans'];
    case 'insurance':
      return ['Insurance'];
    case 'miscellaneous':
      return ['Miscellaneous'];
    default:
      return null;
  }
};
