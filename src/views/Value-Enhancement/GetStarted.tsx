import React, { FC, memo } from 'react';
import Button from 'shared-resources/components/Button/Button';

interface Props {
  onGetStartedClick: () => void;
}

const GetStarted: FC<Props> = ({ onGetStartedClick }) => (
  <div className='h-[calc(100vh-12.4375rem)] flex flex-col gap-3 justify-center items-center mx-auto rounded-xl bg-white'>
    <p className='font-medium text-base px-24'>
      The following contains a comprehensive set of questions relative to the
      operation of any business that are important to buyers. The questions are
      in alignment with the typical due diligence process of the business where
      Value Enhancement Opportunities exist- opportunities to increase the
      overall value of the business. In this Plan Development phase, the
      business owner is simply asked to answer Yes or No to each question.
      Additionally, the business owner may elect to make an brief comment of
      clarification with the answer. Results of this questionnaire will be
      included in the Transition Plan and will become important part of the Plan
      Implementation process. Take your time when thinking through and recording
      your responses.
    </p>
    <h1 className='px-24 font-medium text-base self-start'>
      The Value Enhancement Opportunities have 12 Pillars:
    </h1>
    <ul className='grid grid-cols-3 max-w-[80%] gap-y-3 font-semibold self-start px-24 gap-x-40 text-base whitespace-nowrap'>
      <li>1. Growth (GR)</li>
      <li>5. Product (PD)</li>
      <li>9. Marketing (MK)</li>
      <li>2. Owner Reliance (OR)</li>
      <li>6. Customers (CU)</li>
      <li>10. Property, Plant, Equipment (PP)</li>
      <li>3. Leadership Team (LT)</li>
      <li>7. Systems & Processes (SP)</li>
      <li>11. Legal (LE)</li>
      <li>4. Employees (EM)</li>
      <li>8. Sales (SA)</li>
      <li>12. Financial (FN)</li>
    </ul>
    <div>
      <Button className='px-6 py-2 mt-10' onClick={() => onGetStartedClick()}>
        Get Started
      </Button>
    </div>
  </div>
);
export default memo(GetStarted);
