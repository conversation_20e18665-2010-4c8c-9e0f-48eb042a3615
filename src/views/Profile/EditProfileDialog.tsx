import React, { FC, memo, useEffect, useRef } from 'react';
import { Form, Formik } from 'formik';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import { User } from 'models/entities/User';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { updateUserProfileAction } from 'store/actions/user.action';
import {
  getUserUpdateProfileLoading,
  userErrors,
} from 'store/selectors/user.selector';
import { UserType } from 'types/enum';
import { valuesTobeUpdated } from 'utils/helpers/Helpers';
import * as Yup from 'yup';

type EditProfileDialogProps = {
  user?: User;
  updateByAdmin?: boolean;
  onClose: () => void;
};

const validationSchema = Yup.object({
  first_name: Yup.string().required('First Name is required'),
  last_name: Yup.string().required('Last Name is required'),
  phone: Yup.string().matches(
    /^(\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/,
    'Phone number not from US region'
  ),
  street_address: Yup.string().optional(),
  zip_code: Yup.string()
    .trim()
    .matches(
      /^(\d{5}[-\s]?(?:\d{4})?|[A-Za-z]\d[A-Za-z][-\s]?\d[A-Za-z]\d)$/gm,
      'ZIP (Postal) Code not from US or Canadian region'
    )
    .optional(),
});

const EditProfileDialog: FC<EditProfileDialogProps> = ({ onClose, user, updateByAdmin }) => {
  const dispatch = useDispatch();
  const updateProfileLoading = useSelector(getUserUpdateProfileLoading);
  const isUserBusinessOwner = user?.type === UserType.BUSINESS_OWNER;
  const isUserAdvisor = user?.type === UserType.ADVISOR;
  const businessOwnerValues = isUserBusinessOwner
    ? {
        street_address : (user as BusinessOwner)?.street_address || '',
        zip_code : (user as BusinessOwner)?.zip_code || '',
        business_name : (user as BusinessOwner)?.business_name || '',
      }
    : {};
    const advisorValues = isUserAdvisor
    ? {
        company_name: user.company_name || '',
        company_address: user?.company_address || '',
      }
    : {};

    const initialValues = {
    first_name: user?.first_name || '',
    last_name: user?.last_name || '',
    phone: user?.phone || '',
    ...businessOwnerValues,
    ...advisorValues,
  };

  const formRef: any = useRef();

  const updateUserErrors: any = useSelector(userErrors);

  useEffect(() => {
    if (updateUserErrors) {
      updateUserErrors.map((error: any) =>
        formRef.current.setFieldError(
          error.field,
          error.message.replace('_', ' ')
        )
      );
    }
  }, [updateUserErrors]);

  const handleSubmit = (values: any) => {
    let changedValues;
    if (user && isUserBusinessOwner) {
       changedValues = 
        {
          street_address: values?.street_address,
          zip_code: values?.zip_code,
          business_name: values?.business_name,
        }
      
    } else if (user && isUserAdvisor) {
       changedValues = 
        {
          company_name: values?.company_name,
          company_address: values?.company_address
        }
    }

    const update_by = updateByAdmin ? UserType.ADMIN : UserType.ADVISOR;

    dispatch(
      updateUserProfileAction({
        ...(changedValues as any),
        first_name: values?.first_name,
        last_name: values?.last_name,
        phone: values?.phone,
        userType: user?.type as UserType,
        update_by,
        user_id: user?.id,
        onSuccess: (message: string) => {
          toast.success(message);
          onClose();
        },
      })
    );
  }

  return (
    <Formik
      innerRef={formRef}
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
    >
      <Form>
        <div className='w-[46rem] rounded-xl bg-white shadow-lg relative'>
          <div className='p-6 space-y-3 mt-3'>
            <FormikInput
              asterisk
              id='first_name'
              name='first_name'
              type='text'
              label='First Name'
              className='border-2 w-full p-2 disabled:cursor-not-allowed !border-blue-01'
            />
            <FormikInput
              asterisk
              id='last_name'
              name='last_name'
              type='text'
              label='Last Name'
              className='border-2 w-full p-2 disabled:cursor-not-allowed !border-blue-01'
            />
            <FormikInput
              id='phone'
              name='phone'
              type='text'
              label='Phone'
              className='border-2 w-full p-2 disabled:cursor-not-allowed !border-blue-01'
            />

            {isUserAdvisor && (
              <>
                <FormikInput
                  name='company_name'
                  key='company_name'
                  label='Company Name'
                  labelClassName='font-medium leading-6.5'
                  className='border-2 w-full p-2 disabled:cursor-not-allowed !border-blue-01'
                />
                <FormikInput
                  name='company_address'
                  key='company_address'
                  label='Company Address'
                  labelClassName='font-medium leading-6.5'
                  className='border-2 w-full p-2 disabled:cursor-not-allowed !border-blue-01'
                />
              </>
            )}

            {isUserBusinessOwner && (
              <>
              <FormikInput
                  name='business_name'
                  key='business_name'
                  label='Business Name'
                  labelClassName='font-medium leading-6.5'
                  className='border-2 w-full p-2 disabled:cursor-not-allowed !border-blue-01'
                />
                <FormikInput
                  name='street_address'
                  key='street_address'
                  label='Business Address'
                  labelClassName='font-medium leading-6.5'
                  className='border-2 w-full p-2 disabled:cursor-not-allowed !border-blue-01'
                />
                <FormikInput
                  name='zip_code'
                  key='zip_code'
                  label='ZIP (Postal) Code'
                  labelClassName='font-medium leading-6.5'
                  className='border-2 w-full p-2 disabled:cursor-not-allowed !border-blue-01'
                />
              </>
            )}
            <div className='flex justify-end gap-4'>
              <Button
                isSubmitting={updateProfileLoading}
                disabled={updateProfileLoading}
                type='submit'
                className='bg-blue-01 text-white py-2 px-6'
              >
                Save
              </Button>
              <Button
                theme='secondary'
                type='button'
                onClick={onClose}
                className='border py-2 px-6'
                disabled={updateProfileLoading}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      </Form>
    </Formik>
  );
};

export default memo(EditProfileDialog);
