import { Form, Formik } from 'formik';
import React from 'react';
import { FaEnvelope } from 'react-icons/fa';
import { useDispatch, useSelector } from 'react-redux';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { forgotPasswordAction } from 'store/actions/forgot-password.action';
import { forgotPasswordLoadingSelector } from 'store/selectors/forgot-password.selectors';
import { UserRouteType, UserType } from 'types/enum';
import * as yup from 'yup';
import { useLocation } from 'react-router-dom';
import logo from '../assets/ExitSmartsLogo.svg';

const forgotPasswordInSchema = yup.object().shape({
  email: yup
    .string()
    .required('Email is required')
    .email('This must be a valid e-mail'),
});

interface ForgotPasswordForm {
  email: string;
}
const ForgotPassword: React.FC = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const forgotPasswordLoading = useSelector(forgotPasswordLoadingSelector);
  const initialValues: ForgotPasswordForm = {
    email: '',
  };
  const path = location.pathname.split('/')[1] ?? UserType.ADVISOR;

  const handleSubmit = (values: ForgotPasswordForm) => {
    dispatch(
      forgotPasswordAction({
        email: values.email,
        userType: path as UserRouteType,
      })
    );
  };

  return (
    <div className='flex flex-col justify-center gap-11 items-center h-screen'>
      <img src={logo} alt='' className='h-28 w-135' />
      <div className='w-135 h-[26.6875rem] bg-gray-01 rounded-xl p-12'>
        <Formik
          initialValues={initialValues}
          onSubmit={handleSubmit}
          validationSchema={forgotPasswordInSchema}
        >
          {(formikProps) => (
            <Form
              onSubmit={formikProps.handleSubmit}
              className='h-full w-full flex flex-col'
            >
              <div className='flex flex-col gap-8.5 mb-8.5'>
                <h1 className=' text-xl font-medium text-center'>
                  Forgot Password
                </h1>
                <h2 className='text-gray-02 px-6 leading-6 font-medium text-center'>
                  Enter your e-mail address and a link will be sent to forgot
                  your password.
                </h2>
              </div>
              <FormikInput
                trailingIcon={<FaEnvelope className='text-gray-400' />}
                name='email'
                type='email'
                label='Email Address'
                placeholder='Enter email address'
                className='h-12.5 outline-none rounded-xl'
                labelClassName='font-medium leading-6.5'
              />
              <Button
                className='leading-7 h-13 mt-3.5'
                type='submit'
                disabled={
                  !formikProps.isValid &&
                  !formikProps.dirty &&
                  !forgotPasswordLoading
                }
                isSubmitting={forgotPasswordLoading}
              >
                <div className='flex justify-center font-semibold items-center'>
                  Submit
                </div>
              </Button>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default ForgotPassword;
