import { ManualFollowUpDTO } from 'dtos/follow-up.dto';
import { AssessmentTools, NonAssessmentToolTabs } from 'types/enum';
import { FollowUpActionType } from './actions.constants';

export const fetchFollowUps = (payload: {
  businessOwnerId: number;
  assessmentTool: AssessmentTools | NonAssessmentToolTabs;
}) => ({
  type: FollowUpActionType.GET_FOLLOW_UPS,
  payload,
});

export const followUpCreate = (
  payload: ManualFollowUpDTO,
  callback: () => void
) => ({
  type: FollowUpActionType.CREATE_FOLLOW_UP,
  payload: { payload, callback },
});

export const followUpCancel = (payload: {
  businessOwnerId: number;
  assessmentTool: AssessmentTools | NonAssessmentToolTabs;
}) => ({
  type: FollowUpActionType.CANCEL_UPCOMING_FOLLOW_UP,
  payload,
});
