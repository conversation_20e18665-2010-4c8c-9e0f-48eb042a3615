import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import Checkbox from 'shared-resources/components/CheckBox/Checkbox';
import {
  AssessmentTools,
  RouteKey,
  UserType,
  OwnerRelianceTabs,
} from 'types/enum';
import { fetchAssessmentReport } from 'store/actions/assessment-report.action';
import { fetchComments } from 'store/actions/tool-comment.action';
import {
  getAssessmentReportLoading,
  getAssessmentReportResponse,
} from 'store/selectors/assessment-report.selector';
import { getToolComments } from 'store/selectors/tool-comment.selector';
import { getUserData } from 'store/selectors/user.selector';
import { renderToString } from 'react-dom/server';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import TableComponent from 'views/Owner-Reliance/TableComponent';
import { calculateOwnerReliancePercentageScore } from 'views/Owner-Reliance/OwnerRelianceReportHelper';
import { titleCaseAndRemoveUnderScoreOrHyphen } from 'utils/helpers/Helpers';
import { resetAssessmentData } from 'store/reducers/assessment-tool.reducer';

interface OwnerRelianceBuilderProps {
  onSelectionChange: (selectedData: {
    [key: string]: { content: string; type: string; comments?: any[] };
  }) => void;
}

const OwnerRelianceBuilder: React.FC<OwnerRelianceBuilderProps> = ({
  onSelectionChange,
}) => {
  const dispatch = useDispatch();
  const { id } = useParams<{ id: string }>();
  const [selectedTabs, setSelectedTabs] = useState<{
    [key: string]: boolean;
  }>({});
  const [selectedComments, setSelectedComments] = useState<{
    [key: string]: boolean;
  }>({});

  const loggedInUserData = useSelector(getUserData);
  const response: any = useSelector(getAssessmentReportResponse);
  const navigate = useNavigate();
  const assessmentLoading = useSelector(getAssessmentReportLoading);
  const comments = useSelector(getToolComments);

  useEffect(() => {
    if (id && loggedInUserData?.type === UserType.ADVISOR) {
      dispatch(
        fetchAssessmentReport({
          id,
          tool: AssessmentTools.OWNER_RELIANCE,
          onError: () =>
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`),
        })
      );
      dispatch(fetchComments(Number(id), AssessmentTools.OWNER_RELIANCE));
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  const handleCheckboxChange = useCallback((tab: string) => {
    setSelectedTabs((prev) => ({
      ...prev,
      [tab]: !prev[tab],
    }));
  }, []);

  const handleCommentCheckboxChange = useCallback((commentId: string) => {
    setSelectedComments((prev) => ({
      ...prev,
      [commentId]: !prev[commentId],
    }));
  }, []);

  const renderTabDataForQuill = useCallback(
    (tab: string) => {
      const tabData = response.assessment_response[tab];
      if (!tabData) return null;
      const { scorePercentage } =
        calculateOwnerReliancePercentageScore(tabData);

      return (
        <TableComponent
          showReportTitle
          currentTab={tab as OwnerRelianceTabs}
          response={response?.assessment_response ?? {}}
          isRenderedForReport
          scorePercentage={scorePercentage}
          classname='pt-2'
        />
      );
    },
    [response.assessment_response]
  );

  const selectedData = useMemo(() => {
    const data: {
      [key: string]: { content: string; type: string; comments?: any[] };
    } = {};

    Object.keys(selectedTabs)
      .filter((tab) => selectedTabs[tab])
      .forEach((tab) => {
        const content = renderToString(
          <div className='relative'>
            <span className='text-lg font-semibold mb-2'>
              {titleCaseAndRemoveUnderScoreOrHyphen(tab)}
            </span>
            {renderTabDataForQuill(tab)}
          </div>
        );

        const tabComments = comments
          .filter(
            (comment) =>
              comment.metadata?.screen === tab &&
              selectedComments[comment.id.toString()]
          )
          .map((comment) => ({
            id: comment.id,
            comment: comment.comment,
            metadata: comment.metadata,
          }));

        data[tab] = {
          content,
          type: tab,
          comments: tabComments,
        };
      });

    return data;
  }, [selectedTabs, selectedComments, renderTabDataForQuill, comments]);

  useEffect(() => {
    if (Object.keys(selectedData).length > 0) {
      onSelectionChange(selectedData);
    }
  }, [selectedData, onSelectionChange]);

  useEffect(
    () => () => {
      dispatch(resetAssessmentData());
    },
    []
  );

  const renderTabData = (tab: string) => {
    const tabComments = comments.filter(
      (comment) => comment.metadata?.screen === tab
    );
    return (
      response.assessment_response[tab] && (
        <div key={tab} className='mt-4 flex flex-col gap-4 w-full relative'>
          <Checkbox
            onChange={() => handleCheckboxChange(tab)}
            value={selectedTabs[tab] || false}
            text={titleCaseAndRemoveUnderScoreOrHyphen(tab) as any}
            className='!mt-0'
          />
          {renderTabDataForQuill(tab)}
          {tabComments.length > 0 ? (
            <div className='mt-2'>
              <span className='font-semibold mb-2'>Comments:</span>
              {tabComments.map((comment) => (
                <div key={comment.id} className='ml-4 mb-2 flex items-center'>
                  <Checkbox
                    onChange={() =>
                      handleCommentCheckboxChange(comment.id.toString())
                    }
                    value={selectedComments[comment.id.toString()] || false}
                    text={comment.comment as any}
                    className='!mt-0'
                  />
                </div>
              ))}
            </div>
          ) : null}
        </div>
      )
    );
  };

  if (assessmentLoading)
    return (
      <div className='h-[calc(100vh-18rem)]'>
        <Spinner />
      </div>
    );

  return (
    <div className='flex flex-col gap-4 h-[calc(100vh-18rem)] overflow-y-auto scrollbar pr-4'>
      {Object.values(OwnerRelianceTabs).map(
        (tab) => tab !== OwnerRelianceTabs.MANAGEMENT_TEAM && renderTabData(tab)
      )}
    </div>
  );
};

export default OwnerRelianceBuilder;
