import { BusinessOwner } from 'models/entities/BusinessOwner';
import React, { useState } from 'react';
import { BiSolidPencil } from 'react-icons/bi';
import { useDispatch, useSelector } from 'react-redux';
import Button from 'shared-resources/components/Button/Button';
import { followUpCreate } from 'store/actions/follow-up.action';
import { followUpLoading } from 'store/selectors/follow-up-selector';
import { AssessmentTools, NonAssessmentToolTabs } from 'types/enum';

interface FollowUpModalProps {
  businessOwner: BusinessOwner;
  tool: AssessmentTools | NonAssessmentToolTabs;
  handleModalVisibility: (value: boolean) => void;
  handleFollowUpSuccessVisibility: (value: boolean) => void;
}
const FollowUpModal: React.FC<FollowUpModalProps> = (props) => {
  const {
    handleModalVisibility,
    handleFollowUpSuccessVisibility,
    businessOwner,
    tool,
  } = props;
  const [followUpText, setFollowUpText] = useState('');
  const dispatch = useDispatch();
  const handleOnChange = (e: any) => {
    setFollowUpText(e.target.value);
  };

  const onSuccess = () => {
    handleModalVisibility(false);
    handleFollowUpSuccessVisibility(true);
  };

  const loading = useSelector(followUpLoading);

  const handleSubmit = () => {
    dispatch(
      followUpCreate(
        {
          businessOwnertId: +businessOwner.id,
          follow_up_text: followUpText,
          tool,
        },
        onSuccess
      )
    );
  };

  return (
    <div className='flex w-full rounded-xl h-full bg-white flex-col p-6 gap-4'>
      <div className='flex justify-between'>
        <h2 className='font-medium'>Quick Follow Up Text</h2>
        <BiSolidPencil size={20} className='min-w-5 h-5' />
      </div>
      <textarea
        value={followUpText}
        onChange={handleOnChange}
        placeholder='Quick Follow Up Text'
        className='border-2 text-sm outline-none p-4 border-blue-01 rounded-xl min-h-32 w-full'
      />
      <div className='flex justify-center mt-4'>
        <Button
          className='px-6 py-2'
          isSubmitting={loading}
          onClick={handleSubmit}
        >
          Send Quick Follow Up
        </Button>
      </div>
    </div>
  );
};

export default FollowUpModal;
