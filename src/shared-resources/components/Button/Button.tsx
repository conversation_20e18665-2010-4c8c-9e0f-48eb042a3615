import classNames from 'classnames';
import React from 'react';
import { IconType } from 'react-icons';
import Spinner from '../Spinner/Spinner';

type ThemeType = 'primary' | 'secondary' | 'tertiary' | 'success' | 'danger';

interface Props {
  type?: 'button' | 'submit';
  className?: string;
  onClick?: (e?: any) => void;
  children?: React.ReactNode;
  disabled?: boolean;
  isSubmitting?: boolean;
  theme?: ThemeType;
  TrailingIcon?: IconType;
  LeadingIcon?: IconType;
  leadingIconClassName?: string;
  trailingIconClassName?: string;
  loaderColor?: string;
}

const Button: React.FC<Props> = ({
  type,
  className,
  children,
  onClick,
  disabled,
  isSubmitting,
  TrailingIcon,
  theme = 'primary',
  LeadingIcon,
  leadingIconClassName,
  trailingIconClassName,
  loaderColor,
}) => {
  const themeClasses = classNames(
    theme === 'primary' && 'text-white border border-blue-01 bg-blue-01',
    theme === 'secondary' && 'text-gray-03 bg-white border-gray-03 border',
    theme === 'tertiary' && 'text-blue-01 bg-white border-blue-01 border',
    theme === 'success' && 'text-white border border-green-500 bg-green-500',
    theme === 'danger' && 'text-white border border-red-500 bg-red-500'

  );

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={classNames(
        'inline-flex relative rounded-lg font-medium justify-center max-h-12.5 items-center focus:outline-none transition ease-in-out duration-150',
        themeClasses,
        disabled ? 'pointer-events-none opacity-50' : '',
        className
      )}
    >
      <div className='flex h-full items-center'>
        {LeadingIcon && (
          <LeadingIcon
            onClick={onClick}
            className={classNames('w-4 h-4', leadingIconClassName)}
          />
        )}
        {isSubmitting && (
          <Spinner size='xs' border={loaderColor || 'border-white-300'} />
        )}
        {!isSubmitting && children}
        {!isSubmitting && TrailingIcon && (
          <TrailingIcon
            className={classNames('w-4 h-4', trailingIconClassName)}
          />
        )}
      </div>
    </button>
  );
};

export default React.memo(Button);
