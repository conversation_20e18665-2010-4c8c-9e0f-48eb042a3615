import React from 'react';
import classNames from 'classnames';
import { AwarenessTab } from 'types/business-owner.types';
import { MdOutlineAttachFile } from 'react-icons/md';

interface Props {
  isSelected: boolean;
  tab: AwarenessTab;
  onHandleTabChange?: (tab: AwarenessTab) => void;
  completed?: boolean;
}

const AssessmentTabButton: React.FC<Props> = (props) => {
  const { isSelected, tab, onHandleTabChange, completed } = props;

  const handleClick = () => {
    if (onHandleTabChange && !isSelected) {
      onHandleTabChange(tab); // Call the onClick function with the id as argument
    }
  };

  return (
    <button
      className={classNames(
        'flex outline-none',
        !isSelected ? 'cursor-pointer' : 'cursor-default',
        tab?.disabled ? '!cursor-not-allowed opacity-50' : 'cursor-default'
      )}
      onClick={handleClick}
      id={`${tab?.value}`}
      disabled={tab?.disabled}
    >
      <div
        className={classNames(
          'rounded-lg w-full flex items-center  justify-center  capitalize text-center px-6 py-2 font-medium text-black-02',
          { 'bg-blue-01 text-white': isSelected }
        )}
      >
        {tab?.name}
        {completed && (
          <div className='ml-1'>
            <MdOutlineAttachFile />
          </div>
        )}
      </div>
    </button>
  );
};

export default AssessmentTabButton;
