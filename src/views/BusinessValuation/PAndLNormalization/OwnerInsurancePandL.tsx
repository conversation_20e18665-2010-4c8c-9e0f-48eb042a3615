import { FieldArray, Form, Formik } from 'formik';
import React from 'react';
import { IoInformationCircle } from 'react-icons/io5';
import { useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { getKey } from 'utils/helpers/Helpers';
import * as yup from 'yup';
import { getYear } from '../BusinessValuationConfig';

interface Props {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  data: any;
  onSaveToDraftClick: (data: any) => void;
}
const OwnerInsurancePandL: React.FC<Props> = (props) => {
  const { nextStep, setData, backStep, data, onSaveToDraftClick } = props;
  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup.object().shape({
    [`life_insurance_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`life_insurance_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`life_insurance_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`health_insurance_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`health_insurance_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`health_insurance_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`non_auto_insurance_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`non_auto_insurance_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`non_auto_insurance_${getYear(3)}`]: yup
      .number()
      .positive('Value should be greater than 0')

      .typeError('Must be a valid number')
      .nullable(),
    other_insurance: yup.array().of(
      yup.object({
        [`other_insurance_${getYear(1)}`]: yup
          .number()
          .positive('Value should be greater than 0')
          .typeError('Must be a valid number')
          .nullable(),
        [`other_insurance_${getYear(2)}`]: yup
          .number()
          .positive('Value should be greater than 0')
          .typeError('Must be a valid number')
          .nullable(),
        [`other_insurance_${getYear(3)}`]: yup
          .number()
          .positive('Value should be greater than 0')
          .typeError('Must be a valid number')
          .nullable(),
      })
    ),
  });

  const handleSubmit = (values: any) => {
    setData(values);
    nextStep();
  };

  return (
    <div className='flex flex-col '>
      <Formik
        initialValues={
          data?.owner_insurance || {
            [`life_insurance_${getYear(1)}`]: '',
            [`life_insurance_${getYear(2)}`]: '',
            [`life_insurance_${getYear(3)}`]: '',
            [`health_insurance_${getYear(1)}`]: '',
            [`health_insurance_${getYear(2)}`]: '',
            [`health_insurance_${getYear(3)}`]: '',
            [`non_auto_insurance_${getYear(1)}`]: '',
            [`non_auto_insurance_${getYear(2)}`]: '',
            [`non_auto_insurance_${getYear(3)}`]: '',
            other_insurance: [],
          }
        }
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values }) => (
          <Form className='pl-5 py-3 flex flex-col gap-4 bg-white'>
            <h2 className='text-[1.1rem] font-bold'>
              Owner’s Insurance (Annual Premiums):
            </h2>
            <div className='relative z-10 p-2 text-xl items-center font-semibold'>
              <span className='absolute left-[26%]'>{getYear(1)}</span>
              <span className='absolute left-[51.5%]'>{getYear(2)}</span>
              <span className='absolute left-[77%]'>{getYear(3)}</span>
            </div>
            <div className='h-[calc(100vh-22.5rem)] overflow-auto scrollbar pr-4'>
              <div className='grid grid-cols-4 gap-x-16 gap-y-1 mt-7 text-[1.1rem]'>
                <div
                  id='lifeInsurance'
                  className='flex  items-center gap-2 w-60'
                >
                  <IoInformationCircle className='text-blue-01 ' />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='#lifeInsurance'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      Premiums for life insurance on business owner(s)
                    </Tooltip>
                    <span>Life Insurance</span>
                  </div>
                </div>
                <FormikInput
                  className='!h-10'
                  name={`life_insurance_${getYear(1)}`}
                  key={`life_insurance_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`life_insurance_${getYear(2)}`}
                  key={`life_insurance_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`life_insurance_${getYear(3)}`}
                  key={`life_insurance_${getYear(3)}`}
                />
                <div
                  id='healthInsurance'
                  className='flex items-center gap-2 w-60'
                >
                  <IoInformationCircle className='text-blue-01 ' />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='#healthInsurance'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      Do not include owner&apos;s ealth insurance if health
                      insurance is paid to all the employees.
                    </Tooltip>
                    <span>Health Insurance</span>
                  </div>
                </div>
                <FormikInput
                  className='!h-10'
                  name={`health_insurance_${getYear(1)}`}
                  key={`health_insurance_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`health_insurance_${getYear(2)}`}
                  key={`health_insurance_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`health_insurance_${getYear(3)}`}
                  key={`health_insurance_${getYear(3)}`}
                />

                <div
                  id='otherinsurance'
                  className='flex  items-center gap-2 w-60'
                >
                  <IoInformationCircle className='text-blue-01 flex flex-shrink-0' />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='#otherinsurance'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      Any other insurance premiums paid that Benefits the
                      Business owner(s)
                    </Tooltip>
                    <span>Other (non-auto) insurance</span>
                  </div>
                </div>
                <FormikInput
                  className='!h-10'
                  name={`non_auto_insurance_${getYear(1)}`}
                  key={`non_auto_insurance_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`non_auto_insurance_${getYear(2)}`}
                  key={`non_auto_insurance_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`non_auto_insurance_${getYear(3)}`}
                  key={`non_auto_insurance_${getYear(3)}`}
                />
              </div>

              <FieldArray name='other_insurance'>
                {({ push }) => (
                  <div className=''>
                    {values.other_insurance &&
                      values.other_insurance.length > 0 &&
                      values.other_insurance.map(
                        (other_insurance: any, index: number) => (
                          <div
                            key={getKey(index)}
                            className='grid grid-cols-4 gap-x-16 gap-y-1  text-[1.1rem]'
                          >
                            <div className='flex items-center gap-2 mb-3 ml-5'>
                              <span>Other Insurance</span>
                            </div>
                            <FormikInput
                              className='!h-10'
                              name={`other_insurance.${index}.other_insurance_${getYear(
                                1
                              )}`}
                              key={`other_insurance.${getKey(
                                index
                              )}.other_insurance_${getYear(1)}`}
                            />
                            <FormikInput
                              className='!h-10'
                              name={`other_insurance.${index}.other_insurance_${getYear(
                                2
                              )}`}
                              key={`other_insurance.${getKey(
                                index
                              )}.other_insurance_${getYear(2)}`}
                            />
                            <FormikInput
                              className='!h-10'
                              name={`other_insurance.${index}.other_insurance_${getYear(
                                3
                              )}`}
                              key={`other_insurance.${getKey(
                                index
                              )}.other_insurance_${getYear(3)}`}
                            />
                          </div>
                        )
                      )}
                    <div className='absolute top-[3.5rem] right-[4.2rem]'>
                      <Button
                        type='button'
                        className='px-6 py-2 '
                        onClick={() =>
                          push({
                            [`other_insurance_${getYear(1)}`]: '',
                            [`other_insurance_${getYear(2)}`]: '',
                            [`other_insurance_${getYear(3)}`]: '',
                          })
                        }
                      >
                        Add
                      </Button>
                    </div>
                  </div>
                )}
              </FieldArray>
            </div>
            <div className='pr-7'>
              <BackNextComponent
                backStep={backStep}
                buttonType='submit'
                isLoading={isLoading}
                isNextDisable={false}
                onSaveToDraftClick={() => {
                  onSaveToDraftClick(values);
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default OwnerInsurancePandL;
