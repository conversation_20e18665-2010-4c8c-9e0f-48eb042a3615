import { FieldArray, Form, Formik } from 'formik';
import React from 'react';
import { IoInformationCircle } from 'react-icons/io5';
import { useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { getKey } from 'utils/helpers/Helpers';
import * as yup from 'yup';
import { getYear } from '../BusinessValuationConfig';

interface Props {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  data: any;
  onSaveToDraftClick: (data: any) => void;
}
const RentAdjustmentsPandL: React.FC<Props> = (props) => {
  const { nextStep, setData, backStep, data, onSaveToDraftClick } = props;
  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup.object().shape({
    rentAdjustmentsPandL: yup.array().of(
      yup.object({
        [`actual_rent_paid_minus_market_${getYear(1)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`actual_rent_paid_minus_market_${getYear(2)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`actual_rent_paid_minus_market_${getYear(3)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
      })
    ),
  });

  const handleSubmit = (values: any) => {
    setData(values.rentAdjustmentsPandL);
    nextStep();
  };

  return (
    <div className='flex flex-col '>
      <Formik
        initialValues={{
          rentAdjustmentsPandL: data?.rent_adjustments || [
            {
              [`actual_rent_paid_minus_market_${getYear(1)}`]: '',
              [`actual_rent_paid_minus_market_${getYear(2)}`]: '',
              [`actual_rent_paid_minus_market_${getYear(3)}`]: '',
            },
          ],
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values }) => (
          <Form className='relative  pl-5 py-3  flex flex-col gap-4 bg-white'>
            <h2 className='text-xl font-bold underline'>Rent Adjustments</h2>
            <div className='relative z-10 text-xl mb-3 items-center font-semibold'>
              <span className='absolute left-[25%]'>
                {getYear(1).toString()}
              </span>
              <span className='absolute left-[50.5%]'>
                {getYear(2).toString()}
              </span>
              <span className='absolute left-[75.5%]'>
                {getYear(3).toString()}
              </span>
            </div>

            <FieldArray name='rentAdjustmentsPandL'>
              {({ push }) => (
                <div className='h-[calc(100vh-23rem)] pr-4  overflow-auto scrollbar mt-3'>
                  {values.rentAdjustmentsPandL &&
                    values.rentAdjustmentsPandL.length > 0 &&
                    values.rentAdjustmentsPandL.map(
                      (rentAdjustmentsPandL: any, index: number) => (
                        <div key={getKey(index)} className=''>
                          <div className='grid grid-cols-4 gap-x-8  gap-y-1 mt-2 text-[1.1rem]'>
                            <div
                              id={`rent${index}`}
                              className='flex  items-center gap-2'
                            >
                              <IoInformationCircle className='text-blue-01  fex flex-shrink-0' />
                              <div className='relative'>
                                <Tooltip
                                  anchorSelect={`#rent${index}`}
                                  place='right'
                                  offset={0}
                                  className={` !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1  ${
                                    index > 5 ? '!-top-1' : '!top-8'
                                  } border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg`}
                                  classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                >
                                  If rent paid is different from market-based
                                  rent record here (maybe positive or negative
                                  number could be over paying or under paying)
                                </Tooltip>
                                <span>Actual Rent Paid Minus Market Rent</span>
                              </div>
                            </div>

                            <FormikInput
                              className='!h-10'
                              name={`rentAdjustmentsPandL.${index}.actual_rent_paid_minus_market_${getYear(
                                1
                              )}`}
                              key={`rentAdjustmentsPandL.${getKey(
                                index
                              )}.actual_rent_paid_minus_market_${getYear(1)}`}
                            />
                            <FormikInput
                              className='!h-10'
                              name={`rentAdjustmentsPandL.${index}.actual_rent_paid_minus_market_${getYear(
                                2
                              )}`}
                              key={`rentAdjustmentsPandL.${getKey(
                                index
                              )}.actual_rent_paid_minus_market_${getYear(2)}`}
                            />
                            <FormikInput
                              className='!h-10'
                              name={`rentAdjustmentsPandL.${index}.actual_rent_paid_minus_market_${getYear(
                                3
                              )}`}
                              key={`rentAdjustmentsPandL.${getKey(
                                index
                              )}.actual_rent_paid_minus_market_${getYear(3)}`}
                            />
                          </div>
                        </div>
                      )
                    )}
                  <div className='absolute top-[3rem] right-[3rem]'>
                    <Button
                      type='button'
                      className='px-6 py-2 !absolute right-0'
                      onClick={() =>
                        push({
                          [`actual_rent_paid_minus_market_${getYear(1)}`]: '',
                          [`actual_rent_paid_minus_market_${getYear(2)}`]: '',
                          [`actual_rent_paid_minus_market_${getYear(3)}`]: '',
                        })
                      }
                    >
                      Add
                    </Button>
                  </div>
                </div>
              )}
            </FieldArray>

            <div className='pr-7'>
              <BackNextComponent
                backStep={backStep}
                buttonType='submit'
                isLoading={isLoading}
                isNextDisable={false}
                onSaveToDraftClick={() => {
                  onSaveToDraftClick(values.rentAdjustmentsPandL);
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default RentAdjustmentsPandL;
