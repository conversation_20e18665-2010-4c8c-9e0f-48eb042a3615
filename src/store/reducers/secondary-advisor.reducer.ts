import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface SecondaryAdvisorState {
  entities: { email: string; message: string };
  loading?: boolean;
  error?: string;
  loadingOne?: boolean;
}

const initialState: SecondaryAdvisorState = {
  entities: {
    email: '',
    message: '',
  },
};

const secondaryAdvisorSlice = createSlice({
  name: 'secondaryAdvisor',
  initialState,
  reducers: {
    verficationCompleted: (
      state,
      action: PayloadAction<{ email: string; message: string }>
    ) => {
      state.entities = action.payload;
    },
    addLoading: (state, action: PayloadAction<{ loading: boolean }>) => {
      const { loading } = action.payload;
      state.loading = loading;
    },
    addLoadingOne: (state, action: PayloadAction<{ loading: boolean }>) => {
      const { loading } = action.payload;
      state.loadingOne = loading;
    },
  },
});

export const {
  verficationCompleted: verficationCompletedAction,
  addLoading: addVerifyLoading,
  addLoadingOne: addVerifyLoadingOne,
} = secondaryAdvisorSlice.actions;

export default secondaryAdvisorSlice.reducer;
