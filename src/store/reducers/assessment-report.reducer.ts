/* eslint-disable @typescript-eslint/naming-convention */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

const initialState = {
  assessment_response: {},
  business_owner_data: {
    business_owner_name: '',
    business_owner_email: '',
    business_owner_phone: '',
  },
  advisor_name: '',
  assessment_completion_date: '',
  error: '',
  loading: false,
};

export const assessmentReportSlice = createSlice({
  name: 'assessment-tool-slice',
  initialState,
  reducers: {
    addReport: (state, action: PayloadAction<any>) => {
      const {
        assessment_response,
        business_owner_name,
        business_owner_email,
        business_owner_phone,
        advisor_name,
        assessment_completion_date,
      } = action.payload;
      state.assessment_response = assessment_response;
      state.advisor_name = advisor_name;
      state.assessment_completion_date = assessment_completion_date;
      state.business_owner_data = {
        business_owner_name,
        business_owner_email,
        business_owner_phone,
      };
    },
    addLoading: (state, action: PayloadAction<{ loading: boolean }>) => {
      const { loading } = action.payload;
      state.loading = loading;
    },
    addError: (state, action: PayloadAction<{ message: string }>) => {
      const { message } = action.payload;
      state.error = message;
      state.loading = false;
    },
    resetData: (state) => {
      state.assessment_response = {};
      state.advisor_name = '';
      state.assessment_completion_date = '';
      state.business_owner_data = {
        business_owner_name: '',
        business_owner_email: '',
        business_owner_phone: '',
      };
    },
  },
});

export const {
  addReport: addAssessmentReport,
  addLoading: addAssessmentReportLoading,
  addError: addAssessmentReportError,
  resetData: resetAssessmentReportData,
} = assessmentReportSlice.actions;

export default assessmentReportSlice.reducer;
