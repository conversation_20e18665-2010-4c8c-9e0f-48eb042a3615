import * as yup from 'yup';
import React, { useEffect, useState } from 'react';
import Button from 'shared-resources/components/Button/Button';
import { passwordSchema } from 'utils/helpers/Helpers';
import { Form, Formik } from 'formik';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  signUpSecondaryAdvisor,
  verifySecondaryAdvisorByToken,
} from 'store/actions/secondary-advisor.action';
import { useDispatch, useSelector } from 'react-redux';
import {
  isSecondaryAdvisorDataSelector,
  isSecondaryAdvisorLoadingOneSelector,
  isSecondaryAdvisorLoadingSelector,
} from 'store/selectors/secondary-advisor.selector';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import logo from '../../assets/ExitSmartsLogo.svg';

const AdvisorSignUp: React.FC = () => {
  const navigate = useNavigate();

  const secondaryAdvisorLoading = useSelector(
    isSecondaryAdvisorLoadingSelector
  );
  const secondaryAdvisorLoadingOne = useSelector(
    isSecondaryAdvisorLoadingOneSelector
  );
  const secondaryAdvisorData = useSelector(isSecondaryAdvisorDataSelector);
  const [tokenInvalid, setTokenInvalid] = useState<boolean>();
  const dispatch = useDispatch();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [searchParams, setSearchParams] = useSearchParams();
  const token = searchParams.get('token');
  const changePasswordSchema = yup.object().shape({
    password: passwordSchema.required('Password is required'),
    confirmPassword: yup
      .string()
      .oneOf([yup.ref('password')], 'Confirm Password do not match')
      .required('Confirm Password is required'),
    phone: yup
      .string().required('PhoneNo is required')
      .matches(
        /^(\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/,
        'Phone number not from US region'
      ),
    last_name: yup.string().required('Last Name is required'),
    first_name: yup.string().required('First Name is required'),
    email: yup.string().email().required('Email is required'),
    company_name: yup.string().required('CompanyName is required'),

  });

  const onSubmitSetPassword = (values: any) => {
    dispatch(
      signUpSecondaryAdvisor({
        token: token as string,
        onSucess: () => {
          navigate('/advisor/login');
        },
        ...values,
      })
    );
  };
  useEffect(() => {
    dispatch(
      verifySecondaryAdvisorByToken({
        token: token as string,
        onError: () => {
          setTokenInvalid(true);
        },
      })
    );
  }, []);

  if (secondaryAdvisorLoading) {
    return <Spinner spinnerTheme='overlaySpinner' />;
  }
  return (
    <div className='flex justify-center flex-col h-screen items-center'>
      <img src={logo} alt='' className='h-28 w-135' />
      {tokenInvalid ? (
        <div className='text-4xl'>Your Token is invalid </div>
      ) : (
        <div className='w-135   bg-gray-01 rounded-xl px-12.5 py-8  mb-2'>
          <Formik
            initialValues={{
              password: '',
              confirmPassword: '',
              last_name: '',
              first_name: '',
              email: secondaryAdvisorData?.email,
              phone: '',
            }}
            onSubmit={(values) => {
              onSubmitSetPassword(values);
            }}
            validationSchema={changePasswordSchema}
            validateOnBlur
          >
            {(formikProps) => (
              <Form
                onSubmit={formikProps.handleSubmit}
                className='flex flex-col gap-7'
              >
                <div className='flex flex-col max-w-2/3 gap-2'>
                  <FormikInput
                    name='email'
                    id='email'
                    type='email'
                    disabled
                    className='h-[2.85rem] outline-none rounded-xl'
                    label='Email'
                    labelClassName='font-medium'
                    requiredLabel
                  />
                  <FormikInput
                    asterisk
                    name='first_name'
                    id='first_name'
                    type='text'
                    className='h-[2.85rem] outline-none rounded-xl'
                    label='First Name'
                    labelClassName='font-medium'
                    requiredLabel
                  />
                  <FormikInput
                    asterisk
                    name='last_name'
                    id='last_name'
                    type='text'
                    className='h-[2.85rem] outline-none rounded-xl'
                    label='Last Name'
                    labelClassName='font-medium'
                    requiredLabel
                  />
                  <FormikInput
                    asterisk
                    name='phone'
                    id='phone'
                    type='text'
                    className='h-[2.85rem] outline-none rounded-xl'
                    label='Phone'
                    labelClassName='font-medium'
                    requiredLabel
                  />
                  <FormikInput
                    asterisk
                    name='company_name'
                    key='company_name'
                    type='text'
                    className='h-[2.85rem] outline-none rounded-xl'
                    label='Company Name'
                    labelClassName='font-medium leading-6.5'
                    requiredLabel
                  />

                  <FormikInput
                    name='company_address'
                    key='company_address'
                    type='text'
                    className='h-[2.85rem] outline-none rounded-xl'
                    label='Company Address'
                    labelClassName='font-medium leading-6.5'
                  />
                  <FormikInput
                    asterisk
                    name='password'
                    id='password'
                    type='password'
                    className='h-[2.85rem] outline-none rounded-xl'
                    label='Password'
                    labelClassName='font-medium'
                    requiredLabel
                  />
                  <FormikInput
                    asterisk
                    name='confirmPassword'
                    id='confirmPassword'
                    type='password'
                    className='h-[2.85rem] outline-none rounded-xl'
                    label='Confirm  Password'
                    labelClassName='font-medium'
                    requiredLabel
                  />
                </div>
                <div className='flex space-x-4 justify-end'>
                  <Button
                    className='px-6 py-2'
                    isSubmitting={secondaryAdvisorLoadingOne}
                    disabled={
                      !formikProps.dirty ||
                      secondaryAdvisorLoadingOne ||
                      !formikProps.isValid
                    }
                  >
                    Submit
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      )}
    </div>
  );
};

export default AdvisorSignUp;