/* eslint-disable jsx-a11y/label-has-associated-control */
import React from 'react';
import { CheckboxProps } from 'shared-resources/types/Checbox.type';
import InputHelper from '../Inputhelper/InputHelper';

const Checkbox: React.FC<CheckboxProps> = ({
  onChange,
  error,
  value,
  disabled,
  text,
  inputValue,
  className,
}) => (
  <>
    <div className='flex items-center'>
      <input
        value={inputValue}
        onChange={onChange}
        checked={value}
        disabled={disabled}
        type='checkbox'
        className={`min-w-5 h-5 mt-1.25 checked:bg-white bg-white text-white ${className}`}
      />
      {text && <div className='ml-3'>{text}</div>}
    </div>
    <InputHelper type='error' text={error} />
  </>
);

export default Checkbox;
