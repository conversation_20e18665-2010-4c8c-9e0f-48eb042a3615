import { createSelector } from 'reselect';
import { AppState } from 'store';

const followUpStore = (store: AppState) => store.followUp;

export const getFollowUpHistory = createSelector(
  [followUpStore],
  (followUp) => followUp.followUpHistory
);

export const getFollowUpSchedule = createSelector(
  [followUpStore],
  (followUp) => followUp.followUpSchedule
);

export const getSentFollowUpText = createSelector(
  [followUpStore],
  (followUp) => followUp.sentFollowUpText
);

export const followUpLoading = createSelector(
  [followUpStore],
  (followUp) => followUp.loading
);
