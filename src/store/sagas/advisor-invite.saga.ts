import { AdvisorInviteDTO } from 'dtos/invite-advisor.dto';
import { toast } from 'react-toastify';
import { call, takeLatest, all, put } from 'redux-saga/effects';
import { advisorInviteService } from 'services/api-services/AdvisorInviteService';
import { AdvisorInviteActionType } from 'store/actions/actions.constants';
import {
  cancelOrRevokeAdvisorInvite,
  setAdvisorInviteLoading,
  setAdvisorInvitesData,
  updateAdvisorInvitesData,
} from 'store/reducers/advisor-invite.reducer';
import { InviteStatus } from 'types/enum';
import { SagaPayloadType } from 'types/SagaPayload.type';

interface SecondaryAdvisorInvitePayloadType extends SagaPayloadType {
  payload: AdvisorInviteDTO;
}

interface AdvisorInvitesPayloadType extends SagaPayloadType {
  payload: { id: number, advisorId: number };
}
interface CancelOrRevokePayloadType extends SagaPayloadType {
  payload: { id: number; status: InviteStatus };
}
function* inviteSecondaryAdvisorSaga(
  action: SecondaryAdvisorInvitePayloadType
): any {
  try {
    yield put(setAdvisorInviteLoading(true));
    const response = yield call(
      advisorInviteService.inviteSecondaryAdvisor,
      action.payload
    );

    // setting response
    yield put(updateAdvisorInvitesData(response?.invite));
    yield put(setAdvisorInviteLoading(false));
    toast.success(response?.message, {
      position: 'top-right',
    });
  } catch (e: any) {
    yield put(setAdvisorInviteLoading(false));
  }
}

function* getAdvisorInvitesSaga(action: AdvisorInvitesPayloadType): any {
  try {
    const response = yield call(
      advisorInviteService.getAdvisorInvites,
      action.payload.id,
      action.payload.advisorId
    );
    yield put(setAdvisorInvitesData(response?.invites));
  } catch (e: any) {
    yield put(setAdvisorInviteLoading(false));
  }
}

function* cancelOrRevokeAdvisorInviteSaga(
  action: CancelOrRevokePayloadType
): any {
  try {
    const response = yield call(
      advisorInviteService.cancelOrRevokeAdvisorInvite,
      action.payload
    );

    yield put(cancelOrRevokeAdvisorInvite(action.payload.id));
    toast.success(response.message, {
      position: 'top-right',
    });
  } catch (e: any) {
    yield put(setAdvisorInviteLoading(false));
  }
}

export function* advisorInviteSagaWatcher() {
  yield all([
    takeLatest(
      AdvisorInviteActionType.INVITE_ADVISOR,
      inviteSecondaryAdvisorSaga
    ),
    takeLatest(AdvisorInviteActionType.GET_INVITES, getAdvisorInvitesSaga),
    takeLatest(
      AdvisorInviteActionType.CANCEL_OR_REVOKE_INVITE,
      cancelOrRevokeAdvisorInviteSaga
    ),
  ]);
}
