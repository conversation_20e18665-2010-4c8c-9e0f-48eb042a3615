import { Form, Formik, FormikValues } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import FormikRadio from 'shared-resources/components/Radio/FormikRadio';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import * as yup from 'yup';
import {
  Screen14Keys,
  YesNoOptions,
  getQuestionBykey,
} from '../AssetProtectionConfig';

interface Props {
  data: any;
  handleNextClick: (values: FormikValues) => void;
  handlebackClick: (values: FormikValues) => void;
  handleSaveAsDraftClick: (values: FormikValues) => void;
}
const Screen14: React.FC<Props> = ({
  handleNextClick,
  handlebackClick,
  handleSaveAsDraftClick,
  data,
}) => {
  const validationSchema = yup.object().shape({
    [Screen14Keys.DOES_YOUR_COMPANY_HAVE_BRAND]: yup
      .string()
      .required('Value is required'),
    [Screen14Keys.HAVE_TRADEMARK_PROTECTED]: yup
      .string()
      .when([Screen14Keys.DOES_YOUR_COMPANY_HAVE_BRAND], {
        is: (value: string) => value === 'yes',
        then: (schema) => schema.required('value is Required'),
        otherwise: (schema) => schema.nullable(),
      }),
  });

  const initialValue = data?.screen14 || {
    [Screen14Keys.DOES_YOUR_COMPANY_HAVE_BRAND]: '',
    [Screen14Keys.HAVE_TRADEMARK_PROTECTED]: '',
  };

  const isSubmitting = useSelector(getAssessmentToolLoading);

  return (
    <div className='flex flex-col h-full  gap-6 pl-10 py-10 font-medium'>
      <Formik
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={(values: FormikValues) => {
          handleNextClick(values);
        }}
        enableReinitialize
      >
        {({ values, setFieldValue, setTouched }) => (
          <Form className='flex flex-col justify-between '>
            <div className='h-[calc(100vh-20rem)] overflow-auto scrollbar'>
              <div>
                <div className='mt-4'>
                  <span className='font-semibold'>
                    {getQuestionBykey(
                      Screen14Keys.DOES_YOUR_COMPANY_HAVE_BRAND
                    )}
                  </span>

                  <div className='mt-5 xl:w-[50%]'>
                    <FormikRadio
                      labelClassName='!cursor-pointer'
                      className2='!w-fit'
                      valueChanged={() => {
                        setTouched({
                          [Screen14Keys.DOES_YOUR_COMPANY_HAVE_BRAND]: false,
                        });
                        setFieldValue(
                          Screen14Keys.HAVE_TRADEMARK_PROTECTED,
                          ''
                        );
                      }}
                      className='grid grid-cols-2 gap-4'
                      name={Screen14Keys.DOES_YOUR_COMPANY_HAVE_BRAND}
                      options={YesNoOptions}
                    />
                  </div>
                </div>
                {values[Screen14Keys.DOES_YOUR_COMPANY_HAVE_BRAND] ===
                  'yes' && (
                  <div className='mt-4'>
                    <span className='font-semibold'>
                      {getQuestionBykey(Screen14Keys.HAVE_TRADEMARK_PROTECTED)}
                    </span>

                    <div className='mt-5 xl:w-[50%]'>
                      <FormikRadio
                        labelClassName='!cursor-pointer'
                        className2='!w-fit'
                        className='grid grid-cols-2 gap-4'
                        name={Screen14Keys.HAVE_TRADEMARK_PROTECTED}
                        options={YesNoOptions}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className='pr-5'>
              <BackNextComponent
                backStep={() => handlebackClick(values)}
                buttonType='submit'
                isLoading={isSubmitting}
                isNextDisable={false}
                onSaveToDraftClick={() => {
                  handleSaveAsDraftClick(values);
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Screen14;
