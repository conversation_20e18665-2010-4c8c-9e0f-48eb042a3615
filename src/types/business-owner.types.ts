import {
  AssessmentToolProgressStatus,
  AssessmentToolStatus,
  AssessmentTools,
} from './enum';

export interface AwarenessTab {
  name: string;
  value: AssessmentTools;
  disabled?: boolean;
}

export interface AssessmentToolData {
  progress_status?: AssessmentToolProgressStatus;
  status: AssessmentToolStatus;
  open_count?: number;
  is_report_sent_to_owner?: boolean;
}
export interface AssessmentData {
  [assessment: string]: {
    [assessmentTool: string]: AssessmentToolData;
  };
}
export interface AssessmentDataMapping {
  assessmentDataMapping: Record<number, AssessmentData>;
}

export interface BusinessOwnersFilters {
  page?: number;
  search?: string;
  limit?: number;
  advisorId?: string | null; 
  secondaryAdvisor?: boolean | true ;
}

export interface BusinessOwnersIndustries {
  industries: { items: { label: string; value: string }[]; loading: boolean };
}
export interface BusinessOwnerLearnMore {
  learnMoreLoading: boolean;
}
