import LightThemeReportWrapper from 'HOC/LightThemeReportWrapper';
import React from 'react';

interface PageWrapperProps {
  children: React.ReactNode;
  id: string;
}
const PageWrapper: React.FC<PageWrapperProps> = (props) => {
  const { children, id } = props;
  return (
    <div>
      <LightThemeReportWrapper id={id}>
        <div className='flex flex-col p-20 mx-20 h-full items-center relative'>
          {children}
        </div>
      </LightThemeReportWrapper>
      <div className='mt-9' />
    </div>
  );
};

export default PageWrapper;
