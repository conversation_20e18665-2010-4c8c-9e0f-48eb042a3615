import { Form, Formik, FormikValues } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import FormikRadio from 'shared-resources/components/Radio/FormikRadio';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import * as yup from 'yup';
import {
  Screen18Keys,
  YesNoOptions,
  getQuestionBykey,
} from '../AssetProtectionConfig';

interface Props {
  data: any;
  handleNextClick: (values: FormikValues) => void;
  handlebackClick: (values: FormikValues) => void;
  handleSaveAsDraftClick: (values: FormikValues) => void;
}
const Screen18: React.FC<Props> = ({
  handleNextClick,
  handlebackClick,
  handleSaveAsDraftClick,
  data,
}) => {
  const validationSchema = yup.object().shape({
    [Screen18Keys.IS_IMPORTANT_TO_HAVE_DISASTER]: yup
      .string()
      .required('Value is required'),
    [Screen18Keys.DO_YOU_HAVE_DISASTER_PLAN]: yup
      .string()
      .when([Screen18Keys.IS_IMPORTANT_TO_HAVE_DISASTER], {
        is: (value: string) => value === 'yes',
        then: (schema) => schema.required('value is Required'),
        otherwise: (schema) => schema.nullable(),
      }),
  });

  const initialValue = data?.screen18 || {
    [Screen18Keys.IS_IMPORTANT_TO_HAVE_DISASTER]: '',
    [Screen18Keys.DO_YOU_HAVE_DISASTER_PLAN]: '',
  };

  const isSubmitting = useSelector(getAssessmentToolLoading);

  return (
    <div className='flex flex-col h-full  gap-6 pl-10 py-10 font-medium'>
      <Formik
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={(values: FormikValues) => {
          handleNextClick(values);
        }}
        enableReinitialize
      >
        {({ values, setFieldValue, setTouched }) => (
          <Form className='flex flex-col justify-between '>
            <div className='h-[calc(100vh-20rem)] overflow-auto scrollbar'>
              <div>
                <div className='mt-4'>
                  <span className='font-semibold'>
                    {getQuestionBykey(
                      Screen18Keys.IS_IMPORTANT_TO_HAVE_DISASTER
                    )}
                  </span>

                  <div className='mt-5 xl:w-[50%]'>
                    <FormikRadio
                      labelClassName='!cursor-pointer'
                      className2='!w-fit'
                      valueChanged={() => {
                        setTouched({
                          [Screen18Keys.IS_IMPORTANT_TO_HAVE_DISASTER]: false,
                        });
                        setFieldValue(
                          Screen18Keys.DO_YOU_HAVE_DISASTER_PLAN,
                          ''
                        );
                      }}
                      className='grid grid-cols-2 gap-4'
                      name={Screen18Keys.IS_IMPORTANT_TO_HAVE_DISASTER}
                      options={YesNoOptions}
                    />
                  </div>
                </div>
                {values[Screen18Keys.IS_IMPORTANT_TO_HAVE_DISASTER] ===
                  'yes' && (
                  <div className='mt-4'>
                    <span className='font-semibold'>
                      {getQuestionBykey(Screen18Keys.DO_YOU_HAVE_DISASTER_PLAN)}
                    </span>

                    <div className='mt-5 xl:w-[50%]'>
                      <FormikRadio
                        labelClassName='!cursor-pointer'
                        className2='!w-fit'
                        className='grid grid-cols-2 gap-4'
                        name={Screen18Keys.DO_YOU_HAVE_DISASTER_PLAN}
                        options={YesNoOptions}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className='pr-5'>
              <BackNextComponent
                backStep={() => handlebackClick(values)}
                buttonType='submit'
                isLoading={isSubmitting}
                isNextDisable={false}
                onSaveToDraftClick={() => {
                  handleSaveAsDraftClick(values);
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Screen18;
