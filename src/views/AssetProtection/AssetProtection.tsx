import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import {
  fetchBusinessOwnerAssessment,
  fetchOwnAssessment,
  updateBusinessOwnerAssessment,
  updateOwnAssessment,
} from 'store/actions/assessment-tool.action';
import { resetAssessmentData } from 'store/reducers/assessment-tool.reducer';
import {
  getAssessmentToolLoading,
  getAssessmentToolReEvaluate,
  getAssessmentToolResponse,
  getAssessmentToolStatus,
  getBusinessOwnerProperties,
} from 'store/selectors/assessment-tool.selector';
import { getUserData } from 'store/selectors/user.selector';
import {
  AssessmentResponseType,
  AssessmentToolProgressStatus,
  AssessmentTools,
  RouteKey,
  UserType,
} from 'types/enum';
import ThankYouPage from 'views/layout/ThankYouPage';
import { fetchBusinessOwner } from 'store/actions/business-owner.action';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import { getUserRouteType, thankYouPageContent } from 'utils/helpers/Helpers';
import { OwnerType, getScreen } from './AssetProtectionConfig';
import WithComments from '../../shared-resources/components/ToolComments/WithComments';

const AssetProtection: React.FC = () => {
  const { id } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const user = useSelector(getUserData) as BusinessOwner;
  const assessmentToolStatus = useSelector(getAssessmentToolStatus);
  const dispatch = useDispatch();
  const savedResponse: any = useSelector(getAssessmentToolResponse);
  const isLoading = useSelector(getAssessmentToolLoading);
  const [submitType, setSubmitType] = useState<AssessmentResponseType | null>(
    null
  );
  const businessOwnerProperties = useSelector(
    getBusinessOwnerProperties
  ) as BusinessOwner;
  const [response, setResponse] = useState<any>({});
  const [currentScreen, setCurrentScreen] = useState<{
    screen: number;
  }>({ screen: 1 });
  const assessmentReEvaluate = useSelector(getAssessmentToolReEvaluate);
  const navigate = useNavigate();

  useEffect(() => {
    if (
      assessmentToolStatus === AssessmentToolProgressStatus.COMPLETED &&
      assessmentReEvaluate &&
      !isLoading
    ) {
      dispatch(resetAssessmentData());
    }
  }, [assessmentReEvaluate, user, assessmentToolStatus]);

  useEffect(() => {
    const screen = new URLSearchParams(currentScreen as Record<number, string>);
    setSearchParams(screen);
  }, [currentScreen, setSearchParams, searchParams]);

  // set saved tab if assessment is not completed
  useEffect(() => {
    if (
      assessmentToolStatus !== AssessmentToolProgressStatus.COMPLETED &&
      savedResponse?.saved_screen
    ) {
      setCurrentScreen({ screen: savedResponse?.saved_screen });
    } else {
      setCurrentScreen({ screen: 1 });
    }
  }, [savedResponse]);

  const handleFetchAssessmentError = () => {
    navigate(`/${getUserRouteType(user?.type)}/${RouteKey.DASHBOARD}`);
  };
  // fetch saved assesment
  useEffect(() => {
    if (user?.type === UserType.ADVISOR && id) {
      dispatch(
        fetchBusinessOwnerAssessment({
          tool: AssessmentTools.ASSET_PROTECTION,
          businessOwnerId: +id!,
          onError: handleFetchAssessmentError,
        })
      );
    } else if (user?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchOwnAssessment({
          tool: AssessmentTools.ASSET_PROTECTION,
          onError: handleFetchAssessmentError,
        })
      );
    }
  }, []);

  useEffect(() => {
    if (
      assessmentToolStatus !== AssessmentToolProgressStatus.COMPLETED ||
      assessmentReEvaluate
    ) {
      setResponse(savedResponse);
    }
  }, [savedResponse]);

  const handleBack = (newData: any, key?: string, screenNumber?: number) => {
    if (key) {
      setResponse({
        ...response,
        [key]: newData,
      });
    }
    if (screenNumber) {
      setCurrentScreen({ screen: screenNumber });
    } else if (currentScreen.screen) {
      setCurrentScreen({
        screen: currentScreen.screen - 1,
      });
    }
  };

  const handleNext = (newData: any, key?: string, screenNumber?: number) => {
    if (
      key === 'screen2' &&
      businessOwnerProperties?.metadata?.ownership &&
      !savedResponse?.screen3?.owner_type
    ) {
      setResponse({
        ...response,
        [key]: newData,
        screen3: {
          owner_type: businessOwnerProperties?.metadata?.ownership,
        },
      });
    } else if (key) {
      setResponse({
        ...response,
        [key]: newData,
      });
    }
    if (screenNumber) {
      setCurrentScreen({ screen: screenNumber });
    } else if (currentScreen.screen) {
      setCurrentScreen({
        screen: currentScreen.screen + 1,
      });
    }
  };

  const handleSubmit = (
    newData: any,
    key?: string,
    isSubmit?: boolean,
    savedScreen?: number
  ) => {
    let updatedData = {
      ...response,
      saved_screen:
        response?.saved_screen > currentScreen.screen
          ? response?.saved_screen
          : currentScreen.screen,
    };
    if (key) {
      updatedData = {
        ...updatedData,
        [key]: newData,
      };
    }
    if (savedScreen) {
      updatedData = {
        ...updatedData,
        saved_screen: savedScreen,
      };
    }
    if (user?.type === UserType.ADVISOR && id) {
      dispatch(
        updateBusinessOwnerAssessment({
          businessOwnerId: +id!,
          tool: AssessmentTools.ASSET_PROTECTION,
          assessment_response: updatedData,
          submit_type: isSubmit
            ? AssessmentResponseType.COMPLETE
            : AssessmentResponseType.DRAFT,
        })
      );
    } else {
      dispatch(
        updateOwnAssessment({
          tool: AssessmentTools.ASSET_PROTECTION,
          assessment_response: updatedData,
          submit_type: isSubmit
          ? AssessmentResponseType.SUBMIT
          : AssessmentResponseType.DRAFT,
          onSuccess: () =>
            isSubmit &&
            navigate(`${UserType.BUSINESS_OWNER}/${RouteKey.DASHBOARD}`),
        })
      );
    }
    setSubmitType(
      isSubmit ? AssessmentResponseType.COMPLETE : AssessmentResponseType.DRAFT
    );
  };

  useEffect(() => {
    if (id) dispatch(fetchBusinessOwner(id));
  }, [dispatch, id]);

  const getInitialOwnerType = () => {
    if (savedResponse?.screen3?.owner_type) {
      return savedResponse?.screen3?.owner_type as OwnerType;
    }

    if (businessOwnerProperties?.metadata?.ownership) {
      return businessOwnerProperties?.metadata?.ownership as OwnerType;
    }
    return null;
  };

  const ownerType = getInitialOwnerType();

  if (isLoading && !submitType) {
    return <Spinner />;
  }

  if (assessmentToolStatus === AssessmentToolProgressStatus.COMPLETED) {
    return (
      <ThankYouPage
        pageContent={thankYouPageContent(
          'Asset Protection',
          user?.type || UserType.BUSINESS_OWNER,
          `${businessOwnerProperties?.first_name ?? ''} ${
            businessOwnerProperties?.last_name ?? ''
          }`
        )}
        loggedInUserData={user}
        isPasswordSet={false}
      />
    );
  }

  return (
    <>
      <h1 className='font-bold text-2xl mb-3'>Asset Protection</h1>
      <WithComments tool={AssessmentTools.ASSET_PROTECTION}>
        <div className='pr-3 flex flex-col justify-between bg-white h-[calc(100vh-13rem)] w-full'>
          {getScreen(
            currentScreen.screen!,
            response,
            handleBack,
            handleNext,
            handleSubmit,
            navigate,
            user,
            ownerType
          )}
        </div>
      </WithComments>
    </>
  );
};

export default AssetProtection;
