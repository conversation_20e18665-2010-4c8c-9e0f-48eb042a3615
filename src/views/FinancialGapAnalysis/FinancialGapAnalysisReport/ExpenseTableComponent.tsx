import React, { memo } from 'react';
import classNames from 'classnames';
import {
  ExpenseItem,
  ExpensesReportResponse,
  formatCurrency,
  getQuestionTitleFromKey,
  getTitle,
  headerOptions,
} from '../ExpenseCalculator/ExpenseCalculatorConfig';
import ExpenseTableRow from '../ExpenseCalculator/ExpenseTableRow';
import { FinancialGapAssessmentCalculatorTabs } from '../../../types/enum';
import Input from '../../../shared-resources/components/Input/Input';

const textSizeClassName = '!text-xs ';

interface Props {
  response: ExpensesReportResponse;
  isReportBuilder?: boolean;
}
const ExpenseTableComponent: React.FC<Props> = ({
  response,
  isReportBuilder,
}) => {
  const expenseTable = (
    response: ExpensesReportResponse,
    screenKey: string
  ) => {
    const expenses = Object.keys(
      response[screen<PERSON>ey as keyof ExpensesReportResponse] || {}
    );

    return expenses.length > 0 ? (
      <div className={` relative w-full rounded-t-md`}>
        <div
          className={`bg-blue-01 z-10 tracking-[0.07rem] sticky top-0 grid grid-cols-10 overflow-hidden text-white 
           font-medium rounded-t-md  text-[0.813rem] `}
        >
          {headerOptions.map((h, index) => (
            <div
              key={h}
              className={`px-5 flex items-center border-r  border-gray-02 col-span-3  justify-center py-3 h-full ${
                index === 0 && 'border-l rounded-tl-md col-span-4'
              } ${headerOptions.length - 1 === index && 'rounded-t-md   '} `}
            >
              <span className='text-center'>
                {index === 0
                  ? getTitle(screenKey as FinancialGapAssessmentCalculatorTabs)
                  : h}
              </span>
            </div>
          ))}
        </div>
        <div>
          {expenses.map((expenseFunction, index) => {
            const expenseItem = (
              response[screenKey as keyof ExpensesReportResponse] as {
                [key: string]: ExpenseItem | number;
              }
            )[expenseFunction];

            if (Array.isArray(expenseItem)) {
              return expenseItem.map((item, subIndex) => (
                <ExpenseTableRow
                  // eslint-disable-next-line react/no-array-index-key
                  key={`${index}-${subIndex}`}
                  expenseFunctionName={expenseFunction}
                  expenseFunctionValue={item.expense_name}
                  index={subIndex}
                  monthlyExpenseValue={item.monthly_expense}
                  expenseFunctionValueClassName={`${textSizeClassName} truncate`}
                  expenseValueContainerClassName={textSizeClassName}
                />
              ));
            }

            if (expenseFunction === 'total_monthly_expense') {
              return (
                <div
                  className={classNames(
                    'grid grid-cols-10 font-[500]  text-[0.9rem] '
                  )}
                  // eslint-disable-next-line react/no-array-index-key
                  key={`total_monthly_expense-${index}`}
                >
                  <div className='px-3  rounded-bl-md col-span-4  flex items-center border-b border-l py-3 border-r border-gray-02 bg-gray-08'>
                    <span className='text-xs font-medium'>
                      Total : Post Transition
                    </span>
                  </div>
                  <div className='flex items-center  col-span-3 border-b  border-r border-gray-02 bg-gray-08'>
                    <div className='flex items-center justify-center w-full text-xs font-medium'>
                      <span className='truncate'>
                        {formatCurrency(expenseItem as number)}
                      </span>
                    </div>
                  </div>
                  <div className='flex items-center rounded-br-md col-span-3 border-b  border-r border-gray-02 bg-gray-08'>
                    <div className='flex items-center justify-center w-full text-xs font-medium'>
                      <span className='truncate'>
                        {formatCurrency((expenseItem as number) * 12)}
                      </span>
                    </div>
                  </div>
                </div>
              );
            }

            return (
              <ExpenseTableRow
                // eslint-disable-next-line react/no-array-index-key
                key={`${index}`}
                expenseFunctionName={expenseFunction}
                expenseFunctionValue={getQuestionTitleFromKey(
                  expenseFunction as any,
                  screenKey as any
                )}
                index={index}
                monthlyExpenseValue={
                  (expenseItem as ExpenseItem).monthly_expense
                }
                expenseFunctionValueClassName={`${textSizeClassName} truncate`}
                expenseValueContainerClassName={textSizeClassName}
              />
            );
          })}
        </div>
      </div>
    ) : null;
  };

  return (
    <>
      {!isReportBuilder && (
        <div>
          <span className='text-2xl font-bold text-26 text-blue-01'>
            Post-Exit Living Expenses Worksheet
          </span>
        </div>
      )}
      <div className='flex flex-col w-full space-y-10 mt-5'>
        {Object.keys(response).map((screenKey) =>
          screenKey === 'total_monthly_expense' && !isReportBuilder ? (
            <div className='flex flex-col space-y-10' key={screenKey}>
              <Input
                label='Total Expenses (Monthly)'
                labelClassName='!text-[1rem]'
                onChange={() => null}
                value={formatCurrency(response.total_monthly_expense)}
                disabled
              />
              <Input
                label='Total Essential Expenses (Annually)'
                labelClassName='!text-[1rem]'
                onChange={() => null}
                value={formatCurrency(response.total_monthly_expense * 12)}
                disabled
              />
            </div>
          ) : (
            <div key={screenKey}>{expenseTable(response, screenKey)}</div>
          )
        )}
      </div>
    </>
  );
};

export default memo(ExpenseTableComponent);
