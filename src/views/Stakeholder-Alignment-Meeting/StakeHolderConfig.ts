import { AssessmentToolProgressStatus } from 'types/enum';
import { pick } from 'lodash';
import { TableValueType } from './Questions';

export const calculateStakeholderAlignmentPercentage = (
  response: any,
  progressStatus: string
) => {
  if (response && progressStatus) {
    if (progressStatus === AssessmentToolProgressStatus.COMPLETED) {
      return 100;
    }
    const filteredResponse = pick(
      response,
      Object.keys(response)?.filter((key) => key !== 'currentTabVal')
    );
    const unAnsweredQuestionsLength = Object?.values(
      filteredResponse ?? {}
    )?.filter(
      (ans: any) =>
        !ans?.length ||
        (typeof ans?.[0] === 'object' &&
          (ans as TableValueType[])?.every((val: TableValueType) => !val?.name))
    )?.length;
    const totalQuestionsLength = Object?.values(filteredResponse)?.length;

    const completedPercentage =
      ((totalQuestionsLength - unAnsweredQuestionsLength) /
        totalQuestionsLength) *
      100;
    return completedPercentage;
  }

  return 0;
};

export const relationshipOptions = [
  { value: 'Owner', label: 'Owner' },
  { value: 'Spouse/SO', label: 'Spouse/SO' },
  { value: 'Child', label: 'Child' },
  { value: 'Investor', label: 'Investor' },
  { value: 'Employee', label: 'Employee' },
  { value: 'Key Employee', label: 'Key Employee' },
  { value: 'Potential Buyer', label: 'Potential Buyer' },
  { value: 'Other', label: 'Other' },
];
