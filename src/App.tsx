import AuthenticatedRouteHOC from 'HOC/AuthenticatedRoute';
import UnauthenticatedRouteHOC from 'HOC/UnauthenticatedRoute';
import RouteWrapper from 'RouteWrapper';
import React, { useMemo } from 'react';
import { Provider } from 'react-redux';
import { BrowserRouter, Navigate, Route, Routes } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { LAYOUT_ROUTES } from 'routes';
import { authRoutes } from 'routes/authRoutes';
import { publicRoutes } from 'routes/publicRoutes';
import { localStorageService } from 'services/LocalStorageService';
import store from 'store';
import { getUserRouteType } from 'utils/helpers/Helpers';
import Layout from 'views/layout/Layout';
import IntercomHOC from './HOC/Intercom';

const App: React.FC = () => {
  const loggedInUserType = useMemo(
    () => localStorageService.getLoggedInUserType(),
    []
  );

  return (
    <Provider store={store}>
      <BrowserRouter>
        <RouteWrapper>
          <Routes>
            <Route path='/' element={<Layout />}>
              {LAYOUT_ROUTES.map((route) => (
                <Route
                  path={route.key}
                  key={route.key}
                  Component={
                    route.component && AuthenticatedRouteHOC(route.component)
                  }
                />
              ))}
            </Route>
            {authRoutes.map((route) => (
              <Route
                key={route.key}
                path={route.key}
                Component={
                  route.component && UnauthenticatedRouteHOC(route.component)
                }
              />
            ))}
            {publicRoutes.map((route) => (
              <Route
                key={route.key}
                path={route.key}
                Component={route.component}
              />
            ))}
            <Route
              path='*'
              element={
                <Navigate
                  to={`/${getUserRouteType(loggedInUserType)}/dashboard`}
                  replace
                />
              }
            />
            {}
          </Routes>
          <ToastContainer position='top-right' autoClose={2000} />
          <IntercomHOC />
        </RouteWrapper>
      </BrowserRouter>
    </Provider>
  );
};

export default App;
