import cx from 'classnames';
import React, { ButtonHTMLAttributes } from 'react';
import { IconType } from 'react-icons';

interface TitleProps extends ButtonHTMLAttributes<HTMLDivElement> {
  Icon: IconType;
  title: string;
  classNames?: string;
}

const Title: React.FC<TitleProps> = ({ Icon, title, classNames, ...rest }) => (
  <div
    className={cx(
      'flex items-center w-full cursor-pointer justify-center mt-[2.625rem]',
      classNames
    )}
    {...rest}
  >
    <Icon className='min-w-5 h-5' size={20} />
    <div className='w-full text-left ml-2'>{title}</div>
  </div>
);

export default Title;
