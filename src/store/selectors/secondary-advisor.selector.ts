import { createSelector } from 'reselect';
import { AppState } from 'store';
import { SecondaryAdvisorState } from 'store/reducers/secondary-advisor.reducer';

const secondaryAdvisorState = (state: AppState) => state.secondaryAdvisor;

export const isSecondaryAdvisorLoadingSelector = createSelector(
  [secondaryAdvisorState],
  (state: SecondaryAdvisorState) => Boolean(state.loading)
);

export const isSecondaryAdvisorLoadingOneSelector = createSelector(
  [secondaryAdvisorState],
  (state: SecondaryAdvisorState) => Boolean(state.loadingOne)
);

export const isSecondaryAdvisorDataSelector = createSelector(
  [secondaryAdvisorState],
  (state: SecondaryAdvisorState) => state.entities
);
