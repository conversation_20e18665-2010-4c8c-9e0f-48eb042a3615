import React, { ReactElement, useState } from 'react';

interface Props {
  children: ReactElement;
  scrollTriggerBottomOffset?: number;
  scrollDebounceTime?: number;
  onUserScroll: () => any;
}

const InfiniteScroll: React.FC<Props> = (props: Props) => {
  const {
    children,
    scrollTriggerBottomOffset = 10,
    scrollDebounceTime = 1000,
    onUserScroll,
  } = props;

  const [previousScrollHeight, setPreviousScrollHeight] = useState(0);
  const [preventScrollTrigger, setPreventScrollTrigger] = useState(false);
  const [debounceTimer, setDebounceTimer] = useState<any>();

  const resetDebounceTimer = () => {
    clearTimeout(debounceTimer);
    setPreventScrollTrigger(false);
  };

  const startDebounceTimer = () => {
    setPreventScrollTrigger(true);
    const timer = setTimeout(() => resetDebounceTimer(), scrollDebounceTime);
    setDebounceTimer(timer);
  };

  const scrollListener = (event: any) => {
    const { target } = event;
    const totalScrollHeight = target.scrollHeight;
    const heightScrolled = target.offsetHeight + target.scrollTop;
    const shouldEmitEvent =
      totalScrollHeight - heightScrolled <= (scrollTriggerBottomOffset || 0) &&
      heightScrolled > previousScrollHeight &&
      !preventScrollTrigger;

    if (shouldEmitEvent) {
      onUserScroll();
      startDebounceTimer();
    }
    setPreviousScrollHeight(heightScrolled);
  };

  return React.cloneElement(children, {
    onScroll: scrollListener,
  });
};

export default React.memo(InfiniteScroll);
