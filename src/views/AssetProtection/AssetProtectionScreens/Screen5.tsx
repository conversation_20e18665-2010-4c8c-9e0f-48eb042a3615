import { Form, Formik, FormikValues } from 'formik';
import React from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { useSelector } from 'react-redux';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import InputLabel from 'shared-resources/components/InputLabel/InputLabel';
import FormikRadio from 'shared-resources/components/Radio/FormikRadio';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { getFormattedDate } from 'utils/helpers/Helpers';
import * as yup from 'yup';
import {
  Screen5Keys,
  YesNoOptions,
  getQuestionBykey,
} from '../AssetProtectionConfig';

interface Props {
  data: any;
  handleNextClick: (values: FormikValues) => void;
  handlebackClick: (values: FormikValues) => void;
  handleSaveAsDraftClick: (values: FormikValues) => void;
}
const Screen5: React.FC<Props> = ({
  handleNextClick,
  handlebackClick,
  handleSaveAsDraftClick,
  data,
}) => {
  const validationSchema = yup.object().shape({
    [Screen5Keys.HAVE_BUY_SELL_AGREEMENT]: yup
      .string()
      .required('Value is required'),
    [Screen5Keys.BUY_SELL_LAST_UPDATED_ON]: yup
      .string()
      .when([Screen5Keys.HAVE_BUY_SELL_AGREEMENT], {
        is: (value: string) => value === 'yes',
        then: (schema) => schema.required('value is Required'),
        otherwise: (schema) => schema.nullable(),
      }),
  });
  const initialValue = data?.screen5 || {
    [Screen5Keys.HAVE_BUY_SELL_AGREEMENT]: '',
    [Screen5Keys.BUY_SELL_LAST_UPDATED_ON]: '',
  };

  const isSubmitting = useSelector(getAssessmentToolLoading);

  // This screen is for multiple owner
  return (
    <div className='flex flex-col h-full  gap-6 pl-10 py-10 font-medium'>
      <Formik
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={(values: FormikValues) => {
          handleNextClick(values);
        }}
        enableReinitialize
      >
        {({ values, setFieldValue, errors, touched, setTouched }) => (
          <Form className='flex flex-col justify-between '>
            <div className='h-[calc(100vh-20rem)] overflow-auto scrollbar'>
              <div>
                <div className='mt-4'>
                  <span className='font-semibold'>
                    {getQuestionBykey(Screen5Keys.HAVE_BUY_SELL_AGREEMENT)}
                  </span>

                  <div className='mt-5 xl:w-[80%]'>
                    <FormikRadio
                      valueChanged={(value) => {
                        setTouched({
                          [Screen5Keys.HAVE_BUY_SELL_AGREEMENT]: false,
                        });
                        if (value === 'no') {
                          setFieldValue(
                            Screen5Keys.BUY_SELL_LAST_UPDATED_ON,
                            ''
                          );
                        }
                      }}
                      className='grid grid-cols-2 gap-4'
                      name={Screen5Keys.HAVE_BUY_SELL_AGREEMENT}
                      options={YesNoOptions}
                    />
                  </div>
                </div>
              </div>

              {values[Screen5Keys.HAVE_BUY_SELL_AGREEMENT] === 'yes' && (
                <div className='mt-10 flex flex-col'>
                  <InputLabel
                    label={getQuestionBykey(
                      Screen5Keys.BUY_SELL_LAST_UPDATED_ON
                    )}
                  />
                  <DatePicker
                    popperPlacement='bottom-start'
                    popperClassName='!w-[6.5%]'
                    className='text-blue-01 mt-3 w-[50%] border  border-gray-02 !h-12 !rounded-xl focus:outline-none py-2.5 px-5 font-bold'
                    selected={values?.[Screen5Keys.BUY_SELL_LAST_UPDATED_ON]}
                    onChange={(date) => {
                      setFieldValue(
                        Screen5Keys.BUY_SELL_LAST_UPDATED_ON,
                        date && getFormattedDate(date?.toDateString())
                      );
                    }}
                  />
                  {errors[Screen5Keys.BUY_SELL_LAST_UPDATED_ON] &&
                    touched[Screen5Keys.BUY_SELL_LAST_UPDATED_ON] && (
                      <div className='text-red-500 text-sm'>{`${
                        errors[Screen5Keys.BUY_SELL_LAST_UPDATED_ON]
                      }`}</div>
                    )}
                </div>
              )}
            </div>
            <div className='pr-5'>
              <BackNextComponent
                backStep={() => handlebackClick(values)}
                buttonType='submit'
                isLoading={isSubmitting}
                isNextDisable={false}
                onSaveToDraftClick={() => handleSaveAsDraftClick(values)}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Screen5;
