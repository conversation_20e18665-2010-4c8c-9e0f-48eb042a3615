import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { FollowUpHistory, FollowUpSchedule } from 'models/entities/FollowUp';

export interface FollowUpState {
  followUpHistory: FollowUpHistory[];
  followUpSchedule?: FollowUpSchedule;
  sentFollowUpText: string;
  loading?: boolean;
  loadingList?: boolean;
  error?: string;
}

const initialState: FollowUpState = {
  followUpHistory: [],
  sentFollowUpText: '',
};

export const followUpSlice = createSlice({
  name: 'follow_up',
  initialState,
  reducers: {
    addLoading: (state, action: PayloadAction<{ loading: boolean }>) => {
      const { loading } = action.payload;
      state.loading = loading;
    },
    cancelUpcomingFollowUpCompleted: (
      state,
      action: PayloadAction<{
        schedule: FollowUpSchedule;
      }>
    ) => {
      state.followUpSchedule = action.payload.schedule;
    },
    createFollowUp: (state) => {
      state.sentFollowUpText = '';
    },
    createFollowUpCompleted: (
      state,
      action: PayloadAction<{
        sentFollowUpText: string;
      }>
    ) => {
      const { sentFollowUpText } = action.payload;
      state.sentFollowUpText = sentFollowUpText;
    },
    fetchFollowUpHistory: (state) => {
      state.loadingList = true;
      state.followUpHistory = [];
    },
    fetchFollowUpHistoryCompleted: (
      state,
      action: PayloadAction<{
        history: FollowUpHistory[];
        schedule: FollowUpSchedule;
      }>
    ) => {
      state.loadingList = false;
      state.followUpHistory = action.payload.history;
      state.followUpSchedule = action.payload.schedule;
    },
    fetchFollowUpHistoryError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loadingList = false;
    },
    cancelUpcomingFollowUpError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
    createFollowUpError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
  },
});

export const {
  addLoading,
  createFollowUp,
  createFollowUpCompleted,
  cancelUpcomingFollowUpCompleted,
  fetchFollowUpHistory,
  fetchFollowUpHistoryCompleted,
  fetchFollowUpHistoryError,
  cancelUpcomingFollowUpError,
  createFollowUpError,
} = followUpSlice.actions;

export default followUpSlice.reducer;
