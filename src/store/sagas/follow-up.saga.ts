import { ManualFollowUpDTO } from 'dtos/follow-up.dto';
import { toast } from 'react-toastify';
import { all, call, put, takeLatest } from 'redux-saga/effects';
import { followUpService } from 'services/api-services/followUpService';
import { FollowUpActionType } from 'store/actions/actions.constants';
import {
  addLoading,
  cancelUpcomingFollowUpCompleted,
  cancelUpcomingFollowUpError,
  createFollowUp,
  createFollowUpCompleted,
  createFollowUpError,
  fetchFollowUpHistory,
  fetchFollowUpHistoryCompleted,
  fetchFollowUpHistoryError,
} from 'store/reducers/follow-up-reducer';
import { fetchBusinessOwnerAssessment } from '../actions/assessment-tool.action';

function* followUpsFetchSaga(action: any): any {
  const { businessOwnerId, assessmentTool } = action.payload;
  try {
    yield put(fetchFollowUpHistory());
    const response = yield call(
      followUpService.fetchList,
      businessOwnerId,
      assessmentTool
    );
    yield put(
      fetchFollowUpHistoryCompleted({
        history: response.follow_up_record,
        schedule: response.follow_up_schedule,
      })
    );
  } catch (e: any) {
    yield put(fetchFollowUpHistoryError(e?.message));
  }
}

function* followUpCreateSaga(action: any): any {
  const followUpCreatePayload: ManualFollowUpDTO = action.payload.payload;
  const { callback } = action.payload;
  try {
    yield put(addLoading({ loading: true }));
    yield put(createFollowUp());
    const response = yield call(
      followUpService.createFollowUp,
      followUpCreatePayload
    );
    yield put(
      fetchBusinessOwnerAssessment({
        businessOwnerId: followUpCreatePayload.businessOwnertId,
        tool: followUpCreatePayload.tool,
      })
    );
    yield put(
      createFollowUpCompleted({ sentFollowUpText: response.follow_up_text })
    );
    toast.success(response.message);
    callback();
    yield put(addLoading({ loading: false }));
  } catch (e: any) {
    yield put(addLoading({ loading: false }));
    yield put(createFollowUpError(e?.message));
  }
}

function* followUpCancelSaga(action: any): any {
  const { businessOwnerId, assessmentTool } = action.payload;
  try {
    yield put(addLoading({ loading: true }));
    const response = yield call(
      followUpService.cancelFollowUp,
      businessOwnerId,
      assessmentTool
    );
    yield put(
      cancelUpcomingFollowUpCompleted({
        schedule: response.follow_up_schedule,
      })
    );
    toast.success(response.message);
    yield put(addLoading({ loading: false }));
  } catch (e: any) {
    yield put(addLoading({ loading: false }));
    yield put(cancelUpcomingFollowUpError(e?.message));
  }
}

export function* followUpSagaWatcher() {
  yield all([
    takeLatest(FollowUpActionType.GET_FOLLOW_UPS, followUpsFetchSaga),
    takeLatest(FollowUpActionType.CREATE_FOLLOW_UP, followUpCreateSaga),
    takeLatest(
      FollowUpActionType.CANCEL_UPCOMING_FOLLOW_UP,
      followUpCancelSaga
    ),
  ]);
}
