import React from 'react';
import { FaChevronRight } from 'react-icons/fa';

interface Props {
  secondaryTitle?: string;
}

const BusinessValuationHeader: React.FC<Props> = ({ secondaryTitle }) => (
  <div className='flex gap-2 items-center'>
    <h1 className='text-2xl font-semibold'>Valuation & Financial Analysis</h1>
    {secondaryTitle && (
      <div className='flex gap-2 items-center'>
        <FaChevronRight className='text-4xl text-gray-600' />
        <h2 className='text-base text-blue-01 font-[500]'>{secondaryTitle}</h2>
      </div>
    )}
  </div>
);
export default BusinessValuationHeader;
