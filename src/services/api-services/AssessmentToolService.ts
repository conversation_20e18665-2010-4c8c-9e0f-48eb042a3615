import {
  AssessmentReEvaluatePayload,
  FetchBusinessOwnerAssessmentDTO,
  FetchOwnAssessmentDTO,
  FetchPublicAssessmentByTokenDTO,
  UpdateBusinessOwnerAssessmentDTO,
  UpdateOwnAssessmentDTO,
  UpdatePublicAssessmentByTokenDTO,
} from 'dtos/assessment-tools.dto';
import {
  AssessmentToolProgressStatus,
  AssessmentTools,
} from '../../types/enum';
import { baseApiService } from './BaseApiService';

class AssessmentToolService {
  static getInstance(): AssessmentToolService {
    return new AssessmentToolService();
  }

  public async fetchOwnAssessment(data: FetchOwnAssessmentDTO): Promise<{
    assessment_response: any;
    status: AssessmentToolProgressStatus;
  }> {
    return baseApiService.get(
      `/business-owner/assessment-response/${data.tool}`
    );
  }

  public async updateOwnAssessment(data: UpdateOwnAssessmentDTO): Promise<{
    assessment_response: any;
  }> {
    return baseApiService.post('/business-owner/assessment-response', data);
  }

  public async fetchBusinessOwnerAssessment(
    data: FetchBusinessOwnerAssessmentDTO
  ): Promise<{
    assessment_response: any;
    status: AssessmentToolProgressStatus;
  }> {
    return baseApiService.get(
      `/business-owner/${data.businessOwnerId}/assessment-response/${data.tool}`
    );
  }

  public async updateBusinessOwnerAssessment(
    data: UpdateBusinessOwnerAssessmentDTO
  ): Promise<{
    assessment_response: any;
  }> {
    const { businessOwnerId, ...requestData } = data;
    return baseApiService.post(
      `/business-owner/${businessOwnerId}/assessment-response`,
      requestData
    );
  }

  public async fetchPublicAssessmentByToken(
    data: FetchPublicAssessmentByTokenDTO
  ): Promise<{
    assessment_response: any;
    status: AssessmentToolProgressStatus;
  }> {
    return baseApiService.get(
      `/public/assessment-response/${data.tool}?token=${data.token}`
    );
  }

  public async updatePublicAssessmentByToken(
    data: UpdatePublicAssessmentByTokenDTO
  ): Promise<{
    assessment_response: any;
    is_password_set: boolean;
  }> {
    return baseApiService.post('/public/assessment-response', data);
  }

  public async updateAssessmentReEvaluate(data: AssessmentReEvaluatePayload) {
    const { ownerId, tool, status } = data;
    return baseApiService.post(
      `/business-owner/${ownerId}/assessment-tool/re-evaluate`,
      {
        tool,
        status,
      }
    );
  }

  public async incrementOpenCount(tool: AssessmentTools): Promise<{
    success: boolean;
  }> {
    return baseApiService.post(`/business-owner/increment-open-count/${tool}`);
  }
}

export const assessmentToolService = AssessmentToolService.getInstance();
