import React from 'react';
import { User } from 'models/entities/User';
import { NavigateFunction } from 'react-router';
import { RouteKey, UserRouteType, UserType } from 'types/enum';
import GetStartedPage from './AssetProtectionScreens/GetStartedPage';
import Screen10 from './AssetProtectionScreens/Screen10';
import Screen11 from './AssetProtectionScreens/Screen11';
import Screen12 from './AssetProtectionScreens/Screen12';
import Screen13 from './AssetProtectionScreens/Screen13';
import Screen14 from './AssetProtectionScreens/Screen14';
import Screen15 from './AssetProtectionScreens/Screen15';
import Screen16 from './AssetProtectionScreens/Screen16';
import Screen17 from './AssetProtectionScreens/Screen17';
import Screen18 from './AssetProtectionScreens/Screen18';
import Screen2 from './AssetProtectionScreens/Screen2';
import Screen4 from './AssetProtectionScreens/Screen4';
import Screen5 from './AssetProtectionScreens/Screen5';
import Screen6 from './AssetProtectionScreens/Screen6';
import Screen7 from './AssetProtectionScreens/Screen7';
import Screen8 from './AssetProtectionScreens/Screen8';
import Screen9 from './AssetProtectionScreens/Screen9';
import Screen3 from './AssetProtectionScreens/Screen3';
import Screen19 from './AssetProtectionScreens/Screen19';
import Screen20 from './AssetProtectionScreens/Screen20';

export enum Screen2Keys {
  BUSINESS_CONTINUITY_INSTRUCTIONS_IN_EXIT_SMARTS = 'business_continuity_instructions_in_exit_smarts',
  HAVE_BUSINESS_CONTINUITY_INSTRUCTIONS = 'have_business_continuity_instructions',
  BUSINESS_CONTINUITY_INSTRUCTIONS_UPDATED_ON = 'business_continuity_instructions_updated_on',
  HAVE_TOLD_APPROPRIATE_PEOPLE = 'have_told_appropriate_people',
}

export enum Screen3Keys {
  owner_type = 'owner_type',
}

export enum Screen5Keys {
  HAVE_BUY_SELL_AGREEMENT = 'have_buy_sell_agreement',
  BUY_SELL_LAST_UPDATED_ON = 'buy_sell_last_updated_on',
}

export enum Screen6Keys {
  HAVE_KEY_EMPLOYEE_AGREEMENTS_IN_PLACE = 'have_key_employee_agreements_in_place',
}

export enum Screen7Keys {
  HAVE_INCENTIVE_AGREEMENTS_IN_PLACE = 'have_incentive_agreements_in_place',
}

export enum Screen9Keys {
  HAVE_A_PERSONAL_TAX_MINIMIZATION_STRATEGY = 'have_a_personal_tax_minimization_strategy',
  HAVE_A_PERSONAL_TAX_MINIMIZATION_STRATEGY_NOW = 'have_a_personal_tax_minimization_strategy_now',
  HAVE_A_FINANCIAL_PLANNER = 'have_a_financial_planner',
  HAVE_A_WEALTH_MANAGER = 'have_a_wealth_manager',
}

export enum Screen8Keys {
  INSURANCE_TYPE = 'insurance_type',
}

export enum Screen10Keys {
  IS_YOUR_BUSINESS_SUBJECT_TO_SAFETY_RISKS = 'is_your_business_subject_to_safety_risks',
  DO_YOU_PROVIDE_TRAINING_TO_EMPLOYEES = 'do_you_provide_training_to_employees',
  DOES_YOUR_CURRENT_INSURANCE_PROTECT = 'does_your_current_insurance_protect',
}

export enum Screen11Keys {
  ARE_THERE_ANY_USUAL_THREATS = 'are_there_any_usual_threats',
}

export enum Screen12Keys {
  ARE_THERE_COMPLIANCE_RISKS = 'are_there_compliance_risks',
}

export enum Screen13Keys {
  DOES_THE_BUSINESS_HAVE_ANY_PROPRIETARY = 'does_the_business_have_any_proprietary',
  DO_YOU_HAVE_COPYRIGHT_PROTECTION = 'do_you_have_copyright_protection',
  // WHAT_ARE_THE_PRODUCTS_OR_PROCESSES = 'what_are_the_products_or_processes',
}

export enum Screen14Keys {
  DOES_YOUR_COMPANY_HAVE_BRAND = 'does_your_company_have_brand',
  HAVE_TRADEMARK_PROTECTED = 'have_trademark_protected',
}

export enum Screen15Keys {
  IS_THERE_CONCERN_FOR_CYBERSECURITY = 'is_there_concern_for_cybersecurity',
  HAVE_YOU_TAKEN_MEASURES = 'have_you_taken_measures',
}

export enum Screen16Keys {
  HAVE_INTERNAL_FINANCIAL_CONTROL = 'have_internal_financial_control',
}

export enum Screen17Keys {
  IS_IMPORTANT_TO_HAVE_SECURE_PREMISES = 'is_important_to_have_secure_premises',
  HAVE_SECURITY_MEASURES = 'have_security_measures',
}

export enum Screen18Keys {
  IS_IMPORTANT_TO_HAVE_DISASTER = 'is_it_important_to_have_disaster',
  DO_YOU_HAVE_DISASTER_PLAN = 'do_you_have_disaster_plan',
}
export enum Screen19Keys {
  HARASSMENT_POLICY_VISIBLE = 'harassment_policy_visible',
  HARASSMENT_POLICY_LAST_UPDATED = 'harassment_policy_last_updated',
}

export enum Screen20Keys {
  DISCRIMINATION_POLICY_VISIBLE = 'discrimination_policy_visible',
  DISCRIMINATION_POLICY_LAST_UPDATED = 'discrimination_policy_last_updated',
}

export const screen9HaveCreatedPlanForSaleOptions = [
  'Yes, with my Lawyer, Accountant, or Financial Planner',
  'No, but I have spoken with my Lawyer, Accountant, or Financial Planner',
  'No, but I will speak with my Lawyer, Accountant, or Financial Planner',
  'No, but I have a general idea of what to expect',
];

export const screen7HaveIncentiveAgreementsInPlaceOptions = [
  'Yes, for all Employees, regardless of role',
  'Yes, but only at the management level',
  'Yes, but only for select employees',
  'No, none at all',
];

export const screen6HaveKeyEmployeeAgreementsInPlaceOptions = [
  'Yes, for every Key Employee',
  'Yes, but only for employees who demanded one',
  'Yes, but only as the management level',
  'No, I have never seen a need to offer them',
];

export const screen2HaveBusinessCountinuityKeyOptions = [
  'Yes, and they are documented',
  'Yes, but they are loosely defined',
  'No, but I think they’re a good idea',
  'No, I have never taken the time to think about them',
];

export const YesNoOptions = [
  {
    value: 'yes',
    label: 'Yes',
  },
  {
    value: 'no',
    label: 'No',
  },
];

export enum OwnerType {
  SINGLE_OWNER = 'single_owner',
  MULTIPLE_OWNER = 'multiple_owner',
}

export const getQuestionBykey = (key: string) => {
  switch (key) {
    case Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_IN_EXIT_SMARTS:
      return 'Your Business Continuity Instructions in the ExitSmarts Platform were last updated on?';
    case Screen2Keys.HAVE_BUSINESS_CONTINUITY_INSTRUCTIONS:
      return 'Do you have Business Continuity instructions in place that explain how the business will operate immediately upon your unexpected absence or incapacitation?';
    case Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_UPDATED_ON:
      return 'When was the last time your Business Continuity Instructions were updated?';
    case Screen2Keys.HAVE_TOLD_APPROPRIATE_PEOPLE:
      return 'Have you told the appropriate people where you keep your Business Continuity Instructions?';
    case Screen3Keys.owner_type:
      return 'Are you a sole owner of your business or do you have partners?';
    case Screen5Keys.HAVE_BUY_SELL_AGREEMENT:
      return 'Since there are multiple owners of the business, do you have a buy-sell agreement?';
    case Screen5Keys.BUY_SELL_LAST_UPDATED_ON:
      return 'When was your buy-sell agreement last updated?';
    case Screen6Keys.HAVE_KEY_EMPLOYEE_AGREEMENTS_IN_PLACE:
      return 'Do you have Key Employee Agreements in place?';
    case Screen7Keys.HAVE_INCENTIVE_AGREEMENTS_IN_PLACE:
      return 'Do you have Employee Incentive Plans in place?';
    case Screen9Keys.HAVE_A_PERSONAL_TAX_MINIMIZATION_STRATEGY:
      return 'Do you have a personal tax-minimization strategy?';
    case Screen9Keys.HAVE_A_PERSONAL_TAX_MINIMIZATION_STRATEGY_NOW:
      return 'Do you have a business tax-minimization strategy now and for the time when you sell your business?';
    case Screen9Keys.HAVE_A_FINANCIAL_PLANNER:
      return 'Do you have a Financial Planner?';
    case Screen9Keys.HAVE_A_WEALTH_MANAGER:
      return 'Do you have a Wealth Manager?';
    case Screen8Keys.INSURANCE_TYPE:
      return 'Which types of business insurance do you have in place?';
    case Screen10Keys.IS_YOUR_BUSINESS_SUBJECT_TO_SAFETY_RISKS:
      return 'Is your business subject to safety risks and/or accidents?';
    case Screen10Keys.DO_YOU_PROVIDE_TRAINING_TO_EMPLOYEES:
      return 'Do you provide training to employees on security protocols, data protection practices, and emergency procedures to minimize risks ?';
    case Screen10Keys.DOES_YOUR_CURRENT_INSURANCE_PROTECT:
      return 'Does your current insurance protect for this?';
    case Screen11Keys.ARE_THERE_ANY_USUAL_THREATS:
      return 'Are there any unusual threats to your basic business in the industry or the general economy?';
    case Screen12Keys.ARE_THERE_COMPLIANCE_RISKS:
      return "Are there compliance or legal risks that could cause the business to suffer financial losses or other negative consequences due to your organization's failure to comply with applicable laws and regulatory standards?";
    case Screen13Keys.DOES_THE_BUSINESS_HAVE_ANY_PROPRIETARY:
      return 'Does the business have any proprietary products or processes that are, or should be, copyrighted?';
    case Screen13Keys.DO_YOU_HAVE_COPYRIGHT_PROTECTION:
      return 'Do you have copyright protection?';
    // case Screen13Keys.WHAT_ARE_THE_PRODUCTS_OR_PROCESSES:
    //   return 'What are the products or processes that should have copyright protection?';
    case Screen14Keys.DOES_YOUR_COMPANY_HAVE_BRAND:
      return 'Does your company have brand elements that distinguish your products or services in the marketplace, such as names, logos, slogans, and symbols? If so, do you have trademark protection?';
    case Screen14Keys.HAVE_TRADEMARK_PROTECTED:
      return 'Do you have them trademark protected?';
    case Screen15Keys.IS_THERE_CONCERN_FOR_CYBERSECURITY:
      return 'In your industry are there specific concerns for cybersecurity?';
    case Screen15Keys.HAVE_YOU_TAKEN_MEASURES:
      return 'Have you taken any measures to address these cybersecurity issues?';
    case Screen16Keys.HAVE_INTERNAL_FINANCIAL_CONTROL:
      return 'Does the business have internal financial controls in place to prevent fraud, misuse of funds, and financial mismanagement within the business.';
    case Screen17Keys.IS_IMPORTANT_TO_HAVE_SECURE_PREMISES:
      return 'Is it important for your business to have secure premises, equipment, and inventory with alarms, surveillance cameras, access control systems, and physical barriers?';
    case Screen17Keys.HAVE_SECURITY_MEASURES:
      return 'Do you have security measures in place?';
    case Screen18Keys.IS_IMPORTANT_TO_HAVE_DISASTER:
      return 'Is it important for your business to have a disaster preparedness plan to ensure operations can continue in the event of a natural disaster, fire or other emergencies?';
    case Screen18Keys.DO_YOU_HAVE_DISASTER_PLAN:
      return 'Do you have a disaster preparedness plan?';
    case Screen19Keys.HARASSMENT_POLICY_VISIBLE:
      return 'Do you have a well-defined sexual harassment policy that is visible to all employees?';
    case Screen19Keys.HARASSMENT_POLICY_LAST_UPDATED:
      return 'When was your sexual harassment policy last updated?';
    case Screen20Keys.DISCRIMINATION_POLICY_VISIBLE:
      return 'Do you have a discrimination policy that is visible to all employees?';
    case Screen20Keys.DISCRIMINATION_POLICY_LAST_UPDATED:
      return 'When was your discrimination policy last updated?';
    default:
      return '';
  }
};

// Using For Table in Screen8
export const InsuranceTypeheaderOptions = [
  'Type Of Insurance',
  ' ', // because of second header is blank
  'Coverage Amount',
  'Recommended Coverage (completed by Advisor)',
];

export interface InsuranceTypeResponse {
  value: undefined | boolean;
  coverage_amount: number;
  recommended_coverage: number;
}

export interface Screen8TableResponseType {
  'Buy-Sell Agreement': InsuranceTypeResponse;
  'Key Person': InsuranceTypeResponse;
  Umbrella: InsuranceTypeResponse;
  'General Liability': InsuranceTypeResponse;
  'Directors and Officers': InsuranceTypeResponse;
  'Workers Compensation': InsuranceTypeResponse;
  'Data Security': InsuranceTypeResponse;
  'Total: Post Transition': InsuranceTypeResponse;
}

export const screen8TableResponse: Screen8TableResponseType = {
  'Buy-Sell Agreement': {
    value: undefined,
    coverage_amount: 0,
    recommended_coverage: 0,
  },
  'Key Person': {
    value: undefined,
    coverage_amount: 0,
    recommended_coverage: 0,
  },
  Umbrella: {
    value: undefined,
    coverage_amount: 0,
    recommended_coverage: 0,
  },
  'General Liability': {
    value: undefined,
    coverage_amount: 0,
    recommended_coverage: 0,
  },
  'Directors and Officers': {
    value: undefined,
    coverage_amount: 0,
    recommended_coverage: 0,
  },
  'Workers Compensation': {
    value: undefined,
    coverage_amount: 0,
    recommended_coverage: 0,
  },
  'Data Security': {
    value: undefined,
    coverage_amount: 0,
    recommended_coverage: 0,
  },
  'Total: Post Transition': {
    value: undefined,
    coverage_amount: 0,
    recommended_coverage: 0,
  },
};

export const findEmptyKeys = (object: Record<string, InsuranceTypeResponse>) =>
  Object.entries(object).reduce((emptyKeys, [key, entry]) => {
    const { value, coverage_amount: coverageAmount } = entry;

    if (value !== false && (!value || !coverageAmount)) {
      emptyKeys.push(key);
    }
    return emptyKeys;
  }, [] as string[]);

// Some Questions Required based on chosen option
const isQuestionRequired = (
  response: { [key: string]: any },
  questionKey: string,
  isBusinessContinuityCompleted?: boolean
) => {
  const getBusinessContinuityAnswer = () =>
    response?.screen2?.[Screen2Keys.HAVE_BUSINESS_CONTINUITY_INSTRUCTIONS];

  const isYes = (key: string) => response?.[key] === 'yes';
  // const isNo = (key: string) => response?.[key] === 'no';

  switch (questionKey) {
    case Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_IN_EXIT_SMARTS:
      return isBusinessContinuityCompleted || false;

    case Screen2Keys.HAVE_BUSINESS_CONTINUITY_INSTRUCTIONS:
      return !isBusinessContinuityCompleted || false;

    case Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_UPDATED_ON:
    case Screen2Keys.HAVE_TOLD_APPROPRIATE_PEOPLE:
      return [
        screen2HaveBusinessCountinuityKeyOptions[0],
        screen2HaveBusinessCountinuityKeyOptions[1],
      ].includes(getBusinessContinuityAnswer());

    case Screen5Keys.BUY_SELL_LAST_UPDATED_ON:
      return isYes(Screen5Keys.HAVE_BUY_SELL_AGREEMENT);

    case Screen10Keys.DO_YOU_PROVIDE_TRAINING_TO_EMPLOYEES:
      return isYes(Screen10Keys.IS_YOUR_BUSINESS_SUBJECT_TO_SAFETY_RISKS);

    case Screen10Keys.DOES_YOUR_CURRENT_INSURANCE_PROTECT:
      return (
        isYes(Screen10Keys.IS_YOUR_BUSINESS_SUBJECT_TO_SAFETY_RISKS) &&
        isYes(Screen10Keys.DO_YOU_PROVIDE_TRAINING_TO_EMPLOYEES)
      );

    case Screen13Keys.DO_YOU_HAVE_COPYRIGHT_PROTECTION:
      return isYes(Screen13Keys.DOES_THE_BUSINESS_HAVE_ANY_PROPRIETARY);

    // case Screen13Keys.WHAT_ARE_THE_PRODUCTS_OR_PROCESSES:
    //   return isNo(Screen13Keys.DOES_THE_BUSINESS_HAVE_ANY_PROPRIETARY);

    case Screen14Keys.HAVE_TRADEMARK_PROTECTED:
      return isYes(Screen14Keys.DOES_YOUR_COMPANY_HAVE_BRAND);

    case Screen15Keys.HAVE_YOU_TAKEN_MEASURES:
      return isYes(Screen15Keys.IS_THERE_CONCERN_FOR_CYBERSECURITY);

    case Screen17Keys.HAVE_SECURITY_MEASURES:
      return isYes(Screen17Keys.IS_IMPORTANT_TO_HAVE_SECURE_PREMISES);

    case Screen18Keys.DO_YOU_HAVE_DISASTER_PLAN:
      return isYes(Screen18Keys.IS_IMPORTANT_TO_HAVE_DISASTER);
    case Screen19Keys.HARASSMENT_POLICY_VISIBLE:
      return isYes(Screen19Keys.HARASSMENT_POLICY_LAST_UPDATED);
    case Screen20Keys.DISCRIMINATION_POLICY_VISIBLE:
      return isYes(Screen20Keys.DISCRIMINATION_POLICY_LAST_UPDATED);
    default:
      return true;
  }
};

export const getAssetProtectionToolProgressPercentage = (
  response: {
    [key: string]: any;
  },
  isBusinessContinuityCompleted: boolean,
  ownerType: OwnerType | null
) => {
  if (!response || Object.keys(response).length === 0) {
    return 0; // Return 0 if data is null, undefined, or an empty object
  }

  const totalTableKeys = Object.keys(screen8TableResponse).length;
  const screenKeys = [
    Screen2Keys,
    Screen3Keys,
    Screen5Keys,
    Screen6Keys,
    Screen7Keys,
    screen8TableResponse,
    Screen9Keys,
    Screen10Keys,
    Screen11Keys,
    Screen12Keys,
    Screen13Keys,
    Screen14Keys,
    Screen15Keys,
    Screen16Keys,
    Screen17Keys,
    Screen18Keys,
    Screen19Keys,
    Screen20Keys,
  ];

  let totalQuestions = screenKeys.reduce(
    (sum, screen) => sum + Object.keys(screen).length,
    0
  );
  let filledQuestions = 0;

  if (
    ownerType === OwnerType.SINGLE_OWNER ||
    response?.screen3?.[Screen3Keys.owner_type] === OwnerType.SINGLE_OWNER
  ) {
    totalQuestions -= Object.keys(Screen5Keys).length; // because screen5 is only for multiple owner
  }

  Object.keys(response).forEach((key) => {
    // Will consider screen5 only for multiple owner
    if (
      key === 'screen5' &&
      (response?.screen3?.[Screen3Keys.owner_type] ===
        OwnerType.MULTIPLE_OWNER ||
        ownerType === OwnerType.MULTIPLE_OWNER)
    ) {
      Object.keys(response[key]).forEach((subKey) => {
        if (isQuestionRequired(response, subKey)) {
          if (
            response[key][subKey] !== null &&
            response[key][subKey] !== undefined
          ) {
            filledQuestions += 1;
          }
        } else {
          filledQuestions += 1;
        }
      });
    } else if (key === 'screen8') {
      const EmptyKeys = findEmptyKeys(response.screen8);
      filledQuestions += totalTableKeys - EmptyKeys.length;
    } else if (key !== 'saved_screen' && key !== 'screen5') {
      Object.keys(response[key]).forEach((subKey) => {
        if (
          isQuestionRequired(response, subKey, isBusinessContinuityCompleted)
        ) {
          if (
            response[key][subKey] !== null &&
            response[key][subKey] !== undefined
          ) {
            filledQuestions += 1;
          }
        } else {
          filledQuestions += 1;
        }
      });
    }
  });

  return (filledQuestions / totalQuestions) * 100;
};

const createScreenMap = (screenKeys: any, screenNumber: number) =>
  Object.fromEntries(Object.values(screenKeys).map((k) => [k, screenNumber]));

const createSingleKeyMap = (key: string, screenNumber: number) => ({
  [key]: screenNumber,
});

export const getScreenNumberByKey = (key: string): number | undefined => {
  const screenMap: { [key: string]: number } = {
    ...createScreenMap(Screen2Keys, 2),
    ...createSingleKeyMap(Screen3Keys.owner_type, 3),
    ...createScreenMap(Screen5Keys, 5),
    ...createSingleKeyMap(Screen6Keys.HAVE_KEY_EMPLOYEE_AGREEMENTS_IN_PLACE, 6),
    ...createSingleKeyMap(Screen7Keys.HAVE_INCENTIVE_AGREEMENTS_IN_PLACE, 7),
    ...createSingleKeyMap(Screen8Keys.INSURANCE_TYPE, 8),
    ...createScreenMap(Screen9Keys, 9),
    ...createScreenMap(Screen10Keys, 10),
    ...createSingleKeyMap(Screen11Keys.ARE_THERE_ANY_USUAL_THREATS, 11),
    ...createSingleKeyMap(Screen12Keys.ARE_THERE_COMPLIANCE_RISKS, 12),
    ...createScreenMap(Screen13Keys, 13),
    ...createScreenMap(Screen14Keys, 14),
    ...createScreenMap(Screen15Keys, 15),
    ...createSingleKeyMap(Screen16Keys.HAVE_INTERNAL_FINANCIAL_CONTROL, 16),
    ...createScreenMap(Screen17Keys, 17),
    ...createScreenMap(Screen18Keys, 18),
  };

  return screenMap[key];
};

export const getScreen = (
  currentScreen: number,
  AssetProtectionData: any,
  handleBack: (data: any, key?: string, screenNumber?: number) => void,
  handleNext: (data: any, key?: string, screenNumber?: number) => void,
  handleSubmit: (
    data: any,
    key?: string,
    submitType?: boolean,
    saved_screen?: number
  ) => void,
  navigate: NavigateFunction,
  loggedInUser?: User,
  ownerType?: OwnerType | null
) => {
  // get started page is the 1st screen
  if (currentScreen === 1) {
    return (
      <GetStartedPage
        onGestartedClick={() => {
          handleNext(AssetProtectionData);
        }}
      />
    );
  }
  if (currentScreen === 2) {
    return (
      <Screen2
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          let screenNumber;
          if (ownerType) {
            screenNumber = ownerType === OwnerType.SINGLE_OWNER ? 4 : 5;
          }
          handleNext(data, 'screen2', screenNumber);
        }}
        handlebackClick={() => {
          if (loggedInUser?.type === UserType.ADVISOR) {
            navigate(`/${UserRouteType.ADVISOR}/${RouteKey.DASHBOARD}`);
          } else {
            navigate(`/${UserRouteType.BUSINESS_OWNER}/${RouteKey.DASHBOARD}`);
          }
        }}
        handleSaveAsDraftClick={(data: any) => {
          handleSubmit(data, 'screen2');
        }}
      />
    );
  }
  if (currentScreen === 3) {
    return (
      <Screen3
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          const screenNumber =
            data[Screen3Keys.owner_type] === OwnerType.SINGLE_OWNER ? 4 : 5;
          handleNext(data, 'screen3', screenNumber);
        }}
        handlebackClick={(data) => {
          handleBack(data, 'screen3');
        }}
        handleSaveAsDraftClick={(data: any) => {
          if (
            (AssetProtectionData.saved_screen === 4 &&
              data[Screen3Keys.owner_type] === OwnerType.MULTIPLE_OWNER) ||
            (AssetProtectionData.saved_screen === 5 &&
              data[Screen3Keys.owner_type] === OwnerType.SINGLE_OWNER)
          ) {
            handleSubmit(data, 'screen3', undefined, 3);
          } else {
            handleSubmit(data, 'screen3');
          }
        }}
      />
    );
  }
  // screen4 is for single owner
  if (currentScreen === 4) {
    return (
      <Screen4
        handleNextClick={() => {
          handleNext(AssetProtectionData, undefined, 6);
        }}
        handlebackClick={() => {
          if (AssetProtectionData?.screen3) {
            handleBack(AssetProtectionData, undefined, 3);
          } else {
            handleBack(AssetProtectionData, undefined, 2);
          }
        }}
        handleSaveAsDraftClick={() => {
          const savedScreen = AssetProtectionData?.saved_screen || 0;
          if (savedScreen === 5) {
            handleSubmit(AssetProtectionData, undefined, false, 4);
          } else {
            handleSubmit(AssetProtectionData);
          }
        }}
      />
    );
  }
  // screen5 is for multiple owner
  if (currentScreen === 5) {
    return (
      <Screen5
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          handleNext(data, 'screen5');
        }}
        handlebackClick={(data) => {
          handleBack(data, 'screen5', 2);
        }}
        handleSaveAsDraftClick={(data: any) => {
          handleSubmit(data, 'screen5');
        }}
      />
    );
  }
  if (currentScreen === 6) {
    return (
      <Screen6
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          handleNext(data, 'screen6');
        }}
        handlebackClick={(data) => {
          let screenNumber = ownerType === OwnerType.SINGLE_OWNER ? 4 : 5;
          if (AssetProtectionData?.screen3) {
            screenNumber =
              AssetProtectionData?.screen3[Screen3Keys.owner_type] ===
              OwnerType.SINGLE_OWNER
                ? 4
                : 5;
          }
          handleBack(data, 'screen6', screenNumber);
        }}
        handleSaveAsDraftClick={(data: any) => {
          handleSubmit(data, 'screen6');
        }}
      />
    );
  }
  if (currentScreen === 7) {
    return (
      <Screen7
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          handleNext(data, 'screen7');
        }}
        handlebackClick={(data) => {
          handleBack(data, 'screen7');
        }}
        handleSaveAsDraftClick={(data: any) => {
          handleSubmit(data, 'screen7');
        }}
      />
    );
  }
  // This the table screen
  if (currentScreen === 8) {
    return (
      <Screen8
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          handleNext(data, 'screen8');
        }}
        handlebackClick={(data) => {
          handleBack(data, 'screen8');
        }}
        handleSaveAsDraftClick={(data: any) => {
          handleSubmit(data, 'screen8');
        }}
      />
    );
  }
  if (currentScreen === 9) {
    return (
      <Screen9
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          handleNext(data, 'screen9');
        }}
        handleBackClick={(data) => {
          handleBack(data, 'screen9');
        }}
        handleSaveAsDraftClick={(data: any) => {
          handleSubmit(data, 'screen9');
        }}
      />
    );
  }
  if (currentScreen === 10) {
    return (
      <Screen10
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          handleNext(data, 'screen10');
        }}
        handlebackClick={(data) => {
          handleBack(data, 'screen10');
        }}
        handleSaveAsDraftClick={(data: any) => {
          handleSubmit(data, 'screen10');
        }}
      />
    );
  }
  if (currentScreen === 11) {
    return (
      <Screen11
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          handleNext(data, 'screen11');
        }}
        handlebackClick={(data) => {
          handleBack(data, 'screen11');
        }}
        handleSaveAsDraftClick={(data: any) => {
          handleSubmit(data, 'screen11');
        }}
      />
    );
  }
  if (currentScreen === 12) {
    return (
      <Screen12
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          handleNext(data, 'screen12');
        }}
        handlebackClick={(data) => {
          handleBack(data, 'screen12');
        }}
        handleSaveAsDraftClick={(data: any) => {
          handleSubmit(data, 'screen12');
        }}
      />
    );
  }
  if (currentScreen === 13) {
    return (
      <Screen13
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          handleNext(data, 'screen13');
        }}
        handlebackClick={(data) => {
          handleBack(data, 'screen13');
        }}
        handleSaveAsDraftClick={(data: any) => {
          handleSubmit(data, 'screen13');
        }}
      />
    );
  }
  if (currentScreen === 14) {
    return (
      <Screen14
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          handleNext(data, 'screen14');
        }}
        handlebackClick={(data) => {
          handleBack(data, 'screen14');
        }}
        handleSaveAsDraftClick={(data: any) => {
          handleSubmit(data, 'screen14');
        }}
      />
    );
  }
  if (currentScreen === 15) {
    return (
      <Screen15
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          handleNext(data, 'screen15');
        }}
        handlebackClick={(data) => {
          handleBack(data, 'screen15');
        }}
        handleSaveAsDraftClick={(data: any) => {
          handleSubmit(data, 'screen15');
        }}
      />
    );
  }
  if (currentScreen === 16) {
    return (
      <Screen16
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          handleNext(data, 'screen16');
        }}
        handlebackClick={(data) => {
          handleBack(data, 'screen16');
        }}
        handleSaveAsDraftClick={(data: any) => {
          handleSubmit(data, 'screen16');
        }}
      />
    );
  }
  if (currentScreen === 17) {
    return (
      <Screen17
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          handleNext(data, 'screen17');
        }}
        handlebackClick={(data) => {
          handleBack(data, 'screen17');
        }}
        handleSaveAsDraftClick={(data: any) => {
          handleSubmit(data, 'screen17');
        }}
      />
    );
  }

  if (currentScreen === 18) {
    return (
      <Screen18
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          handleNext(data, 'screen18');
        }}
        handlebackClick={(data) => {
          handleBack(data, 'screen18');
        }}
        handleSaveAsDraftClick={(data: any) => {
          handleSubmit(data, 'screen18');
        }}
      />
    );
  }

  if (currentScreen === 19) {
    return (
      <Screen19
        data={AssetProtectionData}
        handleNextClick={(data: any) => {
          handleNext(data, 'screen19');
        }}
        handlebackClick={(data) => {
          handleBack(data, 'screen19');
        }}
        handleSaveAsDraftClick={(data: any) => {
          handleSubmit(data, 'screen19');
        }}
      />
    );
  }
  return (
    <Screen20
      data={AssetProtectionData}
      handleNextClick={(data: any) => {
        handleSubmit(data, 'screen20', true);
      }}
      handlebackClick={(data) => {
        handleBack(data, 'screen20');
      }}
      handleSaveAsDraftClick={(data: any) => {
        handleSubmit(data, 'screen20');
      }}
    />
  );
};
