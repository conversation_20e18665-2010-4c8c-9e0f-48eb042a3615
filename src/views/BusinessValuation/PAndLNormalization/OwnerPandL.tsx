import { FieldArray, Form, Formik } from 'formik';
import React, { useEffect } from 'react';
import { IoInformationCircle } from 'react-icons/io5';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router';
import { Tooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { fetchBusinessOwner } from 'store/actions/business-owner.action';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { useParamSelector } from 'store/selectors/base.selectors';
import { getBusinessOwnerDetails } from 'store/selectors/business-owner.selector';
import { getUserData } from 'store/selectors/user.selector';
import { UserType } from 'types/enum';
import { getKey } from 'utils/helpers/Helpers';
import * as yup from 'yup';
import { getYear, transformOwnerPAndLData } from '../BusinessValuationConfig';

interface Props {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  onSaveToDraftClick: (data: any) => void;
  data: any;
}
const OwnerPandL: React.FC<Props> = (props) => {
  const { nextStep, setData, backStep, data, onSaveToDraftClick } = props;

  const isLoading = useSelector(getAssessmentToolLoading);

  const user = useSelector(getUserData);
  const { id } = useParams();
  const dispatch = useDispatch();
  const businessOwner = useParamSelector(getBusinessOwnerDetails, {
    id: +id!,
  });

  useEffect(() => {
    if (id && user?.type !== UserType.BUSINESS_OWNER) {
      dispatch(fetchBusinessOwner(id));
    }
  }, []);

  const userDetails =
    user?.type === UserType.BUSINESS_OWNER ? user : businessOwner;

  const getInitialvalues = () => {
    let modifiedValues = {
      owner_name:
        data?.owner_p_and_l?.[0]?.owner_name || userDetails?.name || '',
      [`owner_${getYear(1)}_salary`]:
        data?.owner_p_and_l?.[0]?.[`owner_${getYear(1)}_salary`] || '',
      [`owner_${getYear(2)}_salary`]:
        data?.owner_p_and_l?.[0]?.[`owner_${getYear(2)}_salary`] || '',
      [`owner_${getYear(3)}_salary`]:
        data?.owner_p_and_l?.[0]?.[`owner_${getYear(3)}_salary`] || '',
      [`owner_${getYear(1)}_bonus`]:
        data?.owner_p_and_l?.[0]?.[`owner_${getYear(1)}_bonus`] || '',
      [`owner_${getYear(2)}_bonus`]:
        data?.owner_p_and_l?.[0]?.[`owner_${getYear(2)}_bonus`] || '',
      [`owner_${getYear(3)}_bonus`]:
        data?.owner_p_and_l?.[0]?.[`owner_${getYear(3)}_bonus`] || '',
    };

    modifiedValues = {
      ...modifiedValues,
      other_owners: data?.owner_p_and_l?.slice(1) || [],
    };
    return modifiedValues;
  };

  const validationSchema = yup.object().shape({
    owner_name: yup.string().required('Owner Name is required'),
    [`owner_${getYear(1)}_salary`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Salary of Owner is required'),
    [`owner_${getYear(2)}_salary`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Salary of Owner is required'),
    [`owner_${getYear(3)}_salary`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Salary of Owner is required'),
    [`owner_${getYear(1)}_bonus`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Bonus of Owner is required'),
    [`owner_${getYear(2)}_bonus`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Bonus of Owner is required'),
    [`owner_${getYear(3)}_bonus`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Bonus of Owner is required'),
    other_owners: yup.array().of(
      yup.object({
        owner_name: yup.string().required('Owner Name is required'),
        [`owner_${getYear(1)}_salary`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .required('Salary of Owner is required'),
        [`owner_${getYear(2)}_salary`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .required('Salary of Owner is required'),
        [`owner_${getYear(3)}_salary`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .required('Salary of Owner is required'),
        [`owner_${getYear(1)}_bonus`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .required('Bonus of Owner is required'),
        [`owner_${getYear(2)}_bonus`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .required('Bonus of Owner is required'),
        [`owner_${getYear(3)}_bonus`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .required('Bonus of Owner is required'),
        [`market_${getYear(1)}_salary`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .required('Salary of Market is required'),
        [`market_${getYear(2)}_salary`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .required('Salary of Market is required'),
        [`market_${getYear(3)}_salary`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .required('Salary of Market is required'),
        [`market_${getYear(1)}_bonus`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .required('Bonus of Market is required'),
        [`market_${getYear(2)}_bonus`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .required('Bonus of Market is required'),
        [`market_${getYear(3)}_bonus`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .required('Bonus of Market is required'),
        [`net_adjustments_${getYear(1)}_salary`]: yup.number(),
        [`net_adjustments_${getYear(2)}_salary`]: yup.number(),
        [`net_adjustments_${getYear(3)}_salary`]: yup.number(),
        [`net_adjustments_${getYear(1)}_bonus`]: yup.number(),
        [`net_adjustments_${getYear(2)}_bonus`]: yup.number(),
        [`net_adjustments_${getYear(3)}_bonus`]: yup.number(),
      })
    ),
  });

  const handleSubmit = (values: any) => {
    setData(transformOwnerPAndLData(values));
    nextStep();
  };

  return (
    <div className='flex flex-col'>
      <Formik
        initialValues={getInitialvalues()}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values, setFieldValue }) => (
          <Form className='relative pl-5 py-3  flex flex-col gap-4 bg-white'>
            <h2 className='text-[1.1rem] font-[500] pr-10'>
              The following application will guide you through the
              identification and computation of expense add-backs necessary to
              normalize your business P&L. Make entries that apply to your
              situation.
            </h2>
            <div className='relative z-10 mb-5 text-xl items-center font-semibold'>
              <span className='absolute left-[26%]  mt-1'>{getYear(1)}</span>
              <span className='absolute left-[52%] mt-1'>{getYear(2)}</span>
              <span className='absolute left-[78%] mt-1'>{getYear(3)}</span>
            </div>

            {/* First Owner Salary and Bonus Part */}
            <div className='h-[calc(100vh-26rem)] pr-4 scrollbar overflow-auto'>
              <div className='flex gap-1  text-[1.2rem]'>
                <span className='underline font-semibold'>
                  {'First Owner '}
                </span>
                <span className='font-semibold mx-1'> : </span>
                <span className='text-blue-01 underline font-semibold  pb-2'>
                  {userDetails?.name}
                </span>
              </div>
              <span className='text-blue-01 underline font-semibold'>
                Owner’s Salary and Bonus
              </span>
              <div className='grid grid-cols-4 gap-x-16 gap-y-1  mt-2 text-[1.1rem]'>
                <div className='flex  items-center gap-2'>
                  <IoInformationCircle className='text-blue-01 salary' />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='.salary'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      Actual Salary of Owner. If multiple owners Use Add button
                    </Tooltip>
                    <span>Salary</span>
                  </div>
                </div>
                <FormikInput
                  className='!h-10'
                  name={`owner_${getYear(1)}_salary`}
                  key={`owner_${getYear(1)}_salary`}
                />
                <FormikInput
                  className='!h-10'
                  name={`owner_${getYear(2)}_salary`}
                  key={`owner_${getYear(2)}_salary`}
                />
                <FormikInput
                  className='!h-10'
                  name={`owner_${getYear(3)}_salary`}
                  key={`owner_${getYear(3)}_salary`}
                />
                <div className='flex  items-center gap-2'>
                  <IoInformationCircle className='text-blue-01 bonus' />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='.bonus'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96!font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      Actual Bonus taken.
                    </Tooltip>
                    <span>Bonuses</span>
                  </div>
                </div>
                <FormikInput
                  className='!h-10'
                  name={`owner_${getYear(1)}_bonus`}
                  key={`owner_${getYear(1)}_bonus`}
                />
                <FormikInput
                  className='!h-10'
                  name={`owner_${getYear(2)}_bonus`}
                  key={`owner_${getYear(2)}_bonus`}
                />
                <FormikInput
                  className='!h-10'
                  name={`owner_${getYear(3)}_bonus`}
                  key={`owner_${getYear(3)}_bonus`}
                />
              </div>
              <FieldArray name='other_owners'>
                {({ push }) => (
                  <div>
                    {values.other_owners &&
                      values.other_owners.length > 0 &&
                      values.other_owners.map(
                        (other_owners: any, index: number) => (
                          <div key={getKey(index)} className=''>
                            <div className='flex gap-1  text-[1.2rem]'>
                              <span className='underline font-semibold'>
                                Owner
                              </span>
                              <span className='font-semibold mx-1'> : </span>
                              <span className='text-blue-01 underline font-semibold  pb-2'>
                                {values.other_owners[index].owner_name}
                              </span>
                            </div>

                            <div className='grid grid-cols-4 items-center gap-x-16 '>
                              <span>Name</span>
                              <div className='col-span-3'>
                                <FormikInput
                                  className='!h-10 mt-5 '
                                  name={`other_owners.${index}.owner_name`}
                                  key={`other_owners.${getKey(
                                    index
                                  )}.owner_name`}
                                />
                              </div>
                            </div>

                            {/* Owner Salary and Bonus Part */}
                            <span className='text-blue-01 underline font-semibold'>
                              Owner’s Salary and Bonus
                            </span>
                            <div className='grid grid-cols-4 gap-x-16 gap-y-1  mt-2 text-[1.1rem]'>
                              <div className='flex  items-center gap-2'>
                                <IoInformationCircle className='text-blue-01 salary' />
                                <div className='relative'>
                                  <Tooltip
                                    anchorSelect='.salary'
                                    place='right'
                                    offset={0}
                                    className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                    classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                  >
                                    Actual Salary of Owner. If multiple owners
                                    Use Add button
                                  </Tooltip>
                                  <span>Salary</span>
                                </div>
                              </div>
                              <FormikInput
                                valueChanged={(event) => {
                                  const netValue =
                                    event.target.value.replace(/,/g, '') -
                                    Number(
                                      values.other_owners[index][
                                        `market_${getYear(1)}_salary`
                                      ].replace(/,/g, '')
                                    );
                                  setFieldValue(
                                    `other_owners.${getKey(
                                      index
                                    )}.net_adjustments_${getYear(1)}_salary`,
                                    netValue
                                  );
                                }}
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.owner_${getYear(1)}_salary`}
                                key={`other_owners.${getKey(
                                  index
                                )}.owner_${getYear(1)}_salary`}
                              />
                              <FormikInput
                                valueChanged={(event) => {
                                  const netValue =
                                    event.target.value.replace(/,/g, '') -
                                    Number(
                                      values.other_owners[index][
                                        `market_${getYear(2)}_salary`
                                      ].replace(/,/g, '')
                                    );
                                  setFieldValue(
                                    `other_owners.${getKey(
                                      index
                                    )}.net_adjustments_${getYear(2)}_salary`,
                                    netValue
                                  );
                                }}
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.owner_${getYear(2)}_salary`}
                                key={`other_owners.${getKey(
                                  index
                                )}.owner_${getYear(2)}_salary`}
                              />
                              <FormikInput
                                valueChanged={(event) => {
                                  const netValue =
                                    event.target.value.replace(/,/g, '') -
                                    Number(
                                      values.other_owners[index][
                                        `market_${getYear(3)}_salary`
                                      ].replace(/,/g, '')
                                    );
                                  setFieldValue(
                                    `other_owners.${getKey(
                                      index
                                    )}.net_adjustments_${getYear(3)}_salary`,
                                    netValue
                                  );
                                }}
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.owner_${getYear(3)}_salary`}
                                key={`other_owners.${getKey(
                                  index
                                )}.owner_${getYear(3)}_salary,`}
                              />
                              <div className='flex  items-center gap-2'>
                                <IoInformationCircle className='text-blue-01 bonus' />
                                <div className='relative'>
                                  <Tooltip
                                    anchorSelect='.bonus'
                                    place='right'
                                    offset={0}
                                    className=' !bg-gray-100 border !z-10  !w-96!font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                    classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                  >
                                    Actual Bonus taken.
                                  </Tooltip>
                                  <span>Bonuses</span>
                                </div>
                              </div>
                              <FormikInput
                                valueChanged={(event) => {
                                  const netValue =
                                    event.target.value.replace(/,/g, '') -
                                    Number(
                                      values.other_owners[index][
                                        `market_${getYear(1)}_bonus`
                                      ].replace(/,/g, '')
                                    );
                                  setFieldValue(
                                    `other_owners.${getKey(
                                      index
                                    )}.net_adjustments_${getYear(1)}_bonus`,
                                    netValue
                                  );
                                }}
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.owner_${getYear(1)}_bonus`}
                                key={`other_owners.${getKey(
                                  index
                                )}.owner_${getYear(1)}_bonus,`}
                              />
                              <FormikInput
                                valueChanged={(event) => {
                                  const netValue =
                                    event.target.value.replace(/,/g, '') -
                                    Number(
                                      values.other_owners[index][
                                        `market_${getYear(2)}_bonus`
                                      ].replace(/,/g, '')
                                    );
                                  setFieldValue(
                                    `other_owners.${getKey(
                                      index
                                    )}.net_adjustments_${getYear(2)}_bonus`,
                                    netValue
                                  );
                                }}
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.owner_${getYear(2)}_bonus`}
                                key={`other_owners.${getKey(
                                  index
                                )}.owner_${getYear(2)}_bonus,`}
                              />
                              <FormikInput
                                valueChanged={(event) => {
                                  const netValue =
                                    event.target.value.replace(/,/g, '') -
                                    Number(
                                      values.other_owners[index][
                                        `market_${getYear(3)}_bonus`
                                      ].replace(/,/g, '')
                                    );
                                  setFieldValue(
                                    `other_owners.${getKey(
                                      index
                                    )}.net_adjustments_${getYear(3)}_bonus`,
                                    netValue
                                  );
                                }}
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.owner_${getYear(3)}_bonus`}
                                key={`other_owners.${getKey(
                                  index
                                )}.owner_${getYear(3)}_bonus,`}
                              />
                            </div>

                            {/* Market Salary and Bonus Part */}

                            <span className='text-blue-01 underline font-semibold'>
                              Market Salary and Bonus
                            </span>

                            <div className='grid grid-cols-4 gap-x-16 gap-y-1 mt-2 text-[1.1rem]'>
                              <div className='flex  items-center gap-2'>
                                <IoInformationCircle className='text-blue-01 marketsalary' />
                                <div className='relative'>
                                  <Tooltip
                                    anchorSelect='.marketsalary'
                                    place='right'
                                    offset={0}
                                    className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                    classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                  >
                                    Salary for same job done by someone other
                                    than the business owner(s)
                                  </Tooltip>
                                  <span>Salary</span>
                                </div>
                              </div>
                              <FormikInput
                                valueChanged={(event) => {
                                  const netValue =
                                    Number(
                                      values.other_owners[index][
                                        `owner_${getYear(1)}_salary`
                                      ].replace(/,/g, '')
                                    ) - event.target.value.replace(/,/g, '');
                                  setFieldValue(
                                    `other_owners.${getKey(
                                      index
                                    )}.net_adjustments_${getYear(1)}_salary`,
                                    netValue
                                  );
                                }}
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.market_${getYear(1)}_salary`}
                                key={`other_owners.${getKey(
                                  index
                                )}.market_${getYear(1)}_salary`}
                              />
                              <FormikInput
                                valueChanged={(event) => {
                                  const netValue =
                                    Number(
                                      values.other_owners[index][
                                        `owner_${getYear(2)}_salary`
                                      ].replace(/,/g, '')
                                    ) - event.target.value.replace(/,/g, '');
                                  setFieldValue(
                                    `other_owners.${getKey(
                                      index
                                    )}.net_adjustments_${getYear(2)}_salary`,
                                    netValue
                                  );
                                }}
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.market_${getYear(2)}_salary`}
                                key={`other_owners.${getKey(
                                  index
                                )}.market_${getYear(2)}_salary`}
                              />
                              <FormikInput
                                valueChanged={(event) => {
                                  const netValue =
                                    Number(
                                      values.other_owners[index][
                                        `owner_${getYear(3)}_salary`
                                      ].replace(/,/g, '')
                                    ) - event.target.value.replace(/,/g, '');
                                  setFieldValue(
                                    `other_owners.${getKey(
                                      index
                                    )}.net_adjustments_${getYear(3)}_salary`,
                                    netValue
                                  );
                                }}
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.market_${getYear(3)}_salary`}
                                key={`other_owners.${getKey(
                                  index
                                )}.market_${getYear(3)}_salary`}
                              />
                              <div className='flex  items-center gap-2'>
                                <IoInformationCircle className='text-blue-01 marketBonus' />
                                <div className='relative'>
                                  <Tooltip
                                    anchorSelect='.marketBonus'
                                    place='right'
                                    offset={0}
                                    className=' !bg-gray-100 border !z-10 !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                    classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                  >
                                    Bonus for same job done by someone other
                                    than the business owner(s)
                                  </Tooltip>
                                  <span>Bonuses</span>
                                </div>
                              </div>
                              <FormikInput
                                valueChanged={(event) => {
                                  const netValue =
                                    Number(
                                      values.other_owners[index][
                                        `owner_${getYear(1)}_bonus`
                                      ].replace(/,/g, '')
                                    ) - event.target.value.replace(/,/g, '');
                                  setFieldValue(
                                    `other_owners.${getKey(
                                      index
                                    )}.net_adjustments_${getYear(1)}_bonus`,
                                    netValue
                                  );
                                }}
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.market_${getYear(1)}_bonus`}
                                key={`other_owners.${getKey(
                                  index
                                )}.market_${getYear(1)}_bonus`}
                              />
                              <FormikInput
                                valueChanged={(event) => {
                                  const netValue =
                                    Number(
                                      values.other_owners[index][
                                        `owner_${getYear(2)}_bonus`
                                      ].replace(/,/g, '')
                                    ) - event.target.value.replace(/,/g, '');
                                  setFieldValue(
                                    `other_owners.${getKey(
                                      index
                                    )}.net_adjustments_${getYear(2)}_bonus`,
                                    netValue
                                  );
                                }}
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.market_${getYear(2)}_bonus`}
                                key={`other_owners.${getKey(
                                  index
                                )}.market_${getYear(2)}_bonus`}
                              />
                              <FormikInput
                                valueChanged={(event) => {
                                  const netValue =
                                    Number(
                                      values.other_owners[index][
                                        `owner_${getYear(3)}_bonus`
                                      ].replace(/,/g, '')
                                    ) - event.target.value.replace(/,/g, '');
                                  setFieldValue(
                                    `other_owners.${getKey(
                                      index
                                    )}.net_adjustments_${getYear(3)}_bonus`,
                                    netValue
                                  );
                                }}
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.market_${getYear(3)}_bonus`}
                                key={`other_owners.${getKey(
                                  index
                                )}.market_${getYear(3)}_bonus`}
                              />
                            </div>

                            {/* Owner Salary and Benefits Net Adjustments Part */}

                            <span className='text-blue-01 underline font-semibold'>
                              Owner&apos;s Salary and Benefits Net Adjustments
                            </span>

                            <div className='grid grid-cols-4 gap-x-16 gap-y-1 mt-2 text-[1.1rem]'>
                              <div className='flex  items-center gap-2'>
                                <IoInformationCircle className='text-blue-01 ownersalary' />
                                <div className='relative'>
                                  <Tooltip
                                    anchorSelect='.ownersalary'
                                    place='right'
                                    offset={0}
                                    className=' !bg-gray-100 border !z-10  !w-[50rem] !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                    classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                  >
                                    This is the difference calculated by
                                    subtracting Market Salary and Benefits from
                                    Owner&apos;s Salary and Benefits.
                                  </Tooltip>
                                  <span>Salary</span>
                                </div>
                              </div>
                              <FormikInput
                                disabled
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.net_adjustments_${getYear(1)}_salary`}
                                key={`other_owners.${getKey(
                                  index
                                )}.net_adjustments_${getYear(1)}_salary`}
                              />
                              <FormikInput
                                disabled
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.net_adjustments_${getYear(2)}_salary`}
                                key={`other_owners.${getKey(
                                  index
                                )}.net_adjustments_${getYear(2)}_salary`}
                              />
                              <FormikInput
                                disabled
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.net_adjustments_${getYear(3)}_salary`}
                                key={`other_owners.${getKey(
                                  index
                                )}.net_adjustments_${getYear(3)}_salary`}
                              />
                              <div className='flex  items-center gap-2'>
                                <IoInformationCircle
                                  id='ownerbonus'
                                  className='text-blue-01'
                                />
                                <div className='relative'>
                                  <Tooltip
                                    anchorSelect='#ownerbonus'
                                    place='right'
                                    offset={0}
                                    className=' !bg-gray-100 border !z-10  !w-[54rem] !font-[500]  !left-1 !-top-2 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                    classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                  >
                                    This is the difference calculated by
                                    subtracting Market Bonus and Benefits from
                                    Owner&apos;s Bonus and Benefits.
                                  </Tooltip>
                                  <span>Bonuses</span>
                                </div>
                              </div>
                              <FormikInput
                                disabled
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.net_adjustments_${getYear(1)}_bonus`}
                                key={`other_owners.${getKey(
                                  index
                                )}.net_adjustments_${getYear(1)}_bonus`}
                              />
                              <FormikInput
                                disabled
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.net_adjustments_${getYear(2)}_bonus`}
                                key={`other_owners.${getKey(
                                  index
                                )}.net_adjustments_${getYear(2)}_bonus`}
                              />
                              <FormikInput
                                disabled
                                className='!h-10'
                                name={`other_owners.${getKey(
                                  index
                                )}.net_adjustments_${getYear(3)}_bonus`}
                                key={`other_owners.${getKey(
                                  index
                                )}.net_adjustments_${getYear(3)}_bonus`}
                              />
                            </div>
                          </div>
                        )
                      )}

                    <Button
                      type='button'
                      className='px-6 py-2 !absolute top-[5rem] right-[2rem]'
                      onClick={() =>
                        push({
                          owner_name: '',
                          [`owner_${getYear(1)}_salary`]: '',
                          [`owner_${getYear(2)}_salary`]: '',
                          [`owner_${getYear(3)}_salary`]: '',
                          [`owner_${getYear(1)}_bonus`]: '',
                          [`owner_${getYear(2)}_bonus`]: '',
                          [`owner_${getYear(3)}_bonus`]: '',
                          [`market_${getYear(1)}_salary`]: '',
                          [`market_${getYear(2)}_salary`]: '',
                          [`market_${getYear(3)}_salary`]: '',
                          [`market_${getYear(1)}_bonus`]: '',
                          [`market_${getYear(2)}_bonus`]: '',
                          [`market_${getYear(3)}_bonus`]: '',
                          [`net_adjustments_${getYear(1)}_salary`]: 0,
                          [`net_adjustments_${getYear(2)}_salary`]: 0,
                          [`net_adjustments_${getYear(3)}_salary`]: 0,
                          [`net_adjustments_${getYear(1)}_bonus`]: 0,
                          [`net_adjustments_${getYear(2)}_bonus`]: 0,
                          [`net_adjustments_${getYear(3)}_bonus`]: 0,
                        })
                      }
                    >
                      Add
                    </Button>
                  </div>
                )}
              </FieldArray>
            </div>
            <div className='pr-7'>
              <BackNextComponent
                backStep={backStep}
                buttonType='submit'
                isLoading={isLoading}
                isNextDisable={false}
                onSaveToDraftClick={() => {
                  onSaveToDraftClick(transformOwnerPAndLData(values));
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default OwnerPandL;
