import { FC, useState } from 'react';
import Button from 'shared-resources/components/Button/Button';
import { toast } from 'react-toastify';
import { useSelector } from 'react-redux';
import { getBackupCodes } from 'store/selectors/user.selector';

interface BackupCodesModalProps {
  onClose: () => void;
  onCopied: () => void;
}

const BackupCodesModal: FC<BackupCodesModalProps> = ({
  onClose,
  onCopied,
}) => {
  const [copied, setCopied] = useState(false);
  const backupCodes = useSelector(getBackupCodes);
  const handleCopyToClipboard = async () => {
    try {
      const codesText = backupCodes.join('\n');
      await navigator.clipboard.writeText(codesText);
      setCopied(true);
      toast.success('Backup codes copied to clipboard!');
      
      // Call the onCopied callback after successful copy
      setTimeout(() => {
        onCopied();
      }, 1000); // Small delay to show the success message
    } catch (error) {
      console.error('Failed to copy backup codes:', error);
      toast.error('Failed to copy backup codes');
    }
  };

  const handleDownload = () => {
    const codesText = backupCodes.join('\n');
    const blob = new Blob([codesText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'mfa-backup-codes.txt';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    toast.success('Backup codes downloaded!');
     // Call the onCopied callback after successful copy
     setTimeout(() => {
      onClose();
    }, 1000); // Small delay to show the success message
  };

  return (
    <div className='w-[32rem] bg-white rounded-xl'>
      <div className='px-6 py-6'>
        <div className='text-center mb-6'>
          <h2 className='text-xl font-semibold text-gray-900 mb-2'>
            Save Your Backup Codes
          </h2>
          <p className='text-gray-600 text-sm'>
            These backup codes can be used to access your account if you lose your authenticator device. 
            Store them in a safe place.
          </p>
        </div>

        <div className='bg-gray-50 rounded-lg p-4 mb-6'>
          <div className='grid grid-cols-2 gap-2'>
            {backupCodes.map((code, index) => (
              <div
                key={index}
                className='bg-white p-2 rounded border text-center font-mono text-sm'
              >
                {code}
              </div>
            ))}
          </div>
        </div>

        <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6'>
          <div className='flex items-start'>
            <div className='flex-shrink-0'>
              <svg
                className='h-5 w-5 text-yellow-400'
                viewBox='0 0 20 20'
                fill='currentColor'
              >
                <path
                  fillRule='evenodd'
                  d='M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z'
                  clipRule='evenodd'
                />
              </svg>
            </div>
            <div className='ml-3'>
              <h3 className='text-sm font-medium text-yellow-800'>
                Important Security Notice
              </h3>
              <div className='mt-2 text-sm text-yellow-700'>
                <ul className='list-disc list-inside space-y-1'>
                  <li>Each backup code can only be used once</li>
                  <li>Store these codes in a secure location</li>
                  <li>Do not share these codes with anyone</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div className='flex flex-col space-y-3'>
          <div className='flex space-x-3'>
            <Button
              type='button'
              theme='secondary'
              onClick={handleDownload}
              className='flex-1 py-3'
            >
              Download
            </Button>
            <Button
              type='button'
              theme='primary'
              onClick={handleCopyToClipboard}
              className='flex-1 py-3'
              disabled={copied}
            >
              {copied ? 'Copied!' : 'Copy to Clipboard'}
            </Button>
          </div>
          
          <Button
            type='button'
            theme='primary'
            onClick={onClose}
            className='w-full py-3'
            disabled={!copied}
          >
            {copied ? 'Continue' : 'I have saved my backup codes'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BackupCodesModal;
