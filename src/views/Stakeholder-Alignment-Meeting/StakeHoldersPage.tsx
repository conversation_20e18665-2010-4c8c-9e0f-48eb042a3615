import React, { FC, memo } from 'react';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import Button from 'shared-resources/components/Button/Button';
import { SelectOption } from 'shared-resources/components/Select/Select';
import {
  AssessmentResponseType,
  StakeHolderAlignmentMeetingTabs,
} from 'types/enum';
import { TableValueType } from './Questions';
import StakeholdersTableComp from './StakeholdersTableComp';

type StakeHoldersPageProps = {
  tableValues: TableValueType[];
  handleTableValues: (
    id: number,
    field: string,
    value: string | boolean | number
  ) => void;
  addStakeholder: () => void;
  handleDelete: (id: number) => void;
  setCurrentTab: (tab: StakeHolderAlignmentMeetingTabs) => void;
  handleSubmitData: (type: AssessmentResponseType) => void;
  isLoading: boolean;
  options: SelectOption[];
};
const StakeHoldersPage: FC<StakeHoldersPageProps> = ({
  tableValues,
  handleTableValues,
  addStakeholder,
  handleDelete,
  handleSubmitData,
  setCurrentTab,
  isLoading,
  options,
}) => (
  <div className='flex flex-col justify-between h-full'>
    <div className='flex flex-col gap-4 overflow-auto'>
      <div className='flex justify-between'>
        <h1 className='text-base font-bold'>Who are the Stakeholders?</h1>
        <Button className='px-4 py-1' onClick={addStakeholder}>
          Add
        </Button>
      </div>{' '}
      <StakeholdersTableComp
        isStakeHolder
        options={options}
        tableValues={tableValues}
        handleInputChange={handleTableValues}
        handleDelete={handleDelete}
      />
    </div>
    <div>
      <BackNextComponent
        backStep={() => {
          setCurrentTab(StakeHolderAlignmentMeetingTabs.GET_STARTED);
        }}
        nextStep={() => {
          setCurrentTab(StakeHolderAlignmentMeetingTabs.STATE_OBJECTIVE);
        }}
        buttonType='submit'
        isLoading={isLoading}
        isNextDisable={!tableValues.length}
        onSaveToDraftClick={() => {
          handleSubmitData(AssessmentResponseType.DRAFT);
        }}
      />
    </div>
  </div>
);
export default memo(StakeHoldersPage);
