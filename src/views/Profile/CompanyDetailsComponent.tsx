import { FC, memo, useState, useMemo } from 'react';
import { useSelector } from 'react-redux';
import Button from 'shared-resources/components/Button/Button';
import { getUserData } from 'store/selectors/user.selector';
import Modal from 'shared-resources/components/Modal/Modal';

import { getFormattedDate } from 'utils/helpers/Helpers';
import { BusinessOwner } from '../../models/entities/BusinessOwner';
import { Advisor } from '../../models/entities/Advisor';
import { User } from '../../models/entities/User';
import { UserType } from '../../types/enum';
import { localStorageService } from '../../services/LocalStorageService';
import EditCompanyDetailDailog from './EditCompanyDetailDailog';

type CompanyDetailsComponentProps = {};

const CompanyDetailsComponent: FC<CompanyDetailsComponentProps> = () => {
  const rawUser = useSelector(getUserData) as BusinessOwner | Advisor;
  const [showDialog, setShowDialog] = useState(false);

  // Determine user type
  const userType = localStorageService.getLoggedInUserType();
  const isBusinessOwner = userType === UserType.BUSINESS_OWNER;

  // Transform Advisor data to User format if needed
  const user = useMemo(() => {
    if (!rawUser) return rawUser;

    if (userType === UserType.ADVISOR) {
      const advisorUser = rawUser as Advisor;
      return {
        ...advisorUser,
        first_name: advisorUser.firstName,
        last_name: advisorUser.lastName,
        company_name: advisorUser.companyName,
        company_address: advisorUser.companyAddress,
      } as User & Advisor;
    }

    return rawUser;
  }, [rawUser, userType]);

  
  return (
    <div className='bg-white rounded-xl px-12.5 py-10 mt-7'>
      <div className='flex justify-end'>
        <Button
          className='px-6 py-2'
          type='button'
          onClick={() => {
            setShowDialog(true);
          }}
        >
          Edit
        </Button>
      </div>
      <div className='flex gap-20'>
        <div className='w-1/8  flex flex-col gap-8'>
          <h2 className='font-medium'>Company Name :</h2>
          {isBusinessOwner && (user as BusinessOwner)?.business_start_date && (
            <h2 className='font-medium'>Business Start Date :</h2>
          )}
          {isBusinessOwner && (user as BusinessOwner)?.employee_count && (
            <h2 className='font-medium'>Number Of Employees :</h2>
          )}
        </div>
        <div className='flex flex-col gap-8'>
          <h2 className='font-medium text-blue-01'>
            {isBusinessOwner
              ? (user as BusinessOwner)?.business_name
              : (user as Advisor)?.companyName
            }
          </h2>
          {isBusinessOwner && (user as BusinessOwner)?.business_start_date && (
            <h2 className='font-medium text-blue-01'>
              {getFormattedDate((user as BusinessOwner)?.business_start_date)}
            </h2>
          )}
          {isBusinessOwner && (user as BusinessOwner)?.employee_count && (
            <h2 className='font-medium text-blue-01'>
              {(user as BusinessOwner)?.employee_count}
            </h2>
          )}
        </div>
      </div>
      <Modal
        visible={showDialog}
        title='Edit Company Detail'
        handleVisibility={setShowDialog}
        closeOnOutsideClick
        classname='max-w-[46rem]'
      >
        <EditCompanyDetailDailog
          user={user}
          onClose={() => {
            setShowDialog(false);
          }}
        />
      </Modal>
    </div>
  );
};
export default memo(CompanyDetailsComponent);
