import React from 'react';
import { Link } from 'react-router-dom';
import { MdOutlineArrowOutward } from 'react-icons/md';
import SimpleBar from 'shared-resources/Bars/SimpleBar';
import Button from '../Button/Button';

interface IntermediateToolViewProps {
  onOpenAssessment: () => void;
  title: string;
  percentage: number;
  showReport?: boolean;
  reportUrl?: string;
  showDocuments?: boolean;
  onDocumentsClick?: () => void;
}

const IntermediateToolView: React.FC<IntermediateToolViewProps> = ({
  onOpenAssessment,
  title,
  percentage,
  showReport,
  reportUrl,
  showDocuments,
  onDocumentsClick,
}) => (
  <div className='flex flex-col w-full font-medium'>
    <h1 className='text-xl font-semibold mb-5'>{title}</h1>
    <h1 className='text-blue-01'>Your Progress</h1>
    <h1 className=''>{percentage.toFixed(2)}% complete</h1>
    <SimpleBar
      percentage={percentage}
      activeBarClassName='!bg-blue-01'
      containerClassName='h-5'
    />
    <div
      className={`flex ${
        showReport ? 'justify-end flex-row-reverse' : 'justify-start flex-row'
      } mt-10 items-center gap-4`}
    >
      <Button
        theme={showReport ? 'tertiary' : 'primary'}
        className='px-6 py-2 w-fit'
        onClick={onOpenAssessment}
      >
        Open Assessment
      </Button>
      {showDocuments && (
        <Button
          theme='tertiary'
          className='px-6 py-2 w-fit'
          onClick={onDocumentsClick}
        >
          Documents
        </Button>
      )}
      {showReport && reportUrl && (
        <Link to={reportUrl}>
          <Button className='flex gap-2 px-6 py-2'>
            <span className='whitespace-nowrap'>View Report</span>
            <MdOutlineArrowOutward size={24} />
          </Button>
        </Link>
      )}
    </div>
  </div>
);

export default IntermediateToolView;
