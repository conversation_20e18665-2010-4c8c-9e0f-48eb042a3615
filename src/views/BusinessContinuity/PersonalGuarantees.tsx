import React from 'react';
import { BusinessContinuityScreens } from 'types/enum';
import {
  getKey,
  isRequiredFieldDependingOnValidationSchema,
  titleCaseAndRemoveUnderScoreOrHyphen,
} from 'utils/helpers/Helpers';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { FieldArray, Formik, Form, FormikErrors } from 'formik';
import * as yup from 'yup';
import { useSelector } from 'react-redux';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import Button from 'shared-resources/components/Button/Button';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import ContinuityQuestionHeader from './shared-components/ContinuityQuestionHeader';
import ContuinuityRowView from './shared-components/ContuinuityRowView';
import { formEmpty } from './Report/continuityReportHelper';

interface PersonalGuaranteesProps {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  onSaveToDraftClick: (data: any) => void;
  data: any;
  question?: string;
}
const PersonalGuarantees: React.FC<PersonalGuaranteesProps> = (props) => {
  const { nextStep, backStep, setData, onSaveToDraftClick, data, question } =
    props;
  const screen = BusinessContinuityScreens.PERSONAL_GUARANTEES;
  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup
    .object()
    .shape({
      [screen]: yup.array().of(
        yup.object({
          creditor: yup.string().required('Creditor is required'),
          contact: yup.string().required('Contact Name is required'),
          phone: yup
            .string()
            .matches(
              /^(\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/,
              'Phone number not from US region'
            )
            .required('Phone is required'),
          comment: yup.string().nullable(),
        })
      ),
    })
    .required();

  const initialValues = {
    [screen]: data?.[screen] || [
      {
        creditor: '',
        contact: '',
        phone: '',
        comment: '',
        saved: false,
      },
    ],
  };

  return (
    <div className='flex flex-col rounded-lg h-full'>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          setData(values?.[screen]);
          nextStep();
        }}
        enableReinitialize
        validateOnMount
      >
        {({ values, setFieldValue, isValid, errors }) => {
          const isFormEmpty = formEmpty(values?.[screen]);

          return (
            <Form className='relative p-5 h-full flex flex-col gap-4 bg-white'>
              <FieldArray name={screen}>
                {({ push, remove }) => (
                  <div className='flex flex-col h-full'>
                    <ContinuityQuestionHeader
                      fieldKey={screen}
                      push={push}
                      pushData={{
                        creditor: '',
                        contact: '',
                        phone: '',
                        comment: '',
                        saved: false,
                      }}
                      title={titleCaseAndRemoveUnderScoreOrHyphen(screen)}
                    >
                      {question && <h2 className='font-medium'>{question}</h2>}
                    </ContinuityQuestionHeader>
                    <div className=' flex flex-col max-h-[calc(100vh-22rem)] pr-1 scrollbar overflow-auto'>
                      <ContuinuityRowView
                        fieldKey={screen}
                        formValues={values}
                        remove={remove}
                        initialValues={{
                          creditor: '',
                          contact: '',
                          phone: '',
                          comment: '',
                          saved: false,
                        }}
                      />
                      <div className='flex flex-col'>
                        {!!values?.[screen]?.length &&
                          values?.[screen]?.map(
                            (formValues: any, index: number) =>
                              !formValues?.saved && (
                                <div
                                  key={`${getKey(index)}_form`}
                                  className='flex flex-col border border-gray-02 p-4 mt-1 rounded-b-lg'
                                >
                                  <div className='grid grid-cols-2 gap-y-2 gap-x-8 mt-5  overflow-auto scrollbar text-[1rem]'>
                                    <FormikInput
                                      name={`${screen}.${getKey(
                                        index
                                      )}.creditor`}
                                      key={`${screen}.${getKey(
                                        index
                                      )}.creditor`}
                                      label='Creditor (Business or Individual)'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${screen}.${getKey(index)}.creditor`
                                      )}
                                    />
                                    <FormikInput
                                      name={`${screen}.${getKey(
                                        index
                                      )}.contact`}
                                      key={`${screen}.${getKey(index)}.contact`}
                                      label='Contact Name'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${screen}.${getKey(index)}.contact`
                                      )}
                                    />

                                    <FormikInput
                                      name={`${screen}.${getKey(index)}.phone`}
                                      key={`${screen}.${getKey(index)}.phone`}
                                      label='Phone'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${screen}.${getKey(index)}.phone`
                                      )}
                                    />
                                    <div className='col-span-2'>
                                      <FormikInput
                                        name={`${screen}.${getKey(
                                          index
                                        )}.comment`}
                                        key={`${screen}.${getKey(
                                          index
                                        )}.comment`}
                                        fieldType='textarea'
                                        label='Comment if any:'
                                        className='h-28'
                                        labelClassName='leading-6.5'
                                      />
                                    </div>
                                  </div>
                                  <div className='flex justify-end'>
                                    <Button
                                      onClick={() => {
                                        setFieldValue(
                                          `${screen}.${getKey(index)}.saved`,
                                          true
                                        );
                                      }}
                                      className='px-4 py-2'
                                      disabled={
                                        !!(
                                          errors?.[
                                            screen
                                          ] as FormikErrors<any>[]
                                        )?.[index]
                                      }
                                    >
                                      Save
                                    </Button>
                                  </div>
                                </div>
                              )
                          )}
                      </div>
                    </div>
                  </div>
                )}
              </FieldArray>
              <BackNextComponent
                backStep={() => {
                  backStep();
                  setData(values?.[screen]);
                }}
                nextStep={() => {
                  setData(values?.[screen]);
                  nextStep();
                }}
                fieldKey={screen}
                formValues={values}
                isLoading={isLoading}
                isNextDisable={isFormEmpty ? false : !isValid}
                onSaveToDraftClick={onSaveToDraftClick}
              />
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default PersonalGuarantees;
