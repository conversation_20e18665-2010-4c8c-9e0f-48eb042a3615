import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Input from 'shared-resources/components/Input/Input';
import { IoChatboxSharp, IoSearch } from 'react-icons/io5';
import { fetchComments } from 'store/actions/tool-comment.action';
import {
  getToolComments,
  getToolCommentsLoading,
} from 'store/selectors/tool-comment.selector';
import { getUserData } from 'store/selectors/user.selector';
import { AssessmentTools, UserType } from 'types/enum';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import { useLocation, useParams } from 'react-router';
import { debounce } from 'lodash';
import { useSearchParams } from 'react-router-dom';
import { resetComments } from 'store/reducers/tool-comment.reducer';
import { getCurrentTab } from 'utils/helpers/Helpers';
import Comment from './Comment';
import Spinner from '../Spinner/Spinner';

interface WithCommentsProps {
  children: React.ReactNode;
  tool: AssessmentTools;
  classname?: string;
  containerClassname?: string;
}

const WithComments: React.FC<WithCommentsProps> = ({
  children,
  tool,
  classname,
  containerClassname,
}) => {
  const dispatch = useDispatch();
  const [isCommentsOpen, setIsCommentsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const { pathname } = useLocation();
  const pathSegments = pathname.split('/');
  const tab = getCurrentTab(tool, searchParams, pathSegments);

  const comments = useSelector(getToolComments);
  const loading = useSelector(getToolCommentsLoading);
  const user = useSelector(getUserData) as BusinessOwner;

  const debouncedFetchComments = useCallback(
    debounce((search: string) => {
      if (isCommentsOpen && user?.id) {
        if (user.type === UserType.ADVISOR) {
          dispatch(fetchComments(Number(id), tool, search));
        } else if (user.type === UserType.BUSINESS_OWNER) {
          dispatch(fetchComments(Number(user.id), tool, search));
        }
      }
    }, 300),
    [dispatch, user, tool, isCommentsOpen, id]
  );

  useEffect(() => {
    debouncedFetchComments(searchTerm);
  }, [debouncedFetchComments, searchTerm]);

  useEffect(
    () => () => {
      dispatch(resetComments());
    },
    [dispatch]
  );

  const updateContainerStyles = (isOpen: boolean) => {
    if (containerRef.current && contentRef.current) {
      containerRef.current.style.paddingRight = isOpen ? '300px' : '0';
      contentRef.current.style.borderTopRightRadius = isOpen ? '0' : '0.75rem';
      contentRef.current.style.borderBottomRightRadius = isOpen
        ? '0'
        : '0.75rem';
    }
  };

  const readinessHeaderRef = useRef<HTMLElement | null>(null);
  const transitionHeaderRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    readinessHeaderRef.current = document.getElementById('readiness-header');
    transitionHeaderRef.current = document.getElementById('transition-header');
  }, []);

  const updateHeaderStyles = (isOpen: boolean) => {
    if (isOpen) {
      readinessHeaderRef.current?.classList.remove('!pr-12');
      transitionHeaderRef.current?.classList.remove('mr-6');
    } else {
      readinessHeaderRef.current?.classList.add('!pr-12');
    }
  };

  useEffect(() => {
    if (user?.type === UserType.ADVISOR) {
      updateContainerStyles(isCommentsOpen);
      updateHeaderStyles(isCommentsOpen);
    }
  }, [isCommentsOpen, user, updateHeaderStyles]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const renderCommentContent = () => {
    if (loading) {
      return <Spinner />;
    }

    if (!comments?.length) {
      return <p className='text-center text-gray-500 mt-4'>No comments.</p>;
    }
    const screenComments = comments?.filter(
      (comment: any) => comment.metadata?.screen === tab
    );
    const otherComments = comments?.filter(
      (comment: any) => comment.metadata?.screen !== tab
    );

    return (
      <div className='flex flex-col'>
        <div className='flex flex-col'>
          <div className='bg-gray-100 text-gray-500 text-center py-2 px-4 font-medium text-sm'>
            On this screen
          </div>
          {screenComments.map((comment) => (
            <Comment key={comment.id} comment={comment} />
          ))}
          {screenComments.length === 0 && (
            <p className='text-center text-gray-500 text-sm border-y border-gray-200 py-2'>
              No screen comments.
            </p>
          )}
        </div>
        <div className='flex flex-col'>
          <div className='bg-gray-100 text-gray-500 text-center py-2 px-4 font-medium text-sm'>
            All comments
          </div>
          {otherComments.map((comment) => (
            <Comment key={comment.id} comment={comment} />
          ))}
          {otherComments.length === 0 && (
            <p className='text-center border-t text-sm border-gray-200 text-gray-500 py-2'>
              No other comments.
            </p>
          )}
        </div>
      </div>
    );
  };

  return (
    <div ref={containerRef} className={`relative ${containerClassname}`}>
      <div
        ref={contentRef}
        className={`relative rounded-xl overflow-hidden ${classname}`}
      >
        {children}
      </div>
      {user?.type === UserType.ADVISOR && (
        <button
          className={`absolute right-4 text-2xl z-10 ${
            isCommentsOpen ? 'top-7' : 'top-4'
          }`}
          onClick={() => setIsCommentsOpen(!isCommentsOpen)}
          aria-label={isCommentsOpen ? 'Close comments' : 'Open comments'}
        >
          <IoChatboxSharp />
        </button>
      )}
      {isCommentsOpen && user?.type === UserType.ADVISOR && (
        <div className='absolute top-0 right-0 w-[18.75rem] h-full bg-gray-08 overflow-hidden flex flex-col rounded-tr-xl rounded-br-xl'>
          <div className='p-4 pb-0 mr-8 border-b border-gray-200 flex items-center gap-2'>
            <Input
              type='text'
              placeholder='Search comments...'
              value={searchTerm}
              onChange={handleSearchChange}
              className='w-full h-10 !rounded-lg text-sm'
              trailingIcon={<IoSearch className='text-gray-400' />}
            />
          </div>
          <div className='flex-grow overflow-auto scrollbar'>
            {renderCommentContent()}
          </div>
        </div>
      )}
    </div>
  );
};

export default WithComments;
