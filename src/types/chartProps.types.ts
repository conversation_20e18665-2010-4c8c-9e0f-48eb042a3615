import { ReactElement, SVGProps } from 'react';

export interface BarChartComponentProps {
  data: any[];
  bars: {
    key: string;
    color: string;
  }[];
  label?: {
    xAxis: string;
    yAxis: string;
  };
  customXAxisTick?:
    | SVGProps<SVGTextElement>
    | ReactElement<SVGElement>
    | ((props: any) => ReactElement<SVGElement>)
    | boolean;
  chartWidth?: number;
  chartHeight?: number;
  maxBarSize?: number;
  cartesianProps?: {
    x?: number;
    width?: number;
    syncWithTicks?: boolean;
    vertical?: boolean;
    stroke?: string;
  };

  yAxisProps?: {
    padding?: {
      top?: number;
      bottom?: number;
    };
    tickLine?: boolean | SVGProps<SVGTextElement>;
    axisLine?: boolean | SVGProps<SVGLineElement>;
    tick?:
      | SVGProps<SVGTextElement>
      | ReactElement<SVGElement>
      | ((props: any) => ReactElement<SVGElement>)
      | boolean;
    fontSize?: number;
    fontWeight?: number;
    tickFormatter?: (value: any, index: number) => string;
  };
  referenceLine?: boolean;
  xAxisProps?: {
    axisLine?: boolean | SVGProps<SVGLineElement>;
    tick?:
      | SVGProps<SVGTextElement>
      | ReactElement<SVGElement>
      | ((props: any) => ReactElement<SVGElement>)
      | boolean;
    tickLine?: boolean | SVGProps<SVGTextElement>;
    padding?:
      | {
          left?: number;
          right?: number;
        }
      | 'gap'
      | 'no-gap';
  };
}
