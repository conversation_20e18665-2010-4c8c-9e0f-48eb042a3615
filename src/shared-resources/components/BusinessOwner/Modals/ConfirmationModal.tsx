import React from 'react';
import Button from 'shared-resources/components/Button/Button';

interface ConfirmationModalProps {
  handleCancel: () => void;
  onSubmitConfirm: () => void;
  description: string;
  cancelText?: string;
  confirmText?: string;
}
const ConfirmationModal: React.FC<ConfirmationModalProps> = (props) => {
  const {
    handleCancel,
    onSubmitConfirm,
    description,
    cancelText,
    confirmText,
  } = props;

  return (
    <div className='w-[38.25rem] bg-white rounded-xl'>
      <div className='flex flex-col items-center justify-center px-4 py-[4rem]'>
        <h1 className='py-[1.5rem] text-lg font-semibold text-center'>
          {description}
        </h1>
      </div>
      <div className='flex justify-evenly rounded-b-xl bg-slate-500'>
        <Button
          theme='secondary'
          onClick={() => handleCancel()}
          className='w-full py-4 rounded-bl-xl border-none bg-blue-02 rounded-none  '
        >
          {cancelText || 'Cancel'}
        </Button>
        <Button
          onClick={() => onSubmitConfirm()}
          className='w-full py-4 rounded-none rounded-br-xl border-none'
        >
          {confirmText || 'Confirm'}
        </Button>
      </div>
    </div>
  );
};

export default ConfirmationModal;
