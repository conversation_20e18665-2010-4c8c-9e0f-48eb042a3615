import { FinancialGapAssessmentCalculatorTabs } from 'types/enum';

export const headerOptions = ['', 'Monthly', 'Annual'];

export interface ExpenseItem {
  monthly_expense: number;
}

export interface OtherExpenseItem {
  expense_name: string;
  monthly_expense: number;
}

export interface Expenses {
  [FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES]: {
    [key: string]: ExpenseItem | OtherExpenseItem[];
  };
  [FinancialGapAssessmentCalculatorTabs.FOOD_AND_GROCERY]: {
    [key: string]: ExpenseItem | OtherExpenseItem[];
  };
  [FinancialGapAssessmentCalculatorTabs.PERSONAL_CARE]: {
    [key: string]: ExpenseItem | OtherExpenseItem[];
  };
  [FinancialGapAssessmentCalculatorTabs.HEALTH_CARE]: {
    [key: string]: ExpenseItem | OtherExpenseItem[];
  };
  [FinancialGapAssessmentCalculatorTabs.TRANSPORTATION]: {
    [key: string]: ExpenseItem | OtherExpenseItem[];
  };
  [FinancialGapAssessmentCalculatorTabs.LOANSPAYMENTS]: {
    [key: string]: ExpenseItem | OtherExpenseItem[];
  };
  [FinancialGapAssessmentCalculatorTabs.PROFESSIONAL]: {
    [key: string]: ExpenseItem | OtherExpenseItem[];
  };
  [FinancialGapAssessmentCalculatorTabs.MISCELLANEOUS]: {
    [key: string]: ExpenseItem | OtherExpenseItem[];
  };
  [FinancialGapAssessmentCalculatorTabs.DISCRETIONARY]: {
    [key: string]: ExpenseItem | OtherExpenseItem[];
  };
}

export interface ExpensesReportResponse extends Expenses {
  total_monthly_expense: number;
}

export const EXPENSE_OTHER_KEY_NAME = 'other';

export enum HouseHoldExpenseQuestionsKeys {
  MORTGAGE_ON_PRIMARY_RESIDENCE = 'mortgage_on_primary_residence',
  SECOND_MORTGAGE = 'second_mortgage',
  REAL_ESTATE_TAX_PRIMARY = 'real_estate_tax_primary',
  INSURANCE_ON_PRIMARY_RESIDENCE = 'insurance_on_primary_residence',
  HOUSEHOLD_REPAIR_AND_MAINTENANCE = 'household_repair_and_maintenance',
  LANDSCAPING = 'landscaping',
  ASSOCIATION_DUES = 'association_dues',
  UTILILITIES = 'utililities',
  INTERNET = 'internet',
  CELL_PHONES = 'cell_phones',
  CABLE = 'cable',
  HOUSEHOLD_SUPPLIES = 'household_supplies',
  HOME_DECOR = 'home_decor',
  OTHER = 'other',
}
export enum FoodAndGroceryQuestionsKeys {
  DAILY_AT_HOME_MEALS = 'daily_at_home_meals',
  BEVERAGES = 'beverages',
  PET_FOOD_AND_SUPPLIES = 'pet_food_and_supplies',
  DINING_OUT = 'dining_out',
  ENTERTAINMENT = 'entertainment',
  OTHER = 'other',
}

export enum PersonalCareQuestionsKeys {
  DAILY_CLOTHING = 'daily_clothing',
  BOOTS_SHOES_COATS = 'boots_shoes_coats',
  PERSONAL_GROOMING_HEALTH_AND_BEAUTY_ITEMS = 'personal_grooming_health_and_beauty_items',
  GYM_MEMBERSHIP = 'gym_membership',
  DRY_CLEANING = 'dry_cleaning',
  PET_GROOMING_LODGING = 'pet_grooming_lodging',
  EDUCATION = 'education',
  MISCELLANEOUS_MAD_MONEY_DISCRETIONARY_EXPENSES = 'miscellaneous_mad_money_discretionary_expenses',
  CHILD_CARE = 'child_care',
  HOBBIES = 'hobbies',
  OTHER = 'other',
}

export enum HealthCareQuestionsKeys {
  HEALTH_INSURANCE_PRIMARY = 'health_insurance_primary',
  HEALTH_INSURANCE_SECONDARY = 'health_insurance_secondary',
  PRESCRIPTION_INSURANCE = 'prescription_insurance',
  DENTAL_INSURANCE = 'dental_insurance',
  EYE_CARE_INSURANCE = 'eye_care_insurance',
  OUT_OF_POCKET_INSURANCE_COSTS = 'out_of_pocket_insurance_costs',
  LIFE_INSURANCE = 'life_insurance',
  LONG_TERM_CARE_INSURANCE = 'long_term_care_insurance',
  PET_MEDICAL_EXPENSE = 'pet_medical_expense',
  FAMILY_MEMBER_SPECIAL_NEEDS_EXPENSES = 'family_member_special_needs_expenses',
  OTHER = 'other',
}

export enum TransportationsQuestionsKeys {
  CAR_PAYMENTS = 'car_payments',
  CAR_INSURANCE = 'car_insurance',
  GAS = 'gas',
  VEHICLE_MAINTENANCE = 'vehicle_maintenance',
  PUBLIC_TRANSPORTATION = 'public_transportation',
  OTHER = 'other',
}
export enum LoansPaymentQuestionsKeys {
  CREDIT_CARD_PAYMENTS = 'credit_card_payments',
  PERSONAL_LOANS = 'personal_loans',
  BUSINESS_LOANS = 'business_loans',
  STUDENT_LOANS = 'student_loans',
  ALIMONY = 'alimony',
  CHILD_SUPPORT = 'child_support',
  FAMILY_MEMBER_SUPPORT = 'family_member_support',
  ALLOWANCES_TO_ANYONE = 'allowances_to_anyone',
  OTHER = 'other',
}
export enum ProfessionalQuestionsKeys {
  LEGAL = 'legal',
  ACCOUNTING = 'accounting',
  OTHER_PROFESSIONAL_FEES = 'other_professional_fees',
  OTHER = 'other',
}
export enum MiscellaneousQuestionsKeys {
  GIFTS_ESSENTIAL = 'gifts_essential',
  RELIGIOUS_AFFILIATIONS = 'religious_affiliations ',
  OTHER_CHARITABLE_DONATIONS = 'other_charitable_donations',
  MISCELLANEOUS = 'miscellaneous',
  OTHER = 'other',
}

export enum DiscretionaryQuestionsKeys {
  MORTGAGE_ON_VACATION_HOME = 'mortgage_on_vacation_home',
  REAL_ESTATE_TAX_VACATION_HOME = 'real_estate_tax_vacation_home',
  INSURANCE_ON_VACATION_HOME = 'insurance_on_vacation_home',
  REPAIRS_AND_MAINTENANCE_VACATION_HOME = 'repairs_and_maintenance_vacation_home',
  LANDSCAPING = 'landscaping',
  ASSOCIATION_DUES = 'association_dues',
  UTILITIES_ON_VACATION_HOME = 'utilities_on_vacation_home',
  INTERNET = 'internet',
  CABLE = 'cable',
  HOME_SUPPLIES = 'home_supplies',
  ALL_OTHER_VACATION_HOME_EXPENSES = 'all_other_vacation_home_expenses',
  FURNISHINGS = 'furnishings',
  COUNTRY_CLUB_EXPENSES = 'country_club_expenses',
  RECREATION_EXPENSES = 'recreation_expenses',
  OTHER = 'other',
}

const generateQuestionResponse = <T extends Record<string, any>>(
  enumObj: T
): Record<string, any> =>
  Object.values(enumObj).reduce((acc, key) => {
    if (key === EXPENSE_OTHER_KEY_NAME) {
      acc[key] = [
        {
          expense_name: 'Other',
          monthly_expense: 0,
        },
      ];
    } else {
      acc[key] = { monthly_expense: 0 }; // Example default value
    }
    return acc;
  }, {});

export const expenseCalculatorResponse: Expenses = {
  [FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES]:
    generateQuestionResponse(HouseHoldExpenseQuestionsKeys),
  [FinancialGapAssessmentCalculatorTabs.FOOD_AND_GROCERY]:
    generateQuestionResponse(FoodAndGroceryQuestionsKeys),
  [FinancialGapAssessmentCalculatorTabs.PERSONAL_CARE]:
    generateQuestionResponse(PersonalCareQuestionsKeys),
  [FinancialGapAssessmentCalculatorTabs.HEALTH_CARE]: generateQuestionResponse(
    HealthCareQuestionsKeys
  ),
  [FinancialGapAssessmentCalculatorTabs.TRANSPORTATION]:
    generateQuestionResponse(TransportationsQuestionsKeys),
  [FinancialGapAssessmentCalculatorTabs.LOANSPAYMENTS]:
    generateQuestionResponse(LoansPaymentQuestionsKeys),

  [FinancialGapAssessmentCalculatorTabs.PROFESSIONAL]: generateQuestionResponse(
    ProfessionalQuestionsKeys
  ),
  [FinancialGapAssessmentCalculatorTabs.MISCELLANEOUS]:
    generateQuestionResponse(MiscellaneousQuestionsKeys),
  [FinancialGapAssessmentCalculatorTabs.DISCRETIONARY]:
    generateQuestionResponse(DiscretionaryQuestionsKeys),
};

export const handleNextTabSwitch = (
  currentTab: { tab: FinancialGapAssessmentCalculatorTabs } | undefined,
  setCurrentTab: (tab: { tab: FinancialGapAssessmentCalculatorTabs }) => void
): void => {
  switch (currentTab && currentTab.tab) {
    case FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.FOOD_AND_GROCERY,
      });
      break;
    case FinancialGapAssessmentCalculatorTabs.FOOD_AND_GROCERY:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.PERSONAL_CARE,
      });
      break;
    case FinancialGapAssessmentCalculatorTabs.PERSONAL_CARE:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.HEALTH_CARE,
      });
      break;
    case FinancialGapAssessmentCalculatorTabs.HEALTH_CARE:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.TRANSPORTATION,
      });
      break;
    case FinancialGapAssessmentCalculatorTabs.TRANSPORTATION:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.LOANSPAYMENTS,
      });
      break;
    case FinancialGapAssessmentCalculatorTabs.LOANSPAYMENTS:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.PROFESSIONAL,
      });
      break;
    case FinancialGapAssessmentCalculatorTabs.PROFESSIONAL:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.MISCELLANEOUS,
      });
      break;
    case FinancialGapAssessmentCalculatorTabs.MISCELLANEOUS:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.DISCRETIONARY,
      });
      break;

    case FinancialGapAssessmentCalculatorTabs.DISCRETIONARY:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.EXPENSES,
      });
      break;
    default:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES,
      });
  }
};

export const handleBackTabSwitch = (
  currentTab: { tab: FinancialGapAssessmentCalculatorTabs } | undefined,
  setCurrentTab: (tab: { tab: FinancialGapAssessmentCalculatorTabs }) => void
): void => {
  switch (currentTab && currentTab.tab) {
    case FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES,
      });
      break;
    case FinancialGapAssessmentCalculatorTabs.FOOD_AND_GROCERY:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES,
      });
      break;
    case FinancialGapAssessmentCalculatorTabs.PERSONAL_CARE:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.FOOD_AND_GROCERY,
      });
      break;
    case FinancialGapAssessmentCalculatorTabs.HEALTH_CARE:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.PERSONAL_CARE,
      });
      break;
    case FinancialGapAssessmentCalculatorTabs.TRANSPORTATION:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.HEALTH_CARE,
      });
      break;
    case FinancialGapAssessmentCalculatorTabs.LOANSPAYMENTS:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.TRANSPORTATION,
      });
      break;
    case FinancialGapAssessmentCalculatorTabs.PROFESSIONAL:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.LOANSPAYMENTS,
      });
      break;
    case FinancialGapAssessmentCalculatorTabs.MISCELLANEOUS:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.PROFESSIONAL,
      });
      break;

    case FinancialGapAssessmentCalculatorTabs.DISCRETIONARY:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.MISCELLANEOUS,
      });
      break;
    case FinancialGapAssessmentCalculatorTabs.EXPENSES:
      setCurrentTab({
        tab: FinancialGapAssessmentCalculatorTabs.DISCRETIONARY,
      });
      break;
    default:
      break;
  }
};

export const getTitle = (tab: FinancialGapAssessmentCalculatorTabs) => {
  switch (tab) {
    case FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES:
      return 'Household Expenses';
    case FinancialGapAssessmentCalculatorTabs.FOOD_AND_GROCERY:
      return 'Food and Grocery';
    case FinancialGapAssessmentCalculatorTabs.PERSONAL_CARE:
      return 'Personal Care';
    case FinancialGapAssessmentCalculatorTabs.HEALTH_CARE:
      return 'Healthcare';
    case FinancialGapAssessmentCalculatorTabs.TRANSPORTATION:
      return 'Transportation';
    case FinancialGapAssessmentCalculatorTabs.LOANSPAYMENTS:
      return 'Loans/Payments';
    case FinancialGapAssessmentCalculatorTabs.PROFESSIONAL:
      return 'Professional';
    case FinancialGapAssessmentCalculatorTabs.MISCELLANEOUS:
      return 'Miscellaneous';
    case FinancialGapAssessmentCalculatorTabs.DISCRETIONARY:
      return 'Discretionary';
    default:
      return 'Others';
  }
};

export const getQuestionTitleFromKey = (
  questionKey:
    | HouseHoldExpenseQuestionsKeys
    | FoodAndGroceryQuestionsKeys
    | PersonalCareQuestionsKeys
    | HealthCareQuestionsKeys
    | TransportationsQuestionsKeys
    | LoansPaymentQuestionsKeys
    | ProfessionalQuestionsKeys
    | MiscellaneousQuestionsKeys
    | DiscretionaryQuestionsKeys,
  screenKey:
    | FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES
    | FinancialGapAssessmentCalculatorTabs.FOOD_AND_GROCERY
    | FinancialGapAssessmentCalculatorTabs.PERSONAL_CARE
    | FinancialGapAssessmentCalculatorTabs.HEALTH_CARE
    | FinancialGapAssessmentCalculatorTabs.TRANSPORTATION
    | FinancialGapAssessmentCalculatorTabs.LOANSPAYMENTS
    | FinancialGapAssessmentCalculatorTabs.PROFESSIONAL
    | FinancialGapAssessmentCalculatorTabs.MISCELLANEOUS
    | FinancialGapAssessmentCalculatorTabs.DISCRETIONARY
) => {
  switch (screenKey) {
    case FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES:
      switch (questionKey) {
        case HouseHoldExpenseQuestionsKeys.MORTGAGE_ON_PRIMARY_RESIDENCE:
          return 'Mortgage on Primary Residence';
        case HouseHoldExpenseQuestionsKeys.SECOND_MORTGAGE:
          return 'Second Mortgage';
        case HouseHoldExpenseQuestionsKeys.REAL_ESTATE_TAX_PRIMARY:
          return 'Real Estate Tax-Primary';
        case HouseHoldExpenseQuestionsKeys.INSURANCE_ON_PRIMARY_RESIDENCE:
          return 'Insurance on Primary Residence';
        case HouseHoldExpenseQuestionsKeys.HOUSEHOLD_REPAIR_AND_MAINTENANCE:
          return 'Household Repair and Maintenance';
        case HouseHoldExpenseQuestionsKeys.LANDSCAPING:
          return 'Landscaping';
        case HouseHoldExpenseQuestionsKeys.ASSOCIATION_DUES:
          return 'Association Dues';
        case HouseHoldExpenseQuestionsKeys.UTILILITIES:
          return 'Utilities';
        case HouseHoldExpenseQuestionsKeys.INTERNET:
          return 'Internet';
        case HouseHoldExpenseQuestionsKeys.CELL_PHONES:
          return 'Cell Phones';
        case HouseHoldExpenseQuestionsKeys.CABLE:
          return 'Cable';
        case HouseHoldExpenseQuestionsKeys.HOUSEHOLD_SUPPLIES:
          return 'Household Supplies';
        case HouseHoldExpenseQuestionsKeys.HOME_DECOR:
          return 'Home Decor';
        default:
          return 'Other';
      }
    case FinancialGapAssessmentCalculatorTabs.FOOD_AND_GROCERY:
      switch (questionKey) {
        case FoodAndGroceryQuestionsKeys.DAILY_AT_HOME_MEALS:
          return 'Daily at-Home Meals';
        case FoodAndGroceryQuestionsKeys.BEVERAGES:
          return 'Beverages';
        case FoodAndGroceryQuestionsKeys.PET_FOOD_AND_SUPPLIES:
          return 'Pet Food and Supplies';
        case FoodAndGroceryQuestionsKeys.DINING_OUT:
          return 'Dining Out';
        case FoodAndGroceryQuestionsKeys.ENTERTAINMENT:
          return 'Entertainment';
        default:
          return 'Other';
      }
    case FinancialGapAssessmentCalculatorTabs.PERSONAL_CARE:
      switch (questionKey) {
        case PersonalCareQuestionsKeys.DAILY_CLOTHING:
          return 'Daily Clothing';
        case PersonalCareQuestionsKeys.BOOTS_SHOES_COATS:
          return 'Boots, Shoes, Coats';
        case PersonalCareQuestionsKeys.PERSONAL_GROOMING_HEALTH_AND_BEAUTY_ITEMS:
          return 'Personal Grooming/Health & Beauty Items';
        case PersonalCareQuestionsKeys.GYM_MEMBERSHIP:
          return 'Gym Membership';
        case PersonalCareQuestionsKeys.DRY_CLEANING:
          return 'Dry Cleaning';
        case PersonalCareQuestionsKeys.PET_GROOMING_LODGING:
          return 'Pet Grooming/Lodging';
        case PersonalCareQuestionsKeys.EDUCATION:
          return 'Education';
        case PersonalCareQuestionsKeys.MISCELLANEOUS_MAD_MONEY_DISCRETIONARY_EXPENSES:
          return 'Miscellaneous (Mad Money) Discretionary Expenses';
        case PersonalCareQuestionsKeys.CHILD_CARE:
          return 'Child Care';
        case PersonalCareQuestionsKeys.HOBBIES:
          return 'Hobbies';
        default:
          return 'Other';
      }
    case FinancialGapAssessmentCalculatorTabs.HEALTH_CARE:
      switch (questionKey) {
        case HealthCareQuestionsKeys.HEALTH_INSURANCE_PRIMARY:
          return 'Health Insurance-Primary';
        case HealthCareQuestionsKeys.HEALTH_INSURANCE_SECONDARY:
          return 'Health Insurance-Secondary';
        case HealthCareQuestionsKeys.PRESCRIPTION_INSURANCE:
          return 'Prescription Insurance';
        case HealthCareQuestionsKeys.DENTAL_INSURANCE:
          return 'Dental Insurance';
        case HealthCareQuestionsKeys.EYE_CARE_INSURANCE:
          return 'Eye Care Insurance';
        case HealthCareQuestionsKeys.OUT_OF_POCKET_INSURANCE_COSTS:
          return 'Out of Pocket Insurance Costs';
        case HealthCareQuestionsKeys.LIFE_INSURANCE:
          return 'Life Insurance';
        case HealthCareQuestionsKeys.LONG_TERM_CARE_INSURANCE:
          return 'Long-Term Care Insurance';
        case HealthCareQuestionsKeys.PET_MEDICAL_EXPENSE:
          return 'Pet Medical Expense';
        case HealthCareQuestionsKeys.FAMILY_MEMBER_SPECIAL_NEEDS_EXPENSES:
          return 'Family Member Special Needs Expenses';
        default:
          return 'Other';
      }
    case FinancialGapAssessmentCalculatorTabs.TRANSPORTATION:
      switch (questionKey) {
        case TransportationsQuestionsKeys.CAR_PAYMENTS:
          return 'Car Payments';
        case TransportationsQuestionsKeys.CAR_INSURANCE:
          return 'Car Insurance';
        case TransportationsQuestionsKeys.GAS:
          return 'Gas';
        case TransportationsQuestionsKeys.VEHICLE_MAINTENANCE:
          return 'Vehicle Maintenance';
        case TransportationsQuestionsKeys.PUBLIC_TRANSPORTATION:
          return 'Public Transportation';
        default:
          return 'Other';
      }
    case FinancialGapAssessmentCalculatorTabs.LOANSPAYMENTS:
      switch (questionKey) {
        case LoansPaymentQuestionsKeys.CREDIT_CARD_PAYMENTS:
          return 'Credit Card Payments';
        case LoansPaymentQuestionsKeys.PERSONAL_LOANS:
          return 'Personal Loans';
        case LoansPaymentQuestionsKeys.BUSINESS_LOANS:
          return 'Business Loans';
        case LoansPaymentQuestionsKeys.STUDENT_LOANS:
          return 'Student Loans';
        case LoansPaymentQuestionsKeys.ALIMONY:
          return 'Alimony';
        case LoansPaymentQuestionsKeys.CHILD_SUPPORT:
          return 'Child Support';
        case LoansPaymentQuestionsKeys.FAMILY_MEMBER_SUPPORT:
          return 'Family Member Support';
        case LoansPaymentQuestionsKeys.ALLOWANCES_TO_ANYONE:
          return 'Allowances to Anyone';
        default:
          return 'Other';
      }
    case FinancialGapAssessmentCalculatorTabs.PROFESSIONAL:
      switch (questionKey) {
        case ProfessionalQuestionsKeys.LEGAL:
          return 'Legal';
        case ProfessionalQuestionsKeys.ACCOUNTING:
          return 'Accounting';
        case ProfessionalQuestionsKeys.OTHER_PROFESSIONAL_FEES:
          return 'Other Professional Fees';
        default:
          return 'Other';
      }
    case FinancialGapAssessmentCalculatorTabs.MISCELLANEOUS:
      switch (questionKey) {
        case MiscellaneousQuestionsKeys.GIFTS_ESSENTIAL:
          return 'Gifts (essential)';
        case MiscellaneousQuestionsKeys.RELIGIOUS_AFFILIATIONS:
          return 'Religious Affiliations';
        case MiscellaneousQuestionsKeys.OTHER_CHARITABLE_DONATIONS:
          return 'Other Charitable Donations';
        case MiscellaneousQuestionsKeys.MISCELLANEOUS:
          return 'Miscellaneous';
        default:
          return 'Other';
      }
    case FinancialGapAssessmentCalculatorTabs.DISCRETIONARY:
      switch (questionKey) {
        case DiscretionaryQuestionsKeys.MORTGAGE_ON_VACATION_HOME:
          return 'Mortgage on Vacation Home';
        case DiscretionaryQuestionsKeys.REAL_ESTATE_TAX_VACATION_HOME:
          return 'Real Estate Tax-Vacation Home';
        case DiscretionaryQuestionsKeys.INSURANCE_ON_VACATION_HOME:
          return 'Insurance on Vacation Home';
        case DiscretionaryQuestionsKeys.REPAIRS_AND_MAINTENANCE_VACATION_HOME:
          return 'Repairs & Maintenance-Vacation Home';
        case DiscretionaryQuestionsKeys.LANDSCAPING:
          return 'Landscaping';
        case DiscretionaryQuestionsKeys.ASSOCIATION_DUES:
          return 'Association Dues';
        case DiscretionaryQuestionsKeys.UTILITIES_ON_VACATION_HOME:
          return 'Utilities on Vacation Home';
        case DiscretionaryQuestionsKeys.INTERNET:
          return 'Internet';
        case DiscretionaryQuestionsKeys.CABLE:
          return 'Cable';
        case DiscretionaryQuestionsKeys.HOME_SUPPLIES:
          return 'Home Supplies';
        case DiscretionaryQuestionsKeys.ALL_OTHER_VACATION_HOME_EXPENSES:
          return 'All other Vacation Home Expenses';
        case DiscretionaryQuestionsKeys.FURNISHINGS:
          return 'Furnishings';
        case DiscretionaryQuestionsKeys.COUNTRY_CLUB_EXPENSES:
          return 'Country Club Expenses';

        case DiscretionaryQuestionsKeys.RECREATION_EXPENSES:
          return 'Recreation Expenses';
        default:
          return 'Other';
      }
    default:
      return 'Other';
  }
};

export const screenToNumberObject: Record<
  FinancialGapAssessmentCalculatorTabs,
  number
> = {
  [FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES]: 1,
  [FinancialGapAssessmentCalculatorTabs.FOOD_AND_GROCERY]: 2,
  [FinancialGapAssessmentCalculatorTabs.PERSONAL_CARE]: 3,
  [FinancialGapAssessmentCalculatorTabs.HEALTH_CARE]: 4,
  [FinancialGapAssessmentCalculatorTabs.TRANSPORTATION]: 5,
  [FinancialGapAssessmentCalculatorTabs.LOANSPAYMENTS]: 6,
  [FinancialGapAssessmentCalculatorTabs.PROFESSIONAL]: 7,
  [FinancialGapAssessmentCalculatorTabs.MISCELLANEOUS]: 8,
  [FinancialGapAssessmentCalculatorTabs.DISCRETIONARY]: 9,
  [FinancialGapAssessmentCalculatorTabs.EXPENSES]: 10,
};
// screen number against its screen name
export const numberToScreenObject: Record<
  number,
  FinancialGapAssessmentCalculatorTabs
> = {
  1: FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES,
  2: FinancialGapAssessmentCalculatorTabs.FOOD_AND_GROCERY,
  3: FinancialGapAssessmentCalculatorTabs.PERSONAL_CARE,
  4: FinancialGapAssessmentCalculatorTabs.HEALTH_CARE,
  5: FinancialGapAssessmentCalculatorTabs.TRANSPORTATION,
  6: FinancialGapAssessmentCalculatorTabs.LOANSPAYMENTS,
  7: FinancialGapAssessmentCalculatorTabs.PROFESSIONAL,
  8: FinancialGapAssessmentCalculatorTabs.MISCELLANEOUS,
  9: FinancialGapAssessmentCalculatorTabs.DISCRETIONARY,
  10: FinancialGapAssessmentCalculatorTabs.EXPENSES,
};

export const getTotalExpenses = (expenses: Expenses) => {
  const totalExpenses = Object.values(expenses).reduce(
    (
      sum: number,
      category: { [key: string]: ExpenseItem | OtherExpenseItem[] }
    ) => {
      const categoryTotal = Object.values(category).reduce(
        (categorySum: number, item: ExpenseItem | OtherExpenseItem[]) => {
          if (Array.isArray(item)) {
            // Sum the monthly expenses for each item in the array (for 'Other' key)
            const otherTotal = item.reduce(
              (otherSum: number, otherItem: OtherExpenseItem) =>
                otherSum + (otherItem.monthly_expense || 0),
              0
            );
            return categorySum + otherTotal;
          }
          return categorySum + (item.monthly_expense || 0);
        },
        0
      );
      return sum + categoryTotal;
    },
    0
  );

  return totalExpenses || 0;
};
export const reportPagination = (data: Expenses): ExpensesReportResponse[] => {
  const result: any[] = [];
  let overallTotalExpense = 0; // Track overall total expense
  const currentPage: { [screen: string]: any } = {};

  Object.entries(data).forEach(([screen, questions]) => {
    if (
      ![
        FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES,
        FinancialGapAssessmentCalculatorTabs.FOOD_AND_GROCERY,
        FinancialGapAssessmentCalculatorTabs.PERSONAL_CARE,
        FinancialGapAssessmentCalculatorTabs.HEALTH_CARE,
        FinancialGapAssessmentCalculatorTabs.TRANSPORTATION,
        FinancialGapAssessmentCalculatorTabs.LOANSPAYMENTS,
        FinancialGapAssessmentCalculatorTabs.PROFESSIONAL,
        FinancialGapAssessmentCalculatorTabs.MISCELLANEOUS,
        FinancialGapAssessmentCalculatorTabs.DISCRETIONARY,
      ].includes(screen as FinancialGapAssessmentCalculatorTabs)
    )
      return;

    const screenQuestions: Array<[string, any | any[]]> = Object.entries(
      questions as any
    );
    let screenTotalExpense = 0;

    // Add header for new screen
    currentPage[screen] = {};

    screenQuestions.forEach(([questionId, answer]) => {
      if (Array.isArray(answer)) {
        answer.forEach((item) => {
          if (!currentPage[screen][questionId]) {
            currentPage[screen][questionId] = [];
          }
          (currentPage[screen][questionId] as any[]).push(item);
          screenTotalExpense += item.monthly_expense || 0;
          overallTotalExpense += item.monthly_expense || 0;
        });
      } else {
        currentPage[screen][questionId] = answer;
        screenTotalExpense += answer.monthly_expense || 0;
        overallTotalExpense += answer.monthly_expense || 0;
      }
    });

    // Add total expense for the screen
    currentPage[screen].total_monthly_expense = screenTotalExpense;
  });

  // Add the overall total expense to the result
  currentPage.total_monthly_expense = overallTotalExpense;
  result.push(currentPage);

  return result;
};

export const paginateData = (data: Expenses): ExpensesReportResponse[] => {
  const MAX_PAGE_HEIGHT = 1400; // Define the maximum height for a data on page in pixels
  const ROW_HEIGHT = 41; // Estimated height of each expense row in pixels
  const TABLE_MARGIN = 40; // Margin between tables in pixels
  const TOTAL_EXPENSE_ROW_HEIGHT = 41; // Height for the total expense row
  const TOTAL_EXPENSE_TABLE_HEIGHT = 230; // Height for the total expense Inputs Rows
  const HEADER_HEIGHT = 44; // Height for each table header
  const result: any[] = [];
  let currentPage: { [screen: string]: any } = {};
  let currentPageHeight = 0;
  let overallTotalExpense = 0; // Track overall total expense

  const addPage = () => {
    if (Object.keys(currentPage).length > 0) {
      result.push(currentPage);
      currentPage = {};
      currentPageHeight = 0;
    }
  };
  Object.entries(data).forEach(([screen, questions]) => {
    if (
      ![
        FinancialGapAssessmentCalculatorTabs.HOUSEHOLD_EXPENSES,
        FinancialGapAssessmentCalculatorTabs.FOOD_AND_GROCERY,
        FinancialGapAssessmentCalculatorTabs.PERSONAL_CARE,
        FinancialGapAssessmentCalculatorTabs.HEALTH_CARE,
        FinancialGapAssessmentCalculatorTabs.TRANSPORTATION,
        FinancialGapAssessmentCalculatorTabs.LOANSPAYMENTS,
        FinancialGapAssessmentCalculatorTabs.PROFESSIONAL,
        FinancialGapAssessmentCalculatorTabs.MISCELLANEOUS,
        FinancialGapAssessmentCalculatorTabs.DISCRETIONARY,
      ].includes(screen as FinancialGapAssessmentCalculatorTabs)
    )
      return;

    const screenQuestions: Array<[string, any | any[]]> = Object.entries(
      questions as any
    );
    let screenTotalExpense = 0;

    // Add header height and table margin for new table if currentPage already has data
    if (Object.keys(currentPage).length > 0) {
      currentPageHeight += HEADER_HEIGHT + TABLE_MARGIN;
    } else {
      currentPageHeight += HEADER_HEIGHT;
    }

    screenQuestions.forEach(([questionId, answer]) => {
      if (!currentPage[screen]) {
        currentPage[screen] = {};
      }
      if (Array.isArray(answer)) {
        answer.forEach((item) => {
          if (currentPageHeight + ROW_HEIGHT > MAX_PAGE_HEIGHT) {
            addPage();
            if (!currentPage[screen]) {
              currentPage[screen] = {};
              currentPageHeight += HEADER_HEIGHT; // Add header height for new table on new page
            }
          }
          if (!currentPage[screen][questionId]) {
            currentPage[screen][questionId] = [];
          }
          (currentPage[screen][questionId] as any[]).push(item);
          screenTotalExpense += item.monthly_expense || 0;
          overallTotalExpense += item.monthly_expense || 0;
          currentPageHeight += ROW_HEIGHT;
        });
      } else {
        if (currentPageHeight + ROW_HEIGHT > MAX_PAGE_HEIGHT) {
          addPage();
          if (!currentPage[screen]) {
            currentPage[screen] = {};
            currentPageHeight += HEADER_HEIGHT; // Add header height for new table on new page
          }
        }
        currentPage[screen][questionId] = answer;
        screenTotalExpense += answer.monthly_expense || 0;
        overallTotalExpense += answer.monthly_expense || 0;
        currentPageHeight += ROW_HEIGHT;
      }
    });

    // Check if there is enough space for the total expense row
    if (currentPageHeight + TOTAL_EXPENSE_ROW_HEIGHT > MAX_PAGE_HEIGHT) {
      addPage();
    }

    // Add the total expense for the screen to the last page containing its questions
    if (!currentPage[screen]) {
      currentPage[screen] = {};
    }
    currentPage[screen].total_monthly_expense = screenTotalExpense;
    currentPageHeight += TOTAL_EXPENSE_ROW_HEIGHT; // Add height of the total expense row
  });
  // Add the overall total expense to the last page if there is enough space
  if (currentPageHeight + TOTAL_EXPENSE_TABLE_HEIGHT > MAX_PAGE_HEIGHT) {
    addPage();
  }

  // If current page is empty, we don't need to add it
  if (Object.keys(currentPage).length > 0) {
    currentPage.total_monthly_expense = overallTotalExpense;
    result.push(currentPage);
  } else {
    result.push({});
    result[result.length - 1].total_monthly_expense = overallTotalExpense;
  }
  return result;
};

export const formatCurrency = (value: number) =>
  value.toLocaleString('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });
