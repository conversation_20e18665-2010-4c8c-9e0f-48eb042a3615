import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AdvisorInvite } from 'types/AdvisorInvites.type';

export interface SecondaryAdvisorState {
  entities: {
    invites: AdvisorInvite[] | [];
  };
  loading?: boolean;
  error?: string;
}

const initialState: SecondaryAdvisorState = {
  entities: { invites: [] },
};

const secondaryAdvisorSlice = createSlice({
  name: 'secondaryAdvisorInvite',
  initialState,
  reducers: {
    setAdvisorInviteLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setAdvisorInvitesData: (state, action: PayloadAction<AdvisorInvite[]>) => {
      state.entities.invites = action.payload;
    },

    updateAdvisorInvitesData: (state, action: PayloadAction<AdvisorInvite>) => {
      const invite = action.payload;
      state.entities.invites = [...state.entities.invites, invite];
    },
    cancelOrRevokeAdvisorInvite: (state, action: PayloadAction<number>) => {
      state.entities.invites = state.entities.invites.filter(
        (advisorInvite) => advisorInvite.id !== action.payload
      );
    },
  },
});

export const {
  setAdvisorInvitesData,
  setAdvisorInviteLoading,
  updateAdvisorInvitesData,
  cancelOrRevokeAdvisorInvite,
} = secondaryAdvisorSlice.actions;

export default secondaryAdvisorSlice.reducer;
