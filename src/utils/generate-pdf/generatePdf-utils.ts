import jsPDF from 'jspdf';
import domtoimage from 'dom-to-image';

export async function generatePdf(
  reportPages: any[],
  reportTitle: string,
  onComplete?: () => void,
  orientation?: 'p' | 'portrait' | 'l' | 'landscape',
  format?: string | number[]
) {
  // eslint-disable-next-line new-cap
  const pdf = new jsPDF(orientation || 'portrait', 'px', format || 'a4');
  const scale = 1;
  const totalPages = reportPages.length;

  try {
    for (let i = 0; i < totalPages; i += 1) {
      const reportPage = reportPages[i];

      // eslint-disable-next-line no-await-in-loop
      const dataUrl = await domtoimage.toJpeg(reportPage, {
        height: (reportPage.offsetWidth / 0.707) * scale, // This ensures that aspect ratio of the page remain as per A4 size.
        style: {
          transform: `scale(${scale})`,
          transformOrigin: 'top left',
          width: `${reportPage.offsetWidth * scale}px`,
          height: `${(reportPage.offsetWidth / 0.707) * scale}px`,
        },
        width: reportPage.offsetWidth * scale,
      });

      const imgWidth = pdf.internal.pageSize.getWidth();
      const imgHeight = pdf.internal.pageSize.getHeight();

      pdf.addImage(
        dataUrl,
        'jpeg',
        0,
        0,
        imgWidth,
        imgHeight,
        undefined,
        'FAST'
      );

      if (i !== totalPages - 1) {
        pdf.addPage(format || 'a4', orientation || 'portrait');
      }
    }

    // Save the PDF
    pdf.save(reportTitle);

    if (onComplete) {
      onComplete();
    }
  } catch (error) {
    // Handle errors here
    console.error('Error generating PDF:', error);
  }
}
