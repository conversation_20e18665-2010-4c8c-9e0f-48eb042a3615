import React from 'react';
import { MdEmail } from 'react-icons/md';
import { FaPhoneAlt } from 'react-icons/fa';
import { format } from 'date-fns';
import logo from '../../../assets/ExitSmartsLogo.svg';

interface Props {
  pageTitle: string;
  presentedBy: string;
  preparedFor: string;
  reportDate: string;
  email: string;
  phone?: string;
  companyName?: string;
}

const CoverPage: React.FC<Props> = ({
  pageTitle,
  presentedBy,
  preparedFor,
  reportDate,
  email,
  phone,
  companyName,
}) => (
  <div className='flex flex-col items-center justify-center h-full '>
    <img src={logo} alt='logo' className=' w-[33.75rem]' />
    <h1 className='text-2xl font-openSans font-semibold text-blue-01 mt-9'>
      {pageTitle}
    </h1>

    <div className='mt-10 font-medium space-y-3'>
      <div className='flex justify-between gap-6'>
        <span>Presented By :</span>
        <span className='text-blue-01'>{presentedBy}</span>
      </div>
      <div className='flex justify-between gap-6'>
        <span>Prepared For :</span>
        <span className='text-blue-01'>{preparedFor}</span>
      </div>
      {companyName && (
        <div className='flex justify-between gap-6'>
          <span>Company Name :</span>
          <span className='text-blue-01'>{companyName}</span>
        </div>
      )}
      {!!reportDate && (
        <div className='flex justify-between gap-6'>
          <span>Date :</span>
          <span className='text-blue-01'>{`${format(
            new Date(reportDate),
            'MM/dd/yyyy'
          )}`}</span>
        </div>
      )}
    </div>

    <div className='h-0.5 w-1/3 bg-blue-01 mt-9 mb-6' />

    <div className='flex space-x-20'>
      <div className='flex space-x-5 items-center'>
        <MdEmail />
        <span className='text-blue-01 font-medium'>{email}</span>
      </div>
      {!!phone && (
        <div className='flex space-x-5 items-center'>
          <FaPhoneAlt />
          <span className='text-blue-01 font-medium'>{phone}</span>
        </div>
      )}
    </div>
  </div>
);

export default CoverPage;
