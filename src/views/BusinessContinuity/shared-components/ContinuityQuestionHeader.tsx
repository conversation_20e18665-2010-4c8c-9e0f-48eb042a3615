import { useFormikContext } from 'formik';
import React from 'react';
import Button from 'shared-resources/components/Button/Button';
import { BusinessContinuityScreens } from 'types/enum';

interface ContinuityQuestionHeaderProps {
  push: (values: any) => void;
  children?: React.ReactNode;
  fieldKey: string;
  pushData: any;
  title?: string;
  classname?: string;
  buttonText?: string;
}
const ContinuityQuestionHeader: React.FC<ContinuityQuestionHeaderProps> = (
  props
) => {
  const { push, children, fieldKey, pushData, title, classname, buttonText } =
    props;
  const { values } = useFormikContext<any>();

  const isUnsavedValue = (formValues: any) =>
    formValues?.[fieldKey]?.length &&
    fieldKey !== BusinessContinuityScreens.MESSAGE_WISHES &&
    formValues?.[fieldKey]?.find((data: any) => !data?.saved);

  const showAddButton =
    fieldKey === 'partners'
      ? values?.is_sole_owner === false &&
        values?.partners?.length &&
        !isUnsavedValue(values)
      : !isUnsavedValue(values);

  return (
    <div
      className={`${classname} ${
        title ? 'flex-col' : 'flex-row'
      } flex  items-center justify-between  mb-4`}
    >
      {children && (
        <div className={`${title ? 'w-full min-h-10' : 'w-full'}`}>
          {children}
        </div>
      )}
      <div
        className={`flex items-center   ${
          title ? 'w-full justify-between' : 'justify-end'
        }`}
      >
        <span className='text-xl font-semibold min-h-10 flex items-center text-blue-01'>
          {title}
        </span>
        {showAddButton && (
          <div className='flex h-full items-center'>
            <Button
              onClick={() => {
                push(pushData);
              }}
              className='px-4 py-2 mr-6'
              type='button'
            >
              Add {buttonText}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContinuityQuestionHeader;
