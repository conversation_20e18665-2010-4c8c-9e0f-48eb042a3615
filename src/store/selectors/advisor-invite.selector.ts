import { createSelector } from 'reselect';
import { AppState } from 'store';
import { SecondaryAdvisorState } from 'store/reducers/advisor-invite.reducer';

const secondaryAdvisorStore = (store: AppState) => store.secondaryAdvisorInvite;

export const advisorInvitesSelector = createSelector(
  [secondaryAdvisorStore],
  (state: SecondaryAdvisorState) => state.entities.invites
);
export const advisorInviteLoadingSelector = createSelector(
  [secondaryAdvisorStore],
  (state: SecondaryAdvisorState) => state.loading
);
