import { FieldArray, Form, Formik } from 'formik';
import React from 'react';
import { IoInformationCircle } from 'react-icons/io5';
import { useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { getKey } from 'utils/helpers/Helpers';
import * as yup from 'yup';
import { getYear } from '../BusinessValuationConfig';

interface Props {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  data: any;
  onSaveToDraftClick: (data: any) => void;
}
const SpouseOrFamilyPandL: React.FC<Props> = (props) => {
  const { nextStep, setData, backStep, data, onSaveToDraftClick } = props;
  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup.object().shape({
    spouseOrFamilyPandL: yup.array().of(
      yup.object({
        name: yup.string().nullable(),
        [`${getYear(1)}_salary`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`${getYear(2)}_salary`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`${getYear(3)}_salary`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`${getYear(1)}_bonus`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`${getYear(2)}_bonus`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`${getYear(3)}_bonus`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`market_${getYear(1)}_salary`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`market_${getYear(2)}_salary`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`market_${getYear(3)}_salary`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`market_${getYear(1)}_bonus`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`market_${getYear(2)}_bonus`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`market_${getYear(3)}_bonus`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`net_adjustments_${getYear(1)}_salary`]: yup
          .number()
          .typeError('Must be a valid number')
          .nullable(),
        [`net_adjustments_${getYear(2)}_salary`]: yup
          .number()
          .typeError('Must be a valid number')
          .nullable(),
        [`net_adjustments_${getYear(3)}_salary`]: yup
          .number()
          .typeError('Must be a valid number')
          .nullable(),
        [`net_adjustments_${getYear(1)}_bonus`]: yup
          .number()
          .typeError('Must be a valid number')
          .nullable(),
        [`net_adjustments_${getYear(2)}_bonus`]: yup
          .number()
          .typeError('Must be a valid number')
          .nullable(),
        [`net_adjustments_${getYear(3)}_bonus`]: yup
          .number()
          .typeError('Must be a valid number')
          .nullable(),
      })
    ),
  });

  const handleSubmit = (values: { spouseOrFamilyPandL: any }) => {
    setData(values.spouseOrFamilyPandL);
    nextStep();
  };

  return (
    <div className='flex flex-col '>
      <Formik
        initialValues={{
          spouseOrFamilyPandL: data?.spouse_or_family_p_and_l || [
            {
              name: '',
              [`${getYear(1)}_salary`]: '',
              [`${getYear(2)}_salary`]: '',
              [`${getYear(3)}_salary`]: '',
              [`${getYear(1)}_bonus`]: '',
              [`${getYear(2)}_bonus`]: '',
              [`${getYear(3)}_bonus`]: '',
              [`market_${getYear(1)}_salary`]: '',
              [`market_${getYear(2)}_salary`]: '',
              [`market_${getYear(3)}_salary`]: '',
              [`market_${getYear(1)}_bonus`]: '',
              [`market_${getYear(2)}_bonus`]: '',
              [`market_${getYear(3)}_bonus`]: '',
              [`net_adjustments_${getYear(1)}_salary`]: 0,
              [`net_adjustments_${getYear(2)}_salary`]: 0,
              [`net_adjustments_${getYear(3)}_salary`]: 0,
              [`net_adjustments_${getYear(1)}_bonus`]: 0,
              [`net_adjustments_${getYear(2)}_bonus`]: 0,
              [`net_adjustments_${getYear(3)}_bonus`]: 0,
            },
          ],
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values, setFieldValue }) => (
          <Form className='relative pl-5 py-3 flex flex-col gap-4 bg-white'>
            <h2 className='text-[1.1rem] font-[500] pr-10'>
              Spouses/family members may be paid over or under the market salary
              for the same job. If multiple Spouse/Family Members are employed
              use ADD button.
            </h2>
            <div className='relative z-10 text-xl mb-5 items-center font-semibold'>
              <span className='absolute left-[26%] mt-1'>{getYear(1)}</span>
              <span className='absolute left-[52%] mt-1'>{getYear(2)}</span>
              <span className='absolute left-[78%] mt-1'>{getYear(3)}</span>
            </div>

            <FieldArray name='spouseOrFamilyPandL'>
              {({ push }) => (
                <div className='h-[calc(100vh-26rem)] overflow-auto scrollbar pr-4'>
                  {values.spouseOrFamilyPandL &&
                    values.spouseOrFamilyPandL.length > 0 &&
                    values.spouseOrFamilyPandL.map(
                      (spouseOrFamilyPandL: any, index: number) => (
                        <div key={getKey(index)} className=''>
                          <div className='flex gap-1  text-[1.2rem]'>
                            <span className='underline font-semibold'>
                              Spouse or Family Member
                            </span>
                          </div>

                          <div className='grid grid-cols-4 items-center gap-x-16'>
                            <span>Name</span>
                            <div className='col-span-3'>
                              <FormikInput
                                className='!h-10 mt-5'
                                name={`spouseOrFamilyPandL.${index}.name`}
                                key={`spouseOrFamilyPandL.${getKey(
                                  index
                                )}.name`}
                              />
                            </div>
                          </div>
                          {/*  Salary and Bonus Part */}
                          <span className='text-blue-01 underline font-semibold'>
                            Spouse/Family Member Salary/Bonus
                          </span>
                          <div className='grid grid-cols-4 gap-x-16 gap-y-1 mt-2 text-[1.1rem]'>
                            <div className='flex  items-center gap-2'>
                              <IoInformationCircle className='text-blue-01 salary' />
                              <div className='relative'>
                                <Tooltip
                                  anchorSelect='.salary'
                                  place='right'
                                  offset={0}
                                  className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                  classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                >
                                  Actual Salary of Spouse/Family Member. If
                                  multiple owners Use Add button
                                </Tooltip>
                                <span>Salary</span>
                              </div>
                            </div>
                            <FormikInput
                              valueChanged={(event) => {
                                const netValue =
                                  event.target.value.replace(/,/g, '') -
                                  Number(
                                    values.spouseOrFamilyPandL[index][
                                      `market_${getYear(1)}_salary`
                                    ]?.replace(/,/g, '') || 0
                                  );

                                setFieldValue(
                                  `spouseOrFamilyPandL.${getKey(
                                    index
                                  )}.net_adjustments_${getYear(1)}_salary`,
                                  netValue
                                );
                              }}
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.${getYear(
                                1
                              )}_salary`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.${getYear(1)}_salary`}
                            />
                            <FormikInput
                              valueChanged={(event) => {
                                const netValue =
                                  event.target.value.replace(/,/g, '') -
                                  Number(
                                    values.spouseOrFamilyPandL[index][
                                      `market_${getYear(2)}_salary`
                                    ]?.replace(/,/g, '') || 0
                                  );
                                setFieldValue(
                                  `spouseOrFamilyPandL.${getKey(
                                    index
                                  )}.net_adjustments_${getYear(2)}_salary`,
                                  netValue
                                );
                              }}
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.${getYear(
                                2
                              )}_salary`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.${getYear(2)}_salary`}
                            />
                            <FormikInput
                              valueChanged={(event) => {
                                const netValue =
                                  event.target.value.replace(/,/g, '') -
                                  Number(
                                    values.spouseOrFamilyPandL[index][
                                      `market_${getYear(3)}_salary`
                                    ]?.replace(/,/g, '') || 0
                                  );
                                setFieldValue(
                                  `spouseOrFamilyPandL.${getKey(
                                    index
                                  )}.net_adjustments_${getYear(3)}_salary`,
                                  netValue
                                );
                              }}
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.${getYear(
                                3
                              )}_salary`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.${getYear(3)}_salary`}
                            />
                            <div className='flex  items-center gap-2'>
                              <IoInformationCircle className='text-blue-01 bonus' />
                              <div className='relative'>
                                <Tooltip
                                  anchorSelect='.bonus'
                                  place='right'
                                  offset={0}
                                  className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                  classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                >
                                  Actual Bonus taken.
                                </Tooltip>
                                <span>Bonuses</span>
                              </div>
                            </div>
                            <FormikInput
                              valueChanged={(event) => {
                                const netValue =
                                  event.target.value.replace(/,/g, '') -
                                  Number(
                                    values.spouseOrFamilyPandL[index][
                                      `market_${getYear(1)}_bonus`
                                    ]?.replace(/,/g, '') || 0
                                  );
                                setFieldValue(
                                  `spouseOrFamilyPandL.${getKey(
                                    index
                                  )}.net_adjustments_${getYear(1)}_bonus`,
                                  netValue
                                );
                              }}
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.${getYear(
                                1
                              )}_bonus`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.${getYear(1)}_bonus`}
                            />
                            <FormikInput
                              valueChanged={(event) => {
                                const netValue =
                                  event.target.value.replace(/,/g, '') -
                                  Number(
                                    values.spouseOrFamilyPandL[index][
                                      `market_${getYear(2)}_bonus`
                                    ]?.replace(/,/g, '') || 0
                                  );
                                setFieldValue(
                                  `spouseOrFamilyPandL.${getKey(
                                    index
                                  )}.net_adjustments_${getYear(2)}_bonus`,
                                  netValue
                                );
                              }}
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.${getYear(
                                2
                              )}_bonus`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.${getYear(2)}_bonus`}
                            />
                            <FormikInput
                              valueChanged={(event) => {
                                const netValue =
                                  event.target.value.replace(/,/g, '') -
                                  Number(
                                    values.spouseOrFamilyPandL[index][
                                      `market_${getYear(3)}_bonus`
                                    ]?.replace(/,/g, '') || 0
                                  );
                                setFieldValue(
                                  `spouseOrFamilyPandL.${getKey(
                                    index
                                  )}.net_adjustments_${getYear(3)}_bonus`,
                                  netValue
                                );
                              }}
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.${getYear(
                                3
                              )}_bonus`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.${getYear(3)}_bonus`}
                            />
                          </div>

                          {/* Market Salary and Bonus Part */}
                          <span className='text-blue-01 underline font-semibold'>
                            Spouse/Family Member Market Salary/Benefits
                          </span>
                          <div className='grid grid-cols-4 gap-x-16 gap-y-1 mt-2 text-[1.1rem]'>
                            <div className='flex  items-center gap-2'>
                              <IoInformationCircle className='text-blue-01 marketsalary' />
                              <div className='relative'>
                                <Tooltip
                                  anchorSelect='.marketsalary'
                                  place='right'
                                  offset={0}
                                  className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                  classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                >
                                  Salary for same job done by someone other than
                                  the business owner(s)
                                </Tooltip>
                                <span>Salary</span>
                              </div>
                            </div>
                            <FormikInput
                              valueChanged={(event) => {
                                const netValue =
                                  Number(
                                    values.spouseOrFamilyPandL[index][
                                      `${getYear(1)}_salary`
                                    ]?.replace(/,/g, '') || 0
                                  ) - event.target.value.replace(/,/g, '');
                                setFieldValue(
                                  `spouseOrFamilyPandL.${getKey(
                                    index
                                  )}.net_adjustments_${getYear(1)}_salary`,
                                  netValue
                                );
                              }}
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.market_${getYear(
                                1
                              )}_salary`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.market_${getYear(1)}_salary`}
                            />
                            <FormikInput
                              valueChanged={(event) => {
                                const netValue =
                                  Number(
                                    values.spouseOrFamilyPandL[index][
                                      `${getYear(2)}_salary`
                                    ]?.replace(/,/g, '') || 0
                                  ) - event.target.value.replace(/,/g, '');
                                setFieldValue(
                                  `spouseOrFamilyPandL.${getKey(
                                    index
                                  )}.net_adjustments_${getYear(2)}_salary`,
                                  netValue
                                );
                              }}
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.market_${getYear(
                                2
                              )}_salary`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.market_${getYear(2)}_salary`}
                            />
                            <FormikInput
                              valueChanged={(event) => {
                                const netValue =
                                  Number(
                                    values.spouseOrFamilyPandL[index][
                                      `${getYear(3)}_salary`
                                    ]?.replace(/,/g, '') || 0
                                  ) - event.target.value.replace(/,/g, '');
                                setFieldValue(
                                  `spouseOrFamilyPandL.${getKey(
                                    index
                                  )}.net_adjustments_${getYear(3)}_salary`,
                                  netValue
                                );
                              }}
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.market_${getYear(
                                3
                              )}_salary`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.market_${getYear(3)}_salary`}
                            />
                            <div className='flex  items-center gap-2'>
                              <IoInformationCircle className='text-blue-01 marketBonus' />
                              <div className='relative'>
                                <Tooltip
                                  anchorSelect='.marketBonus'
                                  place='right'
                                  offset={0}
                                  className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                  classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                >
                                  Bonus for same job done by someone other than
                                  the business owner(s)
                                </Tooltip>
                                <span>Bonuses</span>
                              </div>
                            </div>
                            <FormikInput
                              valueChanged={(event) => {
                                const netValue =
                                  Number(
                                    values.spouseOrFamilyPandL[index][
                                      `${getYear(1)}_bonus`
                                    ].replace(/,/g, '') || 0
                                  ) - event.target.value.replace(/,/g, '');
                                setFieldValue(
                                  `spouseOrFamilyPandL.${getKey(
                                    index
                                  )}.net_adjustments_${getYear(1)}_bonus`,
                                  netValue
                                );
                              }}
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.market_${getYear(
                                1
                              )}_bonus`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.market_${getYear(1)}_bonus`}
                            />
                            <FormikInput
                              valueChanged={(event) => {
                                const netValue =
                                  Number(
                                    values.spouseOrFamilyPandL[index][
                                      `${getYear(2)}_bonus`
                                    ]?.replace(/,/g, '') || 0
                                  ) - event.target.value.replace(/,/g, '');
                                setFieldValue(
                                  `spouseOrFamilyPandL.${getKey(
                                    index
                                  )}.net_adjustments_${getYear(2)}_bonus`,
                                  netValue
                                );
                              }}
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.market_${getYear(
                                2
                              )}_bonus`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.market_${getYear(2)}_bonus`}
                            />
                            <FormikInput
                              valueChanged={(event) => {
                                const netValue =
                                  Number(
                                    values.spouseOrFamilyPandL[index][
                                      `${getYear(2)}_bonus`
                                    ]?.replace(/,/g, '') || 0
                                  ) - event.target.value.replace(/,/g, '');
                                setFieldValue(
                                  `spouseOrFamilyPandL.${getKey(
                                    index
                                  )}.net_adjustments_${getYear(3)}_bonus`,
                                  netValue
                                );
                              }}
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.market_${getYear(
                                3
                              )}_bonus`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.market_${getYear(3)}_bonus`}
                            />
                          </div>

                          {/* Net Adjustments Part */}
                          <span className='text-blue-01 underline font-semibold'>
                            Spouse/Family Member Market Salary/Benefits Net
                            Adjustment
                          </span>
                          <div className='grid grid-cols-4 gap-x-16 gap-y-1 mt-2 text-[1.1rem]'>
                            <div className='flex  items-center gap-2'>
                              <IoInformationCircle className='text-blue-01 ownersalary' />
                              <div className='relative'>
                                <Tooltip
                                  anchorSelect='.ownersalary'
                                  place='right'
                                  offset={0}
                                  className=' !bg-gray-100 border !z-10 !w-[29rem] !font-[500]  !left-1 !top-1 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                  classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                >
                                  This is the difference calculated by
                                  subtracting Market Salary and Benefits from
                                  Owner&apos;s Salary and Benefits.
                                </Tooltip>
                                <span>Salary</span>
                              </div>
                            </div>
                            <FormikInput
                              disabled
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.net_adjustments_${getYear(
                                1
                              )}_salary`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.net_adjustments_${getYear(1)}_salary`}
                            />
                            <FormikInput
                              disabled
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.net_adjustments_${getYear(
                                2
                              )}_salary`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.net_adjustments_${getYear(2)}_salary`}
                            />
                            <FormikInput
                              disabled
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.net_adjustments_${getYear(
                                3
                              )}_salary`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.net_adjustments_${getYear(3)}_salary`}
                            />
                            <div className='flex  items-center gap-2'>
                              <IoInformationCircle
                                id='ownerbonus'
                                className='text-blue-01'
                              />
                              <div className='relative'>
                                <Tooltip
                                  anchorSelect='#ownerbonus'
                                  place='right'
                                  offset={0}
                                  className=' !bg-gray-100 border !z-10  !w-[54rem] !font-[500]  !left-1 !-top-2 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                  classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                >
                                  This is the difference calculated by
                                  subtracting Market Bonus and Benefits from
                                  Owner&apos;s Bonus and Benefits.
                                </Tooltip>
                                <span>Bonuses</span>
                              </div>
                            </div>
                            <FormikInput
                              disabled
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.net_adjustments_${getYear(
                                1
                              )}_bonus`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.net_adjustments_${getYear(1)}_bonus`}
                            />
                            <FormikInput
                              disabled
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.net_adjustments_${getYear(
                                2
                              )}_bonus`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.net_adjustments_${getYear(2)}_bonus`}
                            />
                            <FormikInput
                              disabled
                              className='!h-10'
                              name={`spouseOrFamilyPandL.${index}.net_adjustments_${getYear(
                                3
                              )}_bonus`}
                              key={`spouseOrFamilyPandL.${getKey(
                                index
                              )}.net_adjustments_${getYear(3)}_bonus`}
                            />
                          </div>
                        </div>
                      )
                    )}

                  <Button
                    type='button'
                    className='px-6 py-2 !absolute top-[5rem] 3xl:top-[4rem] right-[2rem]'
                    onClick={() =>
                      push({
                        name: '',
                        [`${getYear(1)}_salary`]: '',
                        [`${getYear(2)}_salary`]: '',
                        [`${getYear(3)}_salary`]: '',
                        [`${getYear(1)}_bonus`]: '',
                        [`${getYear(2)}_bonus`]: '',
                        [`${getYear(3)}_bonus`]: '',
                        [`market_${getYear(1)}_salary`]: '',
                        [`market_${getYear(2)}_salary`]: '',
                        [`market_${getYear(3)}_salary`]: '',
                        [`market_${getYear(1)}_bonus`]: '',
                        [`market_${getYear(2)}_bonus`]: '',
                        [`market_${getYear(3)}_bonus`]: '',
                        [`net_adjustments_${getYear(1)}_salary`]: 0,
                        [`net_adjustments_${getYear(2)}_salary`]: 0,
                        [`net_adjustments_${getYear(3)}_salary`]: 0,
                        [`net_adjustments_${getYear(1)}_bonus`]: 0,
                        [`net_adjustments_${getYear(2)}_bonus`]: 0,
                        [`net_adjustments_${getYear(3)}_bonus`]: 0,
                      })
                    }
                  >
                    Add
                  </Button>
                </div>
              )}
            </FieldArray>

            <div className='pr-7'>
              <BackNextComponent
                backStep={backStep}
                buttonType='submit'
                isLoading={isLoading}
                isNextDisable={false}
                onSaveToDraftClick={() => {
                  onSaveToDraftClick(values.spouseOrFamilyPandL);
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default SpouseOrFamilyPandL;
