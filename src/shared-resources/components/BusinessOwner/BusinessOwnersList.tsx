import React, { useEffect, useRef } from 'react';
import InfiniteScroll from 'HOC/InfiniteScroll';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useSearchParams } from 'react-router-dom';
import { fetchBusinessOwners } from 'store/actions/business-owner.action';
import {
  allBusinessOwners,
  businessOwnerListLoadingState,
  businessOwnerPaginationMeta,
} from 'store/selectors/business-owner.selector';
import { getUserData } from 'store/selectors/user.selector';
import Spinner from '../Spinner/Spinner';
import BusinessOwnerCard from './BusinessOwnerCard';

interface Props {
  isPrimaryAdvisor?: boolean;
  advisorId?: string;
  showSecondaryAdvisor?: boolean;
}

const BusinessOwnersList = (props: Props) => {
  const location = useLocation();
  const state = location.state as Props | null;

  const isPrimaryAdvisor =
    props.isPrimaryAdvisor ?? state?.isPrimaryAdvisor ?? false;

  const advisorId = state?.advisorId?? null;  

  const dispatch = useDispatch();
  const businessOwnersLoading = useSelector(businessOwnerListLoadingState);
  const businessOwners = useSelector(allBusinessOwners);
  const [searchParams] = useSearchParams();
  const pageMeta = useSelector(businessOwnerPaginationMeta);
  const listRef = useRef<HTMLDivElement | null>(null);
  const user = useSelector(getUserData);

  useEffect(() => {
    if (user) {
      dispatch(
        fetchBusinessOwners({
          filters: { 
            search: searchParams.get('search') ?? '' ,
            advisorId,
            secondaryAdvisor : props.showSecondaryAdvisor
          },
        })
      );
    }
  }, [dispatch, user]);

  const handleScroll = () => {
    dispatch(
      fetchBusinessOwners({
        filters: {
          page: (pageMeta.currentPage || 1) + 1,
          search: searchParams.get('search') ?? '',
          advisorId,
        },
      })
    );
  };

  useEffect(() => {
    listRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
  }, [searchParams]);

  return (
    <InfiniteScroll
      onUserScroll={() => {
        if (pageMeta.nextPageUrl) {
          handleScroll();
        }
      }}
    >
      <div
        ref={listRef}
        className='mt-7.75 w-full h-full overflow-auto scrollbar px-3 pt-3'
      >
        {!!businessOwners?.length && (
          <div className='w-full h-fit grid grid-cols-2 gap-x-6 gap-y-6 1.5xl:grid-cols-3 3xl:grid-cols-4 4xl:grid-cols-5'>
            {businessOwners.map((businessOwner: BusinessOwner) => (
              <BusinessOwnerCard
                key={businessOwner?.id}
                businessOwner={businessOwner}
                disableDetailView={advisorId ? true : false}
              />
            ))}
          </div>
        )}
        {!businessOwners?.length && !businessOwnersLoading && (
          <div className='flex items-center justify-center'>
            <p className='text-blue-01 font-medium text-xl'>
              {isPrimaryAdvisor
                ? 'No Business Owners to show'
                : "You don't have access to any Business Owner"}
            </p>
          </div>
        )}
        {businessOwnersLoading && (
          <div className='flex items-center justify-center'>
            <Spinner size='sm' />
          </div>
        )}
      </div>
    </InfiniteScroll>
  );
};

export default BusinessOwnersList;
