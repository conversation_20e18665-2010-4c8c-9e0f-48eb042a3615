import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { renderToString } from 'react-dom/server';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import Checkbox from 'shared-resources/components/CheckBox/Checkbox';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import { fetchAssessmentReport } from 'store/actions/assessment-report.action';
import { fetchComments } from 'store/actions/tool-comment.action';
import { resetAssessmentData } from 'store/reducers/assessment-tool.reducer';
import {
  getAssessmentReportLoading,
  getAssessmentReportResponse,
} from 'store/selectors/assessment-report.selector';
import { getToolComments } from 'store/selectors/tool-comment.selector';
import { getUserData } from 'store/selectors/user.selector';
import { AssessmentTools, RouteKey, UserType } from 'types/enum';
import { getUserRouteType } from 'utils/helpers/Helpers';
import {
  TableValueType,
  shareHoldersQuestions,
} from 'views/Stakeholder-Alignment-Meeting/Questions';
import StakeHoldersPdfTableComponent from 'views/Stakeholder-Alignment-Meeting/StakeHoldersPdfTableComponent';

interface StakeholderAlignmentBuilderProps {
  onSelectionChange: (selectedData: {
    [key: string]: { content: string; type: string; comments?: any[] };
  }) => void;
}

const StakeholderAlignmentBuilder: React.FC<
  StakeholderAlignmentBuilderProps
> = ({ onSelectionChange }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const response: any = useSelector(getAssessmentReportResponse);
  const loggedInUserData = useSelector(getUserData);
  const assessmentLoading = useSelector(getAssessmentReportLoading);
  const comments = useSelector(getToolComments);

  const [selectedSections, setSelectedSections] = useState<
    Record<string, boolean>
  >({});
  const [selectedComments, setSelectedComments] = useState<
    Record<string, boolean>
  >({});

  useEffect(() => {
    if (id && loggedInUserData?.type === UserType.ADVISOR) {
      dispatch(
        fetchAssessmentReport({
          id,
          tool: AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING,
          onError: () =>
            navigate(
              `/${getUserRouteType(loggedInUserData?.type)}/${
                RouteKey.DASHBOARD
              }`
            ),
        })
      );
      dispatch(
        fetchComments(Number(id), AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING)
      );
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  useEffect(
    () => () => {
      dispatch(resetAssessmentData());
    },
    []
  );

  const handleCheckboxChange = useCallback((sectionId: string) => {
    setSelectedSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId],
    }));
  }, []);

  const handleCommentCheckboxChange = useCallback((commentId: string) => {
    setSelectedComments((prev) => ({
      ...prev,
      [commentId]: !prev[commentId],
    }));
  }, []);

  const renderTextArea = useCallback(
    (question: string, answer: string, isSelected: boolean) => (
      <div className='flex !text-base flex-col gap-4'>
        {isSelected && <div className=' font-medium'>{question}</div>}
        <div className='mt-2 w-full min-h-64 border-2 border-gray-300 rounded-md p-4'>
          {answer}
        </div>
      </div>
    ),
    []
  );

  const renderTable = useCallback(
    (tableValues: TableValueType[]) => (
      <StakeHoldersPdfTableComponent
        tableValues={tableValues}
        isReportBuilder
      />
    ),
    []
  );

  const renderCommentsForQuill = useCallback(
    (comments: any[]) => (
      <div className='mt-4'>
        <span className='font-semibold mb-2'>Comments:</span>
        {comments.map((comment) => (
          <div key={comment.id} className='ml-4 mb-2'>
            <p>{comment.comment}</p>
          </div>
        ))}
      </div>
    ),
    []
  );

  const stateObjectiveQuestions = useMemo(
    () => shareHoldersQuestions.filter((q) => q.questionType === 'textArea'),
    []
  );

  const selectedData = useMemo(() => {
    if (!response?.assessment_response) return {};

    const data: {
      [key: string]: { content: string; type: string; comments?: any[] };
    } = {};

    // Handle table and questions data
    Object.entries(selectedSections)
      .filter(([, isSelected]) => isSelected)
      .forEach(([sectionId]) => {
        let content = '';

        if (sectionId === 'stakeholderTable') {
          content = renderToString(
            renderTable(response?.assessment_response?.stakeholderTable)
          );
        } else {
          const question = stateObjectiveQuestions.find(
            (q) => q?.id === sectionId
          );
          if (question) {
            content = renderToString(
              renderTextArea(
                question?.question,
                response?.assessment_response?.[sectionId],
                true
              )
            );
          }
        }

        // Get comments specific to this section

        data[sectionId] = {
          content,
          type: 'stakeholder-alignment',
        };
      });

    const sectionComments = comments
      .filter((comment) => selectedComments[comment.id.toString()])
      .map((comment) => ({
        id: comment.id,
        comment: comment.comment,
        metadata: comment.metadata,
      }));
    data.comments = { comments: sectionComments } as any;
    return data;
  }, [
    selectedSections,
    selectedComments,
    response?.assessment_response,
    comments,
    renderTable,
    renderTextArea,
    renderCommentsForQuill,
    stateObjectiveQuestions,
  ]);

  useEffect(() => {
    if (Object.keys(selectedData)?.length > 0) {
      onSelectionChange(selectedData);
    }
  }, [selectedData, onSelectionChange]);

  if (assessmentLoading)
    return (
      <div className='h-[calc(100vh-18rem)]'>
        <Spinner />
      </div>
    );

  return (
    <div className='flex flex-col gap-4 h-[calc(100vh-18rem)] overflow-y-auto scrollbar pr-4'>
      <Checkbox
        onChange={() => handleCheckboxChange('stakeholderTable')}
        value={selectedSections.stakeholderTable || false}
        className='!mt-0'
        text={<div>Stakeholder Table</div>}
      />
      {response.assessment_response?.stakeholderTable && (
        <div className='pl-8'>
          {renderTable(response.assessment_response.stakeholderTable)}
        </div>
      )}
      {stateObjectiveQuestions.map((question) => {
        const key = question?.id;
        return (
          <div key={key}>
            <Checkbox
              onChange={() => handleCheckboxChange(key)}
              value={selectedSections[key] || false}
              text={question?.question as any}
            />
            <div className='pl-8'>
              {renderTextArea(
                question?.question,
                response?.assessment_response?.[key] || '',
                false
              )}
            </div>
          </div>
        );
      })}
      <div className='mt-8'>
        <span className='font-semibold mb-2 text-lg'>Comments:</span>
        {comments.map((comment) => (
          <div key={comment.id} className='ml-4 mb-2 flex items-center'>
            <Checkbox
              onChange={() =>
                handleCommentCheckboxChange(comment.id.toString())
              }
              value={selectedComments[comment.id.toString()] || false}
              text={comment.comment as any}
              className='!mt-0'
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default StakeholderAlignmentBuilder;
