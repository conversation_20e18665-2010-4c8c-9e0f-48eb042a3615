import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import Spinner from 'shared-resources/components/Spinner/Spinner';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';

import cx from 'classnames';

import QuestionProgressBar from 'shared-resources/components/QuestionProgressBar';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import BuyerAssessmentComp from './BuyerAssesmentComp';
import { BuyerType, BuyerTypeScreens, DealStructure } from './BuyerTypeConfig';

interface Props {
  data: any;
  handleNextClick: (values: any) => void;
  handlebackClick: (values: any) => void;
  handleSaveAsDraftClick: (values: any) => void;
  isDealStructure?: boolean;
  questions: {
    ques: string;
    id: DealStructure | BuyerType;
  }[];
}

const BuyerAssessmentContainer: React.FC<Props> = ({
  data,
  handleNextClick,
  handleSaveAsDraftClick,
  handlebackClick,
  isDealStructure,
  questions,
}) => {
  const isLoading = useSelector(getAssessmentToolLoading);
  const response = isDealStructure
    ? data?.[BuyerTypeScreens.DEAL_STRUCTURE]
    : data?.[BuyerTypeScreens.TYPE_OF_BUYER];
  const [responses, setResponses] = useState(response || {});
  const [nextButtonEnable, setNextButtonEnable] = useState<boolean>(true);
  const [questionProgressData, setQuestionProgressData] = useState(
    questions.map(() => ({ isActive: false }))
  );
  useEffect(() => {
    const updatedQuestionProgressData = questions.map((question) => ({
      isActive: Object.prototype.hasOwnProperty.call(
        responses,
        question.id.toString()
      ),
    }));
    const allActive = updatedQuestionProgressData.every(
      (item) => item.isActive
    );
    setNextButtonEnable(allActive);
    setQuestionProgressData(updatedQuestionProgressData);
  }, [responses]);
  const handleAnswerClick = (quesid: string, value: string | number) => {
    setResponses((prevResponses: any) => ({
      ...prevResponses,
      [quesid]: value,
    }));
  };

  if (isLoading) {
    return <Spinner spinnerTheme='overlaySpinner' />;
  }

  return (
    <div className={cx('flex flex-col h-[calc(100vh-9.6875rem)] pt-10  ')}>
      {isLoading ? (
        <Spinner customClassName='mt-4' size='sm' spinnerTheme='default' />
      ) : (
        <div className='w-full relative  bg-white'>
          <div className=' sticky top-[1rem] z-10 space-y-6 bg-white  mb-6'>
            {' '}
            {!isDealStructure ? (
              <h1 className='px-8 font-medium text-blue-500 '>
                How much do you know about the following types of buyers? Record
                your understanding of each using a scale of 1 to 6 where 1
                indicates that you know almost nothing and 6 means you totally
                understand the buyer type.
              </h1>
            ) : (
              <h1 className='px-8 font-medium '>
                Record your understanding of the following questions/concepts
                using a scale of 1 to 6 where 1 indicates that you know almost
                nothing and 6 means you totally understand the difference.
              </h1>
            )}
            <QuestionProgressBar buttonData={questionProgressData} />
          </div>
          <BuyerAssessmentComp
            ansData={[
              { label: 1, value: 1 },
              { label: 2, value: 2 },
              { label: 3, value: 3 },
              { label: 4, value: 4 },
              { label: 5, value: 5 },
              { label: 6, value: 6 },
            ]}
            questions={questions}
            responses={responses}
            onAnswerClick={handleAnswerClick}
          />
        </div>
      )}{' '}
      <div className='px-8  py-6'>
        <BackNextComponent
          backStep={() => handlebackClick(responses)}
          buttonType='submit'
          nextStep={() => handleNextClick(responses)}
          isLoading={isLoading}
          isNextDisable={!nextButtonEnable}
          onSaveToDraftClick={() => {
            handleSaveAsDraftClick(responses);
            // setOnclickError(false);
          }}
        />
      </div>
    </div>
  );
};

export default BuyerAssessmentContainer;
