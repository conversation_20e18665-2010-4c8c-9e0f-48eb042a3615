import { Form, Formik, FormikValues } from 'formik';
import React from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { useSelector } from 'react-redux';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import InputLabel from 'shared-resources/components/InputLabel/InputLabel';
import FormikRadio from 'shared-resources/components/Radio/FormikRadio';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { getFormattedDate } from 'utils/helpers/Helpers';
import * as yup from 'yup';
import {
  Screen19Keys,
  YesNoOptions,
  getQuestionBykey,
} from '../AssetProtectionConfig';

interface Props {
  data: any;
  handleNextClick: (values: FormikValues) => void;
  handlebackClick: (values: FormikValues) => void;
  handleSaveAsDraftClick: (values: FormikValues) => void;
}

const Screen19: React.FC<Props> = ({
  handleNextClick,
  handlebackClick,
  handleSaveAsDraftClick,
  data,
}) => {
  const validationSchema = yup.object().shape({
    [Screen19Keys.HARASSMENT_POLICY_VISIBLE]: yup
      .string()
      .required('Value is required'),
    [Screen19Keys.HARASSMENT_POLICY_LAST_UPDATED]: yup
      .string()
      .when([Screen19Keys.HARASSMENT_POLICY_VISIBLE], {
        is: (value: string) => value === 'yes',
        then: (schema) => schema.required('Date is required'),
        otherwise: (schema) => schema.nullable(),
      }),
  });

  const initialValue = data?.screen19 || {
    [Screen19Keys.HARASSMENT_POLICY_VISIBLE]: '',
    [Screen19Keys.HARASSMENT_POLICY_LAST_UPDATED]: '',
  };

  const isSubmitting = useSelector(getAssessmentToolLoading);

  return (
    <div className='flex flex-col h-full gap-6 pl-10 py-10 font-medium'>
      <Formik
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={(values: FormikValues) => {
          handleNextClick(values);
        }}
        enableReinitialize
      >
        {({ values, setFieldValue, errors, touched, setTouched }) => (
          <Form className='flex flex-col justify-between'>
            <div className='h-[calc(100vh-20rem)] overflow-auto scrollbar'>
              <div>
                <div className='mt-4'>
                  <span className='font-semibold'>
                    {getQuestionBykey(Screen19Keys.HARASSMENT_POLICY_VISIBLE)}
                  </span>

                  <div className='mt-5 xl:w-[50%]'>
                    <FormikRadio
                      labelClassName='!cursor-pointer'
                      valueChanged={(value) => {
                        setTouched({
                          [Screen19Keys.HARASSMENT_POLICY_VISIBLE]: false,
                        });
                        if (value === 'no') {
                          setFieldValue(
                            Screen19Keys.HARASSMENT_POLICY_LAST_UPDATED,
                            ''
                          );
                        }
                      }}
                      className='grid grid-cols-2 gap-4'
                      name={Screen19Keys.HARASSMENT_POLICY_VISIBLE}
                      options={YesNoOptions}
                    />
                  </div>
                </div>
              </div>

              {values[Screen19Keys.HARASSMENT_POLICY_VISIBLE] === 'yes' && (
                <div className='mt-10 flex flex-col'>
                  <InputLabel
                    label={getQuestionBykey(
                      Screen19Keys.HARASSMENT_POLICY_LAST_UPDATED
                    )}
                  />
                  <DatePicker
                    popperPlacement='bottom-start'
                    popperClassName='!w-[6.5%]'
                    className='text-blue-01 mt-3 w-[50%] border border-gray-02 !h-12 !rounded-xl focus:outline-none py-2.5 px-5 font-bold'
                    selected={
                      values?.[Screen19Keys.HARASSMENT_POLICY_LAST_UPDATED]
                    }
                    onChange={(date) => {
                      setFieldValue(
                        Screen19Keys.HARASSMENT_POLICY_LAST_UPDATED,
                        date && getFormattedDate(date?.toDateString())
                      );
                    }}
                  />
                  {errors[Screen19Keys.HARASSMENT_POLICY_LAST_UPDATED] &&
                    touched[Screen19Keys.HARASSMENT_POLICY_LAST_UPDATED] && (
                      <div className='text-red-500 text-sm'>{`${
                        errors[Screen19Keys.HARASSMENT_POLICY_LAST_UPDATED]
                      }`}</div>
                    )}
                </div>
              )}
            </div>
            <div className='pr-5'>
              <BackNextComponent
                backStep={() => handlebackClick(values)}
                buttonType='submit'
                isLoading={isSubmitting}
                isNextDisable={false}
                onSaveToDraftClick={() => handleSaveAsDraftClick(values)}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Screen19;
