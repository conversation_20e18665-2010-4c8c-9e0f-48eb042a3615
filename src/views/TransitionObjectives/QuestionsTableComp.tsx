import React, { FC, memo, useEffect, useRef, useState } from 'react';
import {
  BusinessGoals,
  BusinessObjectives,
  BusinessObjectivesKeys,
  ObjectiveKeys,
} from 'shared-resources/types/TransitionObjectives.type';
import { generateUniqueKey } from 'utils/helpers/Helpers';
import { ObjectivesForStakeHoldertable } from './TransitionObjectiveConfig';

type QuestionsTableCompProps = {
  headers: string[];
  objectives: ObjectiveKeys[];
  tableValues: BusinessGoals;
  setTableValues: (goals: BusinessGoals) => void;
  allowEditing?: boolean;
  isReportView?: boolean;
  initialDynamicTableValues?: Partial<
    Record<ObjectiveKeys, Record<keyof BusinessObjectives, string>>
  >;
};

const QuestionsTableComp: FC<QuestionsTableCompProps> = ({
  objectives,
  tableValues,
  setTableValues,
  headers,
  allowEditing = true,
  isReportView,
  initialDynamicTableValues,
}) => {
  const [dynamicTableValues, setDynamicTableValues] = useState<
    Partial<Record<ObjectiveKeys, Record<keyof BusinessObjectives, string>>>
  >(initialDynamicTableValues || {});

  const inputRefs = useRef<Record<string, HTMLInputElement | null>>({});
  useEffect(() => {
    if (!initialDynamicTableValues) {
      const initialValues: Partial<
        Record<ObjectiveKeys, Record<keyof BusinessObjectives, string>>
      > = {};
      objectives.forEach((objective) => {
        initialValues[objective] = {
          name: tableValues[objective]?.name || '',
          objective: '',
          withinNextYear: tableValues[objective]?.withinNextYear || '',
          withinNextTwoYear: tableValues[objective]?.withinNextTwoYear || '',
          havePlan: '',
          willInfluence: '',
          haveDiscussed: '',
          inAgreement: '',
          other: tableValues[objective]?.other || '',
          others: tableValues[objective]?.others || '',
          notes: tableValues[objective]?.notes || '',
        };
      });
      setDynamicTableValues(initialValues);
    }
  }, [objectives, initialDynamicTableValues]);
  const handleValuesChange = (
    objective: ObjectiveKeys,
    key: keyof BusinessObjectives,
    value: string | boolean
  ) => {
    if (tableValues) {
      const updatedTableValues = { ...tableValues };
      updatedTableValues[objective] = {
        ...updatedTableValues[objective],
        [key]: value,
      };
      setTableValues(updatedTableValues);
    }
  };

  const handleInputChange = (
    objective: ObjectiveKeys,
    objectiveKey: keyof BusinessObjectives,
    value: string
  ) => {
    if (allowEditing) {
      setDynamicTableValues((prevValues) => ({
        ...prevValues,
        [objective]: {
          ...prevValues[objective],
          [objectiveKey]: value,
        },
      }));

      handleValuesChange(objective, objectiveKey, value);

      const refKey = `${objective}-${objectiveKey}`;
      setTimeout(() => {
        const inputElement = inputRefs.current[refKey];
        if (inputElement) {
          inputElement.focus();
          const { length } = inputElement.value;
          inputElement.setSelectionRange(length, length);
        }
      }, 0);
    }
  };

  return (
    <div className={`${isReportView ? 'p-0 mt-4' : 'p-10'} w-full`}>
      <div className='w-full overflow-x-auto'>
        <table className='w-full table-auto'>
          <thead>
            <tr className='shadow-blue-border  rounded-t-xl border-t-0'>
              {headers?.map((header: string, index) => {
                if (index === 0) {
                  return (
                    <th
                      key={generateUniqueKey(header[4])}
                      className='bg-blue-01 !py-4 px-2  rounded-ss-xl border-r-2 border-gray-500 text-white'
                    >
                      {header}
                    </th>
                  );
                }
                if (index === headers.length - 1) {
                  return (
                    <th
                      key={generateUniqueKey(header[4])}
                      className='bg-blue-01 !px-2 !!py-4 rounded-se-xl  border-gray-500 text-white'
                    >
                      {header}
                    </th>
                  );
                }
                return (
                  <th
                    key={generateUniqueKey(header[4])}
                    className='bg-blue-01 !px-2 !!py-4 border-r-2  border-gray-500 text-white'
                  >
                    {header}
                  </th>
                );
              })}
            </tr>
          </thead>
          <tbody>
            {objectives.map((objective, index) => (
              <tr key={generateUniqueKey(objective[0])}>
                <td
                  className={`!py-4 !pl-2 justify-start
                 border-2 border-gray-500 h-full ${
                   isReportView && index === 0 ? 'border-t-2' : 'border-t-0'
                 } `}
                >
                  <h3
                    className={`font-montserrat ${
                      isReportView ? '!text-sm px-2 ' : 'text-base'
                    }`}
                  >
                    {objective === 'Others' ? 'Other' : objective}{' '}
                    {objectives !== ObjectivesForStakeHoldertable && (
                      <span className='text-red-600'>*</span>
                    )}
                  </h3>
                </td>
                {}
                {tableValues?.[objective] &&
                  tableValues?.[objective]?.name !== undefined && (
                    <td className='border-2 border-gray-500 border-l-0'>
                      <div className='flex'>
                        {isReportView ? (
                          <div className='mx-auto focus:outline-none overflow-visible break-words'>
                            {dynamicTableValues[objective]?.name}
                          </div>
                        ) : (
                          <input
                            disabled={!allowEditing}
                            id={`${objective}-${BusinessObjectivesKeys.Name}`}
                            ref={(el) => {
                              inputRefs.current[
                                `${objective}-${BusinessObjectivesKeys.Name}`
                              ] = el;
                            }}
                            aria-label={`${objective}-${BusinessObjectivesKeys.Name}`}
                            value={dynamicTableValues[objective]?.name}
                            type='text'
                            className={`${
                              isReportView && 'overflow-visible break-words'
                            }  mx-auto focus:outline-none`}
                            onChange={(e) => {
                              handleInputChange(
                                objective,
                                BusinessObjectivesKeys.Name,
                                e.target.value
                              );
                            }}
                          />
                        )}
                      </div>
                    </td>
                  )}

                {tableValues?.[objective] &&
                  tableValues[objective]?.objective !== undefined && (
                    <td
                      className={`border-2 border-gray-500 ${
                        isReportView ? 'py-4' : 'py-1'
                      } px-2 border-l-0 `}
                    >
                      <div className='flex justify-center '>
                        <button
                          className={`${
                            isReportView ? '!px-3 !text-sm' : 'px-5'
                          }  py-1 rounded-l-xl border-l-2 border-y-2 border-gray-300 ${
                            tableValues[objective]?.objective === true
                              ? 'bg-blue-01'
                              : ''
                          }`}
                          onClick={() => {
                            handleValuesChange(
                              objective,
                              BusinessObjectivesKeys.Objective,
                              true
                            );
                          }}
                        >
                          Yes
                        </button>
                        <button
                          className={`${
                            isReportView ? '!px-3 text-sm' : 'px-5'
                          } py-1 rounded-r-xl border-2 border-gray-300 ${
                            tableValues[objective]?.objective === false
                              ? 'bg-blue-01'
                              : ''
                          }`}
                          onClick={() => {
                            handleValuesChange(
                              objective,
                              BusinessObjectivesKeys.Objective,
                              false
                            );
                          }}
                        >
                          No
                        </button>
                      </div>
                    </td>
                  )}
                {tableValues?.[objective] &&
                  tableValues[objective]?.withinNextYear !== undefined && (
                    <td className='border-2 border-gray-500 border-l-0'>
                      <div className='flex'>
                        {isReportView ? (
                          <div className='mx-auto focus:outline-none !text-sm px-2 overflow-auto'>
                            {dynamicTableValues[objective]?.withinNextYear ||
                              ''}
                          </div>
                        ) : (
                          <input
                            disabled={!allowEditing}
                            id={`${objective}-${BusinessObjectivesKeys.WithinNextYear}`}
                            aria-label={`${objective}-${BusinessObjectivesKeys.WithinNextYear}`}
                            value={
                              dynamicTableValues[objective]?.withinNextYear
                            }
                            ref={(el) => {
                              inputRefs.current[
                                `${objective}-${BusinessObjectivesKeys.WithinNextYear}`
                              ] = el;
                            }}
                            type='text'
                            className={`${
                              isReportView ? '!text-sm' : '!text-base'
                            } mx-auto focus:outline-none`}
                            onChange={(e) => {
                              handleInputChange(
                                objective,
                                BusinessObjectivesKeys.WithinNextYear,
                                e.target.value
                              );
                            }}
                          />
                        )}
                      </div>
                    </td>
                  )}
                {tableValues?.[objective] &&
                  tableValues[objective]?.withinNextTwoYear !== undefined && (
                    <td className='border-2 border-gray-500 border-l-0'>
                      <div className='flex'>
                        {isReportView ? (
                          <div className='mx-auto focus:outline-none !text-sm px-2 overflow-auto'>
                            {dynamicTableValues[objective]?.withinNextTwoYear ||
                              ''}
                          </div>
                        ) : (
                          <input
                            disabled={!allowEditing}
                            aria-label={`${objective}-${BusinessObjectivesKeys.WithinNextTwoYear}`}
                            id={`${objective}-${BusinessObjectivesKeys.WithinNextTwoYear}`}
                            value={
                              dynamicTableValues[objective]?.withinNextTwoYear
                            }
                            ref={(el) => {
                              inputRefs.current[
                                `${objective}-${BusinessObjectivesKeys.WithinNextTwoYear}`
                              ] = el;
                            }}
                            type='text'
                            className={`${
                              isReportView ? 'text-sm' : 'text-base'
                            } mx-auto focus:outline-none`}
                            onChange={(e) => {
                              handleInputChange(
                                objective,
                                BusinessObjectivesKeys.WithinNextTwoYear,
                                e.target.value
                              );
                            }}
                          />
                        )}
                      </div>
                    </td>
                  )}
                {tableValues?.[objective] &&
                  tableValues[objective]?.havePlan !== undefined && (
                    <td className='border-2 border-gray-500 !px-2 border-l-0 '>
                      <div className='flex justify-center '>
                        <button
                          className={`${
                            isReportView ? '!px-3 !text-sm' : 'px-5'
                          } py-1 rounded-l-xl border-l-2 border-y-2 border-gray-300 ${
                            tableValues[objective]?.havePlan === true
                              ? 'bg-blue-01'
                              : ''
                          }`}
                          onClick={() => {
                            handleValuesChange(
                              objective,
                              BusinessObjectivesKeys.HavePlan,
                              true
                            );
                          }}
                        >
                          Yes
                        </button>
                        <button
                          className={`${
                            isReportView ? '!px-3 text-sm' : 'px-5'
                          } py-1 rounded-r-xl border-2 border-gray-300 ${
                            tableValues[objective]?.havePlan === false
                              ? 'bg-blue-01'
                              : ''
                          }`}
                          onClick={() => {
                            handleValuesChange(
                              objective,
                              BusinessObjectivesKeys.HavePlan,
                              false
                            );
                          }}
                        >
                          No
                        </button>
                      </div>
                    </td>
                  )}
                {tableValues?.[objective] &&
                  tableValues[objective]?.other !== undefined && (
                    <td className='border-2 border-gray-500 border-l-0'>
                      <div className='flex'>
                        {isReportView ? (
                          <div className='mx-auto focus:outline-none !text-sm px-2 overflow-auto'>
                            {dynamicTableValues[objective]?.other || ''}
                          </div>
                        ) : (
                          <input
                            disabled={!allowEditing}
                            id={`${objective}-${BusinessObjectivesKeys.Other}`}
                            aria-label={`${objective}-${BusinessObjectivesKeys.Other}`}
                            value={dynamicTableValues[objective]?.other}
                            ref={(el) => {
                              inputRefs.current[
                                `${objective}-${BusinessObjectivesKeys.Other}`
                              ] = el;
                            }}
                            type='text'
                            className={`${
                              isReportView ? '!text-sm' : '!text-base'
                            } mx-auto focus:outline-none`}
                            onChange={(e) => {
                              handleInputChange(
                                objective,
                                BusinessObjectivesKeys.Other,
                                e.target.value
                              );
                            }}
                          />
                        )}
                      </div>
                    </td>
                  )}
                {tableValues?.[objective] &&
                  tableValues[objective]?.willInfluence !== undefined && (
                    <td className='border-2 border-gray-500 border-l-0 '>
                      <div className='flex justify-center '>
                        <button
                          className={`${
                            isReportView ? '!px-3 !text-sm' : 'px-5'
                          } py-1 rounded-l-xl border-l-2 border-y-2 border-gray-300 ${
                            tableValues[objective]?.willInfluence === true
                              ? 'bg-blue-01'
                              : ''
                          }`}
                          onClick={() => {
                            handleValuesChange(
                              objective,
                              BusinessObjectivesKeys.WillInfluenece,
                              true
                            );
                          }}
                        >
                          Yes
                        </button>
                        <button
                          className={`${
                            isReportView ? '!px-3 !text-sm' : 'px-5'
                          } py-1 rounded-r-xl border-2 border-gray-300 ${
                            tableValues[objective]?.willInfluence === false
                              ? 'bg-blue-01'
                              : ''
                          }`}
                          onClick={() => {
                            handleValuesChange(
                              objective,
                              BusinessObjectivesKeys.WillInfluenece,
                              false
                            );
                          }}
                        >
                          No
                        </button>
                      </div>
                    </td>
                  )}
                {tableValues?.[objective] &&
                  tableValues[objective]?.haveDiscussed !== undefined && (
                    <td className='border-2 border-gray-500 border-l-0 '>
                      <div className='flex justify-center '>
                        <button
                          className={`${
                            isReportView ? '!px-3 !text-sm' : 'px-5'
                          } py-1 rounded-l-xl border-l-2 border-y-2 border-gray-300 ${
                            tableValues[objective]?.haveDiscussed === true
                              ? 'bg-blue-01'
                              : ''
                          }`}
                          onClick={() => {
                            handleValuesChange(
                              objective,
                              BusinessObjectivesKeys.HaveDiscussed,
                              true
                            );
                          }}
                        >
                          Yes
                        </button>
                        <button
                          className={`${
                            isReportView ? '!px-3 !text-sm' : 'px-5'
                          } py-1 rounded-r-xl border-2 border-gray-300 ${
                            tableValues[objective]?.haveDiscussed === false
                              ? 'bg-blue-01'
                              : ''
                          }`}
                          onClick={() => {
                            handleValuesChange(
                              objective,
                              BusinessObjectivesKeys.HaveDiscussed,
                              false
                            );
                          }}
                        >
                          No
                        </button>
                      </div>
                    </td>
                  )}

                {tableValues?.[objective] &&
                  tableValues[objective]?.inAgreement !== undefined && (
                    <td className='border-2 border-gray-500 border-l-0 '>
                      <div className='flex justify-center '>
                        <button
                          className={`${
                            isReportView ? '!px-3 !text-sm' : 'px-5'
                          } py-1 rounded-l-xl border-l-2 border-y-2 border-gray-300 ${
                            tableValues[objective]?.inAgreement === true
                              ? 'bg-blue-01'
                              : ''
                          }`}
                          onClick={() => {
                            handleValuesChange(
                              objective,
                              BusinessObjectivesKeys.InAgreement,
                              true
                            );
                          }}
                        >
                          Yes
                        </button>
                        <button
                          className={`${
                            isReportView ? '!px-3 !text-sm' : 'px-5'
                          } py-1 rounded-r-xl border-2 border-gray-300 ${
                            tableValues[objective]?.inAgreement === false
                              ? 'bg-blue-01'
                              : ''
                          }`}
                          onClick={() => {
                            handleValuesChange(
                              objective,
                              BusinessObjectivesKeys.InAgreement,
                              false
                            );
                          }}
                        >
                          No
                        </button>
                      </div>
                    </td>
                  )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
export default memo(QuestionsTableComp);
