import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Modal from 'shared-resources/components/Modal/Modal';
import Button from 'shared-resources/components/Button/Button';
import {
  createComment,
  updateComment,
} from 'store/actions/tool-comment.action';
import { getToolCommentsLoading } from 'store/selectors/tool-comment.selector';
import { AssessmentTools } from 'types/enum';
import { useLocation, useParams } from 'react-router';
import { useSearchParams } from 'react-router-dom';
import { getCurrentTab } from 'utils/helpers/Helpers';

interface AddCommentPopupProps {
  isOpen: boolean;
  onClose: () => void;
  isEdit?: boolean;
  initialComment?: string;
  commentId?: number;
}

const AddCommentPopup: React.FC<AddCommentPopupProps> = ({
  isOpen,
  onClose,
  isEdit = false,
  initialComment = '',
  commentId,
}) => {
  const dispatch = useDispatch();
  const { id } = useParams();
  const [comment, setComment] = useState('');
  const isLoading = useSelector(getToolCommentsLoading);
  const { pathname } = useLocation();

  useEffect(() => {
    if (isEdit && initialComment) {
      setComment(initialComment);
    }
  }, [isEdit, initialComment]);
  const pathSegments = pathname.split('/');
  const extractToolFromPath = () => {
    const toolSegment = pathSegments.find((segment) =>
      Object.values(AssessmentTools).includes(segment as AssessmentTools)
    );
    if (pathSegments.includes('expense-calculator')) {
      return AssessmentTools.FINANCIAL_GAP_ANALYSIS;
    }
    return toolSegment ? (toolSegment as AssessmentTools) : undefined;
  };

  const extractedTool = extractToolFromPath();

  const [searchParams] = useSearchParams();

  const tab = getCurrentTab(
    extractedTool as AssessmentTools,
    searchParams,
    pathSegments
  );

  const handleSave = () => {
    if (isEdit && commentId) {
      dispatch(updateComment(commentId, comment));
    } else {
      dispatch(
        createComment(Number(id), extractedTool as AssessmentTools, comment, {
          screen: tab,
        })
      );
    }
    setComment('');
    onClose();
  };

  return (
    <Modal
      visible={isOpen}
      handleVisibility={onClose}
      title={isEdit ? 'Edit Comment' : 'Add Comment'}
      classname='w-[600px]'
    >
      <div className='p-6'>
        <textarea
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          placeholder='Enter your comment here...'
          className='w-full min-h-[120px] p-2 mb-4 border border-blue-01 focus:border-2 outline-none rounded-lg resize-vertical'
        />
        <div className='flex justify-end gap-4'>
          <Button
            onClick={handleSave}
            disabled={!comment.trim() || isLoading}
            isSubmitting={isLoading}
            className='px-6 py-2'
          >
            {isEdit ? 'Update' : 'Save'}
          </Button>
          <Button
            onClick={onClose}
            theme='secondary'
            disabled={isLoading}
            className='px-6 py-2'
          >
            Cancel
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default AddCommentPopup;
