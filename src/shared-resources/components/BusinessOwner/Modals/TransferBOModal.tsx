import { Form, Formik } from 'formik';
import React, { useEffect } from 'react';
import { IoWarning } from 'react-icons/io5';
import { useDispatch, useSelector } from 'react-redux';
import * as yup from 'yup';
import Button from 'shared-resources/components/Button/Button';
import { FormikSelect } from 'shared-resources/components/Select/FormikSelect';
import { fetchAdvisorsList } from 'store/actions/advisor.action';
import { allAdvisors } from 'store/selectors/advisor.selector';

interface TransferBOModalProps {
  onCancelClick: () => void;
  onConfirmClick: (advisor: string) => void;
  loading: boolean | undefined;
}
const TransferBOModal: React.FC<TransferBOModalProps> = ({
  onCancelClick,
  onConfirmClick,
  loading,
}) => {
    const dispatch = useDispatch();
  const advisors = useSelector(allAdvisors);

   const validationSchema = yup.object().shape({
    Advisor: yup.string().required('Advisor is required'),
  });

  const advisorOptions = advisors.map((advisor) => ({
    label: `${advisor.email}`,
    value: String(advisor.id),
  }));

  useEffect(() => {
    dispatch(
      fetchAdvisorsList({
        filters: {},
      })
    ); // Fetch advisors on mount
  }, [dispatch]);

  return (
    <div className='flex flex-col p-8'>
      <div className='flex items-start pb-5'>
        <IoWarning className='min-w-7 min-h-7' color='red' />
        <div className='flex flex-col ml-4'>
          <span className='font-medium'>
            Please select the Advisor to transfer this Business Owner?
          </span>
        </div>
      </div>

      <Formik
        initialValues={{ Advisor: '' }}
        validationSchema={validationSchema}
        validateOnBlur
        onSubmit={(values) => {
          onConfirmClick(values.Advisor);
        }}
      >
        {() => (
          <Form>
            <FormikSelect
              asterisk
              name='Advisor'
              placeholder='Select Advisor'
              options={advisorOptions}
              classname='w-full'
              menuClassname='max-h-72 overflow-auto scrollbar border-r-0'
              outerClassName='!h-[3.125rem]'
              labelClassName='!text-lg'
              label='Advisor'
              errorClassname='mt-0'
              isSearch={true}
            />

            <div className='flex justify-end gap-6 mt-4'>
              <Button
                className='px-6 py-2'
                type='submit'
                isSubmitting={loading}
              >
                Proceed
              </Button>
              <Button
                className='px-6 py-2'
                theme='secondary'
                type='button'
                onClick={onCancelClick}
              >
                Cancel
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default TransferBOModal;
