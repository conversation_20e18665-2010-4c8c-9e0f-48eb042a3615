import React from 'react';
import Button from 'shared-resources/components/Button/Button';

interface GetStartedScreenProps {
  onNextStep: () => void;
}
const GetStartedScreen: React.FC<GetStartedScreenProps> = (props) => {
  const { onNextStep } = props;
  return (
    <div className='flex flex-col  rounded-xl h-full gap-2'>
      <div className='flex flex-col gap-7 justify-center items-center px-[10%] bg-white w-full h-full '>
        <p className='text-lg font-medium text-center'>
          The Business Continuity Instructions are intended to include that most
          important items to be addressed in the event of the Business
          Owner&apos;s incapaciation. Each Business Owner&apos;s situation is
          different, so this document is not intended nor warranted to include
          every possible situation that needs to be addressed.
        </p>
        <Button onClick={onNextStep} className='px-4 py-2'>
          Get Started
        </Button>
      </div>
    </div>
  );
};

export default GetStartedScreen;
