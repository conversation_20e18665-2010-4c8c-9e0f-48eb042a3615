import { Form, Formik, FormikValues } from 'formik';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import React from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { useSelector } from 'react-redux';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import InputLabel from 'shared-resources/components/InputLabel/InputLabel';
import FormikRadio from 'shared-resources/components/Radio/FormikRadio';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import {
  getAssessmentToolLoading,
  getBusinessOwnerProperties,
} from 'store/selectors/assessment-tool.selector';
import { getBusinessOwnerDetailsLoading } from 'store/selectors/business-owner.selector';
import { getFormattedDate } from 'utils/helpers/Helpers';
import * as yup from 'yup';
import {
  Screen2Keys,
  YesNoOptions,
  getQuestionBykey,
  screen2HaveBusinessCountinuityKeyOptions,
} from '../AssetProtectionConfig';

interface Props {
  data: any;
  handleNextClick: (values: FormikValues) => void;
  handlebackClick: (values: FormikValues) => void;
  handleSaveAsDraftClick: (values: FormikValues) => void;
}

const Screen2: React.FC<Props> = ({
  handleNextClick,
  handlebackClick,
  handleSaveAsDraftClick,
  data,
}) => {
  const businessOwnerLoading = useSelector(getBusinessOwnerDetailsLoading);
  const businessOwnerProperties = useSelector(
    getBusinessOwnerProperties
  ) as BusinessOwner;

  const requiredbasedOnProgress = (isForCompleted: boolean, message: string) =>
    yup
      .string()
      .nullable()
      .test(
        'required-when-progress-completed',
        message || 'Value is required',
        function validateValueBasedOnProgress(value) {
          const { path, createError } = this;
          if (!isForCompleted && (!value || value.trim() === '')) {
            if (
              !businessOwnerProperties?.metadata?.business_continuity_updated_at
            ) {
              return createError({
                path,
                message: message || 'Value is required',
              });
            }
          }
          return true;
        }
      );

  const isSubmitting = useSelector(getAssessmentToolLoading);

  const validationSchema = yup.object().shape({
    [Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_IN_EXIT_SMARTS]:
      requiredbasedOnProgress(true, 'Value is required'),
    [Screen2Keys.HAVE_BUSINESS_CONTINUITY_INSTRUCTIONS]:
      requiredbasedOnProgress(false, 'Value is required'),
    [Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_UPDATED_ON]: yup
      .string()
      .nullable()
      .when([Screen2Keys.HAVE_BUSINESS_CONTINUITY_INSTRUCTIONS], {
        is: (value: string) =>
          value === screen2HaveBusinessCountinuityKeyOptions[0] ||
          value === screen2HaveBusinessCountinuityKeyOptions[1],
        then: (schema) => schema.required('Value is required'),
      }),
    [Screen2Keys.HAVE_TOLD_APPROPRIATE_PEOPLE]: yup
      .string()
      .nullable()
      .when([Screen2Keys.HAVE_BUSINESS_CONTINUITY_INSTRUCTIONS], {
        is: (value: string) =>
          value === screen2HaveBusinessCountinuityKeyOptions[0] ||
          value === screen2HaveBusinessCountinuityKeyOptions[1],
        then: (schema) => schema.required('Value is required'),
      }),
  });

  const getContinuityUpdatedOn = (value?: string) => {
    if (value) {
      return new Date(value);
    }

    return businessOwnerProperties?.metadata?.business_continuity_updated_at
      ? new Date(
          businessOwnerProperties?.metadata?.business_continuity_updated_at
        )
      : null;
  };

  const initialValue = {
    [Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_IN_EXIT_SMARTS]:
      data?.screen2?.[
        Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_IN_EXIT_SMARTS
      ] ||
      getContinuityUpdatedOn() ||
      '',
    [Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_UPDATED_ON]:
      data?.screen2?.[
        Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_UPDATED_ON
      ] || null,
    [Screen2Keys.HAVE_BUSINESS_CONTINUITY_INSTRUCTIONS]:
      data?.screen2?.[Screen2Keys.HAVE_BUSINESS_CONTINUITY_INSTRUCTIONS] || '',
    [Screen2Keys.HAVE_TOLD_APPROPRIATE_PEOPLE]:
      data?.screen2?.[Screen2Keys.HAVE_TOLD_APPROPRIATE_PEOPLE] || '',
  };

  if (businessOwnerLoading) return <Spinner customClassName='mt-10' />;

  return (
    <div className='flex flex-col h-full gap-6 pl-10 py-10 font-medium'>
      <Formik
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={(values: FormikValues) => {
          handleNextClick(values);
        }}
        enableReinitialize
      >
        {({ values, setFieldValue, errors, touched, handleSubmit }) => (
          <Form
            className='flex flex-col justify-between'
            onSubmit={handleSubmit}
          >
            <div className='h-[calc(100vh-20rem)] overflow-auto scrollbar'>
              {businessOwnerProperties?.metadata
                ?.business_continuity_updated_at ? (
                <div className='flex flex-col mt-4'>
                  <InputLabel
                    className='font-semibold'
                    label={getQuestionBykey(
                      Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_IN_EXIT_SMARTS
                    )}
                  />
                  <DatePicker
                    popperPlacement='bottom-start'
                    popperClassName='!w-[10%]'
                    className={`text-blue-01 mt-3 w-3/5 border border-gray-02 h-12 rounded-xl focus:outline-none py-2.5 px-5 font-bold `}
                    selected={getContinuityUpdatedOn(
                      values[
                        Screen2Keys
                          .BUSINESS_CONTINUITY_INSTRUCTIONS_IN_EXIT_SMARTS
                      ]
                    )}
                    onChange={(date) => {
                      setFieldValue(
                        Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_IN_EXIT_SMARTS,
                        date && getFormattedDate(date?.toDateString())
                      );
                    }}
                  />
                  {errors[
                    Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_IN_EXIT_SMARTS
                  ] &&
                    touched[
                      Screen2Keys
                        .BUSINESS_CONTINUITY_INSTRUCTIONS_IN_EXIT_SMARTS
                    ] && (
                      <div className='text-red-500 text-sm'>{`${
                        errors[
                          Screen2Keys
                            .BUSINESS_CONTINUITY_INSTRUCTIONS_IN_EXIT_SMARTS
                        ]
                      }`}</div>
                    )}
                </div>
              ) : (
                <div>
                  <div className='mt-4'>
                    <span className='font-semibold'>
                      {getQuestionBykey(
                        Screen2Keys.HAVE_BUSINESS_CONTINUITY_INSTRUCTIONS
                      )}
                    </span>

                    <div className='mt-5 xl:w-[80%]'>
                      <FormikRadio
                        labelClassName='!cursor-pointer'
                        valueChanged={(value) => {
                          if (
                            value ===
                              screen2HaveBusinessCountinuityKeyOptions[2] ||
                            value ===
                              screen2HaveBusinessCountinuityKeyOptions[3]
                          ) {
                            setFieldValue(
                              Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_UPDATED_ON,
                              ''
                            );
                            setFieldValue(
                              Screen2Keys.HAVE_TOLD_APPROPRIATE_PEOPLE,
                              ''
                            );
                          }
                        }}
                        className='grid grid-cols-2 gap-4'
                        name={Screen2Keys.HAVE_BUSINESS_CONTINUITY_INSTRUCTIONS}
                        options={screen2HaveBusinessCountinuityKeyOptions.map(
                          (option) => ({
                            value: option,
                            label: option,
                          })
                        )}
                      />
                    </div>
                  </div>
                </div>
              )}
              {(values[Screen2Keys.HAVE_BUSINESS_CONTINUITY_INSTRUCTIONS] ===
                screen2HaveBusinessCountinuityKeyOptions[0] ||
                values[Screen2Keys.HAVE_BUSINESS_CONTINUITY_INSTRUCTIONS] ===
                  screen2HaveBusinessCountinuityKeyOptions[1]) && (
                <div className='mt-10 flex flex-col gap-6'>
                  <InputLabel
                    className='!font-semibold'
                    label={getQuestionBykey(
                      Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_UPDATED_ON
                    )}
                  />

                  <DatePicker
                    popperPlacement='bottom-start'
                    popperClassName='!w-[10%]'
                    className={`text-blue-01 w-3/5 border border-gray-02 h-12 rounded-xl focus:outline-none py-2.5 px-5 font-bold `}
                    selected={getContinuityUpdatedOn(
                      values[
                        Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_UPDATED_ON
                      ]
                    )}
                    onChange={(date) => {
                      setFieldValue(
                        Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_UPDATED_ON,
                        date && getFormattedDate(date?.toDateString())
                      );
                    }}
                  />
                  {errors[
                    Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_UPDATED_ON
                  ] &&
                    touched[
                      Screen2Keys.BUSINESS_CONTINUITY_INSTRUCTIONS_UPDATED_ON
                    ] && (
                      <div className='text-red-500 text-sm'>{`${
                        errors[
                          Screen2Keys
                            .BUSINESS_CONTINUITY_INSTRUCTIONS_UPDATED_ON
                        ]
                      }`}</div>
                    )}

                  <div className='mt-4'>
                    <span className='font-semibold'>
                      {getQuestionBykey(
                        Screen2Keys.HAVE_TOLD_APPROPRIATE_PEOPLE
                      )}
                    </span>
                    <div className='mt-5 xl:w-[50%]'>
                      <FormikRadio
                        labelClassName='!cursor-pointer'
                        className2='!w-fit'
                        className='grid grid-cols-2 gap-4'
                        name={Screen2Keys.HAVE_TOLD_APPROPRIATE_PEOPLE}
                        options={YesNoOptions}
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className='pr-7'>
              <BackNextComponent
                backStep={() => handlebackClick(values)}
                buttonType='submit'
                isLoading={isSubmitting}
                isNextDisable={false}
                onSaveToDraftClick={() => handleSaveAsDraftClick(values)}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Screen2;
