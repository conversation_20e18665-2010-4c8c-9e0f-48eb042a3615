import axios from 'axios';
import { PresignedUrlPayload } from 'dtos/assessment-tools.dto';
import { AssessmentTools } from 'types/enum';
import { baseApiService } from './BaseApiService';

export class FileService {
  static getInstance(): FileService {
    return new FileService();
  }

  private async uploadFileToS3(file: File, presignedUrl: string) {
    return axios.put(presignedUrl, file);
  }

  private async getPresignedUrlByOwner(payload: PresignedUrlPayload) {
    return baseApiService.post('/business-owner/get-presigned-url/', payload);
  }

  private async getPresignedUrlByAdvisor(
    ownerId: number,
    payload: PresignedUrlPayload
  ) {
    return baseApiService.post(
      `/business-owner/${ownerId}/get-presigned-url/`,
      payload
    );
  }

  static async uploadFile(
    file: File,
    payload: PresignedUrlPayload,
    ownerId?: number
  ) {
    let presignedUrl: any = {};
    if (ownerId) {
      presignedUrl = await FileService.getInstance().getPresignedUrlByAdvisor(
        ownerId,
        payload
      );
    } else {
      presignedUrl = await FileService.getInstance().getPresignedUrlByOwner(
        payload
      );
    }

    if (presignedUrl.url.length > 0) {
      await FileService.getInstance().uploadFileToS3(file, presignedUrl.url);
      return {
        url: presignedUrl.url.split('?')[0],
      };
    }
    throw new Error('Something Went Wrong');
  }

  static async getDocumentUrl(
    presignedUrl: { url: string },
    tool: AssessmentTools,
    ownerId?: number
  ) {
    const splittedUrl = presignedUrl?.url?.split('?')?.[0];
    if (ownerId) {
      const url = await baseApiService.post(
        `/business-owner/${ownerId}/get-document-url/business_continuity`,
        { url: splittedUrl }
      );
      return url;
    }
    const url = await baseApiService.post(
      `/business-owner/get-document-url/${tool}`,
      {
        url: splittedUrl,
      }
    );
    return url;
  }
}
