import { Form, Formik } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { BusinessContinuityScreens } from 'types/enum';
import { isRequiredFieldDependingOnValidationSchema } from 'utils/helpers/Helpers';
import * as yup from 'yup';
import BackNextComponent from 'shared-resources/components/BackNextComponent';

interface MiscellaneousProps {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  onSaveToDraftClick: (data: any) => void;
  data: any;
}
const Miscellaneous: React.FC<MiscellaneousProps> = (props) => {
  const { nextStep, backStep, setData, onSaveToDraftClick, data } = props;

  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup
    .object()
    .shape({
      [BusinessContinuityScreens.MISCELLANEOUS]: yup.object({
        user_listing: yup.string().required('This field is required'),
        properties_automobiles: yup.string().required('This field is required'),
        safes_combination: yup.string().required('This field is required'),
        other_messages: yup.string().required('This field is required'),
      }),
    })
    .required();

  const initialValues = {
    [BusinessContinuityScreens.MISCELLANEOUS]:
      data?.[BusinessContinuityScreens.MISCELLANEOUS] || {},
  };

  return (
    <div className='flex flex-col h-full'>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          setData(values?.[BusinessContinuityScreens.MISCELLANEOUS]);
          nextStep();
        }}
        enableReinitialize
        validateOnMount
      >
        {({ values, isValid }) => (
          <Form className='relative p-5 h-full flex flex-col gap-4 justify-between bg-white'>
            <div className=' flex flex-col max-h-[calc(100vh-20rem)] pr-1 scrollbar overflow-auto'>
              <div className='flex flex-col gap-4'>
                <FormikInput
                  name={`${BusinessContinuityScreens.MISCELLANEOUS}.user_listing`}
                  key={`${BusinessContinuityScreens.MISCELLANEOUS}.user_listing`}
                  label='Q.1. Where do you keep your listing of user names and passwords?'
                  labelClassName='mb-1 text-lg font-medium'
                  asterisk={isRequiredFieldDependingOnValidationSchema(
                    validationSchema,
                    `${BusinessContinuityScreens.MISCELLANEOUS}.user_listing`
                  )}
                />
                <FormikInput
                  name={`${BusinessContinuityScreens.MISCELLANEOUS}.properties_automobiles`}
                  key={`${BusinessContinuityScreens.MISCELLANEOUS}.properties_automobiles`}
                  label='Q.2. Where do you keep keys to property/automobile(s)?'
                  labelClassName='mb-1 text-lg font-medium'
                  asterisk={isRequiredFieldDependingOnValidationSchema(
                    validationSchema,
                    `${BusinessContinuityScreens.MISCELLANEOUS}.properties_automobiles`
                  )}
                />
                <FormikInput
                  name={`${BusinessContinuityScreens.MISCELLANEOUS}.safes_combination`}
                  key={`${BusinessContinuityScreens.MISCELLANEOUS}.safes_combination`}
                  label='Q.3. If you have any safes, where are they and what are the combinations?'
                  labelClassName='mb-1 text-lg font-medium'
                  asterisk={isRequiredFieldDependingOnValidationSchema(
                    validationSchema,
                    `${BusinessContinuityScreens.MISCELLANEOUS}.safes_combination`
                  )}
                />
                <FormikInput
                  name={`${BusinessContinuityScreens.MISCELLANEOUS}.other_messages`}
                  key={`${BusinessContinuityScreens.MISCELLANEOUS}.other_messages`}
                  label='Q.4. Do you have other messages that will be useful in the event of your incapacitation?'
                  labelClassName='mb-1 text-lg font-medium'
                  asterisk={isRequiredFieldDependingOnValidationSchema(
                    validationSchema,
                    `${BusinessContinuityScreens.MISCELLANEOUS}.other_messages`
                  )}
                  fieldType='textarea'
                  className='h-28'
                />
              </div>
            </div>
            <BackNextComponent
              backStep={() => {
                backStep();
                setData(values?.[BusinessContinuityScreens.MISCELLANEOUS]);
              }}
              fieldKey={BusinessContinuityScreens.MISCELLANEOUS}
              formValues={values}
              isLoading={isLoading}
              isNextDisable={!isValid}
              onSaveToDraftClick={onSaveToDraftClick}
              buttonType='submit'
            />
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Miscellaneous;
