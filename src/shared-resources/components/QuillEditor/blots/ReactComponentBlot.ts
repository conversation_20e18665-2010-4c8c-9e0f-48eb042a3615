import Quill from 'quill';

const BlockEmbed = Quill.import('blots/block/embed') as any;

class ReactComponentBlot extends BlockEmbed {
  static create(value: any) {
    const node = super.create();
    node.innerHTML = value.content;
    node.setAttribute('data-type', value.type);
    return node;
  }

  static value(node: any) {
    return {
      content: node.innerHTML,
      type: node.getAttribute('data-type'),
    };
  }
}

export default ReactComponentBlot;
