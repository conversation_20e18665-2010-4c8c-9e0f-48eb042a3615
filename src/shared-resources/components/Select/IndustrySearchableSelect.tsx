import React, { useCallback, useEffect, useState } from 'react';
import { connect, useDispatch } from 'react-redux';
import cx from 'classnames';
import _ from 'lodash';
import { fetchBusinessOwnerIndustries } from 'store/actions/business-owner.action';
import { clearBusinessOwnerIndustries } from 'store/reducers/business-owner.reducer';
import {
  getBusinessOwnerIndustries,
  getBusinessOwnerIndustriesLoading,
} from 'store/selectors/business-owner.selector';
import { AppState } from 'store';
import Select, { SelectOption, SelectOptionValue } from './Select';

interface Props {
  industryOptions?: SelectOption[];
  industriesFetch: any;
  disabled?: boolean;
  selectedIndustry?: SelectOptionValue;
  onChange?: any;
  className?: string;
  placeHolder?: string;
  asterisk?: boolean;
  label?: string;
  labelClasses?: string;
  rounded?: 'xl' | 'md' | '4xl' | '2xl' | undefined;
  touched?: boolean;
  errors?: string;
  industryOptionsLoading?: boolean;
}

const IndustrySearchableSelect: React.FC<Props> = (props: Props) => {
  const {
    industryOptions,
    industriesFetch,
    disabled,
    selectedIndustry,
    onChange,
    className,
    placeHolder,
    asterisk,
    label,
    labelClasses,
    rounded,
    touched,
    errors,
    industryOptionsLoading,
  } = props;
  const [searchValue, setSearchValue] = useState('');
  const [preLoadedOptions, setPreLoadedOptions] = useState<any>();
  const dispatch = useDispatch();
  const industriesFetching = (industryValue: any) => {
    if (industryValue) {
      industriesFetch({ search: industryValue });
    }
    if (!selectedIndustry && !industryValue) {
      dispatch(clearBusinessOwnerIndustries());
    }
  };
  const delayedQuery = useCallback(_.debounce(industriesFetching, 500), [
    selectedIndustry,
  ]);

  useEffect(() => {
    delayedQuery(searchValue);
  }, [searchValue, selectedIndustry]);

  useEffect(() => {
    if (industryOptions?.length) {
      setPreLoadedOptions(industryOptions);
    } else {
      setPreLoadedOptions([]);
    }
  }, [industryOptions]);

  const isMenuOpen =
    !!industryOptions?.length && !!searchValue && !industryOptionsLoading;

  return (
    <Select
      rounded={rounded}
      touched={touched}
      errors={errors}
      asterisk={asterisk || false}
      placeholder={placeHolder || 'Select Assignee'}
      label={label || ''}
      labelClasses={labelClasses}
      disabled={disabled}
      optionTextAlignment='left'
      options={preLoadedOptions || []}
      selectedOption={selectedIndustry}
      onChange={(option: SelectOptionValue) => {
        onChange(option);
      }}
      onInputChange={(v: string) => {
        setSearchValue(v);
      }}
      isClearable
      className={cx(className)}
      disableInternalSearch
      menuIsOpen={isMenuOpen}
      showDropdownIcon={isMenuOpen}
      isLoading={industryOptionsLoading}
    />
  );
};

const mapStateToProps = (state: AppState) => ({
  industryOptions: getBusinessOwnerIndustries(state),
  industryOptionsLoading: getBusinessOwnerIndustriesLoading(state),
});

const mapDispatchToProps = {
  industriesFetch: fetchBusinessOwnerIndustries,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(React.memo(IndustrySearchableSelect));
