import { Form, Formik, FormikValues } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import FormikRadio from 'shared-resources/components/Radio/FormikRadio';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import * as yup from 'yup';
import {
  Screen15Keys,
  YesNoOptions,
  getQuestionBykey,
} from '../AssetProtectionConfig';

interface Props {
  data: any;
  handleNextClick: (values: FormikValues) => void;
  handlebackClick: (values: FormikValues) => void;
  handleSaveAsDraftClick: (values: FormikValues) => void;
}
const Screen15: React.FC<Props> = ({
  handleNextClick,
  handlebackClick,
  handleSaveAsDraftClick,
  data,
}) => {
  const validationSchema = yup.object().shape({
    [Screen15Keys.IS_THERE_CONCERN_FOR_CYBERSECURITY]: yup
      .string()
      .required('Value is required'),
    [Screen15Keys.HAVE_YOU_TAKEN_MEASURES]: yup
      .string()
      .when([Screen15Keys.IS_THERE_CONCERN_FOR_CYBERSECURITY], {
        is: (value: string) => value === 'yes',
        then: (schema) => schema.required('value is Required'),
        otherwise: (schema) => schema.nullable(),
      }),
  });

  const initialValue = data?.screen15 || {
    [Screen15Keys.IS_THERE_CONCERN_FOR_CYBERSECURITY]: '',
    [Screen15Keys.HAVE_YOU_TAKEN_MEASURES]: '',
  };

  const isSubmitting = useSelector(getAssessmentToolLoading);

  return (
    <div className='flex flex-col h-full  gap-6 pl-10 py-10 font-medium'>
      <Formik
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={(values: FormikValues) => {
          handleNextClick(values);
        }}
        enableReinitialize
      >
        {({ values, setFieldValue, setTouched }) => (
          <Form className='flex flex-col justify-between '>
            <div className='h-[calc(100vh-20rem)] overflow-auto scrollbar'>
              <div>
                <div className='mt-4'>
                  <span className='font-semibold'>
                    {getQuestionBykey(
                      Screen15Keys.IS_THERE_CONCERN_FOR_CYBERSECURITY
                    )}
                  </span>

                  <div className='mt-5 xl:w-[50%]'>
                    <FormikRadio
                      labelClassName='!cursor-pointer'
                      className2='!w-fit'
                      valueChanged={() => {
                        setTouched({
                          [Screen15Keys.IS_THERE_CONCERN_FOR_CYBERSECURITY]:
                            false,
                        });
                        setFieldValue(Screen15Keys.HAVE_YOU_TAKEN_MEASURES, '');
                      }}
                      className='grid grid-cols-2 gap-4'
                      name={Screen15Keys.IS_THERE_CONCERN_FOR_CYBERSECURITY}
                      options={YesNoOptions}
                    />
                  </div>
                </div>
                {values[Screen15Keys.IS_THERE_CONCERN_FOR_CYBERSECURITY] ===
                  'yes' && (
                  <div className='mt-4'>
                    <span className='font-semibold'>
                      {getQuestionBykey(Screen15Keys.HAVE_YOU_TAKEN_MEASURES)}
                    </span>
                    <div className='mt-5 xl:w-[50%]'>
                      <FormikRadio
                        labelClassName='!cursor-pointer'
                        className2='!w-fit'
                        className='grid grid-cols-2 gap-4'
                        name={Screen15Keys.HAVE_YOU_TAKEN_MEASURES}
                        options={YesNoOptions}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className='pr-5'>
              <BackNextComponent
                backStep={() => handlebackClick(values)}
                buttonType='submit'
                isLoading={isSubmitting}
                isNextDisable={false}
                onSaveToDraftClick={() => {
                  handleSaveAsDraftClick(values);
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Screen15;
