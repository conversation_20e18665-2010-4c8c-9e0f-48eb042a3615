import React, { FC, memo } from 'react';
import { FaChevronRight } from 'react-icons/fa';
import { getKey } from 'utils/helpers/Helpers';

type BusinessContinuityHeaderProps = {
  subHeadings?: string[] | null;
};

const BusinessContinuityHeader: FC<BusinessContinuityHeaderProps> = ({
  subHeadings,
}) => (
  <div className='flex items-center'>
    <h1 className='text-2xl font-semibold'>Business Continuity</h1>
    {!!subHeadings?.length && (
      <div className='flex '>
        <FaChevronRight className='text-4xl text-gray-600' />
        {subHeadings.map((subHeading: string, index) => (
          <div
            key={`${subHeading.slice(4)}_${getKey(index)}`}
            className='flex gap-2 items-center'
          >
            {index !== 0 && (
              <h1 className='text-blue-01 ml-2 font-bold text-xl'>/</h1>
            )}

            <h2 className='text-blue-01 font-bold text-xl'>{subHeading}</h2>
          </div>
        ))}
      </div>
    )}
  </div>
);
export default memo(BusinessContinuityHeader);
