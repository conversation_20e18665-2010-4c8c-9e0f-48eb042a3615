import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import {
  fetchBusinessOwnerAssessment,
  fetchOwnAssessment,
  updateBusinessOwnerAssessment,
  updateOwnAssessment,
} from 'store/actions/assessment-tool.action';
import {
  resetAssessmentData,
  resetProgressStatus,
} from 'store/reducers/assessment-tool.reducer';
import {
  getAssessmentToolLoading,
  getAssessmentToolReEvaluate,
  getAssessmentToolResponse,
  getAssessmentToolStatus,
  getBusinessOwnerProperties,
} from 'store/selectors/assessment-tool.selector';
import { getUserData } from 'store/selectors/user.selector';
import {
  AssessmentResponseType,
  AssessmentToolProgressStatus,
  AssessmentTools,
  FinancialGapAnalysisScreens,
  RouteKey,
  UserType,
} from 'types/enum';
import ThankYouPage from 'views/layout/ThankYouPage';
import { getUserRouteType, thankYouPageContent } from 'utils/helpers/Helpers';
import WithComments from 'shared-resources/components/ToolComments/WithComments';
import {
  getScreen,
  numberToScreenObject,
  screenToNumberObject,
} from './FinancialGapAnalysisConfig';

const FinancialGapAnalysisScreenContainer: React.FC = () => {
  const { id } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const savedDraftData: any = useSelector(getAssessmentToolResponse);
  const progressStatus = useSelector(getAssessmentToolStatus);
  const businessOwnerProperties: any = useSelector(getBusinessOwnerProperties);
  const [financialGapData, setFinancialGapData] = useState<any>();

  const isLoading = useSelector(getAssessmentToolLoading);
  const navigate = useNavigate();

  const [currentScreen, setCurrentScreen] = useState<{
    screen: FinancialGapAnalysisScreens | undefined;
  }>();

  const backNextClickHandler = (nextScreen: FinancialGapAnalysisScreens) => {
    setCurrentScreen({ screen: nextScreen });
  };
  const assessmentReEvaluate = useSelector(getAssessmentToolReEvaluate);
  const loggedInUser = useSelector(getUserData);
  const dispatch = useDispatch();

  const [submitType, setSubmitType] = useState<
    AssessmentResponseType | null | undefined
  >(null);

  useEffect(() => {
    if (
      progressStatus === AssessmentToolProgressStatus.COMPLETED &&
      assessmentReEvaluate &&
      !isLoading
    ) {
      dispatch(resetProgressStatus());
      dispatch(resetAssessmentData());
    }
  }, [assessmentReEvaluate, loggedInUser, progressStatus]);

  useEffect(() => {
    const screenNumber: number = savedDraftData?.saved_screen;
    if (
      progressStatus !== AssessmentToolProgressStatus.COMPLETED &&
      screenNumber &&
      !assessmentReEvaluate
    ) {
      setCurrentScreen({
        screen: numberToScreenObject[screenNumber],
      });
    } else {
      setCurrentScreen({
        screen: FinancialGapAnalysisScreens.GET_STARTED,
      });
    }
  }, [savedDraftData, progressStatus, businessOwnerProperties]);

  useEffect(() => {
    const screen = new URLSearchParams(currentScreen as Record<string, string>);
    setSearchParams(screen);
  }, [currentScreen, setSearchParams, searchParams]);

  const handleFetchAssessmentError = () => {
    navigate(
      `/${getUserRouteType(loggedInUser?.type as UserType)}/${
        RouteKey.DASHBOARD
      }`
    );
  };
  useEffect(() => {
    if (id && loggedInUser?.type === UserType.ADVISOR) {
      dispatch(
        fetchBusinessOwnerAssessment({
          tool: AssessmentTools.FINANCIAL_GAP_ANALYSIS,
          businessOwnerId: +id!,
          onError: handleFetchAssessmentError,
        })
      );
    }
    if (loggedInUser?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchOwnAssessment({
          tool: AssessmentTools.FINANCIAL_GAP_ANALYSIS,
          onError: handleFetchAssessmentError,
        })
      );
    }
  }, []);

  useEffect(() => {
    if (
      progressStatus !== AssessmentToolProgressStatus.COMPLETED ||
      assessmentReEvaluate
    ) {
      setFinancialGapData(savedDraftData);
    }
  }, [savedDraftData]);

  const handleSubmit = (
    response: any,
    submitType: AssessmentResponseType,
    onSuccess?: () => void
  ) => {
    let data = response;
    if (assessmentReEvaluate && currentScreen?.screen) {
      data = {
        ...response,
        saved_screen: screenToNumberObject[currentScreen.screen],
      };
    }

    if (
      submitType === AssessmentResponseType.COMPLETE &&
      loggedInUser?.type === UserType.BUSINESS_OWNER
    ) {
      data = {
        ...response,
        saved_screen:
          screenToNumberObject[FinancialGapAnalysisScreens.BUSINESS_SALE_COSTS],
      };
    }

    if (loggedInUser?.type === UserType.ADVISOR && id) {
      dispatch(
        updateBusinessOwnerAssessment({
          businessOwnerId: +id!,
          tool: AssessmentTools.FINANCIAL_GAP_ANALYSIS,
          assessment_response: data,
          submit_type: submitType,
          onSuccess,
        })
      );
    } else {
      dispatch(
        updateOwnAssessment({
          tool: AssessmentTools.FINANCIAL_GAP_ANALYSIS,
          assessment_response: data,
          submit_type:
            submitType === AssessmentResponseType.DRAFT
              ? AssessmentResponseType.DRAFT
              : AssessmentResponseType.SUBMIT,
          onSuccess,
        })
      );
    }
    setSubmitType(undefined);
  };

  if (isLoading && submitType === null) {
    return <Spinner />;
  }

  if (progressStatus === AssessmentToolProgressStatus.COMPLETED) {
    return (
      <ThankYouPage
        pageContent={thankYouPageContent(
          'Wealth Gap Analysis',
          loggedInUser?.type || UserType.BUSINESS_OWNER,
          `${businessOwnerProperties?.first_name ?? ''} ${
            businessOwnerProperties?.last_name ?? ''
          }`
        )}
        loggedInUserData={loggedInUser}
        isPasswordSet={false}
      />
    );
  }

  return (
    <div className='flex flex-col gap-4'>
      <div className='flex gap-2 items-center'>
        <h1 className='text-2xl font-semibold'>Wealth Gap Analysis</h1>
      </div>
      <WithComments tool={AssessmentTools.FINANCIAL_GAP_ANALYSIS}>
        {getScreen(
          currentScreen!,
          loggedInUser!,
          financialGapData,
          setFinancialGapData,
          backNextClickHandler,
          handleSubmit,
          navigate
        )}
      </WithComments>
    </div>
  );
};

export default FinancialGapAnalysisScreenContainer;
