import React from 'react';
import { BsFileEarmarkPdfFill } from 'react-icons/bs';

interface ReportGenerationModalProps {
  isPdf?: boolean;
}
const ReportGenerationModal: React.FC<ReportGenerationModalProps> = (props) => {
  const { isPdf } = props;
  return (
    <div className='w-[20rem] py-5 bg-white rounded-xl'>
      <div className='flex flex-col text-blue-01 items-center justify-center'>
        <BsFileEarmarkPdfFill className='text-6xl' />
        <span className='text-6xl leading-4 mb-6 animate-pulse'>...</span>
        <h1 className='text-center font-semibold'>
          {isPdf ? 'Pdf' : 'Report'} is Being Generated
        </h1>
      </div>
    </div>
  );
};

export default ReportGenerationModal;
