import { useFormikContext } from 'formik';
import { capitalize, pick } from 'lodash';
import React, { useEffect, useState } from 'react';
import { MdDelete, MdEdit } from 'react-icons/md';
import { getKey, stringifyNumber } from 'utils/helpers/Helpers';
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from '@hello-pangea/dnd';
import { FileService } from 'services/api-services/FileService';
import { AssessmentTools } from 'types/enum';
import { useParams } from 'react-router';
import { BiLoaderCircle } from 'react-icons/bi';
import { toast } from 'react-toastify';

interface ContuinuityRowViewProps {
  formValues: any;
  fieldKey: string;
  remove?: (index: number) => void;
  initialValues?: any;
  isReportView?: boolean;
  isDragDisabled?: boolean;
  classname?: string;
}
const ContuinuityRowView: React.FC<ContuinuityRowViewProps> = (props) => {
  const {
    formValues,
    fieldKey,
    remove,
    initialValues = {},
    isReportView,
    isDragDisabled = true,
    classname,
  } = props;
  const context = useFormikContext();
  const { id } = useParams();
  const [isDocumentLoading, setIsDocumentLoading] = useState<
    Record<number, boolean>
  >({});

  const downloadDocument = (url: string, index: number) => {
    setIsDocumentLoading({ [index]: true });
    FileService.getDocumentUrl(
      { url },
      AssessmentTools.BUSINESS_CONTINUITY,
      id ? +id : undefined
    )
      .then((res) => {
        const fileUrl = res.url;
        window.open(fileUrl, '_blank');
        setIsDocumentLoading({ [index]: false });
      })
      .catch((error) => {
        toast('Error retrieving document URL:', error);
        setIsDocumentLoading({ [index]: false });
      });
  };

  const convertedValues = (formValues: any, index: number) => {
    const orderedFields = Object.keys(initialValues || {})
      ?.map((value) => capitalize(value?.replace(/_/g, ' ')))
      .filter((value) => {
        if (isReportView) {
          return value !== 'Document' && value !== 'Saved';
        }
        return value !== 'Saved';
      });

    const orderedValues = Object.entries(formValues || {}).reduce<any[]>(
      (acc, [field, value]) => {
        if (field !== 'saved' && field !== 'document') {
          acc.push(value);
        }

        if (field === 'document' && !isReportView) {
          if (value) {
            acc.push(
              <div>
                {isDocumentLoading?.[index] ? (
                  <div className='flex w-full justify-start pl-6 items-center'>
                    <BiLoaderCircle
                      size={18}
                      className='w-10 h-19 text-gray-400 animate-spin'
                    />
                  </div>
                ) : (
                  <button
                    onClick={() => {
                      downloadDocument(value as string, index);
                    }}
                    className='no-underline text-blue-01'
                    type='button'
                  >
                    Download Document
                  </button>
                )}
              </div>
            );
          } else {
            acc.push(' ');
          }
        }
        return acc;
      },
      []
    );
    return {
      orderedFields: isReportView
        ? orderedFields
        : [...orderedFields, 'Action'],
      orderedValues: isReportView
        ? orderedValues
        : [...orderedValues, 'row-action'],
    };
  };

  const filteredInitialValues = Object.keys(initialValues)?.filter((key) => {
    if (isReportView) {
      return key !== 'saved' && key !== 'document';
    }
    return key !== 'saved';
  });
  const numKeys = Object.keys(
    pick(initialValues, filteredInitialValues)
  ).length;

  useEffect(() => {
    if (formValues?.[fieldKey] && formValues?.[fieldKey]?.length) {
      const isPriority = Object.keys(formValues?.[fieldKey]?.[0]).includes(
        'priority'
      );
      if (isPriority && !isReportView && context) {
        formValues?.[fieldKey]?.forEach((data: any, dataIndex: number) => {
          context?.setFieldValue?.(
            `${fieldKey}.${getKey(dataIndex)}.priority`,
            capitalize(stringifyNumber(dataIndex))
          );
        });
      }
    }
  }, [formValues]);
  const onDragEnd = (result: DropResult) => {
    const { source, destination } = result;

    // Dropped outside the list
    if (!destination) return;

    const reorderedItems = Array.from(formValues?.[fieldKey]);
    const [movedItem] = reorderedItems.splice(source.index, 1);
    reorderedItems.splice(destination.index, 0, movedItem);

    context?.setFieldValue?.(
      fieldKey,
      reorderedItems.map((item: any, index) => ({
        ...item,
        priority: capitalize(stringifyNumber(index)),
      }))
    );
  };
  return (
    <div className='flex flex-col w-full'>
      <div
        className={`absolute z-10 bg-white w-full  ${
          isReportView
            ? 'max-w-[calc(100%-160px)] left-20 rounded-t-xl'
            : 'max-w-[calc(100%-40px)] left-5 '
        } ${classname}`}
      >
        <div
          style={
            isReportView
              ? {
                  gridTemplateColumns: `repeat(${numKeys}, minmax(0, 1fr))`,
                }
              : { display: 'flex' }
          }
          className={`${
            isReportView ? `grid !pr-20` : 'flex gap-2'
          } bg-blue-01  rounded-t-xl px-4 py-3 justify-between`}
        >
          {convertedValues(formValues?.[fieldKey]?.[0], 0).orderedFields.map(
            (field) => (
              <span
                key={field}
                className={`${
                  field === 'Action' ? 'text-center' : 'text-start'
                } text-white ${
                  !isReportView && 'w-1/4'
                } max-w-[15rem] whitespace-nowrap`}
              >
                {field}
              </span>
            )
          )}
        </div>
      </div>
      <DragDropContext onDragEnd={onDragEnd}>
        <Droppable droppableId='priorityList'>
          {(provided) => (
            <div
              className='flex flex-col mt-12 h-full'
              {...provided.droppableProps}
              ref={provided.innerRef}
            >
              {!!formValues?.[fieldKey]?.length &&
                formValues?.[fieldKey]?.map(
                  (values: any, index: number) =>
                    values?.saved && (
                      <Draggable
                        key={`${getKey(index)}_row`}
                        draggableId={`${getKey(index)}_row`}
                        index={index}
                        isDragDisabled={isDragDisabled}
                      >
                        {(provided) => (
                          <div
                            className='flex flex-col mt-1 '
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                          >
                            <div
                              style={
                                isReportView
                                  ? {
                                      gridTemplateColumns: `repeat(${numKeys}, minmax(0, 1fr))`,
                                    }
                                  : { display: 'flex' }
                              }
                              className={`${
                                isReportView ? `grid !pr-20` : 'flex'
                              } px-4 py-3 border border-gray-02 gap-1 justify-between w-full`}
                            >
                              {convertedValues(values, index).orderedValues.map(
                                (value, valueIndex) => (
                                  <div
                                    key={`${getKey(valueIndex)}_value`}
                                    className={`text-black-02   break-words ${
                                      !isReportView &&
                                      'w-1/4 max-h-24 max-w-[15rem] overflow-auto scrollbar'
                                    } `}
                                  >
                                    {value === 'row-action' ? (
                                      <div className='flex gap-1 justify-center items-center'>
                                        <button
                                          className='w-10 h-12 hover:bg-blue-02'
                                          name='edit-row'
                                          aria-label='edit-row'
                                          onClick={() => {
                                            context.setFieldValue?.(
                                              `${fieldKey}.${getKey(
                                                index
                                              )}.saved`,
                                              false
                                            );
                                          }}
                                          type='button'
                                        >
                                          <MdEdit
                                            size={24}
                                            className='w-full h-6'
                                          />
                                        </button>
                                        <button
                                          name='delete-row'
                                          aria-label='delete-row'
                                          onClick={() => {
                                            if (
                                              index === 0 &&
                                              formValues?.[fieldKey]?.length ===
                                                1
                                            ) {
                                              context.setFieldValue(fieldKey, [
                                                initialValues,
                                              ]);
                                            } else {
                                              remove?.(getKey(index));
                                            }
                                          }}
                                          className='w-10 h-12 hover:bg-red-200'
                                          type='button'
                                        >
                                          <MdDelete
                                            size={24}
                                            color='red'
                                            className='w-full h-6'
                                          />
                                        </button>
                                      </div>
                                    ) : (
                                      (value as string) ?? ' '
                                    )}
                                  </div>
                                )
                              )}
                            </div>
                          </div>
                        )}
                      </Draggable>
                    )
                )}

              {!formValues?.[fieldKey]?.length && (
                <div className='px-4 mt-1 justify-center py-3 border border-gray-02 flex gap-2 w-full'>
                  No Data
                </div>
              )}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </div>
  );
};

export default ContuinuityRowView;
