import React, { useEffect } from 'react';
import { FaPhone } from 'react-icons/fa6';
import { IoMdMail } from 'react-icons/io';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import Button from 'shared-resources/components/Button/Button';
import { fetchReports } from 'store/actions/report-builder.action';
import { getReports } from 'store/selectors/report-builder.selector';
import { getUserData } from 'store/selectors/user.selector';
import { BusinessOwner } from '../../models/entities/BusinessOwner';
import { getInitials } from '../../utils/helpers/Helpers';

const BusinessDashboard = () => {
  const dispatch = useDispatch();
  const user = useSelector(getUserData) as BusinessOwner;
  const reports = useSelector(getReports);

  useEffect(() => {
    if (user?.id) {
      dispatch(fetchReports(+user.id));
    }
  }, [dispatch, user?.id]);

  const hasReport = reports?.length > 0;
  const navigate = useNavigate();

  const businessDetails = [
    {
      field: 'Business Name',
      value: user?.business_name,
    },
    {
      field: 'Business Address',
      value: user?.street_address,
    },
    {
      field: 'ZIP (Postal) Code',
      value: user?.zip_code,
    },
  ];

  return (
    <div className='flex flex-col gap-4 h-[calc(100vh-9.6875rem)]'>
      <div className='flex justify-between items-center'>
        <h1 className='text-xl font-medium'>Welcome, {user?.first_name}</h1>
        {hasReport && (
          <Button
            onClick={() => {
              navigate('/business-owner/report-builder');
            }}
            className='py-2 px-6'
          >
            Open Report
          </Button>
        )}
      </div>
      <div className='flex flex-col p-8 border-2 border-blue-01 bg-white rounded-[2.5rem] gap-10 h-full'>
        <div className='flex items-center gap-14'>
          <div className='rounded-full w-[7.5rem] h-[7.5rem] border-2 border-blue-01'>
            {user && (
              <div className='flex items-center font-medium w-full h-full text-3xl justify-center border border-blue-01 rounded-full text-blue-01'>
                {getInitials(user)}
              </div>
            )}
          </div>
          <div className='flex flex-col gap-4 w-full'>
            <span className='text-blue-01 text-xl font-medium whitespace-nowrap'>
              {user?.name}
            </span>
            <div className='flex justify-between w-full'>
              {/* User contact info - left side */}
              <div className='flex flex-col gap-3'>
                <div className='flex gap-3 items-center'>
                  <IoMdMail className='min-w-4 h-4 text-black-02' />
                  {user?.email}
                </div>
                {user?.phone && (
                  <div className='flex gap-3 items-center'>
                    <FaPhone className='min-w-4 h-4 text-black-02' />
                    {user.phone}
                  </div>
                )}
              </div>
              
              {/* Empty spacer to push advisor info to the right */}
              <div className='flex-grow'></div>
              
              {/* Advisor info - right side */}
              <div className='flex flex-col gap-3 ml-auto'>
                <div className='flex gap-3 items-center'>
                  <span>Advisor Name:</span>
                  <span className='text-black-02'>
                    {user?.primary_advisor_name}
                  </span>
                </div>
                <div className='flex gap-3 items-center'>
                  <span>Advisor Email:</span>
                  <IoMdMail className='min-w-4 h-4 text-black-02' />
                  {user?.primary_advisor_email}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className='flex flex-col gap-5'>
          <div className='flex flex-col gap-1'>
            <h2 className='text-lg font-medium'>Business Details</h2>
            <hr className='h-0.5 bg-black-01' />
          </div>
          <div className='grid grid-cols-3 gap-8 w-fit'>
            {businessDetails.map(
              (item) =>
                item.value && (
                  <div key={item.field} className='flex flex-col gap-2'>
                    <span className='font-medium'>{item.field}</span>
                    <span className='text-blue-01 font-medium'>
                      {item.value}
                    </span>
                  </div>
                )
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessDashboard;
