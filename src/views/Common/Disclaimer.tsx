import React from 'react';

interface Props {
  disclaimerText?: string;
}

const DisclaimerPage: React.FC<Props> = ({
  disclaimerText = `This tool is the exclusive property and responsibility of ExitSmarts, Inc. ExitSmarts, Inc., and the Advisor who gave you this tool are separate and independent entities. Under no circumstances shall the Advisor who gave you this tool be liable for any indirect, incidental, consequential, special or exemplary damages arising out of or otherwise in connection with its use. Additionally, ExitSmarts, or its affiliates, partners, suppliers, or licensors shall not be liable for any indirect, incidental, consequential, special or exemplary damages arising out of or in connection with its use. Without limiting the generality of the foregoing, ExitSmarts aggregate liability (whether under contract, tort, statute or otherwise) shall not exceed the amount of fifty dollars ($50.00). The foregoing limitations will apply even if the above stated remedy fails of its essential purpose.`,
}) => (
  <div className='flex justify-center'>
    <div className='flex flex-col w-4/5 3xl:w-13/20 h-full'>
      <h1 className='text-blue-01 font-openSan font-semibold mt-[7.5rem] text-center'>
        Disclaimer
      </h1>
      <div className='mt-6 flex flex-col space-y-12'>
        <p>{disclaimerText}</p>
      </div>
    </div>
  </div>
);

export default DisclaimerPage;
