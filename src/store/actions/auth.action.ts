import { User } from 'models/entities/User';
import { UserRouteType, UserType } from 'types/enum';
import { AuthActionType } from './actions.constants';

// TODO: MOVE TO SPECIFIC TYPE FILE
export interface AuthLoginActionPayloadType {
  email: string;
  password: string;
  userType: UserType;
  mfa_preference?: 'email' | 'totp';
  onSuccess: () => void;
}
export interface MfaCodeActionPayloadType {
  code: string;
  userType: UserType;
  onError?: () => void;
}
export interface ResendCodeActionPayloadType {
  userType: UserType;
  onSuccess?: () => void;
  onError?: () => void;
}

export const authLoginAction = (payload: MfaCodeActionPayloadType) => ({
  type: AuthActionType.LOGIN,
  payload,
});

export const getMfaCode = (payload: AuthLoginActionPayloadType) => ({
  type: AuthActionType.GET_MFA_CODE,
  payload,
});
export const resendMfaCode = (payload: ResendCodeActionPayloadType) => ({
  type: AuthActionType.RESEND_MFA_CODE,
  payload,
});

export const authLogoutAction = () => ({
  type: AuthActionType.LOGOUT,
});

export const authLoginCompletedAction = (user: User) => ({
  type: AuthActionType.LOGIN_COMPLETED,
  payload: user,
});

export const authLoginErrorAction = (message: string) => ({
  type: AuthActionType.LOGIN_ERROR,
  payload: message,
});

export const authFetchMeAction = (payload: { userType: UserRouteType }) => ({
  type: AuthActionType.FETCH_ME,
  payload,
});

export const authFetchMeCompletedAction = (user: User) => ({
  type: AuthActionType.FETCH_ME_COMPLETED,
  payload: user,
});

export const authFetchMeErrorAction = (message: string) => ({
  type: AuthActionType.FETCH_ME_ERROR,
  payload: message,
});
