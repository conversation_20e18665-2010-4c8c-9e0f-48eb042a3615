import { FieldArray, Form, Formik } from 'formik';
import React from 'react';
import { IoInformationCircle } from 'react-icons/io5';
import { useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { getKey } from 'utils/helpers/Helpers';
import * as yup from 'yup';
import { getYear } from '../BusinessValuationConfig';

interface Props {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  data: any;
  onSaveToDraftClick: (data: any) => void;
}
const NonActiveFamilyBenefitsPandL: React.FC<Props> = (props) => {
  const { nextStep, setData, backStep, data, onSaveToDraftClick } = props;
  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup.object().shape({
    [`vehicles_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`vehicles_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`vehicles_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`insurance_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`insurance_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`insurance_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`education_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`education_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`education_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`housing_${getYear(1)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`housing_${getYear(2)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    [`housing_${getYear(3)}`]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .nullable(),
    other: yup.array().of(
      yup.object({
        [`other_${getYear(1)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`other_${getYear(2)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`other_${getYear(3)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
      })
    ),
  });

  const handleSubmit = (values: any) => {
    setData(values);
    nextStep();
  };

  return (
    <div className='flex flex-col '>
      <Formik
        initialValues={
          data?.non_active_family_benefits || {
            [`vehicles_${getYear(1)}`]: '',
            [`vehicles_${getYear(2)}`]: '',
            [`vehicles_${getYear(3)}`]: '',
            [`insurance_${getYear(1)}`]: '',
            [`insurance_${getYear(2)}`]: '',
            [`insurance_${getYear(3)}`]: '',
            [`education_${getYear(1)}`]: '',
            [`education_${getYear(2)}`]: '',
            [`education_${getYear(3)}`]: '',
            [`housing_${getYear(1)}`]: '',
            [`housing_${getYear(2)}`]: '',
            [`housing_${getYear(3)}`]: '',
            other: [
              {
                [`other_${getYear(1)}`]: '',
                [`other_${getYear(2)}`]: '',
                [`other_${getYear(3)}`]: '',
              },
            ],
          }
        }
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values }) => (
          <Form className=' relative pl-5 py-3 flex flex-col gap-4 bg-white'>
            <h2 className='text-xl font-bold underline'>
              Non-Active Family Benefits:
            </h2>
            <div className='relative z-10 mb-3 text-xl items-center font-semibold'>
              <span className='absolute left-[26.5%]'>{getYear(1)}</span>
              <span className='absolute left-[52%]'>{getYear(2)}</span>
              <span className='absolute left-[78%]'>{getYear(3)}</span>
            </div>
            <div className='h-[calc(100vh-24rem)]  overflow-auto scrollbar pr-4 mt-3'>
              <div className='grid grid-cols-4 gap-x-16 gap-y-1  text-[1.1rem]'>
                <div id='vehicle' className='flex mb-5 items-end gap-2'>
                  <IoInformationCircle className='text-blue-01 mb-1' />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='#vehicle'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      Finance payments, fuel, maintenance and repair on
                      non-business vehicles for family.
                    </Tooltip>
                    <span>Vehicles</span>
                  </div>
                </div>

                <FormikInput
                  className='!h-10'
                  name={`vehicles_${getYear(1)}`}
                  key={`vehicles_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`vehicles_${getYear(2)}`}
                  key={`vehicles_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`vehicles_${getYear(3)}`}
                  key={`vehicles_${getYear(3)}`}
                />
                <div id='insurance' className='flex  items-center gap-2'>
                  <IoInformationCircle className='text-blue-01' />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='#insurance'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      Expense for non-business education of any type.
                    </Tooltip>
                    <span>Insurance</span>
                  </div>
                </div>

                <FormikInput
                  className='!h-10'
                  name={`insurance_${getYear(1)}`}
                  key={`insurance_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`insurance_${getYear(2)}`}
                  key={`insurance_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`insurance_${getYear(3)}`}
                  key={`insurance_${getYear(3)}`}
                />
                <div id='education' className='flex  items-center gap-2'>
                  <IoInformationCircle className='text-blue-01' />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='#education'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      Expense for non-business education of any type.
                    </Tooltip>
                    <span>Education</span>
                  </div>
                </div>
                <FormikInput
                  className='!h-10'
                  name={`education_${getYear(1)}`}
                  key={`education_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`education_${getYear(2)}`}
                  key={`education_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`education_${getYear(3)}`}
                  key={`education_${getYear(3)}`}
                />
                <div id='housing' className='flex  items-center gap-2'>
                  <IoInformationCircle className='text-blue-01' />
                  <div className='relative'>
                    <Tooltip
                      anchorSelect='#housing'
                      place='right'
                      offset={0}
                      className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                      classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                    >
                      Housing/rent costs for non-business purposes.
                    </Tooltip>
                    <span>Housing</span>
                  </div>
                </div>
                <FormikInput
                  className='!h-10'
                  name={`housing_${getYear(1)}`}
                  key={`housing_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`housing_${getYear(2)}`}
                  key={`housing_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  name={`housing_${getYear(3)}`}
                  key={`housing_${getYear(3)}`}
                />
              </div>

              <FieldArray name='other'>
                {({ push }) => (
                  <div className=''>
                    {values.other &&
                      values.other.length > 0 &&
                      values.other.map((other: any, index: number) => (
                        <div
                          key={getKey(index)}
                          className='grid grid-cols-4 gap-x-16 gap-y-1  text-[1.1rem]'
                        >
                          <div
                            id={`other${index}`}
                            className='flex  items-center gap-2'
                          >
                            <IoInformationCircle className='text-blue-01' />
                            <div className='relative'>
                              <Tooltip
                                anchorSelect={`#other${index}`}
                                place='right'
                                offset={0}
                                className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !-top-1 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                              >
                                Other non-business expenses for owner(s) and
                                family members.
                              </Tooltip>
                              <span>Other</span>
                            </div>
                          </div>
                          <FormikInput
                            className='!h-10'
                            name={`other.${index}.other_${getYear(1)}`}
                            key={`other.${getKey(index)}.other_${getYear(1)}`}
                          />
                          <FormikInput
                            className='!h-10'
                            name={`other.${index}.other_${getYear(2)}`}
                            key={`other.${getKey(index)}.other_${getYear(2)}`}
                          />
                          <FormikInput
                            className='!h-10'
                            name={`other.${index}.other_${getYear(3)}`}
                            key={`other.${getKey(index)}.other_${getYear(3)}`}
                          />
                        </div>
                      ))}
                    <div className='absolute top-[3rem] right-[3rem]'>
                      <Button
                        type='button'
                        className='px-6 py-2 !absolute right-0 '
                        onClick={() =>
                          push({
                            [`other_${getYear(1)}`]: '',
                            [`other_${getYear(2)}`]: '',
                            [`other_${getYear(3)}`]: '',
                          })
                        }
                      >
                        Add
                      </Button>
                    </div>
                  </div>
                )}
              </FieldArray>
            </div>

            <div className='pr-7'>
              <BackNextComponent
                backStep={backStep}
                buttonType='submit'
                isLoading={isLoading}
                isNextDisable={false}
                onSaveToDraftClick={() => {
                  onSaveToDraftClick(values);
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default NonActiveFamilyBenefitsPandL;
