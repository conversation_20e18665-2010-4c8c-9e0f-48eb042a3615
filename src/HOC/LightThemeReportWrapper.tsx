import React from 'react';
import reportWrapperLogoLight from '../assets/reportWrapperLogoLight.svg';
import logo from '../assets/ExitSmartsLogo.svg';

interface Props {
  children: React.ReactElement;
  id?: string;
}

const LightThemeReportWrapper: React.FC<Props> = ({ children, id }) => (
  <div id={id} className='h-[96.875rem] bg-white relative aspect-[210/297]'>
    <img
      src={reportWrapperLogoLight}
      className='absolute top-0 left-0 border-amber-900 w-80 3xl:w-auto'
      alt='wrapper-image1'
    />
    <img
      src={reportWrapperLogoLight}
      className='absolute bottom-0 right-0 transform rotate-180 w-80 3xl:w-auto'
      alt='wrapper-image2'
    />
    <img
      src={logo}
      className='absolute top-2 right-2 opacity-20 w-[32rem]'
      alt='wrapper-image3'
    />
    <div className='h-full w-full'>{children}</div>
  </div>
);

export default LightThemeReportWrapper;
