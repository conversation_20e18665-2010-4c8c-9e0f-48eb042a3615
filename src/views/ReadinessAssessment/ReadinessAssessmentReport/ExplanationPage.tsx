import React from 'react';

interface Props {}

const ExplanationPage: React.FC<Props> = () => (
  <div className='flex justify-center'>
    <div className='flex flex-col items-center w-4/5 3xl:w-13/20'>
      <h1 className='text-blue-01 font-openSan font-semibold mt-[7.5rem] text-center'>
        What does the Readiness Score mean?
      </h1>

      <div className='mt-[4.375rem] flex flex-col space-y-6 text-left'>
        <p>
          The Readiness Score provides a general indication of your awareness
          and implementation of key components of the transition process. The
          overall score is a compilation of your score in five different areas
          of Transition Readiness:
        </p>
        <div className=''>
          <p>
            <span className='font-semibold'>Business Readiness : </span>
            Indication of the readiness of the business for
            transition, especially with regard to the intangible
            assets of the business.
          </p>
        </div>
        <div>
          <p>
            <span className='font-semibold'>Finance : </span>
            Indication of your awareness of personal and business financial
            matters related to transition.
          </p>
        </div>
        <div>
            <div>
            <p>
              <span className='font-semibold'>Transition Objectives : </span>
              Indication of your personal readiness as qualified by the answers to the following:
            </p>
            <ul className='list-disc ml-6'>
              <li>How much do I need to transition?</li>
              <li>When do I want to transition?</li>
              <li>To whom do I want to transition?</li>
              <li>What will I do after transition?</li>
            </ul>
            </div>
        </div>
        <div>
          <p>
            <span className='font-semibold'>Transition Knowledge : </span>
            Indication of your understanding of miscellaneous transition concepts that may not be
            covered elsewhere.
          </p>
        </div>
        <div>
          <p>
            <span className='font-semibold'>Planning : </span>
            Indication of the amount of planning related to transition
            that has already been completed.
          </p>
        </div>
      </div>
    </div>
  </div>
);

export default ExplanationPage;
