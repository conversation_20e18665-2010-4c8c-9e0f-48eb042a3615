import React, { FC, memo, useEffect, useState } from 'react';
import { GrNext } from 'react-icons/gr';
import {
  fetchBusinessOwnerAssessment,
  fetchOwnAssessment,
  updateBusinessOwnerAssessment,
  updateOwnAssessment,
} from 'store/actions/assessment-tool.action';
import {
  AssessmentResponseType,
  AssessmentToolProgressStatus,
  AssessmentTools,
  RouteKey,
  TransitionObjectivesTabs,
  UserType,
} from 'types/enum';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import {
  getAssessmentToolLoading,
  getAssessmentToolReEvaluate,
  getAssessmentToolResponse,
  getAssessmentToolStatus,
  getBusinessOwnerProperties,
} from 'store/selectors/assessment-tool.selector';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import { getUserData } from 'store/selectors/user.selector';
import { useSearchParams } from 'react-router-dom';
import { resetAssessmentData } from 'store/reducers/assessment-tool.reducer';
import { getUserRouteType, thankYouPageContent } from 'utils/helpers/Helpers';
import WithComments from 'shared-resources/components/ToolComments/WithComments';
import BasicInfoPage from './BasicInfoPage';
import GetStartedPage from './GetStartedPage';
import QuestionsPage from './QuestionsPage';
import { useParamSelector } from '../../store/selectors/base.selectors';
import {
  getBusinessOwnerDetails,
  getBusinessOwnerDetailsLoading,
} from '../../store/selectors/business-owner.selector';
import { BusinessOwner } from '../../models/entities/BusinessOwner';
import { fetchBusinessOwner } from '../../store/actions/business-owner.action';
import ThankYouPage from '../layout/ThankYouPage';

interface Props {}

const TransitionObjectivePage: FC<Props> = () => {
  const { id } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const businessOwner = useParamSelector(getBusinessOwnerDetails, {
    id,
  }) as BusinessOwner;
  const dispatch = useDispatch();
  const isLoading = useSelector(getAssessmentToolLoading);
  const loggedInUser = useSelector(getUserData);
  const savedResponse: any = useSelector(getAssessmentToolResponse);
  const assessmentToolStatus = useSelector(getAssessmentToolStatus);
  const assessmentReEvaluate = useSelector(getAssessmentToolReEvaluate);
  const navigate = useNavigate();
  const businessOwnerLoading = useSelector(getBusinessOwnerDetailsLoading);
  const [currentTab, setCurrentTab] = useState<TransitionObjectivesTabs>(
    TransitionObjectivesTabs.GET_STARTED
  );
  const [assessmentData, setAssessmentData] = useState<any>({});
  const [isCompleteOwner, setIsCompleteOwner] = useState(false);

  const businessOwnerProperties = useSelector(getBusinessOwnerProperties) as {
    first_name: string;
    last_name: string;
    business_name: string;
  };

  useEffect(() => {
    searchParams.set('tab', currentTab);
    setSearchParams(searchParams);
  }, [currentTab, setSearchParams, searchParams]);

  useEffect(() => {
    if (
      assessmentToolStatus === AssessmentToolProgressStatus.COMPLETED &&
      assessmentReEvaluate &&
      !isLoading
    ) {
      dispatch(resetAssessmentData());
    }
  }, [assessmentReEvaluate, loggedInUser, assessmentToolStatus]);

  useEffect(() => {
    if (id) {
      dispatch(fetchBusinessOwner(id));
    }
  }, [dispatch, id]);

  const handleFetchAssessmentError = () => {
    navigate(
      `/${getUserRouteType(loggedInUser?.type as UserType)}/${
        RouteKey.DASHBOARD
      }`
    );
  };
  useEffect(() => {
    if (loggedInUser?.type === UserType.ADVISOR && id) {
      dispatch(
        fetchBusinessOwnerAssessment({
          tool: AssessmentTools.TRANSITION_OBJECTIVES,
          businessOwnerId: +id!,
          onError: handleFetchAssessmentError,
        })
      );
    } else if (loggedInUser?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchOwnAssessment({
          tool: AssessmentTools.TRANSITION_OBJECTIVES,
          onError: handleFetchAssessmentError,
        })
      );
    }
  }, []);

  // set saved response
  useEffect(() => {
    if (
      savedResponse &&
      assessmentToolStatus !== AssessmentToolProgressStatus.COMPLETED
    ) {
      setAssessmentData(savedResponse);
    } else {
      setAssessmentData({});
    }
  }, [savedResponse, assessmentToolStatus]);

  useEffect(() => {
    if (
      savedResponse &&
      typeof savedResponse === 'object' &&
      Object.keys(savedResponse).length &&
      assessmentToolStatus === AssessmentToolProgressStatus.IN_PROGRESS
    ) {
      setCurrentTab(TransitionObjectivesTabs.QUESTIONS);
    }
  }, [savedResponse, assessmentToolStatus]);

  const submitHandler = (type: AssessmentResponseType, data: any) => {
    if (loggedInUser?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        updateOwnAssessment({
          tool: AssessmentTools.TRANSITION_OBJECTIVES,
          assessment_response: data,
          submit_type: type === AssessmentResponseType.DRAFT
                      ? AssessmentResponseType.DRAFT
                      : AssessmentResponseType.SUBMIT,
          onSuccess: () =>
            type === AssessmentResponseType.COMPLETE &&
            navigate(`${UserType.BUSINESS_OWNER}/${RouteKey.DASHBOARD}`),
        })
      );
    } else {
      dispatch(
        updateBusinessOwnerAssessment({
          businessOwnerId: +id!,
          tool: AssessmentTools.TRANSITION_OBJECTIVES,
          assessment_response: data,
          submit_type: type === AssessmentResponseType.DRAFT
                      ? AssessmentResponseType.DRAFT
                      : AssessmentResponseType.COMPLETE,
        })
      );
    }
  };

  const setData = (res: any, type?: AssessmentResponseType) => {
    if (type) {
      let response = {};
      if (assessmentData.owners) {
        response = { owners: assessmentData.owners };
      }
      setAssessmentData({ ...response, ...res });
      submitHandler(type, { ...response, ...res });
    } else {
      setAssessmentData({ ...assessmentData, ...res });
    }
  };

  useEffect(() => {
    if (assessmentData?.owners) {
      const ownershipData = JSON.parse(assessmentData?.owners).find(
        (owner: { name: string; ownership: string }) =>
          owner.name === businessOwner?.name
      );

      if (Number(ownershipData?.ownership) === 100) {
        setIsCompleteOwner(true);
      } else {
        setIsCompleteOwner(false);
      }
    }
  }, [assessmentData]);

  const switchToQuestionsTab = () => {
    setCurrentTab(TransitionObjectivesTabs.QUESTIONS);
    searchParams.set('questionId', '1');
  };

  useEffect(() => {
    if (
      savedResponse?.owners &&
      savedResponse?.lastId &&
      !searchParams.get('questionId')
    ) {
      const ownershipData = JSON.parse(savedResponse?.owners).find(
        (owner: { name: string; ownership: string }) =>
          owner.name === businessOwner?.name
      );

      const isSavedOwnerComplete = Number(ownershipData?.ownership) === 100;

      if (
        assessmentToolStatus === AssessmentToolProgressStatus.IN_PROGRESS &&
        isSavedOwnerComplete !== isCompleteOwner
      ) {
        setData(
          { owners: assessmentData.owners, lastId: null },
          AssessmentResponseType.DRAFT
        );
      }
    }
  }, [isCompleteOwner, savedResponse]);

  if (assessmentToolStatus === AssessmentToolProgressStatus.COMPLETED) {
    return isLoading ? (
      <Spinner spinnerTheme='overlaySpinner' />
    ) : (
      <ThankYouPage
        isPasswordSet
        loggedInUserData={loggedInUser}
         pageContent={thankYouPageContent(
                'Transition Objectives',
                loggedInUser?.type || UserType.BUSINESS_OWNER,
                `${businessOwnerProperties?.first_name ?? ''} ${businessOwnerProperties?.last_name ?? ''}`
              )}/>
    );
  }

  return (
    <div className='h-[calc(100vh-9.6875rem)] overflow-y-auto'>
      <div className='flex items-center mb-3'>
        <h1 className='text-2xl font-semibold'>Transition Objectives</h1>
        {currentTab === TransitionObjectivesTabs.OWNERSHIP_DETAILS && (
          <span className='flex items-center text-xl text-gray-800'>
            <GrNext />
            <p>{businessOwner?.name}</p>
          </span>
        )}
      </div>
      {isLoading || businessOwnerLoading ? (
        <Spinner customClassName='mt-4' size='sm' spinnerTheme='default' />
      ) : (
        <WithComments tool={AssessmentTools.TRANSITION_OBJECTIVES}>
          <div>
            {currentTab === TransitionObjectivesTabs.GET_STARTED && (
              <GetStartedPage
                onGetStartedClick={() =>
                  setCurrentTab(TransitionObjectivesTabs.OWNERSHIP_DETAILS)
                }
              />
            )}
            {currentTab === TransitionObjectivesTabs.OWNERSHIP_DETAILS && (
              <BasicInfoPage
                setTab={switchToQuestionsTab}
                businessOwner={
                  id ? businessOwner : (loggedInUser as BusinessOwner)
                }
                assessmentResponse={savedResponse}
                setData={setData}
              />
            )}
            {currentTab === TransitionObjectivesTabs.QUESTIONS && (
              <QuestionsPage
                assessmentStatus={assessmentToolStatus}
                assessmentToolResponse={savedResponse}
                setTab={() => {
                  setCurrentTab(TransitionObjectivesTabs.OWNERSHIP_DETAILS);
                }}
                isCompleteOwner={isCompleteOwner}
                setData={setData}
                searchParams={searchParams}
                setSearchParams={setSearchParams}
              />
            )}
          </div>
        </WithComments>
      )}
    </div>
  );
};
export default memo(TransitionObjectivePage);
