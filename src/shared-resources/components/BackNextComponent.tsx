import React, { useState } from 'react';
import Button from 'shared-resources/components/Button/Button';
import { BusinessContinuityScreens, UserType } from 'types/enum';
import AddCommentPopup from 'shared-resources/components/ToolComments/AddCommentPopup';
import { useSelector } from 'react-redux';
import { getUserData } from 'store/selectors/user.selector';
import { getAssessmentSubmittedAt } from 'store/selectors/assessment-tool.selector';

interface BackNextComponentProps {
  backStep: () => void;
  nextStep?: () => void;
  onSaveToDraftClick: (values: any) => void;
  isLoading: boolean;
  formValues?: any;
  fieldKey?: string;
  isNextDisable: boolean;
  buttonText?: string;
  buttonType?: 'button' | 'submit';
}

const BackNextComponent: React.FC<BackNextComponentProps> = (props) => {
  const {
    backStep,
    nextStep,
    onSaveToDraftClick,
    isLoading,
    formValues = '',
    fieldKey = '',
    isNextDisable,
    buttonText = 'Next',
    buttonType = 'button',
  } = props;

  const [isAddCommentOpen, setIsAddCommentOpen] = useState(false);
  const user = useSelector(getUserData);
  const submitted_at: any = useSelector(getAssessmentSubmittedAt);
  const disableDraft = (user?.type === UserType.BUSINESS_OWNER && submitted_at)? true : false;

  return (
    <div className='flex justify-between'>
      <Button
        onClick={() => backStep()}
        className='rounded-xl px-7 py-2  font-medium'
        theme='secondary'
        type='button'
      >
        Back
      </Button>

      <div className='flex gap-4'>
        {user?.type === UserType.ADVISOR && (
          <Button
            onClick={() => setIsAddCommentOpen(true)}
            className='rounded-xl px-4 py-2  font-medium'
            type='button'
          >
            Add Comment
          </Button>
        )}
        <Button
          onClick={() => {
            if (fieldKey === BusinessContinuityScreens.SOLE_OWNERS) {
              onSaveToDraftClick(formValues);
            } else {
              onSaveToDraftClick(formValues?.[fieldKey]);
            }
          }}
          className='rounded-xl px-4 py-2  font-medium min-w-36'
          theme='secondary'
          type='button'
          isSubmitting={isLoading}
          loaderColor='border-black-01'
          disabled={disableDraft}
        >
          Save As Draft
        </Button>
        <Button
          disabled={
            isNextDisable ||
            isLoading ||
            (buttonText === 'Submit' && submitted_at)
          }
          isSubmitting={isLoading}
          className='rounded-xl px-7 py-2  font-medium'
          type={buttonType}
          onClick={() => {
            if (nextStep) nextStep();
          }}
        >
          {buttonText}
        </Button>
      </div>
      <AddCommentPopup
        isOpen={isAddCommentOpen}
        onClose={() => setIsAddCommentOpen(false)}
      />
    </div>
  );
};

export default BackNextComponent;
