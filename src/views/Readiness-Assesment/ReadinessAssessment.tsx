import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from 'react-router-dom';
import QuestionProgressBar from 'shared-resources/components/QuestionProgressBar';
import StepperHeader from 'shared-resources/components/StepperHeader';
import {
  fetchBusinessOwnerAssessment,
  fetchOwnAssessment,
  fetchPublicAssessmentByToken,
  updateBusinessOwnerAssessment,
  updateOwnAssessment,
  updatePublicAssessmentByToken,
} from 'store/actions/assessment-tool.action';
import {
  AssessmentResponseType,
  AssessmentToolProgressStatus,
  AssessmentTools,
  RouteKey,
  UserRouteType,
  UserType,
} from 'types/enum';
import { ReadinessAssessmentTitle } from 'utils/Readiness-Assessment/ReadinessAssessmentEnums';
import { initialTabData } from 'views/contents/Readiness-Assessment/readinessAssessment.constant';

import Spinner from 'shared-resources/components/Spinner/Spinner';
import {
  getAssessmentToolLoading,
  getAssessmentToolResponse,
  getAssessmentToolStatus,
  getBusinessOwnerProperties,
  getIsPasswordSet,
} from 'store/selectors/assessment-tool.selector';
import { getUserData } from 'store/selectors/user.selector';
import ThankYouPage from 'views/layout/ThankYouPage';

import cx from 'classnames';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import { getUserRouteType, thankYouPageContent } from 'utils/helpers/Helpers';

import WithComments from 'shared-resources/components/ToolComments/WithComments';
import AssessmentComponent from './AssessmentComponent';
import {
  getQuestions,
  handleBackTabSwitch,
  handleNextTabSwitch,
  updateTabAndData,
} from './ReadinessConfig';
import { convertToSnakeCase } from './readinessReportHelper';

interface Props {}

const ReadinessAssessment: React.FC<Props> = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = useParams();
  const dispatch = useDispatch();
  const loggedInUserData = useSelector(getUserData);
  const response = useSelector(getAssessmentToolResponse);
  const isLoading = useSelector(getAssessmentToolLoading);

  const assessmentToolStatus = useSelector(getAssessmentToolStatus);

  const tabValues = searchParams.get('tab');
  const businessOwnerProperties: any = useSelector(getBusinessOwnerProperties);
  const isPasswordSet = useSelector(getIsPasswordSet);
  const [nextButtonEnable, setNextButtonEnable] = useState<boolean>(true);
  const [nextButtonClicked, setNextButtonClicked] = useState(false);
  const [currentTabValue, setCurrentTabValue] = useState<{
    tab: string | number;
  }>({
    tab: tabValues || ReadinessAssessmentTitle.BUSINESS_READINESS,
  });
  const [responses, setResponses] = useState({});
  const [tabData, setTabData] = useState<
    {
      id: string;
      name: string;
      isActive: boolean;
      theme: 'primary' | 'success' | 'default';
      isCompleted: boolean;
    }[]
  >(initialTabData);

  const [questionProgressData, setQuestionProgressData] = useState(
    getQuestions(currentTabValue.tab).map(() => ({ isActive: false }))
  );
  const token = searchParams.get('token');
  const handleHeaderClick = (a: number | string) => {
    setCurrentTabValue({ tab: a });
  };

  const handleAnswerClick = (quesid: string, value: string | number) => {
    setResponses((prevResponses) => ({ ...prevResponses, [quesid]: value }));
  };
  const handleFormSubmit = (saveAsDraft: boolean) => {
    if (loggedInUserData?.type === UserType.ADVISOR && id) {
      dispatch(
        updateBusinessOwnerAssessment({
          businessOwnerId: +id!,
          tool: AssessmentTools.READINESS_ASSESSMENT,
          assessment_response: responses,
          submit_type: saveAsDraft
            ? AssessmentResponseType.DRAFT
            : AssessmentResponseType.COMPLETE,
        })
      );
    } else if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        updateOwnAssessment({
          tool: AssessmentTools.READINESS_ASSESSMENT,
          assessment_response: responses,
          submit_type: saveAsDraft
            ? AssessmentResponseType.DRAFT
            : AssessmentResponseType.SUBMIT,
          onSuccess: () =>
            !saveAsDraft &&
            navigate(`${UserType.BUSINESS_OWNER}/${RouteKey.DASHBOARD}`),
        })
      );
    } else if (location.pathname.includes('public')) {
      dispatch(
        updatePublicAssessmentByToken({
          token: token as string,
          tool: AssessmentTools.READINESS_ASSESSMENT,
          assessment_response: responses,
          submit_type: saveAsDraft
            ? AssessmentResponseType.DRAFT
            : AssessmentResponseType.COMPLETE,
        })
      );
    }
  };

  const handleNextButtonClick = () => {
    const updatedTabData = tabData.map((tab) => {
      if (tab.id === currentTabValue.tab) {
        return { ...tab, isActive: false, isCompleted: true };
      }
      return tab;
    });
    handleNextTabSwitch(currentTabValue, setCurrentTabValue, handleFormSubmit);
    setNextButtonClicked(!nextButtonClicked);
    setTabData(updatedTabData);
  };
  const handleBackButton = () => {
    handleBackTabSwitch(
      currentTabValue,
      navigate,
      setCurrentTabValue,
      loggedInUserData
    );
  };

  useEffect(() => {
    if (location.pathname.includes('public')) {
      const tab = new URLSearchParams(location.search);
      tab.set('tab', currentTabValue.tab.toString());
      navigate({ search: `?${tab.toString()}` });
    } else {
      const tab = new URLSearchParams(
        currentTabValue as Record<string, string>
      );
      setSearchParams(tab);
    }
  }, [currentTabValue.tab, location.pathname, location.search, navigate]);

  useEffect(() => {
    const currentTabIndex = tabData.findIndex(
      (item) => item.id === currentTabValue.tab
    );

    setTabData((prevTabData) =>
      prevTabData.map((tab, index) => {
        const isActive =
          tab.id === currentTabValue.tab || index < currentTabIndex;
        return { ...tab, isActive };
      })
    );
  }, [nextButtonClicked]);

  useEffect(() => {
    if (Object.keys(responses).length <= 0) {
      setCurrentTabValue({
        tab: convertToSnakeCase(ReadinessAssessmentTitle.BUSINESS_READINESS),
      });
    }
    setTabData(initialTabData);
  }, []);

  useEffect(() => {
    setTabData((prevTabData) =>
      prevTabData.map((tab) => ({
        ...tab,
        theme:
          (tab.id === currentTabValue.tab && 'primary') ||
          (tab.isCompleted && 'success') ||
          'default',
      }))
    );
  }, [currentTabValue]);

  const memoizedQuestions = useMemo(
    () => getQuestions(currentTabValue.tab),
    [currentTabValue.tab]
  );

  useEffect(() => {
    const updatedQuestionProgressData = memoizedQuestions.map((question) => ({
      isActive: Object.prototype.hasOwnProperty.call(
        responses,
        question.id.toString()
      ),
    }));
    const allActive = updatedQuestionProgressData.every(
      (item) => item.isActive
    );
    setQuestionProgressData(updatedQuestionProgressData);
    setNextButtonEnable(allActive);
  }, [responses, memoizedQuestions]);

  const handleFetchAssessmentError = () => {
    if (location.pathname.includes('public')) {
      navigate(`/${UserRouteType.BUSINESS_OWNER}/${RouteKey.LOGIN}`);
    } else {
      navigate(
        `/${getUserRouteType(loggedInUserData?.type as UserType)}/${
          RouteKey.DASHBOARD
        }`
      );
    }
  };
  useEffect(() => {
    if (loggedInUserData?.type === UserType.ADVISOR && id) {
      dispatch(
        fetchBusinessOwnerAssessment({
          tool: AssessmentTools.READINESS_ASSESSMENT,
          businessOwnerId: +id!,
          onError: handleFetchAssessmentError,
        })
      );
    } else if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchOwnAssessment({
          tool: AssessmentTools.READINESS_ASSESSMENT,
          onError: handleFetchAssessmentError,
        })
      );
    } else if (location.pathname.includes('public')) {
      dispatch(
        fetchPublicAssessmentByToken({
          tool: AssessmentTools.READINESS_ASSESSMENT,
          token: token as string,
          onError: handleFetchAssessmentError,
        })
      );
    }
  }, []);

  useEffect(() => {
    updateTabAndData(
      response,
      setResponses,
      setCurrentTabValue,
      setTabData,
      setNextButtonClicked,
      nextButtonClicked
    );
  }, [response]);

  if (assessmentToolStatus === AssessmentToolProgressStatus.COMPLETED) {
    return isLoading ? (
      <Spinner spinnerTheme='overlaySpinner' />
    ) : (
      <ThankYouPage
        token={token}
        isPasswordSet={isPasswordSet}
        loggedInUserData={loggedInUserData}
        businessOwnerProperties={businessOwnerProperties}
        pageContent={thankYouPageContent(
          'Readiness Assessment',
          loggedInUserData?.type || UserType.BUSINESS_OWNER,
          `${businessOwnerProperties?.first_name ?? ''} ${
            businessOwnerProperties?.last_name ?? ''
          }`
        )}
      />
    );
  }

  const height = location.pathname.includes('public')
    ? 'h-screen p-8 bg-gray-01'
    : 'h-[calc(100vh-9.6875rem)]';

  return (
    <div className={cx('flex flex-col', height)}>
      <h1 className='text-2xl font-semibold mb-3'>Readiness Assessment</h1>
      {isLoading ? (
        <Spinner customClassName='mt-4' size='sm' spinnerTheme='default' />
      ) : (
        <WithComments tool={AssessmentTools.READINESS_ASSESSMENT}>
          <div className='w-full relative border bg-white'>
            <div className='relative'>
              <div className='h-24 z-10 bg-white sticky w-full top-0 '>
                <StepperHeader
                  onHandleTabChange={(e) => handleHeaderClick(e)}
                  tabData={tabData}
                />
              </div>
              <div className=' sticky top-[6rem] z-10 bg-white'>
                <QuestionProgressBar buttonData={questionProgressData} />
                <h1 className='px-8 font-medium text-blue-500 sticky top-[9rem] z-10 mt-7'>
                  Respond to each question using a scale of 1 through 6 where 1
                  means you need a lot of help in this area and a 6 means that
                  you are on top of this area.
                </h1>
              </div>

              <AssessmentComponent
                ansData={[
                  { label: 1, value: 1 },
                  { label: 2, value: 2 },
                  { label: 3, value: 3 },
                  { label: 4, value: 4 },
                  { label: 5, value: 5 },
                  { label: 6, value: 6 },
                ]}
                questions={getQuestions(currentTabValue.tab)}
                responses={responses}
                onAnswerClick={handleAnswerClick}
              />
            </div>
            <div className='px-8 py-4'>
              <BackNextComponent
                backStep={() => {
                  handleBackButton();
                }}
                nextStep={() => {
                  handleNextButtonClick();
                }}
                buttonText={
                  currentTabValue.tab === ReadinessAssessmentTitle.PLANNING
                    ? (loggedInUserData?.type === UserType.BUSINESS_OWNER
                      ? 'Submit'
                      : 'Complete')
                    : 'Next'
                }
                isLoading={isLoading}
                isNextDisable={!nextButtonEnable}
                onSaveToDraftClick={() => handleFormSubmit(true)}
              />
            </div>
          </div>
        </WithComments>
      )}
    </div>
  );
};

export default ReadinessAssessment;
