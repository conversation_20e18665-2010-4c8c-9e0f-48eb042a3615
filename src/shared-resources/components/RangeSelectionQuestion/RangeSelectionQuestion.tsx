import classNames from 'classnames';
import React from 'react';
import { AnswerData } from 'views/Readiness-Assesment/ReadinessConfig';

interface Props {
  title: string;
  ansData: AnswerData[];
  onAnswerClick: (value: string | number) => void;
  selectedAnswer?: string | number;
  isActive: boolean;
  count: number | string;
}

const RangeSelectionQuestion: React.FC<Props> = ({
  title,
  ansData,
  onAnswerClick,
  selectedAnswer,
  isActive,
  count,
}) => {
  const handleAnswerClick = (value: string | number) => {
    if (isActive) {
      onAnswerClick(value);
    }
  };

  return (
    <div
      className={classNames({
        'opacity-50 pointer-events-none': !isActive,
      })}
    >
      <div className='flex space-x-6 mb-2'>
        <h1 className='font-medium'>{count}</h1>
        <h2 className='font-medium'>{title}</h2>
      </div>
      <div className='flex space-x-6 px-6'>
        {ansData.map(({ label, value }) => (
          <button
            key={label}
            type='button'
            onClick={() => handleAnswerClick(value)}
            className={classNames('h-12 w-24 rounded-xl border font-medium', {
              'bg-blue-02 text-blue-01 border-blue-01':
                value === selectedAnswer && isActive,
              'border-black-01': value !== selectedAnswer || !isActive,
            })}
            disabled={!isActive}
          >
            {label}
          </button>
        ))}
      </div>
    </div>
  );
};

export default RangeSelectionQuestion;
