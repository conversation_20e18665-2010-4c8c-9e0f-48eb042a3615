import { BusinessOwner } from 'models/entities/BusinessOwner';
import { AgeRange, AssessmentTools, RevenueRange } from 'types/enum';

export interface BusinessOwnerDTO {
  first_name: string;
  last_name?: string;
  email?: string;
  business_name: string;
  revenue?: RevenueRange | string;
  notes?: string;
  phone?: string;
  business_type?: string;
  age?: AgeRange | string;
  advisor_id?: number;
  tool?: AssessmentTools;
  business_start_date?: string;
  employee_count?: number;
  street_address?: string;
  zip_code?: string;
  management_team?: any;
}

export type UserProfileUpdateDTO = {
  old_password?: string;
  new_password?: string;
  business_name?: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  street_address?: string;
  zip_code?: string;
};

export interface BusinessOwnerListMetaResponse {
  total: number;
  currentPage: number;
  nextPageUrl: string;
  lastPage: number;
}

export interface BusinessOwnerListResponse {
  business_owners: BusinessOwner[];
  meta: BusinessOwnerListMetaResponse;
}

export interface BusinessOwnerDetailResponse {
  business_owner: BusinessOwner;
}

export interface BusinessOwnerTransferRequest {
  advisor_id: number;
}
