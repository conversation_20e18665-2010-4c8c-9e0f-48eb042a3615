import BusinessOwnersList from "shared-resources/components/BusinessOwner/BusinessOwnersList";
import { RouteKey, UserRouteType } from "types/enum";
import { SidebarRoutesConfigType } from "types/routes/routes.type";
import ProfilePage from "views/Profile/ProfilePage";

export const enterpriseAdminDashboardRoutes: Array<SidebarRoutesConfigType> = [
  {
    name: 'Business Owner List',
    key: `/${UserRouteType.ENT_ADMIN}/${RouteKey.BO_LIST}`,
    component: BusinessOwnersList,
    props: {
    showSecondaryAdvisor: false,
    },
  },
  {
      name: 'Enterprise Admin Profile',
      key: `/${UserRouteType.ENT_ADMIN}/${RouteKey.PROFILE}`,
      component: ProfilePage,
    },
  ];