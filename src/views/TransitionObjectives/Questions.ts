import {
  BusinessGoals,
  BusinessObjectives,
} from 'shared-resources/types/TransitionObjectives.type';

export type QuestionType = {
  id: number;
  question: string;
  note?: string;
  options?: {
    value: string;
    id: number;
    textField?: boolean;
    isLast?: boolean;
    question?: string;
  }[];
  headers?: string[];
  isIndentated?: boolean;
  isLastStep?: boolean;
  tableType?: 'stakeHolders' | 'affect' | 'objective';
  position:
    | 'centrallyAlignedRadio'
    | 'startAlignedCheckbox'
    | 'centrallyAlignedCheckbox'
    | 'table'
    | 'centrallyAlignedTextArea';
  answer?: any;
  isMultipleSelect?: boolean;
};

export function getQuestions(isCompleteOwner: boolean): QuestionType[] {
  return [
    {
      id: 1,
      question: 'Do you plan on exiting your business in the next 2-3 years',
      options: [
        { value: 'No', id: 2 },
        { value: 'Partially-Reduced Time', id: 3 },
        { value: 'Yes-Permanently', id: 4 },
      ],
      position: 'centrallyAlignedCheckbox',
    },
    {
      id: 2,
      question:
        'What statement is true about your ultimate transition from your business',
      options: [
        {
          value:
            'I have not given any thought to transitioning from my business.',
          id: 2.1,
        },
        {
          value:
            'I have thought about transitioning from my business but I have not given it a time frame yet.',
          id: 2.1,
        },
        {
          value:
            'I want to transition from my business in the next five to ten years.',
          id: 2.1,
        },
        {
          value:
            'I want to transition from my business in the next ten to fifteen years.',
          id: 2.1,
        },
        {
          value:
            'I don’t ever plan to transition from my business until they have to carry me out.',
          id: 2.1,
        },
      ],
      position: 'centrallyAlignedRadio',
    },
    {
      id: 2.1,
      question:
        'Since your ultimate business exit is not near-term, are there other transitions that you would welcome? (Check all applicable)',
      options: [
        {
          value:
            'Business is running smoothly and according to plan, so there are not really any business transitions that I desire.',
          id: 2.2,
        },
        {
          value: 'I would like for my business to grow.',
          id: 2.4,
        },
        {
          value: 'I would like for my business to be more profitable.',
          id: 2.4,
        },
        {
          value: 'I would like for my business to be more valuable.',
          id: 2.4,
        },
        {
          value: 'I would like to be able to work part time in the business.',
          id: 2.4,
        },
      ],
      isMultipleSelect: true,
      position: 'centrallyAlignedRadio',
    },
    {
      id: 2.2,
      question:
        'Do you feel that your business is currently as valuable as it can be? i.e. would you get top dollar (multiple) in your industry if you sold the business?',
      options: [
        {
          value: 'Yes',
          id: 2.3,
          isLast: true,
        },
        {
          value: 'No',
          id: 2.3,
        },
      ],
      position: 'centrallyAlignedRadio',
    },
    {
      id: 2.3,
      question:
        'We laud your honesty - very few businesses operate at their top potential value. Regarding the value of the business please check all statements below that apply.',
      options: [
        {
          value:
            'I don’t want to leave money on the table when I ultimately sell the business.',
          id: 2.4,
        },
        {
          value:
            'I want a more valuable business currently in order have more options.',
          id: 2.4,
        },
        {
          value:
            'I know how to increase the value of my business beyond just growing the bottom line.',
          id: 2.4,
        },
        {
          value:
            'I do not know how to increase the value of my business beyond just growing the bottom line.',
          id: 2.4,
        },
      ],
      isMultipleSelect: true,
      position: 'centrallyAlignedRadio',
    },
    {
      id: 2.4,
      question:
        'The non-exit objectives of most Business Owners are accomplished in parallel with building the value of the business. Please record all relevant comments to the following common Business Owner transition objectives.',
      options: [
        {
          value: 'I want my business to operate smoother - too bumpy. ',
          id: 2.5,
          textField: true,
          question: 'What would smoother look like to you?',
        },
        {
          value: 'I would like for my business to grow.',
          id: 2.5,
          textField: true,
          question: 'What growth you would like to achieve?',
        },
        {
          value: 'I would like for my business to be more profitable.',
          id: 2.5,
          textField: true,
          question: 'What profitability you would like to achieve?',
        },
        {
          value: 'I would like for my business to be more valuable.',
          id: 2.5,
          textField: true,
          question: 'What business value would you like to achieve?',
        },
        {
          value: 'I would like to be able to work part time in the business.',
          id: 2.5,
          textField: true,
          question: 'Describe the work schedule you would like to achieve?',
        },
      ],
      position: 'centrallyAlignedRadio',
    },
    {
      id: 2.5,
      question:
        'What is a realistic and acceptable time frame for achieving each of the following objectives',
      options: [
        {
          value: '',
          id: 2.6,
          textField: true,
        },
      ],
      headers: [
        'Objective',
        'Objective (Yes or No)',
        'Within the next Year',
        'Within the next two years',
        'Have a Plan for this (Yes or No)',
        'Other (specify)',
      ],

      position: 'table',
      tableType: 'objective',
    },
    {
      id: 2.6,
      question:
        'Do you have a personal financial plan in place that includes the value of your business',
      options: [
        {
          value: 'Yes',
          id: 2.7,
        },
        {
          value: 'No',
          id: 2.7,
        },
      ],
      position: 'centrallyAlignedRadio',
    },
    {
      id: 2.7,
      question:
        'Given that you will exit your business at some point, do you have any thoughts about who you might want to transition the business to',
      options: [
        {
          value: 'I have not given this any thought',
          id: 2.8,
        },
        {
          value: 'I have thought about it but don’t have any conclusion',
          id: 2.6,
        },
        {
          value:
            'I don’t know enough about the ramifications of different buyer types to be able to form a conclusion about who to transition the business',
          id: 2.6,
        },
        {
          value: 'I want to transition the business to a family member(s).',
          id: 2.6,
        },
        {
          value: 'I want to transition the business to  key employee(s).',
          id: 2.6,
        },
        {
          value: 'I want to transition the business to a third party buyer.',
          id: 2.6,
        },
        {
          value: 'I want to transition the business to the highest buyer.',
          id: 2.6,
        },
        {
          value: 'Other',
          id: 2.6,
          textField: true,
        },
      ],
      isLastStep: true,
      position: 'centrallyAlignedRadio',
    },
    {
      id: 3,
      question: 'How much time do you want to work?',
      options: [
        {
          value: 'I want to work 10hrs/week',
          id: 5,
        },
        { value: 'I want to work 20hrs/week', id: 5 },
        { value: 'Other', id: 5, textField: true },
      ],
      position: 'centrallyAlignedRadio',
    },
    {
      id: 4,
      question:
        'What does your exit look like (i.e. sale, business closure, other)',
      options: [{ value: 'Please specify', id: 4.1 }],
      position: 'centrallyAlignedTextArea',
    },
    {
      id: 4.1,
      question: 'When do you want to transition out of the business',
      options: [
        { value: 'In less than one year', id: 7 },
        { value: 'In about a year', id: 7 },
        { value: 'In two years', id: 7 },
        { value: 'In three years', id: 7 },
        { value: 'In four or more years', id: 7 },
        { value: 'As soon as I can afford', id: 7 },
        { value: 'Other', id: 7, textField: true },
      ],
      position: 'centrallyAlignedRadio',
    },
    {
      id: 5,
      question: 'When would you like to start working reduced hours?',
      options: [
        { value: 'Immediately', id: 6 },
        { value: 'In six months', id: 6 },
        { value: 'In one year', id: 6 },
        { value: 'In two years', id: 6 },
        { value: 'As soon as I can realistically do it.', id: 6 },
        { value: 'Other', id: 6 },
      ],
      position: 'centrallyAlignedRadio',
    },
    {
      id: 6,
      question:
        'What do you want to do at the business with this reduced schedule?',
      options: [{ value: 'Please specify', id: 7 }],
      position: 'centrallyAlignedTextArea',
    },
    {
      id: 7,
      question: 'Regarding your timing, which of the following is true?',
      options: [
        {
          value:
            'The timing of my transition is most important to me, regardless of financial considerations',
          id: 8,
        },
        {
          value:
            'I intend to stay until I can get what I need from the business',
          id: 8,
        },
        {
          value:
            'I intend to stay until I can get what I want from the business',
          id: 8,
        },
        { value: 'Other', id: 8, textField: true },
      ],
      position: 'centrallyAlignedRadio',
    },
    {
      id: 8,
      question: 'What is your plan for life after transition?',
      note: 'This is perhaps the most important question of this Questionnaire.',
      options: [{ value: 'Please specify', id: 9 }],
      position: 'centrallyAlignedTextArea',
    },
    {
      id: 9,
      question:
        'Have you discussed your life-after transition with those who know you best?',
      options: [
        { value: 'Yes, I have', id: 9.1 },
        { value: 'No, I have not', id: 9.2 },
      ],
      isIndentated: true,
      position: 'startAlignedCheckbox',
    },
    {
      id: 9.1,
      question:
        'What has been the reaction to those you have shared your transition objectives with?',
      options: [
        {
          value:
            'They think that my plans for the next chapter of my life will be fulfilling for me.',
          id: 10,
        },
        { value: 'They think I need to give this a lot more thought.', id: 10 },
        {
          value: 'Other',
          id: 10,
          textField: true,
        },
      ],
      position: 'centrallyAlignedRadio',
    },
    {
      id: 9.2,
      question:
        'Do you have plans to talk about your post-transition life with those who know you best?',
      options: [
        {
          value: 'Not really, I don’t feel like I need their input.',
          id: 10,
        },
        {
          value:
            'I plan to talk with them. I just have not gotten around to it yet.',
          id: 10,
        },
        {
          value: 'Other',
          id: 10,
          textField: true,
        },
      ],
      position: 'centrallyAlignedRadio',
    },
    {
      id: 10,
      question: 'Why do you want this transition? (Check all that apply)',
      options: [
        {
          value:
            'I want to retire while I am young enough and healthy enough to enjoy it',
          id: 11,
        },
        {
          value: 'I want a different lifestyle',
          id: 11,
        },
        {
          value: 'I no longer enjoy owning the business',
          id: 11,
        },
        {
          value: `It's just time - I am burned out`,
          id: 11,
        },
        {
          value: 'Age',
          id: 11,
        },
        {
          value: 'Health',
          id: 11,
        },
        { value: 'Market Conditions', id: 11 },
        { value: 'I want to spend more time with my family', id: 11 },
        { value: 'Divorce', id: 11 },

        { value: 'Disagreement', id: 11 },

        { value: 'Additional', id: 11, textField: true },
      ],
      position: 'centrallyAlignedRadio',
      isMultipleSelect: true,
    },
    isCompleteOwner
      ? {
          id: 11,
          question:
            'Have you shared your transition objectives with your key stakeholders? (Please fill out grid.)',
          position: 'table',
          tableType: 'stakeHolders',
          options: [
            { value: 'noHaveDiscussed', id: 12.1 },
            { value: 'noInAgreement', id: 12.2 },
            { value: 'noHaveDiscussedInAgreement', id: 12.3 },
            { value: 'noYesValue', id: 13 },
          ],
          headers: [
            'Stakeholder',
            'Name',
            'Have Discussed (Yes/No)',
            'In Agreement (Yes/No)',
          ],
        }
      : {
          id: 11,
          question:
            'What is the level of agreement among the other owners about your transition?',
          options: [
            { value: 'We have not had any discussions yet', id: 12.1 },
            {
              value:
                'We have discussed my transition and we are in 100% agreement',
              id: 13,
            },
            {
              value:
                'We have discussed my transition and we are not in agreement',
              id: 12.2,
              textField: true,
              question: 'Please specify',
            },
            { value: 'Other', id: 13, textField: true },
          ],
          position: 'centrallyAlignedRadio',
        },
    isCompleteOwner
      ? {
          id: 12.1,
          question:
            'When do you plan on discussing your transition objectives with the key stakeholders you have not talked with yet?',
          options: [
            {
              value:
                'I don’t have a plan yet to discuss my objectives with them ',
              id: 13,
            },
            {
              value:
                'I am ready to discuss my transition objectives now, just have not gotten around to it yet.',
              id: 13,
            },
            {
              value:
                'I am putting this off because I am anticipating it not going well.',
              id: 13,
            },

            { value: 'Other', id: 13, textField: true },
          ],
          position: 'centrallyAlignedRadio',
        }
      : {
          id: 12.1,
          question:
            'When do you plan on discussing your transition objectives with the other owner(s)?',
          options: [
            {
              value:
                'I don’t have a plan yet to discuss my objectives with the other owner(s)',
              id: 13,
            },
            {
              value:
                'I am ready to discuss my transition objectives now, just have not gotten around to it yet.',
              id: 13,
            },
            {
              value:
                'I am putting this off because I am anticipating it not going well.',
              id: 13,
            },

            { value: 'Other', id: 13, textField: true },
          ],
          position: 'centrallyAlignedRadio',
        },
    isCompleteOwner
      ? {
          id: 12.2,
          question:
            'What are your intentions relating to the disagreement with your transition objectives by any of your key stakeholders ?',
          options: [
            {
              value:
                'I intend to move forward with my transition objectives regardless of the consequences.',
              id: 13,
            },
            {
              value:
                'I am convinced I can work out an acceptable compromise with any currently non-aligned stakeholder.',
              id: 13,
            },
            {
              value:
                'I will not move forward without key stakeholder agreement.',
              id: 13,
            },
            { value: 'We will need to seek an arbitrator.', id: 13 },
            { value: 'Other', id: 13, textField: true },
          ],
          position: 'centrallyAlignedRadio',
        }
      : {
          id: 12.2,
          question:
            'What are your intentions relating to the disagreement with the other owner(s) about your transition objectives?',
          options: [
            {
              value:
                'I intend to move forward with my transition objectives regardless of the consequences.',
              id: 13,
            },
            {
              value: 'I am convinced we can work out an acceptable compromise.',
              id: 13,
            },
            { value: 'We will need to seek an arbitrator.', id: 13 },
            { value: 'Other', id: 13, textField: true },
          ],
          position: 'centrallyAlignedRadio',
        },
    {
      id: 12.3,
      question:
        'When do you plan on discussing your transition objectives with the key stakeholders you have not talked with yet?',
      options: [
        {
          value: 'I don’t have a plan yet to discuss my objectives with them ',
          id: 12.2,
        },
        {
          value:
            'I am ready to discuss my transition objectives now, just have not gotten around to it yet.',
          id: 12.2,
        },
        {
          value:
            'I am putting this off because I am anticipating it not going well.',
          id: 12.2,
        },

        { value: 'Other', id: 12.2, textField: true },
      ],
      position: 'centrallyAlignedRadio',
    },
    {
      id: 13,
      question:
        'Do you know how much money you need in order to have the transition you are envisioning?',
      options: [
        { value: 'Don’t know', id: 13.1 },
        { value: 'Yes, I do know', id: 13.2 },
        { value: `Doesn't matter, I already have enough money`, id: 14 },
      ],
      position: 'centrallyAlignedRadio',
    },
    {
      id: 13.1,
      question: 'If you don’t know (Check all that apply)',
      options: [
        { value: 'I have actually never thought about it', id: 14 },
        { value: 'I know that I need to work on my financial plan', id: 14 },
        { value: `Other`, id: 14, textField: true },
      ],
      position: 'centrallyAlignedRadio',
      isMultipleSelect: true,
    },
    {
      id: 13.2,
      question: 'Yes, I know',
      options: [
        { value: 'I determined it on my own', id: 14 },
        {
          value: 'I work with a financial advisor and we know this number',
          id: 14,
        },
      ],
      position: 'centrallyAlignedRadio',
    },
    {
      id: 14,
      question:
        'Do you know how much money, if any, you will need from the sale of your business to fund your post-transition lifestyle?',
      options: [
        { value: `I don't have any idea`, id: 15 },
        {
          value: `Doesn't matter, I have enough money outside of the business`,
          id: 15,
        },
        {
          // add input filed for this
          value: 'I think I will need about - please enter a number',
          id: 14.1,
          textField: true,
        },
      ],
      position: 'centrallyAlignedRadio',
    },
    {
      id: 14.1,
      question: 'How did you arrive at this number?',
      options: [{ value: 'Please specify', id: 15 }],
      position: 'centrallyAlignedTextArea',
    },
    {
      id: 15,
      question: 'Do you know the value of your business?',
      options: [
        { value: 'Yes', id: 15.1 },
        { value: 'No', id: 16 },
      ],
      position: 'startAlignedCheckbox',
    },
    {
      id: 15.1,
      question: 'I know this because:',
      options: [
        {
          value: 'I have had a business valuation in the past year',
          id: 15.2,
        },
        { value: 'Conversation with peers', id: 15.2 },
        { value: 'I know what businesses in my industry sell for', id: 15.2 },
        { value: 'I know stuff like this', id: 15.2 },
        { value: 'Other', id: 15.2, textField: true },
      ],
      position: 'centrallyAlignedRadio',
    },
    {
      id: 15.2,
      question: 'What do you think the current value of the business is',
      options: [{ value: 'Please specify', id: 16 }],
      position: 'centrallyAlignedTextArea',
    },
    {
      id: 16,
      question:
        'Is there a specific person or company to whom you intend to sell your business?',
      options: [
        { value: 'Yes', id: 16.1 },
        { value: 'No', id: 16.2 },
      ],
      isIndentated: true,
      position: 'startAlignedCheckbox',
    },
    {
      id: 16.1,
      question:
        'Have you discussed the potential terms of the sale with this person?',
      options: [
        { value: 'No, we have not discussed details', id: 17 },
        { value: 'Yes, we have talked about potential terms', id: 17 },

        {
          value:
            'If Yes, please specify what the discussions have been about so far',
          id: 17,
          textField: true,
        },
      ],
      position: 'centrallyAlignedRadio',
    },
    {
      id: 16.2,
      question:
        'If you do not have a specific buyer in mind, which types of buyers would be of interest to you? (check all buyer types that you would consider)',
      options: [
        { value: 'Family', id: 17 },
        { value: 'Key Employee(s)', id: 17 },
        {
          value: 'Outsider brought in for internal transfer',
          id: 17,
        },
        { value: 'External third party', id: 17 },
        { value: 'Competitor', id: 17 },
        { value: 'Financial Buyer (Private Equity)', id: 17 },
        { value: 'Strategic', id: 17 },
        { value: 'Business Liquidation', id: 17 },
      ],
      position: 'centrallyAlignedRadio',
      isMultipleSelect: true,
    },
    {
      id: 17,
      question:
        'Which of the following considerations are true for you regarding a potential buyer? (Check all applicable)',
      options: [
        { value: 'I will sell to anyone who will give me my price', id: 18 },
        {
          value:
            'I am more concerned about the right buyer than I am about price.',
          id: 18,
        },
        {
          value:
            'I will only sell to a buyer who aligns with our existing culture',
          id: 18,
        },
        {
          value:
            'I will only sell to a buyer who will keep my Leadership Team in place',
          id: 18,
        },
        { value: 'I will only sell to someone I know', id: 18 },
        { value: 'Other', id: 18, textField: true },
      ],
      position: 'centrallyAlignedRadio',
      isMultipleSelect: true,
    },
    {
      id: 18,
      question:
        'Will any of the following constituents affect your transition objectives?',
      position: 'table',
      tableType: 'affect',
      headers: ['Constituent', 'Will influence my objectives (Yes/No)'],
      options: [{ value: '', id: 18 }],
      isLastStep: true,
    },
  ];
}

export function compareBusinessObjectives(
  obj1?: BusinessObjectives,
  obj2?: BusinessObjectives
): boolean {
  if (!obj1 && !obj2) {
    return true;
  }
  if (!obj1 || !obj2) {
    return false;
  }
  const objKeys1 = Object.keys(obj1) as Array<keyof BusinessObjectives>;
  const objKeys2 = Object.keys(obj2) as Array<keyof BusinessObjectives>;

  if (objKeys1.length !== objKeys2.length) {
    return false;
  }
  return (
    objKeys1.every(
      (key) => objKeys2.includes(key) && obj1[key] === obj2[key]
    ) &&
    objKeys2.every((key) => objKeys1.includes(key) && obj2[key] === obj1[key])
  );
}
export function compareBusinessGoals(
  goal1: BusinessGoals,
  goal2: BusinessGoals
): boolean {
  if (goal1 && goal2) {
    const keys1 = Object.keys(goal1) as Array<keyof BusinessGoals>;
    const keys2 = Object.keys(goal2) as Array<keyof BusinessGoals>;

    if (keys1.length !== keys2.length) {
      return false;
    }

    return keys1.every(
      (key) =>
        keys2.includes(key) && compareBusinessObjectives(goal1[key], goal2[key])
    );
  }
  return false;
}

export const initialAffectTableValues = {
  Family: { willInfluence: null },
  Employees: { willInfluence: null },
  Community: { willInfluence: null },
  Charity: { willInfluence: null },
  'Religious Affiliations': { willInfluence: null },
  Legacy: { willInfluence: null },
};

export const initialStakeHoldersValue = {
  'Spouse, Significant Other': {
    name: '',
    haveDiscussed: null,
    inAgreement: null,
  },
  'Key Employee(s)': {
    name: '',
    haveDiscussed: null,
    inAgreement: null,
  },
  'Child 1': { name: '', haveDiscussed: null, inAgreement: null },
  'Child 2': { name: '', haveDiscussed: null, inAgreement: null },
  'Child 3': { name: '', haveDiscussed: null, inAgreement: null },
  'Lender(s)': { name: '', haveDiscussed: null, inAgreement: null },
  Other: { name: '', haveDiscussed: null, inAgreement: null },
  Others: { name: '', haveDiscussed: null, inAgreement: null },
  stakeHolderNotes: {
    notes: '',
  },
};

export const intialDefaultTableValues = {
  'Smoother Operations': {
    objective: null,
    withinNextYear: '',
    withinNextTwoYear: '',
    havePlan: null,
    other: '',
  },
  'Business Growth': {
    objective: null,
    withinNextYear: '',
    withinNextTwoYear: '',
    havePlan: null,
    other: '',
  },
  'Profitability Growth': {
    objective: null,
    withinNextYear: '',
    withinNextTwoYear: '',
    havePlan: null,
    other: '',
  },
  'Value Growth': {
    objective: null,
    withinNextYear: '',
    withinNextTwoYear: '',
    havePlan: null,
    other: '',
  },
  'Work Less': {
    objective: null,
    withinNextYear: '',
    withinNextTwoYear: '',
    havePlan: null,
    other: '',
  },
};

export function initializeTableValues(questionObj: any): BusinessGoals {
  if (questionObj?.position === 'table' && questionObj?.answer) {
    return questionObj?.answer;
  }
  if (questionObj?.tableType === 'affect') {
    return initialAffectTableValues;
  }
  if (questionObj?.tableType === 'stakeHolders') {
    return initialStakeHoldersValue;
  }
  return intialDefaultTableValues;
}
