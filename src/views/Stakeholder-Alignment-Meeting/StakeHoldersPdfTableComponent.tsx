import React, { FC, memo, useMemo } from 'react';
import { TableValueType } from './Questions';

type StakeHoldersPdfTableComponentProps = {
  tableValues: TableValueType[];
  isReportBuilder?: boolean; // Add this prop
};

const StakeHoldersPdfTableComponent: FC<StakeHoldersPdfTableComponentProps> = ({
  tableValues,
  isReportBuilder = false, // Destructure the new prop with a default value
}) => {
  const attendeeValues = useMemo(() => {
    const minLength = 8;
    const targetLength = Math.max(minLength, tableValues.length);

    return Array.from({ length: targetLength }, (_, index) => {
      if (index < tableValues.length) {
        return tableValues[index];
      }

      return {
        id: Math.max(0, ...tableValues.map((v) => v.id)) + index + 1,
        name: '',
        equityOwned: 0,
        relationship: '',
        invite: false,
        comment: '',
      };
    });
  }, [tableValues]);

  return (
    <div className='overflow-auto scrollbar relative mb-4'>
      <table>
        <thead className='sticky top-0 z-10'>
          <tr className='shadow-blue-border rounded-t-lg border-t-0 sticky'>
            <th
              className={`bg-blue-01 py-4 px-2 ${
                isReportBuilder ? '' : 'w-96'
              } rounded-ss-lg border-r-2 border-gray-500 text-white`}
            >
              StakeHolder Name
            </th>
            <th
              className={`bg-blue-01 px-2 py-4 ${
                isReportBuilder ? '' : 'w-72'
              } border-r-2 border-gray-500 text-white`}
            >
              Equity Owned (if Any)
            </th>
            <th
              className={`bg-blue-01 px-2 py-4 ${
                isReportBuilder ? '' : 'w-72'
              } border-r-2 border-gray-500 text-white`}
            >
              Relationship To Owner Or Business
            </th>
            <th
              className={`bg-blue-01 px-2 py-4 ${
                isReportBuilder ? '' : 'w-72'
              } border-r-2 border-gray-500 text-white`}
            >
              Aligned (Yes/No)
            </th>
            <th
              className={`bg-blue-01 px-2 py-4 ${
                isReportBuilder ? '' : 'w-72'
              } rounded-se-lg border-gray-500 text-white`}
            >
              Comments
            </th>
          </tr>
        </thead>
        <tbody>
          {attendeeValues?.map((tableValObj: TableValueType) => (
            <tr key={tableValObj?.id} className='w-full min-h-14 h-full'>
              <td
                className='p-2 justify-start
                 border-2 border-gray-500 h-full w-1/5'
              >
                <div className='mx-auto border-black-01 w-full min-h-6'>
                  {tableValObj?.name}
                </div>
              </td>
              <td className='border-2 border-gray-500 border-l-0 p-2'>
                <div className='mx-auto border-black-01 w-full '>
                  {tableValObj?.equityOwned ? tableValObj?.equityOwned : ''}
                </div>
              </td>
              <td className='border-2 border-gray-500 w-1/5 flexborder-l-0 p-2'>
                <div className='mx-auto border-black-01 w-full '>
                  {tableValObj?.relationship}
                </div>
              </td>
              <td className='border-2 border-gray-500 border-l-0 p-2'>
                <div className='mx-auto border-black-01 w-full '>
                  {tableValObj?.invite === true && 'Yes'}
                  {tableValObj?.name
                    ? tableValObj?.invite === false && 'No'
                    : ''}
                </div>
              </td>
              <td className='border-2 border-gray-500 border-l-0 p-2 w-[30%]'>
                <div className='mx-auto border-black-01 w-full '>
                  {tableValObj?.comment}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default memo(StakeHoldersPdfTableComponent);
