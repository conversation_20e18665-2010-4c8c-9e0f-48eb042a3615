export enum BusinessObjectivesKeys {
  Name = 'name',
  HaveDiscussed = 'haveDiscussed',
  InAgreement = 'inAgreement',
  WillInfluenece = 'willInfluence',
  Objective = 'objective',
  WithinNextYear = 'withinNextYear',
  WithinNextTwoYear = 'withinNextTwoYear',
  HavePlan = 'havePlan',
  Other = 'other',
  Others = 'others',
}

export type BusinessObjectives = {
  name?: string;
  haveDiscussed?: boolean | null;
  inAgreement?: boolean | null;
  willInfluence?: boolean | null;
  objective?: boolean | null;
  withinNextYear?: string;
  withinNextTwoYear?: string;
  havePlan?: boolean | null;
  other?: string;
  others?: string;
  notes?: string;
};

export type BusinessGoals = {
  'Spouse, Significant Other'?: BusinessObjectives;
  'Key Employee(s)'?: BusinessObjectives;
  'Child 1'?: BusinessObjectives;
  'Child 2'?: BusinessObjectives;
  'Child 3'?: BusinessObjectives;
  'Lender(s)'?: BusinessObjectives;
  Other?: BusinessObjectives;
  Others?: BusinessObjectives;
  stakeHolderNotes?: BusinessObjectives;
  Family?: BusinessObjectives;
  Employees?: BusinessObjectives;
  Community?: BusinessObjectives;
  Charity?: BusinessObjectives;
  'Religious Affiliations'?: BusinessObjectives;
  Legacy?: BusinessObjectives;
  'Smoother Operations'?: BusinessObjectives;
  'Business Growth'?: BusinessObjectives;
  'Profitability Growth'?: BusinessObjectives;
  'Value Growth'?: BusinessObjectives;
  'Work Less'?: BusinessObjectives;
};

export type ObjectiveKeys = keyof BusinessGoals;
