export interface ListingType {
  records: {
    ids: number[];
    loading?: boolean;
    error?: string;
  };
}

export interface ListingPayloadType {
  ids?: number[];
}

export interface ListResponseType {
  meta: { total: number };
}

export interface PagesDataType {
  totalRecords: number;
  currentPage: number;
  pages: {
    [x: number]: {
      ids: number[];
      loading?: boolean;
      error?: string;
    };
  };
}
