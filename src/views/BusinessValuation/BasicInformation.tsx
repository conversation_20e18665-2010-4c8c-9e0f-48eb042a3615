import { Form, Formik } from 'formik';
import { User } from 'models/entities/User';
import React, { useEffect, useRef } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router';
import 'react-tooltip/dist/react-tooltip.css';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import InputLabel from 'shared-resources/components/InputLabel/InputLabel';
import InputHelper from 'shared-resources/components/Inputhelper/InputHelper';
import { FormikSelect } from 'shared-resources/components/Select/FormikSelect';
import IndustrySearchableSelect from 'shared-resources/components/Select/IndustrySearchableSelect';
import { fetchBusinessOwner } from 'store/actions/business-owner.action';
import { useParamSelector } from 'store/selectors/base.selectors';
import {
  getBusinessOwnerDetails,
  updateBusinessOwnerErrors,
  updateBusinessOwnerLoading,
} from 'store/selectors/business-owner.selector';
import { OwnershipTypes, UserType, ValuationReasons } from 'types/enum';
import {
  getOptionsFromEnum,
  isRequiredFieldDependingOnValidationSchema,
} from 'utils/helpers/Helpers';
import * as yup from 'yup';

interface Props {
  nextStep: (values: any) => void;
  backStep: () => void;
  setData: (data: any) => void;
  data: any;
  user?: User;
  businessOwnerProperties: any;
}
const BasicInformation: React.FC<Props> = (props) => {
  const { nextStep, setData, backStep, user, data, businessOwnerProperties } =
    props;
  const { id } = useParams();
  const dispatch = useDispatch();

  const businessOwner = useParamSelector(getBusinessOwnerDetails, {
    id: +id!,
  });

  useEffect(() => {
    if (id && user?.type !== UserType.BUSINESS_OWNER) {
      dispatch(fetchBusinessOwner(id));
    }
  }, []);

  const isSubmitting = useSelector(updateBusinessOwnerLoading);

  const userEmail =
    user?.type === UserType.BUSINESS_OWNER ? user?.email : businessOwner?.email;

  const validationSchema = yup.object().shape({
    business_name: yup.string().required('Business name is required'),
    business_start_date: yup.string().nullable(),
    employee_count: yup.number().typeError('Must be a valid number').nullable(),
    street_address: yup.string().nullable(),
    business_type: yup
      .string()
      .required('Industry Name or NAICS Code is required'),
    zip_code: yup
      .string()
      .matches(
        /^(\d{5}[-\s]?(?:\d{4})?|[A-Za-z]\d[A-Za-z][-\s]?\d[A-Za-z]\d)$/gm,
        'ZIP (Postal) Code not from US or Canadian region'
      )
      .required('ZIP (Postal) Code is required'),
    first_name: yup.string().required('Contact Person First Name is required'),
    last_name: yup.string().required('Contact Person Last Name is required'),
    phone: yup
      .string()
      .matches(
        /^(\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/,
        'Phone number not from US region'
      )
      .nullable(),
    type_of_ownership: yup.string().required('Ownership Type is required'),
    reason_for_valuation: yup
      .string()
      .required('Reason for Valuation is required'),
  });
  const initialValue = {
    business_name: businessOwnerProperties?.business_name || '',
    business_start_date: businessOwnerProperties?.business_start_date || '',
    employee_count: businessOwnerProperties?.employee_count || '',
    street_address: businessOwnerProperties?.street_address || '',
    business_type: businessOwnerProperties?.business_type || '',
    zip_code: businessOwnerProperties?.zip_code || '',
    first_name: businessOwnerProperties?.first_name || '',
    last_name: businessOwnerProperties?.last_name || '',
    email: userEmail || '',
    phone: businessOwnerProperties?.phone || '',
    type_of_ownership: data?.basic_information?.type_of_ownership || '',
    reason_for_valuation: data?.basic_information?.reason_for_valuation || '',
  };

  let ownershipOptions = getOptionsFromEnum(OwnershipTypes);
  const valuationReasonOptions = getOptionsFromEnum(ValuationReasons);

  ownershipOptions = ownershipOptions.map((option) => {
    if (option.value === OwnershipTypes.LLC) {
      return {
        label: 'LLC',
        value: OwnershipTypes.LLC,
      };
    }
    return option;
  });

  const handleSubmit = (values: any) => {
    setData(values);
    nextStep(values);
  };

  const formRef: any = useRef();

  const updateBOErrors: any = useSelector(updateBusinessOwnerErrors);

  useEffect(() => {
    if (updateBOErrors) {
      updateBOErrors.map((error: any) =>
        formRef.current.setFieldError(
          error.field,
          error.message.replace('_', ' ')
        )
      );
    }
  }, [updateBOErrors]);

  return (
    <div className='flex flex-col'>
      <Formik
        innerRef={formRef}
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ setFieldValue, values, errors, touched }) => (
          <Form className='px-5 py-3 h-full bg-white'>
            <h2 className='text-xl font-semibold'>Basic Information</h2>
            <div className='grid grid-cols-3 items-center justify-center gap-x-8 mt-5 h-[calc(100vh-20rem)] overflow-auto scrollbar text-[1rem]'>
              <FormikInput
                name='business_name'
                key='business_name'
                label='Business Name'
                labelClassName='mb-1'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'business_name'
                )}
              />
              <div className='flex flex-col '>
                <InputLabel className='mb-3' label='Business Start Date' />
                <div className='h-13 mb-5  border rounded-xl  border-gray-02'>
                  <DatePicker
                    className='!rounded-xl focus:outline-none py-2.5 px-5'
                    selected={values.business_start_date}
                    onChange={(date) => {
                      setFieldValue('business_start_date', date);
                    }}
                  />
                </div>
              </div>
              <FormikInput
                type='number'
                name='employee_count'
                key='employee_count'
                label='Number Of Employees'
                labelClassName='mb-1'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'employee_count'
                )}
              />
              <FormikInput
                name='street_address'
                key='street_address'
                label='Business Address'
                labelClassName='mb-1'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'street_address'
                )}
              />
              <div className='flex flex-col h-fit'>
                <IndustrySearchableSelect
                  key='business_type'
                  placeHolder='Select Industry'
                  onChange={(value: string) => {
                    setFieldValue('business_type', value);
                  }}
                  selectedIndustry={values.business_type}
                  label='Type Of Business'
                  asterisk={isRequiredFieldDependingOnValidationSchema(
                    validationSchema,
                    'business_type'
                  )}
                />
                <div className='h-3'>
                  {errors?.business_type && touched?.business_type && (
                    <InputHelper
                      type='error'
                      text={errors?.business_type as string}
                    />
                  )}
                </div>
              </div>
              <FormikInput
                name='zip_code'
                key='zip_code'
                label='ZIP (Postal) Code'
                labelClassName='mb-1'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'zip_code'
                )}
              />
              <FormikInput
                name='first_name'
                key='first_name'
                label='Contact Person First Name'
                labelClassName='mb-1'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'first_name'
                )}
              />
              <FormikInput
                name='last_name'
                key='last_name'
                label='Contact Person Last Name'
                labelClassName='mb-1'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'last_name'
                )}
              />
              <FormikInput
                name='email'
                key='email'
                disabled
                label='Email Address'
                labelClassName='mb-1'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'email'
                )}
              />
              <FormikInput
                name='phone'
                key='phone'
                label='Phone Number'
                labelClassName='mb-1'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'phone'
                )}
              />
              <FormikSelect
                menuPlacement='top'
                name='type_of_ownership'
                label='Type Of Ownership'
                options={ownershipOptions}
                labelClassName='mb-1 !text-[1rem]'
                optionClassname='!py-1 !px-1'
                placeholder='Select Ownership Type'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'type_of_ownership'
                )}
              />
              <FormikSelect
                menuPlacement='top'
                label='Reason For Valuation'
                name='reason_for_valuation'
                options={valuationReasonOptions}
                labelClassName='mb-1 !text-[1rem]'
                optionClassname='!py-1 !px-1'
                placeholder='Select Reason'
                asterisk={isRequiredFieldDependingOnValidationSchema(
                  validationSchema,
                  'reason_for_valuation'
                )}
              />
            </div>

            <div className='flex justify-between'>
              <Button
                onClick={() => backStep()}
                className='rounded-xl px-7 py-2  font-medium'
                theme='secondary'
                type='button'
              >
                Back
              </Button>
              <Button
                className='rounded-xl px-7 py-2  font-medium'
                type='submit'
                isSubmitting={isSubmitting}
              >
                Next
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default BasicInformation;
