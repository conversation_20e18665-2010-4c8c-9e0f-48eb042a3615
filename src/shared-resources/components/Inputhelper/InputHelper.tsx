import classNames from 'classnames';
import React from 'react';

export interface InputHelperProps {
  type?: 'error' | 'helper' | 'info';
  text?: string;
}

const InputHelper: React.FC<InputHelperProps> = ({ text, type = 'helper' }) =>
  text ? (
    <p
      className={classNames('text-sm', {
        'text-red-600': type === 'error',
        'text-gray-500': type === 'helper',
        'text-blue-600': type === 'info',
      })}
      id='input-error'
    >
      {text}
    </p>
  ) : null;

export default React.memo(InputHelper);
