import { AssessmentData } from 'types/business-owner.types';
import { AssessmentTools, OwnerType } from 'types/enum';
import { ReportResponse } from 'types/report-builder.type';
import { User } from './User';

export interface BusinessOwner extends User {
  business_name: string;
  business_type?: string;
  revenue?: string;
  notes?: string;
  age: string;
  assessment_data: AssessmentData;
  created_at: string;
  street_address?: string;
  zip_code?: string;
  employee_count?: number;
  business_start_date?: string;
  last_activity_date?: string;
  additional_advisor_email?: string;
  payment_details: Record<AssessmentTools, boolean>;
  metadata: { ownership?: OwnerType; business_continuity_updated_at?: string };
  primary_advisor_id: number;
  primary_advisor_email?: string;
  primary_advisor_name?: string;
  reports?: ReportResponse[];
  secondary_advisor_count?: number;
}
