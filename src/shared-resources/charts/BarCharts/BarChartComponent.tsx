import React, { useMemo } from 'react';
import {
  Bar,
  BarChart,
  CartesianGrid,
  ReferenceLine,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import { BarChartComponentProps } from '../../../types/chartProps.types';

const BarChartComponent: React.FC<BarChartComponentProps> = ({
  data,
  bars,
  chartHeight,
  chartWidth,
  maxBarSize,
  cartesianProps,
  yAxisProps,
  xAxisProps,
  referenceLine,
}) => {
  const Bars = useMemo(
    () =>
      bars.map((bar) => (
        <Bar
          name={bar?.key}
          key={bar?.key}
          fill={bar?.color}
          dataKey={bar?.key}
        />
      )),
    [bars]
  );
  const yAxisTicks = Array.from({ length: 10 }, (_, index) => (index + 1) * 10);
  return (
    <BarChart
      width={chartWidth || 550}
      height={chartHeight || 300}
      data={data}
      maxBarSize={maxBarSize}
      margin={{ bottom: 25 }}
    >
      <CartesianGrid
        vertical={cartesianProps?.vertical}
        stroke={cartesianProps?.stroke}
        x={cartesianProps?.x}
        width={cartesianProps?.width}
        syncWithTicks={cartesianProps?.syncWithTicks}
      />
      <YAxis
        padding={yAxisProps?.padding}
        tickLine={yAxisProps?.tickLine}
        axisLine={yAxisProps?.axisLine}
        tick={yAxisProps?.tick}
        ticks={yAxisTicks}
        fontSize={yAxisProps?.fontSize}
        fontWeight={yAxisProps?.fontWeight}
        tickFormatter={yAxisProps?.tickFormatter}
        domain={[0, 100]}
      />
      <XAxis
        dataKey='name'
        axisLine={xAxisProps?.axisLine}
        tick={xAxisProps?.tick}
        tickLine={xAxisProps?.tickLine}
        padding={xAxisProps?.padding}
        interval={0}
      />
      <Tooltip />
      {Bars}
      {referenceLine && <ReferenceLine y={50} stroke='black' strokeWidth={1} />}
    </BarChart>
  );
};

export default React.memo(BarChartComponent);
