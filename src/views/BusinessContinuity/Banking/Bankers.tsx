import { Field<PERSON><PERSON>y, Form, Formik, FormikErrors } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { FormikSelect } from 'shared-resources/components/Select/FormikSelect';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { BusinessContinuityScreens } from 'types/enum';
import {
  getKey,
  isRequiredFieldDependingOnValidationSchema,
} from 'utils/helpers/Helpers';
import * as yup from 'yup';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import ContinuityQuestionHeader from '../shared-components/ContinuityQuestionHeader';
import ContuinuityRowView from '../shared-components/ContuinuityRowView';
import { formEmpty } from '../Report/continuityReportHelper';

interface BankersProps {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  onSaveToDraftClick: (data: any) => void;
  data: any;
  options: { label: string; value: string }[];
}

const Bankers: React.FC<BankersProps> = (props) => {
  const { nextStep, backStep, setData, onSaveToDraftClick, data, options } =
    props;

  const isLoading = useSelector(getAssessmentToolLoading);

  const validationSchema = yup
    .object()
    .shape({
      [BusinessContinuityScreens.BANKERS]: yup.array().of(
        yup.object({
          type: yup.string().required('Type is required'),
          name: yup.string().required('Name is required'),
          company: yup.string().required('Company is required'),
          phone: yup
            .string()
            .matches(
              /^(\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/,
              'Phone number not from US region'
            )
            .required('Phone is required'),
          comment: yup.string().nullable(),
        })
      ),
    })
    .required();

  const initialValues = {
    [BusinessContinuityScreens.BANKERS]: data?.[
      BusinessContinuityScreens.BANKERS
    ] || [
      {
        type: '',
        name: '',
        company: '',
        phone: '',
        comment: '',
        saved: false,
      },
    ],
  };

  return (
    <div className='flex flex-col rounded-lg  h-full'>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          setData(values?.[BusinessContinuityScreens.BANKERS]);
          nextStep();
        }}
        enableReinitialize
        validateOnMount
      >
        {({ values, setFieldValue, isValid, errors }) => {
          const isFormEmpty = formEmpty(
            values?.[BusinessContinuityScreens.BANKERS]
          );
          return (
            <Form className='relative p-5 h-full flex flex-col gap-4 bg-white'>
              <FieldArray name={BusinessContinuityScreens.BANKERS}>
                {({ push, remove }) => (
                  <div className='flex flex-col   h-full'>
                    <ContinuityQuestionHeader
                      fieldKey={BusinessContinuityScreens.BANKERS}
                      push={push}
                      pushData={{
                        type: '',
                        name: '',
                        company: '',
                        phone: '',
                        comment: '',
                        saved: false,
                      }}
                      title='Bankers'
                    />
                    <div className=' flex flex-col max-h-[calc(100vh-25rem)] pr-1 scrollbar overflow-auto'>
                      <ContuinuityRowView
                        fieldKey={BusinessContinuityScreens.BANKERS}
                        formValues={values}
                        remove={remove}
                        initialValues={{
                          type: '',
                          name: '',
                          company: '',
                          phone: '',
                          comment: '',
                          saved: false,
                        }}
                      />
                      <div className='flex flex-col'>
                        {!!values?.[BusinessContinuityScreens.BANKERS]
                          ?.length &&
                          values?.[BusinessContinuityScreens.BANKERS]?.map(
                            (formValues: any, index: number) =>
                              !formValues.saved && (
                                <div
                                  key={`${getKey(index)}_form`}
                                  className='flex flex-col border border-gray-02 p-4 mt-1 rounded-b-lg'
                                >
                                  <div className='grid grid-cols-2 gap-y-2 gap-x-8 mt-5  overflow-auto scrollbar text-[1rem]'>
                                    <FormikSelect
                                      name={`${
                                        BusinessContinuityScreens.BANKERS
                                      }.${getKey(index)}.type`}
                                      key={`${
                                        BusinessContinuityScreens.BANKERS
                                      }.${getKey(index)}.type`}
                                      label='Type'
                                      options={options}
                                      outerClassName='!h-10'
                                      labelClassName='!text-base'
                                      optionClassname='!p-1 rounded-md'
                                      placeholder='Select type'
                                      menuClassname='!p-2'
                                    />
                                    <FormikInput
                                      name={`${
                                        BusinessContinuityScreens.BANKERS
                                      }.${getKey(index)}.name`}
                                      key={`${
                                        BusinessContinuityScreens.BANKERS
                                      }.${getKey(index)}.name`}
                                      label='Name'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${
                                          BusinessContinuityScreens.BANKERS
                                        }.${getKey(index)}.name`
                                      )}
                                    />
                                    <FormikInput
                                      name={`${
                                        BusinessContinuityScreens.BANKERS
                                      }.${getKey(index)}.company`}
                                      key={`${
                                        BusinessContinuityScreens.BANKERS
                                      }.${getKey(index)}.company`}
                                      label='Company'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${
                                          BusinessContinuityScreens.BANKERS
                                        }.${getKey(index)}.company`
                                      )}
                                    />

                                    <FormikInput
                                      name={`${
                                        BusinessContinuityScreens.BANKERS
                                      }.${getKey(index)}.phone`}
                                      key={`${
                                        BusinessContinuityScreens.BANKERS
                                      }.${getKey(index)}.phone`}
                                      label='Phone'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${
                                          BusinessContinuityScreens.BANKERS
                                        }.${getKey(index)}.phone`
                                      )}
                                    />
                                    <div className='col-span-2'>
                                      <FormikInput
                                        name={`${
                                          BusinessContinuityScreens.BANKERS
                                        }.${getKey(index)}.comment`}
                                        key={`${
                                          BusinessContinuityScreens.BANKERS
                                        }.${getKey(index)}.comment`}
                                        fieldType='textarea'
                                        label='Comment-(If Any)'
                                        className='h-28'
                                        labelClassName='leading-6.5'
                                      />
                                    </div>
                                  </div>
                                  <div className='flex justify-end'>
                                    <Button
                                      onClick={() => {
                                        setFieldValue(
                                          `${
                                            BusinessContinuityScreens.BANKERS
                                          }.${getKey(index)}.saved`,
                                          true
                                        );
                                      }}
                                      className='px-4 py-2'
                                      disabled={
                                        !!(
                                          errors?.[
                                            BusinessContinuityScreens.BANKERS
                                          ] as FormikErrors<any>[]
                                        )?.[index]
                                      }
                                    >
                                      Save
                                    </Button>
                                  </div>
                                </div>
                              )
                          )}
                      </div>
                    </div>
                  </div>
                )}
              </FieldArray>
              <BackNextComponent
                backStep={() => {
                  backStep();
                  setData(values?.[BusinessContinuityScreens.BANKERS]);
                }}
                nextStep={() => {
                  setData(values?.[BusinessContinuityScreens.BANKERS]);
                  nextStep();
                }}
                fieldKey={BusinessContinuityScreens.BANKERS}
                formValues={values}
                isLoading={isLoading}
                isNextDisable={isFormEmpty ? false : !isValid}
                onSaveToDraftClick={onSaveToDraftClick}
              />
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default Bankers;
