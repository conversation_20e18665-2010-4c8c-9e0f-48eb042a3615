import { OwnerRelianceTabs } from 'types/enum';

export const headerOptions = [
  'Business Function',
  'Not Applicable',
  'Nobody else can do it, I always make the final decision',
  'I have some help but, I always make the final decision',
  'I have some help but, I sometimes make the final decision',
  'I am not involved in this, someone else makes the final decision',
  'Current Function Owner',
];

export const ownerRelianceResponse: any = {
  [OwnerRelianceTabs.MANAGEMENT_TEAM]: {
    business_name: '',
    first_name: '',
    last_name: '',
    business_start_date: '',
    members: [],
  },
  [OwnerRelianceTabs.PRESIDENT_CEO]: {
    Strategy: {
      selected_option: '',
      current_function_owner: '',
    },
    'General Management': {
      selected_option: '',
      current_function_owner: '',
    },
    'Business Meetings': {
      selected_option: '',
      current_function_owner: '',
    },
    Legal: {
      selected_option: '',
      current_function_owner: '',
    },
    Governance: {
      selected_option: '',
      current_function_owner: '',
    },
    Accountability: {
      selected_option: '',
      current_function_owner: '',
    },
    'Taxes - Business': {
      selected_option: '',
      current_function_owner: '',
    },
    'Taxes - Employee': {
      selected_option: '',
      current_function_owner: '',
    },
    'Strategic Relationship': {
      selected_option: '',
      current_function_owner: '',
    },
    'Outside Accounting Relationship': {
      selected_option: '',
      current_function_owner: '',
    },
    Contracts: {
      selected_option: '',
      current_function_owner: '',
    },
  },
  [OwnerRelianceTabs.SALES_MARKETING]: {
    Pricing: {
      selected_option: '',
      current_function_owner: '',
    },
    'Customer Relationships (Major)': {
      selected_option: '',
      current_function_owner: '',
    },
    'Customer Relationships (Smaller)': {
      selected_option: '',
      current_function_owner: '',
    },
    'New Business Development': {
      selected_option: '',
      current_function_owner: '',
    },
    'Revenue Generation': {
      selected_option: '',
      current_function_owner: '',
    },
    'Sales Process': {
      selected_option: '',
      current_function_owner: '',
    },
    'Marketing / Advertising Plan Development': {
      selected_option: '',
      current_function_owner: '',
    },
    'Marketing / Advertising Implementation': {
      selected_option: '',
      current_function_owner: '',
    },
    Differentiation: {
      selected_option: '',
      current_function_owner: '',
    },
    'Internal Communications': {
      selected_option: '',
      current_function_owner: '',
    },
    'Brand Development': {
      selected_option: '',
      current_function_owner: '',
    },
    'Public Relations': {
      selected_option: '',
      current_function_owner: '',
    },
    'Event Planning': {
      selected_option: '',
      current_function_owner: '',
    },
    'Website Management': {
      selected_option: '',
      current_function_owner: '',
    },
    'Social Media': {
      selected_option: '',
      current_function_owner: '',
    },
    'Customer Relations': {
      selected_option: '',
      current_function_owner: '',
    },
    'Customer Complaints': {
      selected_option: '',
      current_function_owner: '',
    },
  },
  [OwnerRelianceTabs.FINANCE]: {
    'Cash Flow Management': {
      selected_option: '',
      current_function_owner: '',
    },
    Budgets: {
      selected_option: '',
      current_function_owner: '',
    },
    'Tax Compliance': {
      selected_option: '',
      current_function_owner: '',
    },
    Banking: {
      selected_option: '',
      current_function_owner: '',
    },
    Insurance: {
      selected_option: '',
      current_function_owner: '',
    },
    Bookkeeping: {
      selected_option: '',
      current_function_owner: '',
    },
    'Accounts Payable': {
      selected_option: '',
      current_function_owner: '',
    },
    'Accounts Receivable': {
      selected_option: '',
      current_function_owner: '',
    },
    'Daily Reconciliation': {
      selected_option: '',
      current_function_owner: '',
    },
    'Books Balancing': {
      selected_option: '',
      current_function_owner: '',
    },
    Reporting: {
      selected_option: '',
      current_function_owner: '',
    },
    Collections: {
      selected_option: '',
      current_function_owner: '',
    },
    'PO System': {
      selected_option: '',
      current_function_owner: '',
    },
  },
  [OwnerRelianceTabs.OPERATIONS]: {
    'Manufacturing/ Services': {
      selected_option: '',
      current_function_owner: '',
    },
    Efficiency: {
      selected_option: '',
      current_function_owner: '',
    },
    Quality: {
      selected_option: '',
      current_function_owner: '',
    },
    Capacity: {
      selected_option: '',
      current_function_owner: '',
    },
    Distribution: {
      selected_option: '',
      current_function_owner: '',
    },
    'Vendor Selection': {
      selected_option: '',
      current_function_owner: '',
    },
    'Product/Services Development': {
      selected_option: '',
      current_function_owner: '',
    },
    'Process Documentation': {
      selected_option: '',
      current_function_owner: '',
    },
    Purchasing: {
      selected_option: '',
      current_function_owner: '',
    },
    Sourcing: {
      selected_option: '',
      current_function_owner: '',
    },
    'Quality Management': {
      selected_option: '',
      current_function_owner: '',
    },
    'Inventory Management': {
      selected_option: '',
      current_function_owner: '',
    },
    Scheduling: {
      selected_option: '',
      current_function_owner: '',
    },
    'Price Negotiation': {
      selected_option: '',
      current_function_owner: '',
    },
    'Equipment Maintenance': {
      selected_option: '',
      current_function_owner: '',
    },
    'Capital Expenditures': {
      selected_option: '',
      current_function_owner: '',
    },
    'Facility Management': {
      selected_option: '',
      current_function_owner: '',
    },
  },
  [OwnerRelianceTabs.HUMAN_RESOURCES]: {
    Hiring: {
      selected_option: '',
      current_function_owner: '',
    },
    'Employee Review': {
      selected_option: '',
      current_function_owner: '',
    },
    Compensation: {
      selected_option: '',
      current_function_owner: '',
    },
    'Employee Training': {
      selected_option: '',
      current_function_owner: '',
    },
    'Employee Benefits': {
      selected_option: '',
      current_function_owner: '',
    },
    Compliance: {
      selected_option: '',
      current_function_owner: '',
    },
    Culture: {
      selected_option: '',
      current_function_owner: '',
    },
    Recruiting: {
      selected_option: '',
      current_function_owner: '',
    },
  },
  [OwnerRelianceTabs.RESEARCH_AND_DEVELOPMENT]: {
    'Product Design': {
      selected_option: '',
      current_function_owner: '',
    },
    Innovation: {
      selected_option: '',
      current_function_owner: '',
    },
    Compensation: {
      selected_option: '',
      current_function_owner: '',
    },
    'Market Research /Surveillance': {
      selected_option: '',
      current_function_owner: '',
    },
    'New Products': {
      selected_option: '',
      current_function_owner: '',
    },
  },
  [OwnerRelianceTabs.INFORMATION_TECHNOLOGY]: {
    'Printers, Copiers, Computers': {
      selected_option: '',
      current_function_owner: '',
    },
    'IT Applications Development': {
      selected_option: '',
      current_function_owner: '',
    },
    '3rd Party IT Apps': {
      selected_option: '',
      current_function_owner: '',
    },
    'IT Equipment Purchasing': {
      selected_option: '',
      current_function_owner: '',
    },
    'IT Security and Data': {
      selected_option: '',
      current_function_owner: '',
    },
  },
  [OwnerRelianceTabs.OTHERS]: [],
};

export const handleNextTabSwitch = (
  currentTab: { tab: OwnerRelianceTabs } | undefined,
  setCurrentTab: (tab: { tab: OwnerRelianceTabs }) => void
): void => {
  switch (currentTab && currentTab.tab) {
    case OwnerRelianceTabs.PRESIDENT_CEO:
      setCurrentTab({ tab: OwnerRelianceTabs.SALES_MARKETING });
      break;
    case OwnerRelianceTabs.SALES_MARKETING:
      setCurrentTab({
        tab: OwnerRelianceTabs.FINANCE,
      });
      break;
    case OwnerRelianceTabs.FINANCE:
      setCurrentTab({
        tab: OwnerRelianceTabs.OPERATIONS,
      });
      break;
    case OwnerRelianceTabs.OPERATIONS:
      setCurrentTab({ tab: OwnerRelianceTabs.HUMAN_RESOURCES });
      break;
    case OwnerRelianceTabs.HUMAN_RESOURCES:
      setCurrentTab({ tab: OwnerRelianceTabs.RESEARCH_AND_DEVELOPMENT });
      break;
    case OwnerRelianceTabs.RESEARCH_AND_DEVELOPMENT:
      setCurrentTab({ tab: OwnerRelianceTabs.INFORMATION_TECHNOLOGY });
      break;
    default:
      setCurrentTab({ tab: OwnerRelianceTabs.OTHERS });
  }
};

export const handleBackTabSwitch = (
  currentTab: { tab: OwnerRelianceTabs } | undefined,
  setCurrentTab: (tab: { tab: OwnerRelianceTabs }) => void
): void => {
  switch (currentTab && currentTab.tab) {
    case OwnerRelianceTabs.PRESIDENT_CEO:
      setCurrentTab({ tab: OwnerRelianceTabs.MANAGEMENT_TEAM });
      break;
    case OwnerRelianceTabs.SALES_MARKETING:
      setCurrentTab({ tab: OwnerRelianceTabs.PRESIDENT_CEO });
      break;
    case OwnerRelianceTabs.FINANCE:
      setCurrentTab({ tab: OwnerRelianceTabs.SALES_MARKETING });
      break;
    case OwnerRelianceTabs.OPERATIONS:
      setCurrentTab({ tab: OwnerRelianceTabs.FINANCE });
      break;
    case OwnerRelianceTabs.HUMAN_RESOURCES:
      setCurrentTab({ tab: OwnerRelianceTabs.OPERATIONS });
      break;
    case OwnerRelianceTabs.RESEARCH_AND_DEVELOPMENT:
      setCurrentTab({ tab: OwnerRelianceTabs.HUMAN_RESOURCES });
      break;
    case OwnerRelianceTabs.INFORMATION_TECHNOLOGY:
      setCurrentTab({ tab: OwnerRelianceTabs.RESEARCH_AND_DEVELOPMENT });
      break;
    case OwnerRelianceTabs.OTHERS:
      setCurrentTab({ tab: OwnerRelianceTabs.INFORMATION_TECHNOLOGY });
      break;
    default:
      break;
  }
};

export const getTitle = (tab: string) => {
  switch (tab) {
    case OwnerRelianceTabs.PRESIDENT_CEO:
      return 'President CEO';
    case OwnerRelianceTabs.SALES_MARKETING:
      return 'Sales/Marketing';
    case OwnerRelianceTabs.FINANCE:
      return 'Finance';
    case OwnerRelianceTabs.OPERATIONS:
      return 'Operations';
    case OwnerRelianceTabs.HUMAN_RESOURCES:
      return 'Human Resources';
    case OwnerRelianceTabs.RESEARCH_AND_DEVELOPMENT:
      return 'Research and Development';
    case OwnerRelianceTabs.INFORMATION_TECHNOLOGY:
      return 'Information Technology';
    default:
      return 'Others';
  }
};

// tab name against its tab number
export const numberToTabObject: any = {
  1: OwnerRelianceTabs.PRESIDENT_CEO,
  2: OwnerRelianceTabs.SALES_MARKETING,
  3: OwnerRelianceTabs.FINANCE,
  4: OwnerRelianceTabs.OPERATIONS,
  5: OwnerRelianceTabs.HUMAN_RESOURCES,
  6: OwnerRelianceTabs.RESEARCH_AND_DEVELOPMENT,
  7: OwnerRelianceTabs.INFORMATION_TECHNOLOGY,
  8: OwnerRelianceTabs.OTHERS,
};

// tab number against its tab name
export const tabToNumberObject: any = {
  [OwnerRelianceTabs.PRESIDENT_CEO]: 1,
  [OwnerRelianceTabs.SALES_MARKETING]: 2,
  [OwnerRelianceTabs.FINANCE]: 3,
  [OwnerRelianceTabs.OPERATIONS]: 4,
  [OwnerRelianceTabs.HUMAN_RESOURCES]: 5,
  [OwnerRelianceTabs.RESEARCH_AND_DEVELOPMENT]: 6,
  [OwnerRelianceTabs.INFORMATION_TECHNOLOGY]: 7,
  [OwnerRelianceTabs.OTHERS]: 8,
};

const getTotalOptionCount = (obj: any): number => {
  let count = 0;
  Object.keys(obj).forEach((key) => {
    count += Object.keys(obj[key] ?? {}).length;
  });
  return count * 2 - 5; // Twice: because in every tab option has a current_function_owner and a selected_option except Management Team
};

const getFilledOptionCount = (obj: any): number => {
  let count = 0;
  Object.keys(obj).forEach((key) => {
    Object.values(obj[key] ?? {}).forEach((value: any) => {
      if (key === OwnerRelianceTabs.MANAGEMENT_TEAM && value) {
        count += 1;
      } else {
        if (value?.selected_option) {
          count += 1;
        }
        if (
          value?.selected_option === headerOptions[1] ||
          value?.current_function_owner
        ) {
          count += 1;
        }
      }
    });
  });
  return count;
};

export const calculateOwnerRelianceCompletedPercentage = (response: any) => {
  if (!response || Object.keys(response).length === 0) {
    return 0; // Return 0 if data is null, undefined, or an empty object
  }
  const totalOptions = getTotalOptionCount(response);
  const filledOptions = getFilledOptionCount(response);
  const completedPercentage = (filledOptions / totalOptions) * 100;
  return completedPercentage;
};

export const isOwnerInTeam = (team: any[], ownerName: string) =>
  team.find((member) => member.name === ownerName);
