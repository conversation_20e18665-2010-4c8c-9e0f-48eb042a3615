import { AssessmentToolDTO } from 'dtos/assessment-tools.dto';
import { BusinessOwnerDTO, BusinessOwnerTransferRequest } from 'dtos/business-owner.dto';
import queryString from 'query-string';
import { BusinessOwnersFilters } from 'types/business-owner.types';
import { AssessmentTools } from 'types/enum';
import { BaseApiService, baseApiService } from './BaseApiService';
import { useMemo } from 'react';
import { localStorageService } from 'services/LocalStorageService';

class BusinessOwnerService {

  static getInstance(): BusinessOwnerService {
    return new BusinessOwnerService();
  }

  public async fetchList(filters: BusinessOwnersFilters) {
    const stringify = queryString.stringify(filters);
    return baseApiService.get(`/${BaseApiService.loggedInUserType}/business-owner/list?${stringify}`);
  }

  public async createBusinessOwner(businessOwner: BusinessOwnerDTO) {
    return baseApiService.post(`/${BaseApiService.loggedInUserType}/business-owner`, businessOwner);
  }

  public async updateBusinessOwnerByAdvisor(
    id: string,
    businessOwner: BusinessOwnerDTO
  ) {
    return baseApiService.post(`/${BaseApiService.loggedInUserType}/business-owner/${+id}/update`, businessOwner);
  }

  public async updateBusinessOwner(
    id: number,
    businessOwner: BusinessOwnerDTO
  ) {
    return baseApiService.put(`/${BaseApiService.loggedInUserType}/business-owner/${id}`, businessOwner);
  }

  public async deleteBusinessOwner(id: number) {
    return baseApiService.delete(`/${BaseApiService.loggedInUserType}/business-owner/${id}`);
  }

  public async transferBusinessOwner(id: number, transferRequest: BusinessOwnerTransferRequest) {
    return baseApiService.patch(`/${BaseApiService.loggedInUserType}/business-owner/${id}/transfer-business-owner`, transferRequest);
  }

  public async updateBusinessOwnerByOwner(businessOwner: BusinessOwnerDTO) {
    return baseApiService.post(`/business-owner/update`, businessOwner);
  }

  public async getBusinessOwner(id: number) {
    return baseApiService.get(`/${BaseApiService.loggedInUserType}/business-owner/${id}`);
  }

  public async updateAssessmentTool(
    id: number,
    assessmentTool: AssessmentToolDTO
  ) {
    return baseApiService.post(
      `/business-owner/${id}/assessment-tool/update`,
      assessmentTool
    );
  }

  public async getBusinessOwnerIndustries(search: string) {
    const stringify = queryString.stringify({ search });
    return baseApiService.get(`/naics-list?${stringify}`);
  }

  public async businessLearnMore(tool: AssessmentTools) {
    return baseApiService.post(`/business-owner/learn-more`, { tool });
  }
}

export const businessOwnerService = BusinessOwnerService.getInstance();
