import { AdvisorDTO } from 'dtos/advisor.dto';
import queryString from 'query-string';
import { baseApiService } from './BaseApiService';
import { AdvisorsFilters } from 'types/Advisor.type';

class AdvisorService {
  static getInstance(): AdvisorService {
    return new AdvisorService();
  }

  public async fetchList(filters: AdvisorsFilters) {
    const stringify = queryString.stringify(filters);
    return baseApiService.get(`/advisor/list?${stringify}`);
  }

  public async createAdvisor(advisor: AdvisorDTO) {
    return baseApiService.post('/advisor', advisor);
  }

  public async updateAdvisor(
    id: number,
    advisor: AdvisorDTO
  ) {
    return baseApiService.put(`/advisor/${id}`, advisor);
  }

  public async updateAdvisorAccess(
    id: number,
    isActive: string
  ) {
    return baseApiService.patch(`/advisor/${isActive}/${id}`);
  }

  public async deleteAdvisor(id: number) {
    return baseApiService.delete(`/advisor/${id}`);
  }

  public async getAdvisor(id: number) {
    return baseApiService.get(`/advisor/${id}`);
  }
}

export const advisorService = AdvisorService.getInstance();
