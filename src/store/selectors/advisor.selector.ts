import { createSelector } from 'reselect';
import { AppState } from 'store';
import { Advisor } from 'models/entities/Advisor';

const advisorStore = (store: AppState) => store.advisor;

export const allAdvisors = createSelector(
  [advisorStore],
  (advisors) => {
    const advisorsData = advisors.entities ?? {};
    if (advisors.ids) {
      return advisors.ids.map((id) => advisorsData[id] as Advisor);
    }
    return [];
  }
);

export const getCreateAdvisorErrors = createSelector(
  [advisorStore],
  (advisors) => advisors.create.errors
);

export const getCreateAdvisorLoading = createSelector(
  [advisorStore],
  (advisorsData) => {
    const createAdvisorLoading = advisorsData.create?.loading;
    if (createAdvisorLoading) {
      return createAdvisorLoading;
    }
    return false;
  }
);

export const getUpdateAdvisorLoading = createSelector(
  [advisorStore],
  (advisors) => advisors.update.loading
);


export const updateAdvisorErrors = createSelector(
  [advisorStore],
  (advisors) => advisors.update.errors
);

export const updateAdvisorLoading = createSelector(
  [advisorStore],
  (advisors) => advisors.update.loading
);