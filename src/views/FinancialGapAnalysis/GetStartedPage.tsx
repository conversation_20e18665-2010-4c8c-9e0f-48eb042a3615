import React from 'react';
import Button from 'shared-resources/components/Button/Button';
import { financialGapAnalysisIntroText } from './FinancialGapAnalysisConfig';

interface Props {
  nextStep: () => void;
}
const GetStartedPage: React.FC<Props> = (props) => {
  const { nextStep } = props;
  //   const isLoading = useSelector(getAssessmentToolLoading);

  return (
    <div className='flex flex-col '>
      <div className='flex flex-col gap-6 h-[calc(100vh-15rem)] bg-white overflow-auto scrollbar pl-5 py-3 text-[1rem]'>
        <div className='flex flex-col gap-16 justify-center items-center font-[500]  m-auto w-[70%]'>
          <p>{financialGapAnalysisIntroText}</p>
          <Button onClick={nextStep} className='px-7 py-3'>
            Get Started
          </Button>
        </div>
      </div>
    </div>
  );
};

export default GetStartedPage;
