import React from 'react';
import reportWrapperLogoDarkTop from '../assets/reportWrapperLogoDarkTop.svg';
import reportWrapperLogoDarkBottom from '../assets/reportWrapperLogoDarkBottom.svg';
import logo from '../assets/ExitSmartsLogo.svg';

interface Props {
  children: React.ReactElement;
  isBrandLogo?: boolean;
  id?: string;
}

const DarkThemeReportWrapper: React.FC<Props> = ({
  children,
  isBrandLogo,
  id,
}) => (
  <div id={id} className='h-[96.875rem] bg-white relative aspect-[210/297]'>
    <img
      src={reportWrapperLogoDarkTop}
      className='absolute top-0 left-0  w-80 3xl:w-auto'
      alt='wrapper-image1'
    />
    <img
      src={reportWrapperLogoDarkBottom}
      className='absolute bottom-0 right-0 w-80 3xl:w-auto'
      alt='wrapper-image2'
    />
    {isBrandLogo && (
      <img
        src={logo}
        className='absolute top-2 right-2 opacity-20 w-[32rem]'
        alt='wrapper-image3'
      />
    )}
    <div className='h-full w-full'>{children}</div>
  </div>
);

export default DarkThemeReportWrapper;
