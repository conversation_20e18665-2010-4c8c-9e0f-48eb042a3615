import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router';
import Button from 'shared-resources/components/Button/Button';
import { fetchFollowUps, followUpCancel } from 'store/actions/follow-up.action';
import {
  getFollowUpHistory,
  getFollowUpSchedule,
} from 'store/selectors/follow-up-selector';
import { AssessmentTools, NonAssessmentToolTabs } from 'types/enum';
import {
  getFollowUpDescription,
  getFormattedDate,
} from 'utils/helpers/Helpers';
import { getUserData } from 'store/selectors/user.selector';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import FollowUpHistoryItem from './FollowUpHistoryItem';

interface FollowUpHistoryModalProps {
  tool: AssessmentTools | NonAssessmentToolTabs;
  handleModalVisibility: (value: boolean) => void;
  businessOwner: BusinessOwner;
}
const FollowUpHistoryModal: React.FC<FollowUpHistoryModalProps> = (props) => {
  const { handleModalVisibility, tool, businessOwner } = props;
  const followUps = useSelector(getFollowUpHistory);
  const followUpSchedule = useSelector(getFollowUpSchedule);
  const dispatch = useDispatch();
  const location = useLocation();
  const ownerId = location.pathname.split('/')[3];
  const totalFollowUps = followUpSchedule?.followUps;
  const sentFollowUps = followUpSchedule?.sentFollowUps;
  const isActive = followUpSchedule?.isActive;
  const userData = useSelector(getUserData);

  useEffect(() => {
    dispatch(
      fetchFollowUps({ assessmentTool: tool, businessOwnerId: +ownerId })
    );
  }, []);

  return (
    <div className='flex flex-col gap-5 p-10 max-h-[calc(100vh-20rem)] overflow-auto scrollbar'>
      <div className='flex items-start justify-between'>
        <h2 className='font-medium text-lg'>History</h2>
        {businessOwner?.primary_advisor_id === userData?.id && (
          <Button
            onClick={() => {
              handleModalVisibility(true);
            }}
            className='px-6 py-2'
          >
            Quick Follow Up
          </Button>
        )}
      </div>
      {isActive && totalFollowUps !== sentFollowUps && (
        <div className='border flex justify-between border-gray-02 p-3 rounded-lg'>
          <div className='flex flex-col gap-1'>
            <span>Upcoming Automated Follow Up</span>
            <span className='text-sm text-gray-07'>
              Date:{' '}
              {followUpSchedule?.nextFollowUpDate &&
                getFormattedDate(followUpSchedule?.nextFollowUpDate)}
            </span>
          </div>

          {businessOwner?.primary_advisor_id === userData?.id && (
            <Button
              onClick={() => {
                dispatch(
                  followUpCancel({
                    assessmentTool: tool,
                    businessOwnerId: +ownerId,
                  })
                );
              }}
              className='p-2 h-fit'
            >
              Cancel
            </Button>
          )}
        </div>
      )}
      <div className='flex flex-col gap-4'>
        {followUps?.length
          ? followUps.map((data) => (
              <FollowUpHistoryItem
                followUpNumber={data.metadata?.follow_up_number}
                followUpText={data.metadata?.info}
                date={getFormattedDate(data.createdAt)}
                description={getFollowUpDescription(
                  data.type,
                  data.metadata.follow_up_number,
                  new Date(data.createdAt)
                )}
                key={data.id}
              />
            ))
          : 'No History Available'}
      </div>
    </div>
  );
};

export default FollowUpHistoryModal;
