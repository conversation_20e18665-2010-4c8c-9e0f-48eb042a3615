import React, { useEffect, useRef } from 'react';
import RangeSelectionQuestion from 'shared-resources/components/RangeSelectionQuestion/RangeSelectionQuestion';
import cx from 'classnames';
import { useLocation } from 'react-router-dom';
import { AnswerData } from './ReadinessConfig';

interface Props {
  questions: { ques: string; id: string | number }[];
  responses?: {
    [key: string]: number;
  };
  onAnswerClick: (quesid: string, value: string | number) => void; // Function to handle answer click
  ansData: AnswerData[];
}

const getValueForQuestionId = (
  questionId: string,
  response: {
    [key: string]: number;
  }
): number | undefined => response[questionId];

const AssessmentComponent: React.FC<Props> = ({
  questions,
  responses,
  onAnswerClick,
  ansData,
}) => {
  const location = useLocation();
  const classes = location.pathname.includes('public')
    ? 'h-[calc(100vh-22rem)]'
    : 'h-[calc(100vh-29.0625rem)]';

  const scrollableDivRef = useRef<HTMLDivElement>(null);

  const scrollToTop = () => {
    if (scrollableDivRef.current) {
      scrollableDivRef.current.scrollTop = 0;
    }
  };

  useEffect(() => {
    scrollToTop();
  }, [location]);

  return (
    <div
      ref={scrollableDivRef}
      className={cx('flex flex-col gap-4 overflow-y-auto scrollbar', classes)}
    >
      {questions.map((question, index) => (
        <div className='px-8 mt-4' key={question.id}>
          <RangeSelectionQuestion
            count={index + 1}
            title={question.ques}
            ansData={ansData}
            onAnswerClick={(value) => {
              onAnswerClick(question.id.toString(), value);
            }}
            selectedAnswer={getValueForQuestionId(
              question.id.toString(),
              responses!
            )}
            isActive={
              index === 0 ||
              (Object?.keys(responses || {})?.includes(
                questions[index - 1].id.toString()
              ) ??
                false)
            }
          />
        </div>
      ))}
    </div>
  );
};

export default AssessmentComponent;
