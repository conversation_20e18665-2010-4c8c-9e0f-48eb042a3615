import { createSelector } from '@reduxjs/toolkit';
import { EnterpriseAdmin } from 'models/entities/EnterpriseAdmin';
import { AppState } from 'store';
import { createParamSelector } from './base.selectors';

// Base selector
const selectEnterpriseAdminState = (state: AppState) => state.enterpriseAdmin;

const getEnterpriseAdminId = createParamSelector(
  (params: { id: string }) => params.id
);

// Enterprise Admins List
export const allEnterpriseAdmins = createSelector(
  [selectEnterpriseAdminState],
  (enterpriseAdmins) => {
    const adminsData = enterpriseAdmins.entities ?? {};
    if (enterpriseAdmins.ids) {
      return enterpriseAdmins.ids.map((id) => adminsData[id] as EnterpriseAdmin);
    }
    return [];
  }
);

// Get enterprise admin by ID
export const getEnterpriseAdminById = createSelector(
  [selectEnterpriseAdminState, getEnterpriseAdminId],
  (enterpriseAdminData, adminId) => {
    const { entities } = enterpriseAdminData;
    return entities[adminId];
  }
);

// Get enterprise admins count
export const enterpriseAdminsCount = createSelector(
  [allEnterpriseAdmins],
  (enterpriseAdmins) => enterpriseAdmins.length
);

// Check if enterprise admins list is empty
export const isEnterpriseAdminsListEmpty = createSelector(
  [allEnterpriseAdmins],
  (enterpriseAdmins) => enterpriseAdmins.length === 0
);

// Loading states
export const enterpriseAdminCreateLoading = createSelector(
  [selectEnterpriseAdminState],
  (state) => state.create.loading
);

export const enterpriseAdminUpdateLoading = createSelector(
  [selectEnterpriseAdminState],
  (state) => state.update.loading
);

export const enterpriseAdminListLoading = createSelector(
  [selectEnterpriseAdminState],
  (state) => state.list?.loading
);

export const enterpriseAdminGetLoading = createSelector(
  [selectEnterpriseAdminState],
  (state) => state.get?.loading
);

// Error states
export const enterpriseAdminCreateError = createSelector(
  [selectEnterpriseAdminState],
  (state) => state.create.error
);

export const enterpriseAdminUpdateError = createSelector(
  [selectEnterpriseAdminState],
  (state) => state.update.error
);

export const enterpriseAdminListError = createSelector(
  [selectEnterpriseAdminState],
  (state) => state.list?.error
);

export const enterpriseAdminGetError = createSelector(
  [selectEnterpriseAdminState],
  (state) => state.get?.error
);

// Pagination info
export const enterpriseAdminPagination = createSelector(
  [selectEnterpriseAdminState],
  (state) => state.paginationMeta
);

// Combined loading state (for backward compatibility)
export const enterpriseAdminLoading = createSelector(
  [enterpriseAdminCreateLoading, enterpriseAdminUpdateLoading, enterpriseAdminListLoading],
  (createLoading, updateLoading, listLoading) =>
    createLoading || updateLoading || listLoading
);
