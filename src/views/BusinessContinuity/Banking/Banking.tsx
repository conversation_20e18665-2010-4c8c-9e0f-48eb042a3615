import { <PERSON><PERSON><PERSON>y, Form, Formik, FormikErrors } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { FormikSelect } from 'shared-resources/components/Select/FormikSelect';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { BusinessContinuityScreens } from 'types/enum';
import {
  getKey,
  isRequiredFieldDependingOnValidationSchema,
} from 'utils/helpers/Helpers';
import * as yup from 'yup';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import ContinuityQuestionHeader from '../shared-components/ContinuityQuestionHeader';
import ContuinuityRowView from '../shared-components/ContuinuityRowView';
import { formEmpty } from '../Report/continuityReportHelper';

interface BankingProps {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  onSaveToDraftClick: (data: any) => void;
  data: any;
  options: { label: string; value: string }[];
}

const Banking: React.FC<BankingProps> = (props) => {
  const { nextStep, backStep, setData, onSaveToDraftClick, data, options } =
    props;

  const isLoading = useSelector(getAssessmentToolLoading);

  const validationSchema = yup
    .object()
    .shape({
      [BusinessContinuityScreens.BANKING_INFORMATION]: yup.array().of(
        yup.object({
          type: yup.string().required('Type is required'),
          bank: yup.string().required('Bank is required'),
          use: yup.string().required('Use is required'),
          who_has_access: yup.string().required('Who has access is required'),

          comment: yup.string().nullable(),
        })
      ),
    })
    .required();

  const initialValues = {
    [BusinessContinuityScreens.BANKING_INFORMATION]: data?.[
      BusinessContinuityScreens.BANKING_INFORMATION
    ] || [
      {
        type: '',
        bank: '',
        use: '',
        who_has_access: '',
        comment: '',
        saved: false,
      },
    ],
  };

  return (
    <div className='flex flex-col rounded-lg h-full'>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          setData(values?.[BusinessContinuityScreens.BANKING_INFORMATION]);
          nextStep();
        }}
        enableReinitialize
        validateOnMount
      >
        {({ values, setFieldValue, isValid, errors }) => {
          const isFormEmpty = formEmpty(
            values?.[BusinessContinuityScreens.BANKING_INFORMATION]
          );
          return (
            <Form className='relative p-5 h-full flex flex-col gap-4 bg-white'>
              <FieldArray name={BusinessContinuityScreens.BANKING_INFORMATION}>
                {({ push, remove }) => (
                  <div className='flex flex-col   h-full'>
                    <ContinuityQuestionHeader
                      fieldKey={BusinessContinuityScreens.BANKING_INFORMATION}
                      push={push}
                      pushData={{
                        type: '',
                        bank: '',
                        use: '',
                        who_has_access: '',
                        comment: '',
                        saved: false,
                      }}
                      title='Banking'
                    />
                    <div className=' flex flex-col max-h-[calc(100vh-25rem)]  pr-1 scrollbar overflow-auto'>
                      <ContuinuityRowView
                        fieldKey={BusinessContinuityScreens.BANKING_INFORMATION}
                        formValues={values}
                        remove={remove}
                        initialValues={{
                          type: '',
                          bank: '',
                          use: '',
                          who_has_access: '',
                          comment: '',
                          saved: false,
                        }}
                      />
                      <div className='flex flex-col'>
                        {!!values?.[
                          BusinessContinuityScreens.BANKING_INFORMATION
                        ]?.length &&
                          values?.[
                            BusinessContinuityScreens.BANKING_INFORMATION
                          ]?.map(
                            (formValues: any, index: number) =>
                              !formValues.saved && (
                                <div
                                  key={`${getKey(index)}_form`}
                                  className='flex flex-col border border-gray-02 p-4 mt-1 rounded-b-lg'
                                >
                                  <div className='grid grid-cols-2 gap-y-2 gap-x-8 mt-5  overflow-auto scrollbar text-[1rem]'>
                                    <FormikSelect
                                      name={`${
                                        BusinessContinuityScreens.BANKING_INFORMATION
                                      }.${getKey(index)}.type`}
                                      key={`${
                                        BusinessContinuityScreens.BANKING_INFORMATION
                                      }.${getKey(index)}.type`}
                                      label='Type'
                                      options={options}
                                      outerClassName='!h-10'
                                      labelClassName='!text-base'
                                      optionClassname='!p-1 rounded-md'
                                      placeholder='Select type'
                                      menuClassname='!p-2'
                                    />
                                    <FormikInput
                                      name={`${
                                        BusinessContinuityScreens.BANKING_INFORMATION
                                      }.${getKey(index)}.bank`}
                                      key={`${
                                        BusinessContinuityScreens.BANKING_INFORMATION
                                      }.${getKey(index)}.bank`}
                                      label='Bank'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${
                                          BusinessContinuityScreens.BANKING_INFORMATION
                                        }.${getKey(index)}.bank`
                                      )}
                                    />
                                    <FormikInput
                                      name={`${
                                        BusinessContinuityScreens.BANKING_INFORMATION
                                      }.${getKey(index)}.use`}
                                      key={`${
                                        BusinessContinuityScreens.BANKING_INFORMATION
                                      }.${getKey(index)}.use`}
                                      label='Use'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${
                                          BusinessContinuityScreens.BANKING_INFORMATION
                                        }.${getKey(index)}.use`
                                      )}
                                    />
                                    <FormikInput
                                      name={`${
                                        BusinessContinuityScreens.BANKING_INFORMATION
                                      }.${getKey(index)}.who_has_access`}
                                      key={`${
                                        BusinessContinuityScreens.BANKING_INFORMATION
                                      }.${getKey(index)}.who_has_access`}
                                      label='Who has access?'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${
                                          BusinessContinuityScreens.BANKING_INFORMATION
                                        }.${getKey(index)}.who_has_access`
                                      )}
                                    />
                                    <div className='col-span-2'>
                                      <FormikInput
                                        name={`${
                                          BusinessContinuityScreens.BANKING_INFORMATION
                                        }.${getKey(index)}.comment`}
                                        key={`${
                                          BusinessContinuityScreens.BANKING_INFORMATION
                                        }.${getKey(index)}.comment`}
                                        fieldType='textarea'
                                        label='Comment-(If Any)'
                                        className='h-28'
                                        labelClassName='leading-6.5'
                                      />
                                    </div>
                                  </div>
                                  <div className='flex justify-end'>
                                    <Button
                                      onClick={() => {
                                        setFieldValue(
                                          `${
                                            BusinessContinuityScreens.BANKING_INFORMATION
                                          }.${getKey(index)}.saved`,
                                          true
                                        );
                                      }}
                                      className='px-4 py-2'
                                      disabled={
                                        !!(
                                          errors?.[
                                            BusinessContinuityScreens
                                              .BANKING_INFORMATION
                                          ] as FormikErrors<any>[]
                                        )?.[index]
                                      }
                                    >
                                      Save
                                    </Button>
                                  </div>
                                </div>
                              )
                          )}
                      </div>
                    </div>
                  </div>
                )}
              </FieldArray>
              <BackNextComponent
                backStep={() => {
                  backStep();
                  setData(
                    values?.[BusinessContinuityScreens.BANKING_INFORMATION]
                  );
                }}
                nextStep={() => {
                  setData(
                    values?.[BusinessContinuityScreens.BANKING_INFORMATION]
                  );
                  nextStep();
                }}
                fieldKey={BusinessContinuityScreens.BANKING_INFORMATION}
                formValues={values}
                isLoading={isLoading}
                isNextDisable={isFormEmpty ? false : !isValid}
                onSaveToDraftClick={onSaveToDraftClick}
              />
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default Banking;
