import { ReportActionType } from './actions.constants';

export const createReport = (payload: {
  businessOwnerId: number;
  fileName: string;
  content: string;
  reportTitle: string;
}) => ({
  type: ReportActionType.CREATE_REPORT,
  payload,
});

export const fetchReports = (businessOwnerId: number) => ({
  type: ReportActionType.FETCH_REPORTS,
  payload: { businessOwnerId },
});

export const sendReportToBO = (reportId: number, status: boolean) => ({
  type: ReportActionType.SEND_REPORT_TO_BO,
  payload: { reportId, status },
});

export const fetchReport = (reportId: number, businessOwnerId: number) => ({
  type: ReportActionType.FETCH_REPORT,
  payload: { reportId, businessOwnerId },
});
