export enum BusinessReadiness {
  BUSINESS_RELIANCE = 'business_reliance',
  PROCESS_DOCUMENTATION = 'process_documentation',
  CUSTOMER_RELATIONSHIPS = 'customer_relationships',
  VENDOR_RELATIONSHIPS = 'vendor_relationships',
  CUSTOMER_CONCENTRATION = 'customer_concentration',
}
export enum FinanceReadiness {
  INDUSTRY_COMPARISON = 'industry_comparison',
  FINANCIAL_GAP = 'financial_gap',
  CURRENT_VALUE = 'current_value',
  VALUE_COMPONENTS = 'value_components',
  HISTORY_OF_REVENUE_PROFIT = 'history_of_revenue_profit',
}
export enum TransitionObjectiveReadiness {
  SELLING_TIMELINE = 'selling_timeline',
  POST_SALE_LIFESTYLE = 'post_sale_lifestyle',
  INTENDED_BUYER = 'intended_buyer',
  POST_SALE_PLANS = 'post_sale_plans',
  SELLING_PLAN = 'selling_plan',
}

export enum TransitionKnowledge {
  TAX_IMPLICATIONS = 'tax_implications',
  BUYER_TYPES = 'buyer_types',
  DEAL_STRUCTURES = 'deal_structures',
  MARKET_STATISTICS = 'market_statistics',
  BUSINESS_VALUE_OPTIMIZATION = 'business_value_optimization',
}

export enum PlanningReadiness {
  PERSONAL_CONTINGENCY_PLAN = 'personal_contingency_plan',
  FAMILY_FINANCIAL_FUTURE = 'family_financial_future',
  BUSINESS_VALUE_DRIVERS = 'business_value_drivers',
  STAKEHOLDER_AGREEMENT = 'stakeholder_agreement',
  EXIT_PLANNING_ADVISORS = 'exit_planning_advisors',
}

export enum ReadinessAssessmentTitle {
  BUSINESS_READINESS = 'Business Readiness',
  FINANCE = 'Finance',
  TRANSITION_OBJECTIVES = 'Transition Objective',
  TRANSITION_KNOWLEDGE = 'Transition Knowledge',
  PLANNING = 'Planning',
}
