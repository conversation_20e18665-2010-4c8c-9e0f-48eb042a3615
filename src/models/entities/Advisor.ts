import { Entity } from '../entity';
import { UserType } from '../../types/enum';

export interface Advisor extends Entity {
  email: string;
  firstName: string;
  lastName?: string;
  phone: string;
  companyName: string;
  companyAddress?: string;
  type: UserType;
  hash?: string;
  created_at_unix?: number;
  isActive?: boolean;
  businessOwnerCount: string;
  lastAccess: string,
  noOfAccess: number,
  trial?: boolean;
  trialDuration?: string;
  epId? : number;
}
