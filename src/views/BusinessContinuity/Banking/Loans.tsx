import { Field<PERSON><PERSON>y, Form, Formik, FormikErrors } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { FormikSelect } from 'shared-resources/components/Select/FormikSelect';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { BusinessContinuityScreens } from 'types/enum';
import {
  getKey,
  isRequiredFieldDependingOnValidationSchema,
} from 'utils/helpers/Helpers';
import * as yup from 'yup';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import ContinuityQuestionHeader from '../shared-components/ContinuityQuestionHeader';
import ContuinuityRowView from '../shared-components/ContuinuityRowView';
import { formEmpty } from '../Report/continuityReportHelper';

interface LoansProps {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  onSaveToDraftClick: (data: any) => void;
  data: any;
  options: { label: string; value: string }[];
}

const Loans: React.FC<LoansProps> = (props) => {
  const { nextStep, backStep, setData, onSaveToDraftClick, data, options } =
    props;

  const isLoading = useSelector(getAssessmentToolLoading);

  const validationSchema = yup
    .object()
    .shape({
      [BusinessContinuityScreens.LOANS_INFORMATION]: yup.array().of(
        yup.object({
          type: yup.string().required('Type is required'),
          lender: yup.string().required('Lender is required'),
          approximate_balance: yup
            .string()
            .required('Approximate Balance is required'),
          comment: yup.string().nullable(),
        })
      ),
    })
    .required();

  const initialValues = {
    [BusinessContinuityScreens.LOANS_INFORMATION]: data?.[
      BusinessContinuityScreens.LOANS_INFORMATION
    ] || [
      {
        type: '',
        lender: '',
        approximate_balance: '',
        comment: '',
        saved: false,
      },
    ],
  };

  return (
    <div className='flex rounded-lg flex-col h-full'>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          setData(values?.[BusinessContinuityScreens.LOANS_INFORMATION]);
          nextStep();
        }}
        enableReinitialize
        validateOnMount
      >
        {({ values, setFieldValue, isValid, errors }) => {
          const isFormEmpty = formEmpty(
            values?.[BusinessContinuityScreens.LOANS_INFORMATION]
          );
          return (
            <Form className='relative p-5 h-full flex flex-col gap-4 bg-white'>
              <FieldArray name={BusinessContinuityScreens.LOANS_INFORMATION}>
                {({ push, remove }) => (
                  <div className='flex flex-col   h-full'>
                    <ContinuityQuestionHeader
                      fieldKey={BusinessContinuityScreens.LOANS_INFORMATION}
                      push={push}
                      pushData={{
                        type: '',
                        lender: '',
                        approximate_balance: '',
                        comment: '',
                        saved: false,
                      }}
                      title='Loans'
                    />
                    <div className=' flex flex-col max-h-[calc(100vh-25rem)]  pr-1 scrollbar overflow-auto'>
                      <ContuinuityRowView
                        fieldKey={BusinessContinuityScreens.LOANS_INFORMATION}
                        formValues={values}
                        remove={remove}
                        initialValues={{
                          type: '',
                          lender: '',
                          approximate_balance: '',
                          comment: '',
                          saved: false,
                        }}
                      />
                      <div className='flex flex-col'>
                        {!!values?.[BusinessContinuityScreens.LOANS_INFORMATION]
                          ?.length &&
                          values?.[
                            BusinessContinuityScreens.LOANS_INFORMATION
                          ]?.map(
                            (formValues: any, index: number) =>
                              !formValues.saved && (
                                <div
                                  key={`${getKey(index)}_form`}
                                  className='flex flex-col border border-gray-02 p-4 mt-1 rounded-b-lg'
                                >
                                  <div className='grid grid-cols-2 gap-y-2 gap-x-8 mt-5  overflow-auto scrollbar text-[1rem]'>
                                    <FormikSelect
                                      name={`${
                                        BusinessContinuityScreens.LOANS_INFORMATION
                                      }.${getKey(index)}.type`}
                                      key={`${
                                        BusinessContinuityScreens.LOANS_INFORMATION
                                      }.${getKey(index)}.type`}
                                      label='Type'
                                      options={options}
                                      outerClassName='!h-10'
                                      labelClassName='!text-base'
                                      optionClassname='!p-1 rounded-md'
                                      placeholder='Select type'
                                      menuClassname='!p-2'
                                    />
                                    <FormikInput
                                      name={`${
                                        BusinessContinuityScreens.LOANS_INFORMATION
                                      }.${getKey(index)}.lender`}
                                      key={`${
                                        BusinessContinuityScreens.LOANS_INFORMATION
                                      }.${getKey(index)}.lender`}
                                      label='Lender'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${
                                          BusinessContinuityScreens.LOANS_INFORMATION
                                        }.${getKey(index)}.lender`
                                      )}
                                    />
                                    <FormikInput
                                      name={`${
                                        BusinessContinuityScreens.LOANS_INFORMATION
                                      }.${getKey(index)}.approximate_balance`}
                                      key={`${
                                        BusinessContinuityScreens.LOANS_INFORMATION
                                      }.${getKey(index)}.approximate_balance`}
                                      label='Approximate Balance'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${
                                          BusinessContinuityScreens.LOANS_INFORMATION
                                        }.${getKey(index)}.approximate_balance`
                                      )}
                                    />
                                    <div className='col-span-2'>
                                      <FormikInput
                                        name={`${
                                          BusinessContinuityScreens.LOANS_INFORMATION
                                        }.${getKey(index)}.comment`}
                                        key={`${
                                          BusinessContinuityScreens.LOANS_INFORMATION
                                        }.${getKey(index)}.comment`}
                                        fieldType='textarea'
                                        label='Comment-(If Any)'
                                        className='h-28'
                                        labelClassName='leading-6.5'
                                      />
                                    </div>
                                  </div>
                                  <div className='flex justify-end'>
                                    <Button
                                      onClick={() => {
                                        setFieldValue(
                                          `${
                                            BusinessContinuityScreens.LOANS_INFORMATION
                                          }.${getKey(index)}.saved`,
                                          true
                                        );
                                      }}
                                      className='px-4 py-2'
                                      disabled={
                                        !!(
                                          errors?.[
                                            BusinessContinuityScreens
                                              .LOANS_INFORMATION
                                          ] as FormikErrors<any>[]
                                        )?.[index]
                                      }
                                    >
                                      Save
                                    </Button>
                                  </div>
                                </div>
                              )
                          )}
                      </div>
                    </div>
                  </div>
                )}
              </FieldArray>
              <BackNextComponent
                backStep={() => {
                  backStep();
                  setData(
                    values?.[BusinessContinuityScreens.LOANS_INFORMATION]
                  );
                }}
                nextStep={() => {
                  setData(
                    values?.[BusinessContinuityScreens.LOANS_INFORMATION]
                  );
                  nextStep();
                }}
                fieldKey={BusinessContinuityScreens.LOANS_INFORMATION}
                formValues={values}
                isLoading={isLoading}
                isNextDisable={isFormEmpty ? false : !isValid}
                onSaveToDraftClick={onSaveToDraftClick}
              />
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default Loans;
