import { HiEye, Hi<PERSON>yeOff } from 'react-icons/hi';
import classNames from 'classnames';
import React, { FunctionComponent, useCallback, useState } from 'react';
import { InputProps } from 'shared-resources/types/Input.type';
import InputHelper from '../Inputhelper/InputHelper';
import InputLabel from '../InputLabel/InputLabel';

interface PasswordProps extends Omit<InputProps, 'trailingIcon'> {}

const PasswordInput: FunctionComponent<PasswordProps> = ({
  value,
  id,
  name,
  onChange,
  placeholder,
  label,
  helperText,
  leadingIcon,
  className,
  labelClassName,
  passwordIconLeft,
  style,
  disabled,
  asterisk,
  error,
  onBlur,
  showErrorText = true,
}) => {
  const [visible, setVisible] = useState<boolean>(false);

  const handleChange = useCallback(() => setVisible(!visible), [visible]);

  return (
    <div className='flex flex-col'>
      {label && (
        <InputLabel
          label={label}
          asterisk={asterisk}
          className={labelClassName}
        />
      )}

      <div className='relative rounded-md '>
        {leadingIcon && (
          <div className='absolute top-6 left-0 flex items-center pl-3 pointer-events-none'>
            {leadingIcon}
          </div>
        )}
        {passwordIconLeft && (
          <div
            aria-hidden
            className='absolute top-6.5  left-5 flex items-center w-8 pr-3'
            onClick={() => handleChange()}
          >
            {visible ? (
              <HiEye className='text-gray-400 cursor-pointer' />
            ) : (
              <HiEyeOff className='text-gray-400 cursor-pointer' />
            )}
          </div>
        )}
        <input
          value={value}
          id={id}
          name={name}
          type={visible ? 'text' : 'password'}
          onChange={onChange}
          disabled={disabled}
          placeholder={placeholder}
          style={style}
          onBlur={onBlur}
          data-lpignore='true'
          className={classNames(
            'block w-full placeholder-opacity-50 rounded-xl h-12.5 text-md md:text-para-02 focus:outline-none py-1.5 2xl:py-3 px-5 border border-solid border-gray-03 shadow-1',
            {
              'pl-10': !!leadingIcon,
              'mt-2': !!label,
              'pl-11': passwordIconLeft,
              'text-gray-500 bg-gray-100 px-2': !!disabled,
              'border ': !!error,
            },
            className
          )}
        />
        {!passwordIconLeft && (
          <div
            aria-hidden
            className='absolute  h-fit top-6 right-0 flex items-center w-8 pr-3'
            onClick={() => handleChange()}
          >
            {visible ? (
              <HiEye className='text-gray-400 cursor-pointer' />
            ) : (
              <HiEyeOff className='text-gray-400 cursor-pointer' />
            )}
          </div>
        )}
      </div>
      <InputHelper type='helper' text={error ? undefined : helperText} />
      <div className='h-5'>
        {showErrorText && <InputHelper type='error' text={error} />}
      </div>
    </div>
  );
};

export default React.memo(PasswordInput);
