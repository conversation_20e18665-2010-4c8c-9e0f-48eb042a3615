import tooltipData from 'constant/tooltip-data.json';
import {
  FaB<PERSON><PERSON><PERSON><PERSON>,
  FaCalendarCheck,
  FaRecycle,
  FaSearchDollar,
} from 'react-icons/fa';

import {
  FaBuildingUser,
  FaHandHoldingDollar,
  FaShuffle,
} from 'react-icons/fa6';
import { BsGraphUp, BsSafe2 } from 'react-icons/bs';
import { IoHomeSharp } from 'react-icons/io5';
import BusinessOwnerAssessment from 'shared-resources/components/BusinessOwner/BusinessOwnerAssessment';
import {
  AssessmentTools,
  NonAssessmentToolTabs,
  RouteKey,
  UserRouteType,
} from 'types/enum';
import { SidebarRoutesConfigType } from 'types/routes/routes.type';
import AdvisorDashboard from 'views/layout/AdvisorDashboard';
import BusinessDashboard from 'views/layout/BusinessDashboard';
import { MdOutlineAutoGraph } from 'react-icons/md';
import { LiaHandshake } from 'react-icons/lia';
import AdminDashboard from 'views/layout/AdminDashboard';
import EnterpriseAdminDashboard from 'views/layout/EnterpriseAdminDashboard';

export const businessOwnerSidebarRoutes: Array<SidebarRoutesConfigType> = [
  {
    name: 'Owner Dashboard',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.DASHBOARD}`,
    icon: IoHomeSharp,
    component: BusinessDashboard,
  },
  {
    name: 'Readiness Assessment',
    id: AssessmentTools.READINESS_ASSESSMENT,
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.READINESS_ASSESSMENT_DASHBOARD}`,
    icon: FaBookReader,
    component: BusinessOwnerAssessment,
    tooltipText: tooltipData.readiness_assessment,
    tooltipRouteTool: RouteKey.READINESS_ASSESSMENT,
  },
  {
    name: 'Transition Objectives',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.TRANSITION_OBJECTIVES_DASHBOARD}`,
    id: AssessmentTools.TRANSITION_OBJECTIVES,
    icon: FaShuffle,
    component: BusinessOwnerAssessment,
    tooltipText: tooltipData.transition_objectives,
    tooltipRouteTool: RouteKey.TRANSITION_OBJECTIVES,
  },
  {
    name: 'Owner Reliance',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.OWNER_RELIANCE_DASHBOARD}`,
    id: AssessmentTools.OWNER_RELIANCE,
    icon: FaBuildingUser,
    component: BusinessOwnerAssessment,
    tooltipText: tooltipData.owner_reliance,
    tooltipRouteTool: RouteKey.OWNER_RELIANCE,
  },
  {
    name: 'Business Valuation',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.BUSINESS_VALUATION_DASHBOARD}`,
    id: AssessmentTools.BUSINESS_VALUATION,
    icon: FaHandHoldingDollar,
    component: BusinessOwnerAssessment,
    tooltipText: tooltipData.business_valuation,
    tooltipRouteTool: RouteKey.BUSINESS_VALUATION,
  },
  {
    name: 'Business Continuity',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.BUSINESS_CONTINUITY_DASHBOARD}`,
    id: AssessmentTools.BUSINESS_CONTINUITY,
    icon: FaRecycle,
    component: BusinessOwnerAssessment,
    tooltipText: tooltipData.business_continuity,
    tooltipRouteTool: RouteKey.BUSINESS_CONTINUITY,
  },
  {
    name: 'Value Enhancement Opportunities',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES_DASHBOARD}`,
    id: AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES,
    icon: FaSearchDollar,
    component: BusinessOwnerAssessment,
    tooltipText: tooltipData.value_enhancement_opportunities,
    tooltipRouteTool: RouteKey.BUSINESS_CONTINUITY,
  },
  {
    name: 'Wealth Gap Analysis',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.FINANCIAL_GAP_ANALYSIS_DASHBOARD}`,
    id: AssessmentTools.FINANCIAL_GAP_ANALYSIS,
    icon: MdOutlineAutoGraph,
    component: BusinessOwnerAssessment,
    tooltipText: tooltipData.financial_gap_analysis,
    tooltipRouteTool: RouteKey.BUSINESS_CONTINUITY,
  },
  {
    name: 'Stakeholder Alignment Meeting',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.STAKEHOLDER_ALIGNMENT_MEETING_DASHBOARD}`,
    id: AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING,
    icon: FaCalendarCheck,
    component: BusinessOwnerAssessment,
    tooltipText: tooltipData.stakeholder_alignment_meeting,
    tooltipRouteTool: RouteKey.STAKEHOLDER_ALIGNMENT_MEETING,
  },
  {
    name: 'Asset Protection',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.ASSET_PROTECTION_DASHBOARD}`,
    id: AssessmentTools.ASSET_PROTECTION,
    icon: BsSafe2,
    component: BusinessOwnerAssessment,
    tooltipText: tooltipData.asset_protection,
    tooltipRouteTool: RouteKey.ASSET_PROTECTION,
  },
  {
    name: 'Buyer Type-Deal Structure',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.BUYER_TYPE_DEAL_STRUCTURE_DASHBOARD}`,
    id: AssessmentTools.BUYER_TYPE_DEAL_STRUCTURE,
    icon: LiaHandshake,
    component: BusinessOwnerAssessment,
    tooltipText: tooltipData.buyer_type_deal_structure,
    tooltipRouteTool: RouteKey.BUYER_TYPE_DEAL_STRUCTURE,
  },
  {
    name: 'Business Financial Analysis',
    id: NonAssessmentToolTabs.BUSINESS_FINANCIAL_ANALYSIS,
    icon: BsGraphUp,
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.BUSINESS_FINANCIAL_ANALYSIS}`,
    component: BusinessOwnerAssessment,
    tooltipText: tooltipData.business_financial_analysis,
    tooltipRouteTool: RouteKey.BUSINESS_FINANCIAL_ANALYSIS,
  },
];

export const advisorSidebarRoutes: Array<SidebarRoutesConfigType> = [
  {
    name: 'Advisor Dashboard',
    key: `/${UserRouteType.ADVISOR}/${RouteKey.DASHBOARD}`,
    icon: IoHomeSharp,
    component: AdvisorDashboard,
  },
];

export const adminSidebarRoutes: Array<SidebarRoutesConfigType> = [
  {
    name: 'Admin Dashboard',
    key: `/${UserRouteType.ADMIN}/${RouteKey.DASHBOARD}`,
    icon: IoHomeSharp,
    component: AdminDashboard,
  },
];

  export const enterpriseAdminSidebarRoutes: Array<SidebarRoutesConfigType> = [
    {
      name: 'Enterprise Admin Dashboard',
      key: `/${UserRouteType.ENT_ADMIN}/${RouteKey.DASHBOARD}`,
      icon: IoHomeSharp,
      component: EnterpriseAdminDashboard,
    },
];