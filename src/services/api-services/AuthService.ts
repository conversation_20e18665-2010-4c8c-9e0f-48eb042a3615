import { UserRouteType, UserType } from 'types/enum';
import { ForgotPasswordDTO, ResetPasswordDTO } from 'dtos/reset-password.dto';
import { localStorageService } from 'services/LocalStorageService';
import { AxiosHeaders } from 'axios';
import { User } from '../../models/entities/User';
import { baseApiService } from './BaseApiService';

class AuthService {
  static getInstance(): AuthService {
    return new AuthService();
  }

  async fetchCode(data: {
    email: string;
    password: string;
    userType: UserType;
  }): Promise<{ token: string }> {
    return baseApiService.post(`${data.userType}/login`, data, {
      extras: { useAuth: false },
    });
  }

  async login(data: {
    code: string;
    userType: UserType;
  }): Promise<{ user: User; token: string }> {
    const headers = new AxiosHeaders({
      Authorization: `Bearer ${localStorageService.getLocalStorageValue(
        'mfa_token'
      )}`,
    });

    return baseApiService.post(
      `${data.userType}/me/mfa/validate-code`,
      { code: data.code },
      {
        headers,
        extras: { useAuth: false },
      }
    );
  }

  async resendCode(data: { userType: UserType }): Promise<{ message: string }> {
    const headers = new AxiosHeaders({
      Authorization: `Bearer ${localStorageService.getLocalStorageValue(
        'mfa_token'
      )}`,
    });

    return baseApiService.post(`${data.userType}/me/mfa/resend-code`, null, {
      headers,
      extras: { useAuth: false },
    });
  }

  async fetchMe(userType: UserRouteType): Promise<{ user: User }> {
    return baseApiService.get(`${userType}/me`);
  }

  async forgotPassword(data: {
    userType: UserRouteType;
    data: ForgotPasswordDTO;
  }): Promise<{ data: string }> {
    return baseApiService.post(`${data.userType}/forgot-password`, data.data);
  }

  async resetPassword(data: {
    userType: UserRouteType;
    data: ResetPasswordDTO;
  }): Promise<{ data: string }> {
    return baseApiService.post(`${data.userType}/reset-password`, data.data);
  }

  async setPublicPassword(data: {
    new_password: string;
    token: string;
  }): Promise<{ data: string }> {
    return baseApiService.post(`public/reset-password?token=${data.token}`, {
      new_password: data.new_password,
    });
  }
}

export const authService = AuthService.getInstance();
