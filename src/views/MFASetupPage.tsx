import { FC, memo } from 'react';
import MFASetup from 'views/Profile/MFASetup';
import logo from '../assets/ExitSmartsLogo.svg';

const MFASetupPage: FC = () => {
  return (
    <div className='bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8'>
      <div className='sm:mx-auto sm:w-full sm:max-w-md'>
        <div className='flex justify-center'>
          <img src={logo} alt='ExitSmarts Logo' className='h-20 w-auto' />
        </div>
      </div>

      <div className='mt-8 sm:mx-auto sm:w-full'>
        <div className='bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10'>
          <MFASetup />
        </div>
      </div>
    </div>
  );
};

export default memo(MFASetupPage);
