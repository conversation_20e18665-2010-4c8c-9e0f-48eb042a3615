import { Form, Formik } from 'formik';
import * as yup from 'yup';
import React from 'react';
import Button from 'shared-resources/components/Button/Button';
import Input from 'shared-resources/components/Input/Input';
import { useDispatch, useSelector } from 'react-redux';
import {
  getUserData,
  getUserUpdateProfileLoading,
} from 'store/selectors/user.selector';
import { UserType } from 'types/enum';
import { updateUserProfileAction } from 'store/actions/user.action';
import { toast } from 'react-toastify';
import { passwordSchema } from 'utils/helpers/Helpers';

const changePasswordSchema = yup.object().shape({
  oldPassword: yup.string().required('Old Password is required'),
  newPassword: passwordSchema.required('New Password is required'),
  confirmNewPassword: yup
    .string()
    .oneOf([yup.ref('newPassword')], 'Confirm Password do not match')
    .required('Confirm Password is required'),
});
interface Props {}
const ChangePassword: React.FC<Props> = () => {
  const dispatch = useDispatch();
  const user = useSelector(getUserData);
  const updateProfileLoading = useSelector(getUserUpdateProfileLoading);

  return (
    <div className='bg-white rounded-xl px-12.5 py-10 mt-7'>
      <h1 className='text-lg font-semibold mb-8'>Password</h1>
      <Formik
        initialValues={{
          oldPassword: '',
          newPassword: '',
          confirmNewPassword: '',
        }}
        onSubmit={(values, formikHelpers) => {
          dispatch(
            updateUserProfileAction({
              first_name: user?.first_name,
              last_name: user?.last_name,
              business_name: user?.business_name,
              new_password: values.newPassword,
              old_password: values.oldPassword,
              userType: user?.type as UserType,
              onSuccess: (message) => {
                toast.success(message);
              },
            })
          );
          formikHelpers.resetForm();
        }}
        validationSchema={changePasswordSchema}
      >
        {(formikProps) => (
          <Form
            onSubmit={formikProps.handleSubmit}
            className='flex flex-col gap-7'
          >
            <div className='w-96'>
              <Input
                asterisk
                name='oldPassword'
                id='oldPassword'
                type='password'
                className='h-[2.85rem] outline-none rounded-xl '
                label='Old Password'
                labelClassName='font-medium'
                requiredLabel
                value={formikProps.values.oldPassword}
                onChange={formikProps.handleChange}
                onBlur={formikProps.handleBlur}
                error={
                  formikProps.touched.oldPassword &&
                  formikProps.errors.oldPassword
                }
              />
            </div>
            <div className='flex max-w-2/3 gap-20'>
              <div className='w-96'>
                <Input
                  asterisk
                  name='newPassword'
                  id='newPassword'
                  type='password'
                  className='h-[2.85rem] outline-none rounded-xl'
                  label='New Password'
                  labelClassName='font-medium'
                  requiredLabel
                  value={formikProps.values.newPassword}
                  onChange={formikProps.handleChange}
                  onBlur={formikProps.handleBlur}
                  error={
                    formikProps.touched.newPassword &&
                    formikProps.errors.newPassword
                  }
                />{' '}
              </div>
              <div className='w-96'>
                <Input
                  asterisk
                  name='confirmNewPassword'
                  id='confirmNewPassword'
                  type='password'
                  className='h-[2.85rem] outline-none rounded-xl'
                  label='Confirm New Password'
                  labelClassName='font-medium'
                  requiredLabel
                  value={formikProps.values.confirmNewPassword}
                  onChange={formikProps.handleChange}
                  onBlur={formikProps.handleBlur}
                  error={
                    formikProps.touched.confirmNewPassword &&
                    formikProps.errors.confirmNewPassword
                  }
                />
              </div>
            </div>
            <div className='flex space-x-4 pt-20 justify-end'>
              <Button
                className='px-6 py-2'
                type='submit'
                disabled={!formikProps.dirty || updateProfileLoading}
                isSubmitting={updateProfileLoading}
              >
                Save
              </Button>
              <Button
                theme='secondary'
                className='px-6 py-2'
                type='button'
                onClick={formikProps.resetForm}
                disabled={updateProfileLoading}
              >
                Cancel
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};
export default ChangePassword;
