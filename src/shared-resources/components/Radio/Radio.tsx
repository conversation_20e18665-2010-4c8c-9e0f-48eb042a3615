import React, { FC, FocusEvent } from 'react';
// types
import { RadioItem } from 'shared-resources/types/Radio.type';

export interface RadioProps {
  options: RadioItem[];
  onChange: (selected: string | null) => void;
  onBlur?: (e: FocusEvent<HTMLInputElement>) => void;
  onFocus?: (e: FocusEvent<HTMLInputElement>) => void;
  selected?: string | null;
  name?: string;
  showLabel?: boolean;
  className?: string; // using for outer div
  className2?: string; // using for inner div
  inputClassName?: string;
  labelClassName?: string;
}

const Radio: FC<RadioProps> = ({
  options,
  name,
  onChange,
  selected,
  onBlur,
  onFocus,
  showLabel = true,
  className,
  className2,
  inputClassName,
  labelClassName,
}) => {
  const handleDivClick = (value: string) => {
    if (selected !== value) {
      onChange(value);
    }
  };

  return (
    <div className={`flex gap-x-4  ${className}`}>
      {options.map((item, index) => (
        <div
          tabIndex={0}
          role='button'
          onClick={() => handleDivClick(item.value)}
          onKeyDown={() => handleDivClick(item.value)}
          key={name ? `${name}_${item.value}_${index}` : item.value}
          className={`flex items-center ${className2}`}
        >
          <input
            id={name ? `${name}_${item.value}` : item.value}
            name={name}
            type='radio'
            onBlur={onBlur}
            onFocus={onFocus}
            checked={selected === item.value}
            onChange={(e) => onChange(e.target.checked ? item.value : null)}
            className={`w-4 h-4 text-indigo-600 border-gray-300 cursor-pointer focus:ring-indigo-600 ${inputClassName}`}
          />
          {!showLabel ? (
            ''
          ) : (
            <label
              htmlFor={item.value}
              className={`block ml-3 text-sm font-medium leading-6 text-gray-900 ${labelClassName}`}
            >
              {item.label}
            </label>
          )}
        </div>
      ))}
    </div>
  );
};

export default Radio;
