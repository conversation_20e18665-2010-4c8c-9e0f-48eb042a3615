import { createSelector } from 'reselect';
import { AppState } from 'store';
import {
  ForgotPasswordState,
  ResetPasswordState,
  SetPublicPasswordState,
} from 'store/types/forgot-password.type';

const forgotPasswordState = (state: AppState) => state.forgotPassword;

type ForgotAndResetPasswordState = ForgotPasswordState &
  ResetPasswordState &
  SetPublicPasswordState;

export const forgotPasswordSuccessSelector = createSelector(
  [forgotPasswordState],
  (state: ForgotAndResetPasswordState) => state.success
);

export const forgotPasswordErrorSelector = createSelector(
  [forgotPasswordState],
  (state: ForgotAndResetPasswordState) => state.error
);

export const forgotPasswordLoadingSelector = createSelector(
  [forgotPasswordState],
  (state: ForgotAndResetPasswordState) => state.loading
);

export const resetPasswordSuccessSelector = createSelector(
  [forgotPasswordState],
  (state: ForgotAndResetPasswordState) => state.reset.success
);

export const resetPasswordErrorSelector = createSelector(
  [forgotPasswordState],
  (state: ForgotAndResetPasswordState) => state.reset.error
);

export const resetPasswordLoadingSelector = createSelector(
  [forgotPasswordState],
  (state: ForgotAndResetPasswordState) => state.reset.loading
);
export const setPublicPasswordSuccessSelector = createSelector(
  [forgotPasswordState],
  (state: ForgotAndResetPasswordState) => state.setPublic.success
);

export const setPublicPasswordErrorSelector = createSelector(
  [forgotPasswordState],
  (state: ForgotAndResetPasswordState) => state.setPublic.error
);

export const setPublicPasswordLoadingSelector = createSelector(
  [forgotPasswordState],
  (state: ForgotAndResetPasswordState) => state.setPublic.loading
);
