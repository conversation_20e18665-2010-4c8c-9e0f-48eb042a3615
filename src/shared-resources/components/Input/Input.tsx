import classNames from 'classnames';
import React, { FunctionComponent } from 'react';
import InputHelper from '../Inputhelper/InputHelper';
import InputLabel from '../InputLabel/InputLabel';
import PasswordInput from './PasswordInput';
import { InputProps } from '../../types/Input.type';

const Input: FunctionComponent<InputProps> = (props: InputProps) => {
  const {
    id,
    name,
    value,
    type,
    onChange,
    onBlur,
    placeholder,
    maxLength,
    label,
    helperText,
    leadingIcon,
    trailingIcon,
    trailingIcon2,
    asterisk,
    className,
    labelClassName,
    style,
    disabled,
    error,
    showErrorText = true,
    requiredLabel,
    labelIcon,
    labelIcon2,
    containerClassName,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    passwordIconLeft = false,
    autoComplete,
    fieldType = 'input',
    onLabelIconClick,
  } = props;

  if (type === 'password') return <PasswordInput {...props} />;

  const inputClassname = classNames(
    'block w-full placeholder-opacity-50 rounded-xl h-12.5 text-md md:text-para-02 focus:outline-none py-1.5 2xl:py-3 px-5 border border-solid border-gray-06 shadow-1',
    {
      'pl-10': !!leadingIcon,
      'pr-10': !!trailingIcon,
      'pl-11': !!trailingIcon2,
      'text-gray-500 bg-gray-100 px-2': !!disabled,
      'border ': !!error,
    },
    className
  );
  return (
    <div
      className={classNames(
        `w-full flex flex-col items-start justify-start`,
        containerClassName
      )}
    >
      {label && (
        <div
          className={`flex ${
            labelIcon2 ? '' : 'justify-between'
          }  items-center w-full `}
        >
          {labelIcon2 && labelIcon2}
          <InputLabel
            label={label}
            asterisk={asterisk}
            className={classNames(labelClassName)}
          />
          {labelIcon && (
            <button
              onClick={onLabelIconClick}
              className='h-5 w-5 cursor-pointer'
            >
              <img alt='icon' src={labelIcon} />
            </button>
          )}
        </div>
      )}

      <div
        className={classNames('relative rounded-md w-full', {
          'mt-2': !!label,
        })}
      >
        {leadingIcon && (
          <div className='absolute inset-y-0 left-0 flex items-center w-8 pl-3 pointer-events-none'>
            {leadingIcon}
          </div>
        )}
        {trailingIcon2 && (
          <div className='absolute inset-y-0  left-5 flex items-center w-8 pr-3 pointer-events-none'>
            {trailingIcon2}
          </div>
        )}
        {fieldType === 'input' ? (
          <input
            autoComplete={autoComplete}
            id={id}
            name={name}
            value={value}
            type={type}
            onChange={onChange}
            onBlur={onBlur}
            disabled={disabled}
            placeholder={placeholder}
            style={style}
            maxLength={maxLength ?? 255}
            required={requiredLabel}
            className={inputClassname}
          />
        ) : (
          <textarea
            autoComplete={autoComplete}
            id={id}
            name={name}
            value={value}
            onChange={onChange}
            onBlur={onBlur}
            disabled={disabled}
            placeholder={placeholder}
            style={style}
            maxLength={maxLength}
            required={requiredLabel}
            className={inputClassname}
          />
        )}
        {trailingIcon && (
          <div className='absolute inset-y-0 right-0 flex items-center w-8 pr-3'>
            {trailingIcon}
          </div>
        )}
      </div>
      <InputHelper type='helper' text={error ? undefined : helperText} />
      {showErrorText && (
        <div className='h-5'>
          <InputHelper type='error' text={error} />
        </div>
      )}
    </div>
  );
};

export default React.memo(Input);
