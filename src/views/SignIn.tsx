import { Form, Formik } from 'formik';
import React from 'react';
import { FaEnvelope, FaUnlock } from 'react-icons/fa';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';

import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getMfaCode } from 'store/actions/auth.action';
import { RouteKey, UserRouteType, UserType } from 'types/enum';
import { LoginInitialState } from 'types/login.types';
import * as yup from 'yup';
import { isAuthLoadingSelector } from 'store/selectors/auth.selector';
import logo from '../assets/ExitSmartsLogo.svg';
import linkedinIcon from '../assets/linkedin.svg';



const signInSchema = yup.object().shape({
  email: yup
    .string()
    .required('Email is required')
    .email('This must be a valid e-mail'),
  password: yup.string().required('Password is required'),
});

interface Props {}

const SignIn: React.FC<Props> = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const { pathname } = location;
  const isAuthLoading = useSelector(isAuthLoadingSelector);

  const initialValues: LoginInitialState = {
    email: '',
    password: '',
  };
  const currentPath = pathname.split('/')[1];
  const navigate = useNavigate();
  const handleSubmit = (values: LoginInitialState) => {
    if (Object.values(UserRouteType).includes(currentPath as any)) {
      dispatch(
        getMfaCode({
          ...values,
          userType: currentPath as UserType,
          onSuccess: () => navigate(`/${currentPath}/validate-code`, {
            state: {
              email: values.email,
            },
          })
        })
      );
    }
  };

  const getUserType = () => {
    if (Object.values(UserRouteType).includes(currentPath as UserRouteType)) {
      return currentPath.replaceAll('-', ' ').replaceAll('_', ' ');
    }
    return '';
  };

  const getNavigationRoute = () => {
    if (currentPath === UserRouteType.ADVISOR || currentPath === UserRouteType.ENT_ADMIN) {
      return `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.LOGIN}`;
    }
    else if (currentPath === UserRouteType.BUSINESS_OWNER) {
      return `/${UserRouteType.ADVISOR}/${RouteKey.LOGIN}`;
    }
    else if (currentPath === UserRouteType.ADMIN) {
      return `/${UserRouteType.ADVISOR}/${RouteKey.LOGIN}`;
    }
    return '';
  };

  return (
    <div className='flex flex-col w-full h-full'>
      <div className='relative flex justify-between w-full px-11 py-4.5 bg-white border border-gray-300 shadow-[0_4px_6px_-6px_#222]'>
      
        <img
          src={logo}
          alt='exit-smart-logo'
          width={228}
          height={55}
          className='object-contain w-[14.25rem] h-[3.4375rem]'
        />
        
      <div className='flex justify-end items-center w-full gap-4'>
      {/* <a href="https://www.facebook.com/people/EZDocTracker/61556572380636/?mibextid=ZbWKwL" target="_blank" rel="noopener noreferrer">
          <img src={facebookIcon} alt="Facebook" className="w-8 h-8" />
        </a>
        <a href="https://www.youtube.com/channel/UC3AxjYzt1UABgqQ9xa7CytQ" target="_blank" rel="noopener noreferrer">
          <img src={youtubeIcon} alt="YouTube" className="w-8 h-8" />
        </a> */}
        <a href="https://www.linkedin.com/company/exitsmartsinc/about/" target="_blank" rel="noopener noreferrer">
          <img src={linkedinIcon} alt="LinkedIn" className="w-8 h-8" />
        </a>
      </div>
    </div>
    <div className='flex flex-col justify-center space-y-8 items-center h-screen bg-blue-03'>
      <div className='w-135 bg-gray-01 rounded-xl p-12'>
        <Formik
          initialValues={initialValues}
          onSubmit={handleSubmit}
          validationSchema={signInSchema}
          validateOnBlur
        >
          {(formikProps) => (
            <Form
              onSubmit={formikProps.handleSubmit}
              className='h-full w-full flex flex-col justify-between'
            >
              <div className='space-y-2'>
                <h1 className='text-blue-01 text-xl font-bold text-center capitalize'>
                  Sign In To Your {getUserType()} Account
                </h1>
                <h1 className='text-black font-medium text-center '>
                  Always Be Exit Ready
                </h1>
              </div>
              <div>
                <FormikInput
                  trailingIcon2={<FaEnvelope className='text-gray-400' />}
                  name='email'
                  id='email'
                  type='email'
                  placeholder='e.g. <EMAIL>'
                  className='h-12.5 !border-gray-02 outline-none rounded-xl'
                  labelClassName='mb-2.5'
                  label='E-Mail Address'
                  requiredLabel
                  disabled={isAuthLoading} />
                <FormikInput
                  name='password'
                  id='password'
                  type='password'
                  passwordIconLeft
                  className='h-12.5 !border-gray-02 outline-none rounded-xl'
                  labelClassName='mb-2.5'
                  label='Password'
                  error={formikProps.touched.password && formikProps.errors.password}
                  disabled={isAuthLoading} />
              </div>
                <div className='flex justify-between my-5'>
                <button
                  className='text-blue-01 text-start w-fit font-medium'
                  type='button'
                  onClick={() => {
                  navigate(
                    `/${currentPath as UserRouteType}/forgot-password`
                  );
                  } }
                >
                  Forgot Password ?
                </button>
                <div className='flex flex-col items-center space-y-2'>
                  <button
                  className='text-blue-01 font-medium'
                  type='button'
                  onClick={() => {
                    navigate(getNavigationRoute());
                  }}
                  >
                  {(currentPath === UserRouteType.ADVISOR || currentPath === UserRouteType.ENT_ADMIN)
                    ? 'Are you a Business Owner ?'
                    : 'Are you an Advisor ?'}
                  </button>

                  {currentPath === UserRouteType.ADVISOR && (
                  <button
                    className='text-blue-01 font-medium'
                    type='button'
                    onClick={() => {
                    navigate(`/${UserRouteType.ENT_ADMIN}/${RouteKey.LOGIN}`);
                    }}
                  >
                    Are you an Enterprise Admin?
                  </button>
                  )}
                </div>
                </div>

              <Button
                className='h-13'
                type='submit'
                disabled={!(formikProps.isValid && formikProps.dirty) || isAuthLoading}
                isSubmitting={isAuthLoading}
              >
                <div className='flex justify-center font-semibold items-center gap-2'>
                  <FaUnlock />
                  <span className='mt-1'>Sign In</span>
                </div>
              </Button>
            </Form>
          )}
        </Formik>
      </div>
    </div>
    </div>
  );
};

export default SignIn;
