import classNames from 'classnames';
import React from 'react';
import { ITooltip, Tooltip } from 'react-tooltip';

const TooltipComponent: React.FC<ITooltip> = (props) => {
  const { className, classNameArrow, place } = props;

  const containerPosition = place?.split('-')[0];
  return (
    <Tooltip
      {...props}
      className={`${className} max-w-[16rem] !bg-white border border-gray-02 !text-black-01 !text-sm !rounded-lg`}
      classNameArrow={`${classNameArrow} border-r border-b border-t-transparent border-r-gray-02 border-b-gray-02 !w-3 !h-3 ${classNames(
        containerPosition === 'top' && '!top-[calc(100%-.35rem)]',
        containerPosition === 'bottom' && '!-top-[.45rem]',
        containerPosition === 'right' && '!-left-[0.45rem]',
        containerPosition === 'left' && '!left-[calc(100%-.3rem)]'
      )} `}
    />
  );
};

export default TooltipComponent;
