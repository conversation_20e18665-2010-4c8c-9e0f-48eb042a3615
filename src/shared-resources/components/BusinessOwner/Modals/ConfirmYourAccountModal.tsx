import React from 'react';
import Button from 'shared-resources/components/Button/Button';
import confirmaccount from '../../../../assets/confirmaccount.svg';

interface ConfirmYourAccountModalProps {
  handleConfirmYourAccountModalVisibility: (value: boolean) => void;
  onSubmitConfirmAccount: (value: boolean) => void;
}
const ConfirmYourAccountModal: React.FC<ConfirmYourAccountModalProps> = (
  props
) => {
  const { handleConfirmYourAccountModalVisibility, onSubmitConfirmAccount } =
    props;

  return (
    <div className='w-[31.25rem] bg-white rounded-xl'>
      <div className='flex flex-col items-center justify-center p-[4.375rem]'>
        <img className='h-40 w-40' src={confirmaccount} alt='Confim' />
        <h1 className='py-[2rem] text-lg font-semibold'>
          Confirming Your Account
        </h1>
        <h1 className='text-center text-black-02'>
          Before you proceed, please confirm your account.
        </h1>
      </div>
      <div className='flex justify-evenly rounded-b-xl bg-slate-500'>
        <Button
          theme='secondary'
          onClick={() => handleConfirmYourAccountModalVisibility(false)}
          className='w-full py-4 rounded-bl-xl border-none bg-blue-02 rounded-none  '
        >
          Not Now
        </Button>
        <Button
          onClick={() => onSubmitConfirmAccount(true)}
          className='w-full py-4 rounded-none rounded-br-xl border-none'
        >
          Yes
        </Button>
      </div>
    </div>
  );
};

export default ConfirmYourAccountModal;
