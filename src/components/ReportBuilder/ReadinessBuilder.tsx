import cx from 'classnames';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { renderToString } from 'react-dom/server';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import Checkbox from 'shared-resources/components/CheckBox/Checkbox';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import { fetchAssessmentReport } from 'store/actions/assessment-report.action';
import { fetchComments } from 'store/actions/tool-comment.action';
import { resetAssessmentData } from 'store/reducers/assessment-tool.reducer';
import {
  getAssessmentReportLoading,
  getAssessmentReportResponse,
} from 'store/selectors/assessment-report.selector';
import { getToolComments } from 'store/selectors/tool-comment.selector';
import { getUserData } from 'store/selectors/user.selector';
import { AssessmentTools, RouteKey, UserType } from 'types/enum';
import { findQuestionById } from 'views/Readiness-Assesment/ReadinessConfig';
import {
  BusinessReadiness,
  FinanceReadiness,
  PlanningReadiness,
  ReadinessAssessmentTitle,
  TransitionKnowledge,
  TransitionObjectiveReadiness,
} from 'utils/Readiness-Assessment/ReadinessAssessmentEnums';

interface ReadinessBuilderProps {
  onSelectionChange: (selectedData: {
    [key: string]: { content: string; type: string; comments: any[] };
  }) => void;
}

const ReadinessBuilder: React.FC<ReadinessBuilderProps> = ({
  onSelectionChange,
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const response = useSelector(getAssessmentReportResponse);
  const loggedInUserData = useSelector(getUserData);
  const assessmentLoading = useSelector(getAssessmentReportLoading);
  const comments = useSelector(getToolComments);

  const [selectedQuestions, setSelectedQuestions] = useState<
    Record<string, boolean>
  >({});
  const [selectedComments, setSelectedComments] = useState<
    Record<string, boolean>
  >({});

  useEffect(() => {
    if (id && loggedInUserData?.type === UserType.ADVISOR) {
      dispatch(
        fetchAssessmentReport({
          id,
          tool: AssessmentTools.READINESS_ASSESSMENT,
          onError: () =>
            navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`),
        })
      );
      dispatch(fetchComments(Number(id), AssessmentTools.READINESS_ASSESSMENT));
    }
  }, [dispatch, id, loggedInUserData?.type, navigate]);

  useEffect(
    () => () => {
      dispatch(resetAssessmentData());
    },
    []
  );

  const handleCheckboxChange = useCallback((questionId: string) => {
    setSelectedQuestions((prev) => ({
      ...prev,
      [questionId]: !prev[questionId],
    }));
  }, []);

  const handleCommentCheckboxChange = useCallback((commentId: string) => {
    setSelectedComments((prev) => ({
      ...prev,
      [commentId]: !prev[commentId],
    }));
  }, []);

  const groupQuestionsByEnum = useMemo(() => {
    const grouped: { [key: string]: string[] } = Object.values(
      ReadinessAssessmentTitle
    ).reduce((acc, title) => ({ ...acc, [title]: [] }), {});
    const readinessEnums = [
      BusinessReadiness,
      FinanceReadiness,
      TransitionObjectiveReadiness,
      TransitionKnowledge,
      PlanningReadiness,
    ];

    Object.keys(response.assessment_response).forEach((questionId) => {
      readinessEnums.forEach((enumType, index) => {
        if (Object.values(enumType).includes(questionId as any)) {
          grouped[Object.values(ReadinessAssessmentTitle)[index]].push(
            questionId
          );
        }
      });
    });

    return grouped;
  }, [response.assessment_response]);

  const renderQuestionDataForQuill = (
    questionId: string,
    questionAnswer: string,
    index: number
  ) => (
    <div className='mt-2 flex flex-col gap-4 w-full relative'>
      <div>
        Ques {index + 1}. {findQuestionById(questionId)}
      </div>
      <div className='px-4 py-3 rounded-lg border text-blue-01 border-gray-02'>
        Ans. {questionAnswer}
      </div>
    </div>
  );

  const selectedData = useMemo(() => {
    const data: {
      [key: string]: { content: string; type: string; comments: any[] };
    } = {};

    Object.entries(groupQuestionsByEnum).forEach(([enumKey, questionIds]) => {
      const enumTitle = enumKey;
      let content = '';

      questionIds.forEach((questionId, index) => {
        if (selectedQuestions[questionId]) {
          const questionAnswer = (response.assessment_response as any)[
            questionId
          ];
          const renderedQuestionAnswer = renderToString(
            renderQuestionDataForQuill(questionId, questionAnswer, index + 1)
          );
          content += `${renderedQuestionAnswer}\n`;
        }
      });

      const selectedEnumComments = comments.filter(
        (comment) =>
          comment.metadata?.screen === enumTitle &&
          selectedComments[comment.id.toString()]
      );

      if (content || selectedEnumComments.length > 0) {
        data[enumKey] = {
          content,
          type: 'readiness-data',
          comments: selectedEnumComments,
        };
      }
    });

    return data;
  }, [
    selectedQuestions,
    selectedComments,
    response.assessment_response,
    comments,
    groupQuestionsByEnum,
  ]);

  useEffect(() => {
    if (Object.keys(selectedData).length > 0) {
      onSelectionChange(selectedData);
    }
  }, [selectedData, onSelectionChange]);

  if (assessmentLoading)
    return (
      <div className='h-[calc(100vh-18rem)]'>
        <Spinner />
      </div>
    );

  return (
    <div
      className={cx(
        'h-[calc(100vh-18rem)] bg-white overflow-y-scroll scrollbar rounded-xl'
      )}
    >
      <p className='text-md mb-4 text-blue-01'>
        Respond to each question using a scale of 1 through 6 where 1 means you
        need a lot of help in this area and a 6 means that you are on top of
        this area.
      </p>
      {Object.entries(groupQuestionsByEnum).map(([enumKey, questionIds]) => (
        <div key={enumKey} className='mb-8'>
          <span className='text-lg font-semibold mb-4 text-blue-01'>
            {enumKey}
          </span>
          {questionIds.map((questionId, index) => {
            const questionAnswer = (response.assessment_response as any)[
              questionId
            ];
            return (
              <div
                key={questionId}
                className='w-full pr-4 relative bg-white mb-4 flex flex-col gap-4'
              >
                <Checkbox
                  onChange={() => handleCheckboxChange(questionId)}
                  value={selectedQuestions[questionId] || false}
                  text={
                    <div>
                      Ques {index + 1}. {findQuestionById(questionId)}
                    </div>
                  }
                  className='!mt-0'
                />
                <div className='px-4 py-3 rounded-lg border text-blue-01 border-gray-02'>
                  Ans. {questionAnswer as string}
                </div>
              </div>
            );
          })}
          {comments?.filter((comment) => comment.metadata?.screen === enumKey)
            ?.length > 0 && (
            <span className='text-md font-semibold mb-4'>Comments:</span>
          )}
          {comments
            .filter((comment) => comment.metadata?.screen === enumKey)
            .map((comment) => (
              <div key={comment.id} className='ml-4 mb-2 flex items-center'>
                <Checkbox
                  onChange={() =>
                    handleCommentCheckboxChange(comment.id.toString())
                  }
                  value={selectedComments[comment.id.toString()] || false}
                  text={comment.comment as any}
                  className='!mt-0'
                />
              </div>
            ))}
        </div>
      ))}
    </div>
  );
};

export default ReadinessBuilder;
