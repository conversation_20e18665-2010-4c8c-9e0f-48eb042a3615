import { createSelector } from 'reselect';
import { AppState } from 'store';
import { DocumentsState } from 'store/reducers/documents.reducer';

const documentsState = (state: AppState) => state.documents;

export const documentsDataSelector = createSelector(
  [documentsState],
  (state: DocumentsState) => state.entities
);

export const documentsLoading = createSelector(
  [documentsState],
  (state: DocumentsState) => Boolean(state.loading)
);

export const documentsListLoading = createSelector(
  [documentsState],
  (state: DocumentsState) => Boolean(state.listLoading)
);

export const getValuationAmount = createSelector(
  [documentsState],
  (state: DocumentsState) => state.valuationAmount
);

export const getValuationAmountLoading = createSelector(
  [documentsState],
  (state: DocumentsState) => state.valuationAmountLoading
);
