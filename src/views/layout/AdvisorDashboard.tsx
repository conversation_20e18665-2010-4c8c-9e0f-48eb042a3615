import { debounce } from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';
import { IoSearch } from 'react-icons/io5';
import { useDispatch, useSelector } from 'react-redux';
import { useSearchParams } from 'react-router-dom';
import BusinessOwnersList from 'shared-resources/components/BusinessOwner/BusinessOwnersList';
import CreateOrEditBusinessOwnerModal from 'shared-resources/components/BusinessOwner/Modals/CreateEditBo';
import Button from 'shared-resources/components/Button/Button';
import Input from 'shared-resources/components/Input/Input';
import Modal from 'shared-resources/components/Modal/Modal';
import { fetchBusinessOwners } from 'store/actions/business-owner.action';
import { getAdvisorSubscriptionData } from 'store/selectors/user.selector';

const AdvisorDashboard = () => {
  const [searchedBusinessOwner, setSearchedBusinessOwner] = useState('');
  const [modalVisible, setModalVisibility] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const handleModalVisibility = (value: boolean) => {
    setModalVisibility(value);
  };
  const dispatch = useDispatch();
  const subscriptionsInfo: any = useSelector(getAdvisorSubscriptionData);

  const handleFilterParams = (paramsValue: string) => {
    const filtersParams = new URLSearchParams({
      search: paramsValue,
    } as Record<string, string>);
    setSearchParams(filtersParams);
  };
  const debouncedSearch = useCallback(
    debounce((searchValue: string) => {
      handleFilterParams(searchValue);
      dispatch(fetchBusinessOwners({ filters: { search: searchValue, secondaryAdvisor: true } }));
    }, 500),
    [dispatch]
  );

  const handleOnSearchChange = (event: any) => {
    setSearchedBusinessOwner(event?.target?.value);
    debouncedSearch(event?.target?.value);
  };

  useEffect(() => {
    const params = searchParams.get('search');
    handleFilterParams(params ?? '');
    if (params) {
      setSearchedBusinessOwner(params);
    }
    if (!params) setSearchParams('');
  }, [searchParams, setSearchedBusinessOwner]);

  return (
    <div className='flex flex-col w-full h-full'>
      <div
        className={`flex self-end ${
          subscriptionsInfo?.primaryAdvisor
            ? 'max-w-[44.3125rem]'
            : 'max-w-[28rem]'
        } gap-6 w-full max-h-12.5`}
      >
        <Input
          trailingIcon={<IoSearch className='text-gray-300 ' size={20} />}
          value={searchedBusinessOwner}
          onChange={handleOnSearchChange}
          name='owners-search'
          key='owners-search'
          className='rounded-xl h-12.5 outline-none'
          placeholder='Search Here'
        />

        {subscriptionsInfo?.primaryAdvisor && (
          <Button
            className='w-fit whitespace-nowrap py-2 px-6'
            theme='primary'
            onClick={() => {
              setModalVisibility(true);
            }}
          >
            Create Business Owner
          </Button>
        )}
      </div>
      <BusinessOwnersList
        isPrimaryAdvisor={subscriptionsInfo?.primaryAdvisor}
        showSecondaryAdvisor={true}
      />
      {modalVisible && (
        <Modal
          title='Create Business Owner'
          visible={modalVisible}
          handleVisibility={handleModalVisibility}
          classname='w-[65.185rem]'
        >
          <CreateOrEditBusinessOwnerModal
            handleModalVisibility={handleModalVisibility}
          />
        </Modal>
      )}
    </div>
  );
};

export default AdvisorDashboard;
