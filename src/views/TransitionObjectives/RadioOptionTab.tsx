import React, {
  FC,
  useState,
  useRef,
  useCallback,
  useEffect,
  memo,
} from 'react';

import { debounce } from 'lodash';

type RadioOptionTabProps = {
  option: {
    value: string;
    id: number;
    textField?: boolean | undefined;
    question?: string;
  };
  selectedValue?: {
    key: string;
    value?: string | null;
  };
  handleRadioChange: (value: { key: string; value?: string | null }) => void;
  isMulti?: boolean;
};
const RadioOptionTab: FC<RadioOptionTabProps> = ({
  handleRadioChange,
  selectedValue,
  option,
  isMulti,
}) => {
  const [textAreaAnswer, setTextAreaAnswer] = useState(selectedValue?.value);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  const debouncedUpdate = useCallback(
    debounce((value) => {
      handleRadioChange({ key: option.value, value });
    }, 500),
    [handleRadioChange, option.value]
  );

  useEffect(() => {
    if (textAreaAnswer !== selectedValue?.value) {
      debouncedUpdate(textAreaAnswer);
    }
    return () => debouncedUpdate.cancel();
  }, [textAreaAnswer, debouncedUpdate, selectedValue?.value]);

  useEffect(() => {
    if (option.textField && selectedValue?.key === option.value) {
      const textAreaElement = textAreaRef.current;
      if (textAreaElement) {
        textAreaElement.focus();
        const { length } = textAreaElement.value;
        textAreaElement.setSelectionRange(length, length);
      }
    }
  }, [textAreaAnswer, option.textField, selectedValue?.key, option.value]);

  const isOtherAdditionalOption = ['Additional', 'Other'].includes(
    option.value
  );

  return (
    <div
      className='border-2 w-[52rem] px-2 py-3 border-gray-300 flex items-center rounded-md gap-4 mx-auto'
      onClick={() =>
        handleRadioChange({
          key: option.value,
          value: selectedValue?.value || null,
        })
      }
      role='button'
      tabIndex={0}
    >
      <input
        type={isMulti ? 'checkbox' : 'radio'}
        value={option.value}
        checked={selectedValue?.key === option.value}
        onChange={() => {}}
      />
      <div className='flex flex-col w-full'>
        <h3 className='text-gray-600 font-medium'>{option.value}</h3>
        {option.textField && selectedValue?.key === option.value && (
          <div className='flex flex-col'>
            {(isOtherAdditionalOption || option.question) && (
              <h4 className='text-sm text-gray-600'>
                {(isOtherAdditionalOption && 'Please specify') ||
                  option.question}
              </h4>
            )}
            <textarea
              ref={textAreaRef}
              value={textAreaAnswer || ''}
              onChange={(e) => {
                setTextAreaAnswer(e.target.value);
                e.stopPropagation();
              }}
              onClick={(e) => {
                e.stopPropagation();
              }}
              rows={4}
              className='border-b-2 outline-none w-full mt-4 resize-none'
            />
          </div>
        )}
      </div>
    </div>
  );
};
export default memo(RadioOptionTab);
