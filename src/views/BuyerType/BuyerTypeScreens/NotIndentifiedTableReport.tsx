import React from 'react';
import {
  NotIdentifiedTableHeaderOptions,
  BuyerTypeScreens,
  NotIdentifiedTableResponseType,
  ChoiceOptions,
} from '../BuyerTypeConfig';

interface Props {
  data: any;
}

const NotIndentifiedTableReport: React.FC<Props> = ({ data }) => {
  const values = data?.[BuyerTypeScreens.NOT_IDENTIFIED_TABLE] || {};

  return (
    <div className='px-10 py-5 font-medium select-none pointer-events-none'>
      <span>
        Below is a listing of inside and third-party buyers. This chart gives
        you the opportunity to choose your top three preferences.
      </span>
      <div>
        <div className='pr-1 mt-3 pb-2'>
          <div className='bg-blue-01 z-10 tracking-[0.07rem] grid grid-cols-5 text-white rounded-t-md text-[0.9rem]'>
            {NotIdentifiedTableHeaderOptions?.map((h, index) => (
              <div
                key={h}
                className={`px-5 flex items-center border-r border-gray-02 justify-center py-4 h-full ${
                  index === 0 && 'rounded-tl-md col-span-2'
                } ${index === 3 && 'border-r-0 rounded-t-md'}`}
              >
                <span className='text-center'>{h}</span>
              </div>
            ))}
          </div>
          {Object.keys(values)?.map((key) => (
            <div
              key={key}
              className='grid grid-cols-10'
              hidden={key === 'selectedTypes'}
            >
              <div className='col-span-4 text-[0.9rem] flex items-center gap-3 px-5 py-1 border-b border-r border-l border-gray-02'>
                <div className='min-w-[1rem] min-h-[1rem] w-4 h-4 border border-gray-400 rounded flex items-center justify-center cursor-default shrink-0'>
                  {values?.[key as keyof NotIdentifiedTableResponseType]
                    ?.checked && (
                    <div className='min-w-[0.625rem] min-h-[0.625rem] w-2.5 h-2.5 bg-blue-600 rounded shrink-0' />
                  )}
                </div>
                <span>{key}</span>
              </div>
              <div className='grid grid-cols-3 col-span-6 w-full h-full border-b border-gray-02'>
                {ChoiceOptions.map((option) => (
                  <div
                    key={option.value}
                    className='flex items-center justify-center border-r border-gray-02 h-[3.2rem]'
                  >
                    <div className='min-w-[1.25rem] min-h-[1.25rem] w-5 h-5 border border-gray-400 rounded-full flex items-center justify-center cursor-default shrink-0'>
                      {values?.[key as keyof NotIdentifiedTableResponseType]
                        ?.choice === option.value && (
                        <div className='min-w-[0.75rem] min-h-[0.75rem] w-3 h-3 bg-blue-600 rounded-full shrink-0' />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default NotIndentifiedTableReport;
