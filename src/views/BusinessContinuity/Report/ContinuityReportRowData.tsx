import React from 'react';
import ContuinuityRowView from '../shared-components/ContuinuityRowView';

interface ContinuityReportRowDataProps {
  question?: string;
  responseData: any;
  fieldKey: string;
  questionClassname?: string;
  rowClassname?: string;
}
const ContinuityReportRowData: React.FC<ContinuityReportRowDataProps> = (
  props
) => {
  const { question, responseData, fieldKey, questionClassname, rowClassname } =
    props;

  return (
    <div className='flex flex-col space-y-4 w-full'>
      {question && (
        <span className={`font-semibold ${questionClassname}`}>{question}</span>
      )}
      <ContuinuityRowView
        fieldKey={fieldKey}
        formValues={responseData}
        isReportView
        initialValues={responseData?.[fieldKey]?.[0] ?? {}}
        classname={rowClassname}
      />
    </div>
  );
};

export default ContinuityReportRowData;
