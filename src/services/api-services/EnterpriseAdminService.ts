import { baseApiService } from './BaseApiService';

export interface EnterpriseAdmin {
  id: string;
  ep_name: string;
  enterpriseLogo?: string;
  location?: string;
  is_existing_advisor: 'existing' | 'new';
  advisorId?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  createdAt: string;
  updatedAt: string;
}

export interface EnterpriseAdminsListResponse {
  data: EnterpriseAdmin[];
  total: number;
  page: number;
  limit: number;
}

export interface FetchEnterpriseAdminsParams {
  page?: number;
  limit?: number;
  search?: string;
}

class EnterpriseAdminService {

  // Create Enterprise Admin
  async createEnterpriseAdmin(payload: any) {
    return baseApiService.post('/enterprise/create-enterprise', payload);
  }

  // Update Enterprise Admin
  async updateEnterpriseAdmin(id: string, payload: any) {
    return baseApiService.put(`/enterprise/update-enterprise/${id}`, payload);
  }

  // Delete Enterprise Admin
  async deleteEnterpriseAdmin(id: string) {
    return baseApiService.delete(`/enterprise/delete-enterprise/${id}`);
  }

  // Get Enterprise Admins List
  async getEnterpriseAdminsList(params: FetchEnterpriseAdminsParams = {}) {
    const queryParams = new URLSearchParams();
    
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.search) queryParams.append('search', params.search);
    
    const queryString = queryParams.toString();
    const url = queryString ? `/enterprise/list?${queryString}` : '/enterprise/list';
    
    return baseApiService.get<EnterpriseAdminsListResponse>(url);
  }

  // Get Enterprise Admin by ID
  async getEnterpriseAdminById(id: string) {
    return baseApiService.get<{ data: EnterpriseAdmin }>(`/enterprise/get-enterprise/${id}`);
  }

  // Get Enterprise Admin Profile
  async getEnterpriseAdminProfile() {
    return baseApiService.get<{ data: EnterpriseAdmin }>('/enterprise/profile');
  }

  // Update Enterprise Admin Status
  async updateEnterpriseAdminStatus(id: string, status: 'active' | 'inactive') {
    return baseApiService.patch(`/enterprise/update-status/${id}`, { status });
  }
}

export const enterpriseAdminService = new EnterpriseAdminService();
