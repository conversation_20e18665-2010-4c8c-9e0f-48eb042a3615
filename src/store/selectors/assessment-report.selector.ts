import { createSelector } from 'reselect';
import { AppState } from 'store';

const assessmentReportStore = (store: AppState) => store.assessmentReport;

export const getAssessmentReportResponse = createSelector(
  [assessmentReportStore],
  (assessmentReport) => {
    const data = assessmentReport;
    return {
      assessment_response: data.assessment_response,
      business_owner_data: data.business_owner_data,
      advisor_name: data.advisor_name,
      assessment_completion_date: data.assessment_completion_date,
    };
  }
);

export const getAssessmentReportLoading = createSelector(
  [assessmentReportStore],
  (assessmentReport) => assessmentReport.loading
);
