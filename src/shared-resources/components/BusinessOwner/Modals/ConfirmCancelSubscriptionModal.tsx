import React from 'react';
import { GiCancel } from 'react-icons/gi';
import Button from 'shared-resources/components/Button/Button';

interface Props {
  handleConfirmYourAccountModalVisibility: (value: boolean) => void;
  onConfirm: (value: boolean) => void;
}
const ConfirmCancelSubscriptionModal: React.FC<Props> = (props) => {
  const { handleConfirmYourAccountModalVisibility, onConfirm } = props;

  return (
    <div className='w-[31.25rem] bg-white rounded-xl'>
      <div className='flex flex-col  gap-4 items-center justify-center px-5 py-6'>
        <GiCancel className='text-6xl' />
        <h1 className=' text-lg font-semibold'>
          Confirming Cancel Subscription
        </h1>
        <h1 className='text-center text-black-02'>
          Are you sure you want cancel your subscription ?
        </h1>
      </div>
      <div className='flex justify-evenly rounded-b-xl bg-slate-500'>
        <Button
          theme='secondary'
          onClick={() => handleConfirmYourAccountModalVisibility(false)}
          className='w-full py-4 rounded-bl-xl border-none bg-blue-02 rounded-none  '
        >
          Not Now
        </Button>
        <Button
          onClick={onConfirm}
          className='w-full py-4 rounded-none rounded-br-xl border-none'
        >
          Yes
        </Button>
      </div>
    </div>
  );
};

export default ConfirmCancelSubscriptionModal;
