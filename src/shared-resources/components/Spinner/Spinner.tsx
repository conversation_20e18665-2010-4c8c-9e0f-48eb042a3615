import React from 'react';
import classNames from 'classnames';
import { AbbreviatedSize } from 'shared-resources/types/AbbreviatedSize.type';
import './Spinner.module.css';

export interface Props {
  size?: AbbreviatedSize;
  border?: string;
  customClassName?: string;
  spinnerTheme?: 'default' | 'overlaySpinner';
}

const TopRoundedSpinner: React.FunctionComponent<Props> = ({
  size = 'md',
  border = 'border-blue-01',
  customClassName,
  spinnerTheme = 'default',
}) => {
  const spinnerClassForSize: Record<AbbreviatedSize, string> = {
    xs: 'spinner-xs',
    sm: 'spinner-sm',
    md: 'spinner-md',
    lg: 'spinner-lg',
    xl: 'spinner-xl',
    custom: '',
  };

  const spinnerContainerTheme: Record<string, string> = {
    default: 'flex items-center justify-center w-full',
    overlaySpinner:
      'w-full min-h-screen px-4 text-center absolute flex justify-center items-center inset-0 z-[110] ',
  };

  return (
    <div className={classNames(spinnerContainerTheme[spinnerTheme])}>
      <div
        className={classNames(
          'animate-spin',
          'rounded-full',
          'border-t-4',
          spinnerClassForSize[size],
          border,
          customClassName
        )}
      />
    </div>
  );
};

export default React.memo(TopRoundedSpinner);
