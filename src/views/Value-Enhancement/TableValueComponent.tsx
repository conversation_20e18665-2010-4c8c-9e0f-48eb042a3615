import React, { memo, useEffect, useRef } from 'react';
import Radio from 'shared-resources/components/Radio/Radio';
import { ValueEnhancementTabs } from 'types/enum';
import classNames from 'classnames';
import {
  getTitle,
  headerOptions,
  idToQuestion,
} from './ValueEnhancementConfig';

interface Props {
  currentTab?: ValueEnhancementTabs;
  response: any;
  setResponse?: any;
  emptyKeys?: any;
}
const TableValueComponent: React.FC<Props> = ({
  currentTab,
  response,
  setResponse,
  emptyKeys,
}) => {
  const tableRef = useRef<HTMLDivElement | null>(null);

  const handleSetResponse = (
    value: string | null,
    businessFunction: string,
    key: 'selected_option' | 'comment'
  ) => {
    setResponse({
      ...response,
      [currentTab as string]: {
        ...response[currentTab as string],
        [businessFunction]: {
          ...response[currentTab as string][businessFunction],
          [key]: value,
        },
      },
    });
  };

  useEffect(() => {
    tableRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
  }, [currentTab]);

  return (
    <>
      <div className='flex  justify-between items-center'>
        <div className='flex gap-1'>
          <h1>Pillars:</h1>
          <h2 className='text-blue-01 font-semibold'>
            {currentTab && getTitle(currentTab)}
          </h2>
        </div>
      </div>

      <div
        className={` pr-3 
             h-[calc(100vh-22rem)]
         relative scrollbar overflow-auto w-full rounded-t-md`}
        ref={tableRef}
      >
        <div
          className={`bg-blue-01 min-h-20 z-10 tracking-[0.07rem] sticky top-0 grid grid-cols-12 overflow-hidden text-white
           font-[500] rounded-t-md  text-[0.9rem]`}
        >
          {headerOptions.map((h, index) => (
            <div
              key={h}
              className={`px-5  flex items-center border-r  border-gray-02 ${
                (index === 0 || index === 3) && 'col-span-5'
              }  justify-center py-1 h-full ${
                index === 0 && 'border-l rounded-tl-md '
              } ${index === 6 && 'rounded-t-md  '} `}
            >
              <span className='text-center'>{h}</span>
            </div>
          ))}
        </div>
        <div>
          {currentTab &&
            Object.keys(response[currentTab])?.map(
              (businessFunction, index) => (
                <div
                  key={businessFunction}
                  className={classNames(
                    'grid grid-cols-12 font-[500]  text-[0.9rem] ',
                    emptyKeys?.includes(businessFunction ?? [])
                      ? 'bg-red-100'
                      : ''
                  )}
                >
                  <div
                    className={`px-1  2xl:px-3 ${
                      index === Object.keys(response[currentTab]).length - 1 &&
                      'rounded-bl-md '
                    }  flex items-center border-b border-l col-span-5 py-1 border-r border-gray-02`}
                  >
                    <span className='text-xs xl:text-sm '>
                      {idToQuestion[businessFunction].question}
                      <span className=' text-red-600 ml-1'>*</span>
                    </span>
                  </div>
                  <Radio
                    showLabel={false}
                    className='grid grid-cols-2 col-span-2   !gap-x-0 items-center border-b justify-center  border-gray-02'
                    className2='flex items-center  !border-r !border-gray-02 !h-[5.5rem]'
                    inputClassName='!h-5 !mx-auto !cursor-pointer'
                    selected={
                      response[currentTab][businessFunction].selected_option
                    }
                    options={[
                      {
                        label: `${headerOptions[1]}`,
                        value: `${headerOptions[1]}`,
                      },
                      {
                        label: `${headerOptions[2]}`,
                        value: `${headerOptions[2]}`,
                      },
                    ]}
                    onChange={(value) => {
                      handleSetResponse(
                        value,
                        businessFunction,
                        'selected_option'
                      );
                    }}
                  />
                  <textarea
                    onChange={(e: any) => {
                      handleSetResponse(
                        e.target.value,
                        businessFunction,
                        'comment'
                      );
                    }}
                    value={response[currentTab][businessFunction].comment ?? ''}
                    className={`px-1  2xl:px-3 ${
                      index === Object.keys(response[currentTab]).length - 1 &&
                      'rounded-br-md '
                    }  flex items-center border-b col-span-5 resize-none outline-none  py-1 border-r border-gray-02 ${
                      emptyKeys?.includes(businessFunction ?? [])
                        ? 'bg-red-100'
                        : ''
                    }`}
                  />
                </div>
              )
            )}
        </div>
      </div>
    </>
  );
};

export default memo(TableValueComponent);
