import React, { useEffect, useState } from 'react';
import { IoArrowBackSharp } from 'react-icons/io5';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  deleteBusinessOwner,
  fetchBusinessOwner,
  transferBusinessOwner,
} from 'store/actions/business-owner.action';
import {
  getBusinessOwnerDetails,
  getBusinessOwnerDetailsLoading,
  updateBusinessOwnerLoading,
} from 'store/selectors/business-owner.selector';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import { getUserData } from 'store/selectors/user.selector';
import { getInitials } from 'utils/helpers/Helpers';
import { useParamSelector } from '../../../store/selectors/base.selectors';
import NavigateContainer from '../NavigateContainer';
import Spinner from '../Spinner/Spinner';
import BusinessAwarenessTools from './BusinessAwarenessTools';
import DetailItem from './DetailItems';
import Button from '../Button/Button';
import Modal from '../Modal/Modal';
import ManageAccessModal from './ManageAccessModal';
import DeleteBOModal from './Modals/DeleteBOModal';
import CreateOrEditBusinessOwnerModal from './Modals/CreateEditBo';
import TransferBOModal from './Modals/TransferBOModal';
import ConfirmationModal from './Modals/ConfirmationModal';
import { set } from 'lodash';

const BusinessOwnerDetails: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const ownerId = location.pathname.split('/')[3];
  const dispatch = useDispatch();
  const businessOwner = useParamSelector(getBusinessOwnerDetails, {
    id: ownerId,
  }) as BusinessOwner;
  const businessOwnerLoading = useSelector(getBusinessOwnerDetailsLoading);
  const deleteBOLoading = useSelector(updateBusinessOwnerLoading);
  const transferBOLoading = useSelector(updateBusinessOwnerLoading);
  const user = useSelector(getUserData);
  const [showPopUp, setShowPopUp] = useState(false);

  const [showEditPopUp, setShowEditPopUp] = useState(false);
  const [showDeleteBOPopUp, setShowDeletBOPopUp] = useState(false);
  const [showTransferPopUp, setShowTransferPopUp] = useState(false);
  const [showTransferConfirmationPopUp, setShowTransferConfirmationPopUp] =
    useState(false);

  const [transferAdvisorId, setTransferAdvisorId]= useState('');

  useEffect(() => {
    if (user) {
      dispatch(fetchBusinessOwner(ownerId));
    }
  }, [dispatch, user]);

  const handleDeleteClick = () => {
    dispatch(
      deleteBusinessOwner({
        id: ownerId,
        onSuccess: () => navigate('/dashboard'),
      })
    );
  };

  const handleTransformOwnerClick = (id: string) => {
    setShowTransferPopUp(false);
    setShowTransferConfirmationPopUp(true);
    setTransferAdvisorId(id);
  };

  const confirmTransformOwner = () => {
    dispatch(
      transferBusinessOwner({
        id: ownerId,
        transferRequest: {
          advisor_id: Number(transferAdvisorId),
        },
        onSuccess: () => navigate('/dashboard'),
      })
    );
  }

  if (businessOwnerLoading) {
    return (
      <div className='flex items-center justify-center h-32'>
        <Spinner size='sm' />
      </div>
    );
  }

  return (
    <div className='w-full flex flex-col'>
      <div className='flex rounded-xl p-7 bg-white justify-between'>
        <NavigateContainer className='min-w-7.5 text-black-02 mr-4 mt-2'>
          <IoArrowBackSharp size={24} />
        </NavigateContainer>
        <div className='w-fit '>
          <div className='flex flex-col justify-center items-center'>
            <div className='rounded-full w-[7.5rem] h-[7.5rem] border-2 border-blue-01'>
              {businessOwner && (
                <div className='flex items-center font-medium w-full h-full text-3xl justify-center border border-blue-01 rounded-full text-blue-01'>
                  {getInitials(businessOwner)}
                </div>
              )}
            </div>
            <span className='text-center mt-4 font-medium whitespace-nowrap'>
              {businessOwner?.name}
            </span>
          </div>
        </div>
        <div className='flex flex-wrap justify-between gap-x-1 ml-10 max-w-[80rem]'>
          <DetailItem
            name='Business Name'
            value={businessOwner?.business_name}
          />
          <DetailItem
            name='Type of Business'
            value={businessOwner?.business_type || '-'}
          />
          <DetailItem name='Age of Business Owner' value={businessOwner?.age} />
          {businessOwner?.additional_advisor_email && (
            <DetailItem
              name='Additional Advisor'
              value={businessOwner?.additional_advisor_email}
            />
          )}
          <div className='w-full flex items-start'>
            <DetailItem
              name='Email Address'
              width='w-full'
              value={businessOwner?.email}
            />
          </div>
          {businessOwner?.notes && (
            <div className='flex items-start w-full'>
              <DetailItem
                name='Notes'
                width='w-full'
                value={businessOwner?.notes}
              />
            </div>
          )}
          {/* </div> */}
          {businessOwner?.primary_advisor_id === user?.id && (
            // <div className='flex gap-2 mt-auto'>
            <div className='w-full flex items-start gap-2 mt-auto'>
              <Button
                theme='tertiary'
                className='px-6 py-1'
                onClick={() => {
                  setShowEditPopUp((prev) => !prev);
                }}
                type='button'
              >
                Edit
              </Button>

              <Button
                theme='tertiary'
                className='px-6 py-1'
                onClick={() =>
                  navigate(`/advisor/business-owner/${ownerId}/report-builder`)
                }
                type='button'
              >
                Report Builder
              </Button>
              <Button
                theme='tertiary'
                className='px-6 py-1'
                onClick={() => {
                  setShowPopUp((prev) => !prev);
                }}
                type='button'
              >
                Manage
              </Button>

              <Button
                theme='tertiary'
                className='px-6 py-1'
                onClick={() => {
                  setShowTransferPopUp((prev) => !prev);
                }}
                type='button'
              >
                Transfer
              </Button>
            </div>
          )}
        </div>
      </div>
      <div className='flex mt-5 rounded-xl p-7 bg-white'>
        {businessOwner && (
          <BusinessAwarenessTools businessOwner={businessOwner} />
        )}
      </div>
      {showEditPopUp && (
        <Modal
          title='Edit Business Owner'
          visible={showEditPopUp}
          handleVisibility={setShowEditPopUp}
          classname='w-[65.185rem]'
        >
          <CreateOrEditBusinessOwnerModal
            businessOwner={businessOwner}
            onDeleteBOClick={() => setShowDeletBOPopUp(true)}
            handleModalVisibility={() => setShowEditPopUp(false)}
          />
        </Modal>
      )}
      {showDeleteBOPopUp && (
        <Modal
          title='Delete Business Owner'
          visible={showDeleteBOPopUp}
          handleVisibility={setShowDeletBOPopUp}
        >
          <DeleteBOModal
            loading={deleteBOLoading}
            onDeleteClick={() => handleDeleteClick()}
            onCancelClick={() => setShowDeletBOPopUp(false)}
          />
        </Modal>
      )}
      {showPopUp && (
        <Modal
          title='Manage Access'
          visible={showPopUp}
          handleVisibility={setShowPopUp}
          classname='w-[45%]'
        >
          {' '}
          <ManageAccessModal 
          showForm= {true}
          id={Number(businessOwner.id)}
          advisorId={businessOwner.primary_advisor_id} />
        </Modal>
      )}
      {showTransferPopUp && (
        <Modal
          title='Transfer Business Owner'
          visible={showTransferPopUp}
          handleVisibility={setShowTransferPopUp}
        >
          <TransferBOModal
            loading={transferBOLoading}
            onConfirmClick={handleTransformOwnerClick}
            onCancelClick={() => setShowTransferPopUp(false)}
          />
        </Modal>
      )}

      {showTransferConfirmationPopUp && (
        <Modal
          title='Confirm Business Owner Transfer'
          visible={showTransferConfirmationPopUp}
          handleVisibility={setShowTransferConfirmationPopUp}
          closeOnOutsideClick
        >
          <ConfirmationModal
            handleCancel={() => setShowTransferConfirmationPopUp(false)}
            onSubmitConfirm={ confirmTransformOwner }
            description='Are you sure you want to transfer this Business Owner?'  
            cancelText='Cancel'
            confirmText='Transfer'
          />
        </Modal>
      )}
    </div>
  );
};

export default BusinessOwnerDetails;
