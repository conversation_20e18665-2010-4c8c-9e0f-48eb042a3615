import { Form, Formik } from 'formik';
import React from 'react';
import { IoInformationCircle } from 'react-icons/io5';
import { useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import * as yup from 'yup';
import {
  getTotalNormalizationExpenses,
  getYear,
} from '../BusinessValuationConfig';

interface Props {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  data: any;
  onSaveToDraftClick: (data: any) => void;
}
const TotalNormalizationExpenses: React.FC<Props> = (props) => {
  const { nextStep, setData, backStep, data, onSaveToDraftClick } = props;
  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup.object().shape({
    [`total_normalization_expenses_${getYear(1)}`]: yup.number(),
    [`total_normalization_expenses_${getYear(2)}`]: yup.number(),
    [`total_normalization_expenses_${getYear(3)}`]: yup.number(),
  });

  const handleSubmit = (values: any) => {
    setData(values);
    nextStep();
  };

  const totalNormalizationExpenses = getTotalNormalizationExpenses(data);

  return (
    <div className='flex flex-col '>
      <Formik
        initialValues={{
          [`total_normalization_expenses_${getYear(1)}`]:
            totalNormalizationExpenses.totalNormalizationExpensesFirst || 0,
          [`total_normalization_expenses_${getYear(2)}`]:
            totalNormalizationExpenses.totalNormalizationExpensesSecond || 0,
          [`total_normalization_expenses_${getYear(3)}`]:
            totalNormalizationExpenses.totalNormalizationExpensesThird || 0,
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values }) => (
          <Form className='px-10 py-3 flex flex-col gap-4 bg-white'>
            <div className='h-[calc(100vh-18.5rem)] flex flex-col items-center justify-center overflow-auto hideScroll'>
              <div className='flex items-center self-start ml-10 gap-2'>
                <IoInformationCircle id='Total' className='text-blue-01' />
                <div className='relative'>
                  <Tooltip
                    anchorSelect='#Total'
                    place='right'
                    offset={0}
                    className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                    classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                  >
                    This is the total of ALL expenses to be added back to the
                    bottom line
                  </Tooltip>
                  <h2 className='text-xl font-bold underline'>
                    Total Sellers Discretionary Earnings
                  </h2>
                </div>
              </div>

              <div className='grid grid-cols-3 gap-x-16 gap-y-1 mt-10 text-[1.1rem]'>
                <FormikInput
                  className='!h-10'
                  label={getYear(1).toString()}
                  disabled
                  labelClassName='font-semibold mb-2'
                  name={`total_normalization_expenses_${getYear(1)}`}
                  key={`total_normalization_expenses_${getYear(1)}`}
                />
                <FormikInput
                  className='!h-10'
                  label={getYear(2).toString()}
                  disabled
                  labelClassName='font-semibold mb-2'
                  name={`total_normalization_expenses_${getYear(2)}`}
                  key={`total_normalization_expenses_${getYear(2)}`}
                />
                <FormikInput
                  className='!h-10'
                  label={getYear(3).toString()}
                  disabled
                  labelClassName='font-semibold mb-2'
                  name={`total_normalization_expenses_${getYear(3)}`}
                  key={`total_normalization_expenses_${getYear(3)}`}
                />
              </div>
            </div>

            <BackNextComponent
              backStep={backStep}
              buttonType='submit'
              isLoading={isLoading}
              isNextDisable={false}
              onSaveToDraftClick={() => {
                onSaveToDraftClick(values);
              }}
            />
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default TotalNormalizationExpenses;
