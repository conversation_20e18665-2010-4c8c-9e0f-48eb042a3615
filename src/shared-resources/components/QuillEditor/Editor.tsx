import React, { forwardRef, useEffect, useRef, useState } from 'react';
import Quill from 'quill';
import 'quill/dist/quill.snow.css';
import { LuUndo2, LuRedo2 } from 'react-icons/lu';
import { renderToString } from 'react-dom/server';
import classNames from 'classnames';
import ReactComponentBlot from './blots/ReactComponentBlot';
import ToolSectionBlot from './blots/ToolSectionBlot';

interface EditorProps {
  onChange?: (content: string) => void;
  disabled?: boolean;
}

ReactComponentBlot.blotName = 'react-component';
ReactComponentBlot.tagName = 'div';
ReactComponentBlot.className = 'react-component';

ToolSectionBlot.blotName = 'tool-section';
ToolSectionBlot.tagName = 'div';

Quill.register(ReactComponentBlot);

Quill.register(ToolSectionBlot);

const Editor = forwardRef<Quill, EditorProps>(({ onChange, disabled }, ref) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [quill, setQuill] = useState<Quill | null>(null);

  const handleImageUpload = (file: File, quill: Quill) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const range = quill.getSelection(true);
      quill.insertEmbed(range.index, 'image', e.target?.result);
    };
    reader.readAsDataURL(file);
  };

  useEffect(() => {
    if (!containerRef.current || quill) return;

    const icons = Quill.import('ui/icons') as Record<string, string>;
    icons.undo = renderToString(<LuUndo2 />);
    icons.redo = renderToString(<LuRedo2 />);

    const newQuill = new Quill(containerRef.current, {
      theme: 'snow',
      modules: {
        toolbar: {
          container: [
            { header: [1, 2, 3, 4, 5, 6, false] },
            { font: [] },
            'bold',
            'italic',
            'underline',
            'strike',
            'link',
            'image',
            'code-block',
            'blockquote',
            { align: [] },
            { list: 'ordered' },
            { list: 'bullet' },
            { indent: '-1' },
            { indent: '+1' },
            'undo',
            'redo',
          ],
          handlers: {
            undo() {
              newQuill.history.undo();
            },
            redo() {
              newQuill.history.redo();
            },
            image() {
              const input = document.createElement('input');
              input.setAttribute('type', 'file');
              input.setAttribute('accept', 'image/*');
              input.click();

              input.onchange = async () => {
                const file = input.files?.[0];
                if (file) {
                  handleImageUpload(file, newQuill);
                }
              };
            },
          },
        },
        history: {
          delay: 1000,
          maxStack: 500,
          userOnly: true,
        },
      },
    });

    newQuill.root.style.fontSize = '16px';

    newQuill.on('text-change', () => {
      if (onChange) {
        onChange(newQuill.root.innerHTML);
      }
    });

    setQuill(newQuill);
    if (ref && 'current' in ref) ref.current = newQuill;
  }, []);

  useEffect(() => {
    if (quill) {
      quill.enable(!disabled);
    }
  }, [quill, disabled]);

  return (
    <div className='flex w-full max-w-[calc(100%-12.5rem)] flex-col'>
      <div
        className={classNames('quill-wrapper flex-grow overflow-hidden', {
          'pointer-events-none opacity-60': disabled,
        })}
      >
        <div ref={containerRef} className='w-full h-full' />
      </div>
    </div>
  );
});

Editor.displayName = 'Editor';
export default Editor;
