import { Form, Formik, FormikValues } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import FormikRadio from 'shared-resources/components/Radio/FormikRadio';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import * as yup from 'yup';
import {
  Screen9Keys,
  YesNoOptions,
  getQuestionBykey,
} from '../AssetProtectionConfig';

interface Props {
  data: any;
  handleNextClick: (values: FormikValues) => void;
  handleBackClick: (values: FormikValues) => void;
  handleSaveAsDraftClick: (values: FormikValues) => void;
}

const Screen9: React.FC<Props> = ({
  handleNextClick,
  handleBackClick,
  handleSaveAsDraftClick,
  data,
}) => {
  // Validation schema with all fields required
  const validationSchema = yup.object().shape({
    [Screen9Keys.HAVE_A_PERSONAL_TAX_MINIMIZATION_STRATEGY]: yup
      .string()
      .required('This field is required'),
    [Screen9Keys.HAVE_A_PERSONAL_TAX_MINIMIZATION_STRATEGY_NOW]: yup
      .string()
      .required('This field is required'),
    [Screen9Keys.HAVE_A_FINANCIAL_PLANNER]: yup
      .string()
      .required('This field is required'),
    [Screen9Keys.HAVE_A_WEALTH_MANAGER]: yup
      .string()
      .required('This field is required'),
  });

  // Initial values
  const initialValue = data?.screen9 || {
    [Screen9Keys.HAVE_A_PERSONAL_TAX_MINIMIZATION_STRATEGY]: '',
    [Screen9Keys.HAVE_A_PERSONAL_TAX_MINIMIZATION_STRATEGY_NOW]: '',
    [Screen9Keys.HAVE_A_FINANCIAL_PLANNER]: '',
    [Screen9Keys.HAVE_A_WEALTH_MANAGER]: '',
  };

  const isSubmitting = useSelector(getAssessmentToolLoading);

  return (
    <div className='flex flex-col h-full gap-6 pl-10 py-10 font-medium'>
      <Formik
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={(values: FormikValues) => {
          handleNextClick(values);
        }}
        enableReinitialize
      >
        {({ values }) => (
          <Form className='flex flex-col justify-between'>
            <div className='h-[calc(100vh-20rem)] overflow-auto scrollbar'>
              <div>
                {/* First Question */}
                <div className='mt-4'>
                  <span className='font-semibold'>
                    {getQuestionBykey(
                      Screen9Keys.HAVE_A_PERSONAL_TAX_MINIMIZATION_STRATEGY
                    )}
                  </span>
                  <div className='mt-5 xl:w-[50%]'>
                    <FormikRadio
                      labelClassName='!cursor-pointer'
                      className2='!w-fit'
                      className='grid grid-cols-2 gap-4'
                      name={
                        Screen9Keys.HAVE_A_PERSONAL_TAX_MINIMIZATION_STRATEGY
                      }
                      options={YesNoOptions}
                    />
                  </div>
                </div>

                {/* Second Question */}
                <div className='mt-4'>
                  <span className='font-semibold'>
                    {getQuestionBykey(
                      Screen9Keys.HAVE_A_PERSONAL_TAX_MINIMIZATION_STRATEGY_NOW
                    )}
                  </span>
                  <div className='mt-5 xl:w-[50%]'>
                    <FormikRadio
                      labelClassName='!cursor-pointer'
                      className2='!w-fit'
                      className='grid grid-cols-2 gap-4'
                      name={
                        Screen9Keys.HAVE_A_PERSONAL_TAX_MINIMIZATION_STRATEGY_NOW
                      }
                      options={YesNoOptions}
                    />
                  </div>
                </div>

                {/* Third Question */}
                <div className='mt-4'>
                  <span className='font-semibold'>
                    {getQuestionBykey(Screen9Keys.HAVE_A_FINANCIAL_PLANNER)}
                  </span>
                  <div className='mt-5 xl:w-[50%]'>
                    <FormikRadio
                      labelClassName='!cursor-pointer'
                      className2='!w-fit'
                      className='grid grid-cols-2 gap-4'
                      name={Screen9Keys.HAVE_A_FINANCIAL_PLANNER}
                      options={YesNoOptions}
                    />
                  </div>
                </div>

                {/* Fourth Question */}
                <div className='mt-4'>
                  <span className='font-semibold'>
                    {getQuestionBykey(Screen9Keys.HAVE_A_WEALTH_MANAGER)}
                  </span>
                  <div className='mt-5 xl:w-[50%]'>
                    <FormikRadio
                      labelClassName='!cursor-pointer'
                      className2='!w-fit'
                      className='grid grid-cols-2 gap-4'
                      name={Screen9Keys.HAVE_A_WEALTH_MANAGER}
                      options={YesNoOptions}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className='pr-5'>
              <BackNextComponent
                backStep={() => handleBackClick(values)}
                buttonType='submit'
                isLoading={isSubmitting}
                isNextDisable={false}
                onSaveToDraftClick={() => {
                  handleSaveAsDraftClick(values);
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Screen9;
