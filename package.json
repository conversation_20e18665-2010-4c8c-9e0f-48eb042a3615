{"name": "exitsmarts", "version": "0.0.0", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint \"./src/**/*.{js,ts,tsx}\" --fix", "prettify": "prettier -c --write ./src/**/* ", "install:clean": "rm -rf node_modules/ && yarn", "prepare": "husky install", "pre-commit": "bash scripts/log-alert-hook.sh && lint-staged"}, "dependencies": {"@headlessui/react": "^1.7.18", "@headlessui/tailwindcss": "^0.2.0", "@hello-pangea/dnd": "^16.6.0", "@intercom/messenger-js-sdk": "^0.0.13", "@reduxjs/toolkit": "^1.9.1", "@types/dom-to-image": "^2.6.7", "@types/lodash": "^4.17.0", "@types/rc-tooltip": "^3.7.14", "axios": "^1.2.1", "classname": "^0.0.0", "classnames": "^2.5.1", "date-fns": "^3.6.0", "dom-to-image": "^2.6.0", "font-awesome": "^4.7.0", "formik": "^2.4.5", "history": "^5.3.0", "jspdf": "^2.5.1", "lodash": "^4.17.21", "query-string": "^9.0.0", "quill": "2.0.2", "rc-tooltip": "^6.1.3", "react": "^18.2.0", "react-datepicker": "^6.9.0", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "react-jwt": "^1.2.0", "react-phone-input-2": "^2.15.1", "react-redux": "^8.0.5", "react-router": "^6.22.2", "react-router-dom": "^6.22.2", "react-select": "^5.8.0", "react-toastify": "^10.0.4", "react-tooltip": "^5.26.3", "recharts": "^2.12.2", "redux": "^4.2.0", "redux-saga": "^1.2.2", "reselect": "^4.1.7", "vite-plugin-svgr": "^4.3.0", "yup": "^1.3.3"}, "devDependencies": {"@types/react": "^18.0.26", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18.0.10", "@types/redux-logger": "^3.0.9", "@typescript-eslint/eslint-plugin": "^5.47.0", "@typescript-eslint/parser": "^5.47.0", "@vitejs/plugin-react": "^3.0.0", "autoprefixer": "^10.4.13", "eslint": "^8.30.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.5.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.11", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-redux": "^4.0.0", "eslint-plugin-simple-import-sort": "^8.0.0", "husky": "^8.0.2", "lint-staged": "^13.1.0", "postcss": "^8.4.20", "prettier": "^2.8.1", "sass": "^1.57.1", "tailwindcss": "^3.2.4", "typescript": "^5.4.4", "vite": "^4.0.3", "vite-tsconfig-paths": "^4.0.3"}, "engines": {"npm": "8.19.2", "node": "18.12.1"}, "husky": {"hooks": {"pre-commit": "bash scripts/log-alert-hook.sh && lint-staged"}}, "lint-staged": {"src/**/*.{js,ts,tsx}": ["yarn run lint", "yarn run prettify", "git add --force"]}}