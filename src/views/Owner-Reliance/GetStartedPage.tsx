import React from 'react';
import Button from 'shared-resources/components/Button/Button';

interface Props {
  onGestartedClick: () => void;
}
const GetStartedPage: React.FC<Props> = ({ onGestartedClick }) => (
  <div className='flex flex-col h-full items-center justify-center gap-6 px-24'>
    <p className='text-center font-medium max-h-[calc(100vh-10rem)] leading-loose overflow-y-auto scrollbar pr-2'>
      The reliance of a business on its owner (owner reliance) is the first
      measure of the transferable value of a business. Specifically, the more
      reliant a business is on its owner for day-to-day operations, the less
      transferable value the business has to most buyers. Buyers and their
      professional advisors will turn the company inside out trying to find out
      how engaged the Leadership Team really is in running the company. They
      will discover the true level of owner reliance, so you want to get ahead
      of that discovery (by reducing owner reliance). One of the challenges with
      owner reliance is that most business owners do not realize how many of the
      key decisions about the company they actually control – one way or
      another. There is usually a great disparity between the decisions the
      owner thinks he/she makes and the number of decisions the Leadership Team
      will state that the owner makes. Be totally honest
      as you complete this tool so that you can then use it to help you identify
      specific decision areas that can be moved to Leadership Team members, and
      thereby increase business value.
    </p>
    <div>
      <Button onClick={() => onGestartedClick()} className='px-6 py-2'>
        Get Started
      </Button>
    </div>
  </div>
);

export default GetStartedPage;
