import { RouteKey, UserRouteType } from 'types/enum';
import { SidebarRoutesConfigType } from 'types/routes/routes.type';
import BusinessContinuityContainer from 'views/BusinessContinuity/BusinessContinuityContainer';
import BusinessContinuityReport from 'views/BusinessContinuity/Report/BusinessContinuityReport';
import BusinessValuationScreenContainer from 'views/BusinessValuation/BusinessValuationScreenContainer';
import ExpenseCalculator from 'views/FinancialGapAnalysis/ExpenseCalculator/ExpenseCalculator';
import FinancialGapAnalysisReport from 'views/FinancialGapAnalysis/FinancialGapAnalysisReport/FinancialGapAnalysisReport';
import FinancialGapAnalysisScreenContainer from 'views/FinancialGapAnalysis/FinancialGapAnalysisScreenContainer';
import OwnerReliance from 'views/Owner-Reliance/OwnerReliance';
import OwnerRelianceReport from 'views/Owner-Reliance/OwnerRelianceReport';
import ProfilePage from 'views/Profile/ProfilePage';
import ReadinessAssessmentReport from 'views/ReadinessAssessment/ReadinessAssessmentReport/ReadinessAssessmentReport';
import TransitionObjectivePage from 'views/TransitionObjectives/TransitionObjectivePage';
import TransitionObjectiveReport from 'views/TransitionObjectives/TransitionObjectiveReport';
import ValueEnhancement from 'views/Value-Enhancement/ValueEnhancement';
import ValueEnhancementReport from 'views/Value-Enhancement/report/ValueEnhancementReport';
import StakeHolderAlignmentMeetingPage from 'views/Stakeholder-Alignment-Meeting/StakeHolderAlignmentMeetingPage';
import AssetProtection from 'views/AssetProtection/AssetProtection';
import AssetProtectionReport from 'views/AssetProtection/report/AssetProtectionReport';
import ReportBuilder from 'components/ReportBuilder/ReportBuilder';
import BuyerTypeReport from 'views/BuyerType/BuyerTypeReport';
import BuyerType from 'views/BuyerType/BuyerType';
import ReadinessAssessment from '../views/Readiness-Assesment/ReadinessAssessment';
import LearnMore from '../views/layout/LearnMore';

export const businessOwnerDashboardRoutes: Array<SidebarRoutesConfigType> = [
  {
    name: 'Business Owner Profile',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PROFILE}`,
    component: ProfilePage,
  },
  {
    name: 'Business Owner Readiness Assessment',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.READINESS_ASSESSMENT}`,
    component: ReadinessAssessment,
  },
  {
    name: 'Business Owner Transition Objectives',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.TRANSITION_OBJECTIVES}`,
    component: TransitionObjectivePage,
  },
  {
    name: 'Business Owner Stakeholder Alignment Meeting',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.STAKEHOLDER_ALIGNMENT_MEETING}`,
    component: StakeHolderAlignmentMeetingPage,
  },
  {
    name: 'Business Owner Valuation',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.BUSINESS_VALUATION}`,
    component: BusinessValuationScreenContainer,
  },
  {
    name: 'Business Owner Value Enhancement Opportunities',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES}`,
    component: ValueEnhancement,
  },
  {
    name: 'Business Owner Reliance',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.OWNER_RELIANCE}`,
    component: OwnerReliance,
  },
  {
    name: 'Business Owner Business Continuity',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}/${RouteKey.BUSINESS_CONTINUITY}`,
    component: BusinessContinuityContainer,
  },
  {
    name: 'Business Owner Wealth Gap Analysis',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.FINANCIAL_GAP_ANALYSIS}`,
    component: FinancialGapAnalysisScreenContainer,
  },
  {
    name: 'Business Owner Expense Calculator',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.EXPENSE_CALCULATOR}`,
    component: ExpenseCalculator,
  },
  {
    name: 'Business Owner Asset Protection',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.ASSET_PROTECTION}`,
    component: AssetProtection,
  },
  {
    name: 'Business Owner Buyer Type-Deal Structure',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.BUYER_TYPE_DEAL_STRUCTURE}`,
    component: BuyerType,
  },

  {
    name: 'Learn More',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.LEARN_MORE}`,
    component: LearnMore,
  },
  {
    name: 'Readiness Assessment Report',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.READINESS_REPORT}`,
    component: ReadinessAssessmentReport,
  },
  {
    name: 'Transition Assessment Report',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.TRANSITION_REPORT}`,
    component: TransitionObjectiveReport,
  },
  {
    name: 'Owner Reliance Report',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.OWNER_RELIANCE_REPORT}`,
    component: OwnerRelianceReport,
  },
  {
    name: 'Business Continuity Report',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.BUSINESS_CONTINUITY_REPORT}`,
    component: BusinessContinuityReport,
  },
  {
    name: 'Financial Gap Report',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.FINANCIAL_GAP_REPORT}`,
    component: FinancialGapAnalysisReport,
  },
  {
    name: 'Value Enhancement Opportunities Report',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.VALUE_ENHANCEMENT_OPPORTUNITIES_REPORT}`,
    component: ValueEnhancementReport,
  },
  {
    name: 'Asset Protection Report',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.ASSET_PROTECTION_REPORT}`,
    component: AssetProtectionReport,
  },
  {
    name: 'Buyer Type Report',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.BUYER_TYPE_DEAL_STRUCTURE_REPORT}`,
    component: BuyerTypeReport,
  },
  {
    name: 'Report Builder',
    key: `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.REPORT_BUILDER}`,
    component: ReportBuilder,
  },
];
