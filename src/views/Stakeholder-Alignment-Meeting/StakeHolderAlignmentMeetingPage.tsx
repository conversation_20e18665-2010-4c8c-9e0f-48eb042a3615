import React, { FC, memo, useEffect, useState } from 'react';
import { FaDownload } from 'react-icons/fa';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import Button from 'shared-resources/components/Button/Button';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import {
  fetchBusinessOwnerAssessment,
  fetchOwnAssessment,
  updateBusinessOwnerAssessment,
  updateOwnAssessment,
} from 'store/actions/assessment-tool.action';
import { fetchBusinessOwner } from 'store/actions/business-owner.action';
import { resetAssessmentData } from 'store/reducers/assessment-tool.reducer';
import {
  getAssessmentToolLoading,
  getAssessmentToolReEvaluate,
  getAssessmentToolResponse,
  getAssessmentToolStatus,
  getBusinessOwnerProperties,
} from 'store/selectors/assessment-tool.selector';
import { getUserData } from 'store/selectors/user.selector';
import {
  AssessmentResponseType,
  AssessmentToolProgressStatus,
  AssessmentTools,
  RouteKey,
  StakeHolderAlignmentMeetingTabs,
  UserRouteType,
  UserType,
} from 'types/enum';
import { generatePdf } from 'utils/generate-pdf/generatePdf-utils';
import ThankYouPage from 'views/layout/ThankYouPage';
import Modal from 'shared-resources/components/Modal/Modal';
import ReportGenerationModal from 'shared-resources/components/BusinessOwner/Modals/ReportGenerationModal';
import { sumBy } from 'lodash';
import { getUserRouteType, thankYouPageContent } from 'utils/helpers/Helpers';
import WithComments from 'shared-resources/components/ToolComments/WithComments';
import GetStartedPage from './GetStartedPage';
import QuestionAnswerPage from './QuestionAnswerPage';
import { TableValueType } from './Questions';
import { relationshipOptions } from './StakeHolderConfig';
import StakeHoldersPage from './StakeHoldersPage';
import StakeholderAlignmentPdf from './StakeholderAlignmentPdf';

type StakeHolderAlignmentMeetingPageProps = {};

const initialStakeHolderTableValue = [
  {
    id: 1,
    name: '',
    equityOwned: 0,
    relationship: '',
    invite: false,
    comment: '',
  },
];
const intialTextAreaAnswers = {
  boStateObjectives: '',
  summaryMessage: '',
  nextStepsMessage: '',
  cascadingMessage: '',
  adjournMessage: '',
};

const StakeHolderAlignmentMeetingPage: FC<
  StakeHolderAlignmentMeetingPageProps
> = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const [textAreaAnswers, setTextAreaAnswers] = useState<{
    [id: string]: string;
  }>(intialTextAreaAnswers);

  const [tableValues, setTableValues] = useState<TableValueType[]>(
    initialStakeHolderTableValue
  );

  const loggedInUserData = useSelector(getUserData);
  const dispatch = useDispatch();
  const savedResponse: any = useSelector(getAssessmentToolResponse);
  const assessmentToolStatus = useSelector(getAssessmentToolStatus);
  const assessmentReEvaluate = useSelector(getAssessmentToolReEvaluate);
  const isLoading = useSelector(getAssessmentToolLoading);
  const [currentTab, setCurrentTab] = useState<
    StakeHolderAlignmentMeetingTabs | undefined
  >(undefined);

  const businessOwnerProperties = useSelector(getBusinessOwnerProperties) as {
    first_name: string;
    last_name: string;
    business_name: string;
  };

  // Destructure initial values from saved assessment response
  const getAndSetInitialValues = () => {
    if (savedResponse) {
      const { stakeholderTable, currentTabVal, ...textAreaValues } =
        savedResponse;

      setTableValues(stakeholderTable);
      setTextAreaAnswers(textAreaValues);
      setCurrentTab(currentTabVal);
    } else {
      setCurrentTab(StakeHolderAlignmentMeetingTabs.GET_STARTED);
    }
  };

  // setInitialvalues
  useEffect(() => {
    if (
      savedResponse &&
      typeof savedResponse === 'object' &&
      Object.keys(savedResponse).length &&
      assessmentToolStatus !== AssessmentToolProgressStatus.COMPLETED
    ) {
      getAndSetInitialValues();
    } else if (
      assessmentToolStatus === AssessmentToolProgressStatus.NOT_STARTED
    ) {
      setCurrentTab(StakeHolderAlignmentMeetingTabs.GET_STARTED);
    }
  }, [savedResponse, assessmentToolStatus]);

  const handleGetStartedClick = () => {
    setCurrentTab(StakeHolderAlignmentMeetingTabs.STAKEHOLDERS);
  };

  // setting search params
  useEffect(() => {
    const tab = new URLSearchParams({ tab: currentTab } as Record<
      string,
      string
    >);
    if (currentTab !== undefined) {
      setSearchParams(tab);
    }
  }, [currentTab, setSearchParams, searchParams]);

  // data submit
  const submitDataHandler = (type: AssessmentResponseType) => {
    let data;
    if (currentTab === StakeHolderAlignmentMeetingTabs.STAKEHOLDERS) {
      data = {
        stakeholderTable: tableValues,
        currentTabVal: currentTab,
        ...intialTextAreaAnswers,
      };
    } else {
      data = {
        stakeholderTable: tableValues,
        currentTabVal: currentTab,
        ...textAreaAnswers,
      };
    }
    if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        updateOwnAssessment({
          tool: AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING,
          assessment_response: data,
          submit_type:
            type === AssessmentResponseType.DRAFT
              ? AssessmentResponseType.DRAFT
              : AssessmentResponseType.SUBMIT,
          onSuccess: () =>
            type === AssessmentResponseType.COMPLETE &&
            navigate(`${UserType.BUSINESS_OWNER}/${RouteKey.DASHBOARD}`),
        })
      );
    } else {
      dispatch(
        updateBusinessOwnerAssessment({
          businessOwnerId: +id!,
          tool: AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING,
          assessment_response: data,
          submit_type: type,
        })
      );
    }
  };

  // reset assesment in case of re-evaluate
  useEffect(() => {
    if (
      assessmentToolStatus === AssessmentToolProgressStatus.COMPLETED &&
      assessmentReEvaluate &&
      !isLoading
    ) {
      dispatch(resetAssessmentData());
      setCurrentTab(StakeHolderAlignmentMeetingTabs.GET_STARTED);
    }
  }, [assessmentReEvaluate, loggedInUserData, assessmentToolStatus]);

  // fetch business owner
  useEffect(() => {
    if (id) {
      dispatch(fetchBusinessOwner(id));
    }
  }, [dispatch, id]);

  const handleFetchAssessmentError = () => {
    navigate(
      `/${getUserRouteType(loggedInUserData?.type as UserType)}/${
        RouteKey.DASHBOARD
      }`
    );
  };
  // fetch assesment response for the tool
  useEffect(() => {
    if (loggedInUserData?.type === UserType.ADVISOR && id) {
      dispatch(
        fetchBusinessOwnerAssessment({
          tool: AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING,
          businessOwnerId: +id!,
          onError: handleFetchAssessmentError,
        })
      );
    } else if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchOwnAssessment({
          tool: AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING,
          onError: handleFetchAssessmentError,
        })
      );
      navigate(
        `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.STAKEHOLDER_ALIGNMENT_MEETING}`
      );
    }
  }, []);
  const [isDownloading, setIsDownloading] = useState(false);
  const [pages, setPages] = useState<any[]>([]);

  const downloadPdf = async () => {
    setIsDownloading(true);

    const pdfPages: HTMLElement[] = [];
    const pdfName = `StakeholderAlignmentMeeting.pdf`;

    if (pages.length) {
      pages.forEach((page, index) => {
        pdfPages.push(
          document.getElementById(`stakeholder-pdf-${index}`) as HTMLElement
        );
      });

      await generatePdf(
        pdfPages,
        pdfName,
        () => setIsDownloading(false),
        'portrait'
      );
    }
    // Save reference to the rendered content
  };
  const addStakeHolder = () => {
    setTableValues([
      ...tableValues,
      {
        id: tableValues?.length
          ? tableValues[tableValues.length - 1].id + 1
          : 1,
        name: '',
        equityOwned: 0,
        relationship: '',
        invite: false,
        comment: '',
      },
    ]);
  };

  const handleChange = (
    id: number,
    field: string,
    value: string | boolean | number
  ) => {
    let sum = 0;
    let finalValue = value;
    if (field === 'equityOwned') {
      const isEquityInvalid =
        typeof value === 'string' &&
        (parseInt(value, 10) > 100 || parseInt(value, 10) < 0);
      if (
        typeof value === 'string' &&
        value.startsWith('0') &&
        value.length === 2
      ) {
        finalValue = value.at(1) as string;
      }
      const parsedTableValues = tableValues.map(({ equityOwned, ...rest }) => ({
        ...rest,
        equityOwned: rest.id === id ? 0 : parseInt(equityOwned as any, 10) || 0,
      }));
      sum =
        parseInt(value as any, 10) + sumBy(parsedTableValues, 'equityOwned');
      const isSumValid = sum > 0 && sum <= 100;

      if (isEquityInvalid || !isSumValid) {
        finalValue = 0;
      }
    }

    const updatedTableValues = tableValues.map((item) =>
      item.id === id ? { ...item, [field]: finalValue } : item
    );
    setTableValues(updatedTableValues);
  };

  const handleDelete = (id: number) => {
    const updatedTableValues = tableValues.filter((item) => item.id !== id);
    setTableValues(updatedTableValues);
  };
  if (assessmentToolStatus === AssessmentToolProgressStatus.COMPLETED) {
    return isLoading ? (
      <Spinner spinnerTheme='overlaySpinner' />
    ) : (
      <ThankYouPage
        isPasswordSet
        loggedInUserData={loggedInUserData}
        pageContent={thankYouPageContent(
          'Stakeholder Alignment Meeting',
          loggedInUserData?.type || UserType.BUSINESS_OWNER,
          `${businessOwnerProperties?.first_name ?? ''} ${
            businessOwnerProperties?.last_name ?? ''
          }`
        )}
      />
    );
  }

  return (
    <>
      <div className='flex justify-between items-center mb-3'>
        <h1 className='font-bold text-2xl'>Stakeholder Alignment Meeting</h1>
        {currentTab === StakeHolderAlignmentMeetingTabs.STATE_OBJECTIVE && (
          <Button
            className='py-2 px-6'
            LeadingIcon={FaDownload}
            leadingIconClassName='mr-2'
            onClick={downloadPdf}
          >
            Download Worksheet for In-Person Meeting
          </Button>
        )}
      </div>
      {currentTab === StakeHolderAlignmentMeetingTabs.STATE_OBJECTIVE && (
        <div className='absolute -left-[200%]'>
          <StakeholderAlignmentPdf
            attendeeTableValues={tableValues}
            setPages={setPages}
            pages={pages}
          />
        </div>
      )}
      {!currentTab ? (
        <Spinner customClassName='mt-4' size='sm' spinnerTheme='default' />
      ) : (
        <WithComments tool={AssessmentTools.STAKEHOLDER_ALIGNMENT_MEETING}>
          <div className='border flex flex-col justify-between bg-white h-[calc(100vh-13rem)] p-12 w-full'>
            {currentTab === StakeHolderAlignmentMeetingTabs.GET_STARTED && (
              <GetStartedPage onGetStartedClick={handleGetStartedClick} />
            )}
            {currentTab === StakeHolderAlignmentMeetingTabs.STAKEHOLDERS && (
              <StakeHoldersPage
                tableValues={tableValues}
                handleTableValues={handleChange}
                handleDelete={handleDelete}
                addStakeholder={addStakeHolder}
                setCurrentTab={setCurrentTab}
                handleSubmitData={submitDataHandler}
                isLoading={isLoading}
                options={relationshipOptions}
              />
            )}
            {currentTab === StakeHolderAlignmentMeetingTabs.STATE_OBJECTIVE && (
              <QuestionAnswerPage
                setCurrentTab={setCurrentTab}
                attendeeTableValue={tableValues}
                handleTableValues={handleChange}
                handleDelete={handleDelete}
                addStakeholder={addStakeHolder}
                textAreaValues={textAreaAnswers}
                handleTextAreaValues={setTextAreaAnswers}
                handleSubmitData={submitDataHandler}
                isLoading={isLoading}
                options={relationshipOptions}
              />
            )}
          </div>
        </WithComments>
      )}
      <Modal visible={isDownloading} handleVisibility={() => {}}>
        <ReportGenerationModal isPdf />
      </Modal>
    </>
  );
};
export default memo(StakeHolderAlignmentMeetingPage);
