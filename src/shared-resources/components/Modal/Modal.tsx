import { Dialog, Transition } from '@headlessui/react';
import classNames from 'classnames';
import useEscapeKeyDetector from 'hooks/useEscapeKeyDetector';
import useOutsideClickDetector from 'hooks/useOutsideClickDetector';
import React, { Fragment } from 'react';
import { IoCloseCircle } from 'react-icons/io5';

interface ModalProps {
  children: React.ReactNode;
  visible: boolean;
  handleVisibility: (value: boolean) => void;
  title?: React.ReactNode | string;
  closeOnOutsideClick?: boolean;
  classname?: string;
  theme?: 'primary' | 'secondary';
}

const Modal: React.FC<ModalProps> = (props: ModalProps) => {
  const {
    children,
    visible,
    handleVisibility,
    title,
    closeOnOutsideClick,
    classname,
    theme = 'primary',
  } = props;

  const closeModal = () => {
    if (closeOnOutsideClick) {
      handleVisibility(false);
    }
  };

  const wrapperRef = useOutsideClickDetector<HTMLDivElement>(closeModal);
  const wrap = useEscapeKeyDetector<HTMLDivElement>(closeModal);

  const headerClasses = classNames(
    'flex items-center justify-between py-4.5 px-7 rounded-t-xl',
    {
      'bg-blue-01': theme === 'primary',
      'bg-white': theme === 'secondary',
    }
  );

  const titleClasses = classNames(
    'text-lg capitalize leading-[2.0625rem] font-medium',
    {
      'text-white': theme === 'primary',
      'text-black': theme === 'secondary',
    }
  );

  const closeIconClasses = classNames('w-5 h-5 cursor-pointer', {
    'text-white': theme === 'primary',
    'text-black': theme === 'secondary',
  });

  return (
    <Transition appear show={visible} as={Fragment}>
      <Dialog
        as='div'
        className='fixed inset-0 z-20 overflow-y-auto'
        onClose={() => null}
      >
        <div className='min-h-screen px-4 text-center'>
          <Transition.Child
            as={Fragment}
            enter='ease-out duration-300'
            enterFrom='opacity-0'
            enterTo='opacity-100'
            leave='ease-in duration-200'
            leaveFrom='opacity-100'
            leaveTo='opacity-0'
          >
            <Dialog.Overlay
              className={classNames(
                'fixed inset-0',
                'bg-gray-01 opacity-75 z-0'
              )}
            />
          </Transition.Child>

          {/* This element is to trick the browser into centering the modal contents. */}
          <span
            className='inline-block h-screen align-middle'
            aria-hidden='true'
          >
            &#8203;
          </span>
          <Transition.Child
            as={Fragment}
            enter='ease-out duration-300'
            enterFrom='opacity-0 scale-95'
            enterTo='opacity-100 scale-100'
            leave='ease-in duration-200'
            leaveFrom='opacity-100 scale-100'
            leaveTo='opacity-0 scale-95'
          >
            <div
              ref={wrap}
              className={`${classNames(
                classname
              )} inline-block text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl`}
            >
              <div ref={wrapperRef}>
                {title && (
                  <div className={headerClasses}>
                    {typeof title === 'string' ? (
                      <Dialog.Title as='h3' className={titleClasses}>
                        {title}
                      </Dialog.Title>
                    ) : (
                      title
                    )}
                    <IoCloseCircle
                      onClick={() => handleVisibility(false)}
                      className={closeIconClasses}
                      aria-hidden='true'
                    />
                  </div>
                )}
                {children}
              </div>
            </div>
          </Transition.Child>
        </div>
      </Dialog>
    </Transition>
  );
};

export default React.memo(Modal);
