import React, { memo, useEffect, useRef } from 'react';
import Button from 'shared-resources/components/Button/Button';
import { FinancialGapAssessmentCalculatorTabs } from 'types/enum';
import classNames from 'classnames';
import { IoInformationCircle } from 'react-icons/io5';
import { Tooltip } from 'react-tooltip';
import {
  EXPENSE_OTHER_KEY_NAME,
  ExpenseItem,
  Expenses,
  formatCurrency,
  getQuestionTitleFromKey,
  getTitle,
  headerOptions,
  OtherExpenseItem,
} from './ExpenseCalculatorConfig';
import ExpenseTableRow from './ExpenseTableRow';

interface Props {
  currentTab: FinancialGapAssessmentCalculatorTabs;
  response: Expenses;
  setResponse?: any;
}
const TableComponent: React.FC<Props> = ({
  currentTab,
  response,
  setResponse,
}) => {
  const currentTabKey = currentTab as keyof Expenses;
  const totalMonthlyExpense = Object.keys(response[currentTabKey]).reduce(
    (acc, key) => {
      const item = response[currentTabKey][key];
      if (key === EXPENSE_OTHER_KEY_NAME && Array.isArray(item)) {
        // Sum up the monthly expenses for each item in the 'Other' array
        const otherTotal = item.reduce(
          (otherAcc: number, otherItem: OtherExpenseItem) =>
            otherAcc + (otherItem.monthly_expense || 0),
          0
        );
        return acc + otherTotal;
      }
      // Sum up the monthly expense for the non-array items
      const monthlyExpense = (item as ExpenseItem).monthly_expense || 0;
      return acc + monthlyExpense;
    },
    0
  );

  const monthlyExpenseMonthlyRefs: any = useRef([]);
  const otherMonthlyExpenseMonthlyRefs = useRef([]);
  const expenseFunctionNameRefs = useRef([]);

  const handleEditIconClick = (index: number, inputRefs: any) => {
    inputRefs.current[index].focus();
  };
  const tableRef = useRef<HTMLDivElement | null>(null);

  const updateMonthlyExpenseFunction = (
    value: number | null,
    expenseFunction: string,
    indexOfOtherExpense?: number
  ) => {
    if (
      expenseFunction === EXPENSE_OTHER_KEY_NAME &&
      typeof indexOfOtherExpense === 'number'
    ) {
      // Clone the current 'Other' expenses array
      const otherExpenses = [
        ...(response[currentTabKey][
          EXPENSE_OTHER_KEY_NAME
        ] as OtherExpenseItem[]),
      ];

      // Update the monthly_expense of the specific item at the index
      otherExpenses[indexOfOtherExpense] = {
        ...otherExpenses[indexOfOtherExpense],
        monthly_expense: value || 0,
      };

      // Update the state with the new 'Other' expenses array
      setResponse({
        ...response,
        [currentTabKey]: {
          ...(response[currentTabKey] as any),
          [EXPENSE_OTHER_KEY_NAME]: otherExpenses,
        },
      });
    } else {
      setResponse({
        ...response,
        [currentTabKey]: {
          ...(response[currentTabKey] as any),
          [expenseFunction]: {
            monthly_expense: value,
          },
        },
      });
    }
  };

  const updateOtherExpenseNameFunction = (
    value: string,
    indexOfOtherExpense: number
  ) => {
    // Clone the current 'Other' expenses array
    const otherExpenses = [
      ...(response[currentTabKey][
        EXPENSE_OTHER_KEY_NAME
      ] as OtherExpenseItem[]),
    ];

    // Update the monthly_expense of the specific item at the index
    otherExpenses[indexOfOtherExpense] = {
      ...otherExpenses[indexOfOtherExpense],
      expense_name: value,
    };

    // Update the state with the new 'Other' expenses array
    setResponse({
      ...response,
      [currentTabKey]: {
        ...response[currentTabKey],
        [EXPENSE_OTHER_KEY_NAME]: otherExpenses,
      },
    });
  };

  const addOthers = () => {
    const newAdded = {
      expense_name: 'Other',
      monthly_expense: 0,
    };
    const currentTabResponse = response[currentTabKey];
    const currentTabOther =
      currentTabResponse &&
      Array.isArray(currentTabResponse[EXPENSE_OTHER_KEY_NAME])
        ? currentTabResponse[EXPENSE_OTHER_KEY_NAME]
        : [];

    setResponse({
      ...response,
      [currentTabKey]: {
        ...currentTabResponse,
        [EXPENSE_OTHER_KEY_NAME]: [...currentTabOther, newAdded],
      },
    });
  };

  const removeOthers = (indexToRemove: number) => {
    const currentTabResponse = response[currentTabKey];
    const currentTabOther = Array.isArray(
      currentTabResponse?.[EXPENSE_OTHER_KEY_NAME]
    )
      ? currentTabResponse[EXPENSE_OTHER_KEY_NAME]
      : [];

    // Create a new array without mutating the original
    const newArray = currentTabOther.filter((_, idx) => idx !== indexToRemove);

    // Update state using setResponse
    setResponse({
      ...response,
      [currentTabKey]: {
        ...currentTabResponse,
        [EXPENSE_OTHER_KEY_NAME]: newArray,
      },
    });
  };

  useEffect(() => {
    tableRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
  }, [currentTab]);

  const currentTabTitle = currentTab && getTitle(currentTab);

  return (
    <>
      <div className='flex  justify-between items-center'>
        <div className='flex space-x-3'>
          <h1>Post-Exit Living Expenses Worksheet :</h1>
          <div className='flex space-x-2'>
            <h2 className='text-blue-01 font-semibold'>{currentTabTitle}</h2>
            <div className='flex items-center gap-2'>
              <IoInformationCircle
                id='expenseHeading'
                className='text-blue-01'
              />
              <div className='relative'>
                <Tooltip
                  anchorSelect='#expenseHeading'
                  place='left-start'
                  offset={0}
                  className='!p-4 !bg-gray-100 border !z-20  !w-96 !font-[500]  !left-1 !top-4  !text-black-01 !text-[0.9rem] !rounded-lg'
                  classNameArrow='border-r border-b  !w-[1.1rem] !h-3.5 !-left-[0.5rem]'
                >
                  <p>Annual amounts will autocalculate</p>
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
        <Button onClick={addOthers} className='mr-10 px-5 py-1.5'>
          Add
        </Button>
      </div>

      <div
        className='pr-8 h-[calc(100vh-22rem)]  relative scrollbar overflow-auto w-full rounded-t-md'
        ref={tableRef}
      >
        <div
          className={`bg-blue-01 z-10 tracking-[0.07rem] sticky top-0 grid grid-cols-10 overflow-hidden text-white 
           font-[500] rounded-t-md text-base `}
        >
          {headerOptions.map((h, index) => (
            <div
              key={h}
              className={`px-5 flex items-center border-r border-gray-02 justify-center py-3 h-full col-span-3 ${
                index === 0 && 'border-l rounded-tl-md col-span-4'
              } ${headerOptions.length - 1 === index && 'rounded-t-md  '} `}
            >
              <span className='text-center'>
                {index === 0 ? currentTabTitle : h}
              </span>
            </div>
          ))}
        </div>
        <div>
          {currentTab &&
            Object.keys(response[currentTabKey]).map(
              (expenseFunction, index) => {
                const expenseItem = response[currentTabKey][expenseFunction];

                if (Array.isArray(expenseItem)) {
                  return expenseItem.map((item, subIndex) => (
                    <ExpenseTableRow
                      // eslint-disable-next-line react/no-array-index-key
                      key={`${index}-${subIndex}`}
                      expenseFunctionName={expenseFunction}
                      expenseFunctionValue={item.expense_name}
                      index={subIndex}
                      monthlyExpenseValue={item.monthly_expense}
                      monthlyExpenseInputRefs={otherMonthlyExpenseMonthlyRefs}
                      onDeleteItem={() => {
                        removeOthers(subIndex);
                      }}
                      editExpenseFunctionNameRefs={expenseFunctionNameRefs}
                      updateOtherExpenseNameFunction={
                        updateOtherExpenseNameFunction
                      }
                      handleEditIconClick={handleEditIconClick}
                      updateMonthlyExpenseFunction={
                        updateMonthlyExpenseFunction
                      }
                    />
                  ));
                }

                return (
                  <ExpenseTableRow
                    // eslint-disable-next-line react/no-array-index-key
                    key={`${index}`}
                    expenseFunctionName={expenseFunction}
                    expenseFunctionValue={getQuestionTitleFromKey(
                      expenseFunction as any,
                      currentTabKey
                    )}
                    index={index}
                    monthlyExpenseValue={expenseItem.monthly_expense}
                    monthlyExpenseInputRefs={monthlyExpenseMonthlyRefs}
                    handleEditIconClick={handleEditIconClick}
                    updateMonthlyExpenseFunction={updateMonthlyExpenseFunction}
                  />
                );
              }
            )}
          <div
            className={classNames(
              'grid grid-cols-10 font-[500]  text-[0.9rem] '
            )}
          >
            <div className='px-3  col-span-4 rounded-bl-md  flex items-center border-b border-l py-3 border-r border-gray-02 bg-gray-08'>
              <span className='text-xs xl:text-base '>
                Total : Post Transition
              </span>
            </div>
            <div className='cursor-pointer  col-span-3  border-b  border-r border-gray-02 bg-gray-08'>
              <div className='flex justify-center items-center h-full w-full text-semibold text-base'>
                <span className='truncate mr-[0.7rem]'>
                  {formatCurrency(totalMonthlyExpense)}
                </span>
              </div>
            </div>
            <div className='rounded-br-md border-b col-span-3  border-r border-gray-02 bg-gray-08'>
              <div className='flex items-center justify-center h-full w-full text-semibold text-base'>
                <span className='truncate'>
                  {formatCurrency(totalMonthlyExpense * 12)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default memo(TableComponent);
