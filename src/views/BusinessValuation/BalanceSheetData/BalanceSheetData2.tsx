import { Form, Formik } from 'formik';
import React from 'react';
import { IoInformationCircle } from 'react-icons/io5';
import { useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { FormikSelect } from 'shared-resources/components/Select/FormikSelect';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { ImpactType } from 'types/enum';
import { getOptionsFromEnum } from 'utils/helpers/Helpers';
import * as yup from 'yup';

interface Props {
  nextStep: () => void;
  setData: (data: any) => void;
  backStep: () => void;
  data: any;
  onSaveToDraftClick: (data: any) => void;
}
const BalanceSheetData2: React.FC<Props> = (props) => {
  const { nextStep, setData, backStep, data, onSaveToDraftClick } = props;
  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup.object().shape({
    project_revenue_growth: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Project Revenue Growth is required'),
    percentage_of_recurring_business: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Percentage of Recurring Business is required'),
    projected_ebitda: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Projected EBITDA is required'),
    has_intellectual_property: yup
      .string()
      .required('Intellectual Property is required'),
    sales: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Sales is required'),
    impact_on_revenue: yup.string().required('Impact on revenue is required'),
  });
  const initialValue = data?.balance_sheet_data_2 || {
    project_revenue_growth: '',
    percentage_of_recurring_business: '',
    projected_ebitda: '',
    has_intellectual_property: '',
    sales: '',
    impact_on_revenue: '',
  };

  const impactOptions = getOptionsFromEnum(ImpactType);

  const handleSubmit = (values: any) => {
    nextStep();
    setData(values);
  };

  return (
    <div className='flex flex-col '>
      <Formik
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values }) => (
          <Form className='pl-5 py-3 bg-white'>
            <div className='flex flex-col h-[calc(100vh-19rem)] mt-6 overflow-auto scrollbar pr-4 text-[1rem]'>
              <div className='flex gap-6'>
                <FormikInput
                  labelIcon2={
                    <div className='flex  items-center gap-2'>
                      <IoInformationCircle
                        id='revenueGrowth'
                        className='text-blue-01'
                      />
                      <div className='relative'>
                        <Tooltip
                          anchorSelect='#revenueGrowth'
                          place='right'
                          offset={0}
                          className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                          classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                        >
                          This percentage reflects the company’s intermediate /
                          long term rate of growth in terms of “topline”
                          revenues - the projected “Compound annual growth
                          rate”. The higher the projected revenue growth, the
                          higher the future of profits and current business
                          value. The combination of revue growth rate and
                          long-term EBITDA margin will impact the discounted
                          cash flow paradigm which is particularly important
                          from valuing high growth, high profit companies.
                        </Tooltip>
                      </div>
                    </div>
                  }
                  name='project_revenue_growth'
                  key='project_revenue_growth'
                  label='Projected Revenue Growth (%)'
                  labelClassName='!text-[1rem]'
                />
                <FormikInput
                  labelIcon2={
                    <div className='flex  items-center gap-2'>
                      <IoInformationCircle
                        id='percentage_of_recurring_business'
                        className='text-blue-01'
                      />
                      <div className='relative'>
                        <Tooltip
                          anchorSelect='#percentage_of_recurring_business'
                          place='right'
                          offset={0}
                          className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                          classNameArrow='border-r  border-t border-t-blue-01 border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-[1.2rem] !-left-[0.5rem] !top-[0.3rem]'
                        >
                          This indicator is a key risk determinant in that
                          building a client base which returns year and year
                          will reduce the riskiness of future cash flows (Lower
                          risk, higher values). Repeat customers are easier to
                          work with and tend to spend more money and refer new
                          customers, enhancing their value.
                        </Tooltip>
                      </div>
                    </div>
                  }
                  name='percentage_of_recurring_business'
                  key='percentage_of_recurring_business'
                  label='Percentage of Recurring Business'
                  labelClassName='!text-[1rem]'
                />
              </div>
              <div className='grid grid-cols-2 gap-x-6 items-end'>
                <FormikInput
                  className='!my-auto'
                  labelIcon2={
                    <div className='flex  items-center gap-2'>
                      <IoInformationCircle
                        id='projected_ebitda'
                        className='text-blue-01'
                      />
                      <div className='relative'>
                        <Tooltip
                          anchorSelect='#projected_ebitda'
                          place='right'
                          offset={0}
                          className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                          classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                        >
                          This percentage represents expectations as to long run
                          operation cash flow (EBITDA) margin in the future.
                          EBITDA is a measure of a company’s operation
                          profitability and equals earnings before interest,
                          tax, depreciation and amortization. EBITDA margin is
                          defined as EBITDA divided by total revenues. The
                          combination of revenue rowth rate and long-term EBITDA
                          margin will impact the discounted cash flow paradigm
                          which is particularly important for valuing high
                          growth, high profit companies.
                        </Tooltip>
                      </div>
                    </div>
                  }
                  name='projected_ebitda'
                  key='projected_ebitda'
                  label='Projected EBITDA (%)'
                  labelClassName='!text-[1rem]'
                />
                <FormikInput
                  labelIcon2={
                    <div className='flex items-center gap-2'>
                      <IoInformationCircle
                        id='has_intellectual_property'
                        className='text-blue-01'
                      />
                      <div className='relative'>
                        <Tooltip
                          anchorSelect='#has_intellectual_property'
                          place='right'
                          offset={0}
                          className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                          classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                        >
                          Does the business have any intellectual property or
                          goodwill that should be taken into account. The effect
                          of IP on the final multiple is relatively minimal, and
                          this question is largely included for data gathering
                          purposes.
                        </Tooltip>
                      </div>
                    </div>
                  }
                  name='has_intellectual_property'
                  key='has_intellectual_property'
                  label='Do you have intellectual property including trade secrets and patents?'
                  labelClassName='!text-[1rem]'
                />
              </div>
              <div className='grid grid-cols-2 gap-x-6 items-end'>
                <FormikInput
                  className='!mb-0.5'
                  labelIcon2={
                    <div className='flex  items-center gap-2'>
                      <IoInformationCircle
                        id='sales'
                        className='text-blue-01'
                      />
                      <div className='relative'>
                        <Tooltip
                          anchorSelect='#sales'
                          place='right'
                          offset={0}
                          className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                          classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                        >
                          How much of the businesses revenue comes from a small
                          portion of the customer base? Concentration of revenue
                          streams may have an impact on business value.
                        </Tooltip>
                      </div>
                    </div>
                  }
                  name='sales'
                  key='sales'
                  label='What % of sales come from top three customers?'
                  labelClassName='!text-[1rem]'
                />

                <FormikSelect
                  classname='!w-full'
                  optionClassname='!py-1 !px-1'
                  name='impact_on_revenue'
                  label='What would be the impact on revenue and profit if the owner exited?'
                  options={impactOptions}
                  menuPlacement='top'
                  labelClassName='!text-[1rem]'
                  placeholder='Select Options'
                />
              </div>
            </div>

            <div className='pr-7'>
              <BackNextComponent
                backStep={backStep}
                buttonType='submit'
                isLoading={isLoading}
                isNextDisable={false}
                onSaveToDraftClick={() => {
                  onSaveToDraftClick(values);
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default BalanceSheetData2;
