import React from 'react';
import { Formik, Form } from 'formik';
import { useSelector } from 'react-redux';
import * as yup from 'yup';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import cx from 'classnames';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import Select from 'shared-resources/components/Select/Select';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import { BuyerTypeOther, BuyerTypeScreens } from './BuyerTypeConfig';

interface Props {
  data: any;
  handleNextClick: (values: any) => void;
  handleBackClick: (values: any) => void;
  handleSaveAsDraftClick: (values: any) => void;
}

interface SelectOption {
  value: string;
  label: string;
}

const BuyerTypeIdentified: React.FC<Props> = ({
  data,
  handleNextClick,
  handleBackClick,
  handleSaveAsDraftClick,
}) => {
  const isLoading = useSelector(getAssessmentToolLoading);

  const buyerTypeOptions: SelectOption[] = Object.values(BuyerTypeOther).map(
    (buyerType) => ({
      value: buyerType,
      label: buyerType
        .replace(/_/g, ' ')
        .replace(/\b\w/g, (char) => char.toUpperCase()),
    })
  );

  const validationSchema = yup.object().shape({
    potential_buyer: yup.string(),
    envisioned_deal: yup.string().when('potential_buyer', {
      is: (val: string) => val && val?.trim() !== '',
      then: () => yup.string().required('This field is required'),
      otherwise: () => yup.string().notRequired(),
    }),
    buyer_type: yup.string().when('potential_buyer', {
      is: (val: string) => val && val?.trim() !== '',
      then: () => yup.string().required('This field is required'),
      otherwise: () => yup.string().notRequired(),
    }),
    reason_for_choice: yup.string().when('potential_buyer', {
      is: (val: string) => val && val?.trim() !== '',
      then: () => yup.string().required('This field is required'),
      otherwise: () => yup.string().notRequired(),
    }),
    conversation: yup.string().when('potential_buyer', {
      is: (val: string) => val && val?.trim() !== '',
      then: () => yup.string().required('This field is required'),
      otherwise: () => yup.string().notRequired(),
    }),
  });

  const initialValues = {
    potential_buyer:
      data?.[BuyerTypeScreens.ALREADY_IDENTIFIED]?.potential_buyer || undefined,
    buyer_type:
      data?.[BuyerTypeScreens.ALREADY_IDENTIFIED]?.buyer_type || undefined,
    conversation:
      data?.[BuyerTypeScreens.ALREADY_IDENTIFIED]?.conversation || undefined,
    reason_for_choice:
      data?.[BuyerTypeScreens.ALREADY_IDENTIFIED]?.reason_for_choice ||
      undefined,
    envisioned_deal:
      data?.[BuyerTypeScreens.ALREADY_IDENTIFIED]?.envisioned_deal || undefined,
  };

  if (isLoading) {
    return <Spinner spinnerTheme='overlaySpinner' />;
  }

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={(values) => {
        handleNextClick(values);
      }}
      enableReinitialize
    >
      {({ values, setFieldValue, errors, touched }) => (
        <Form
          className={cx(
            'flex flex-col h-[calc(100vh-14rem)] justify-between px-10 py-5'
          )}
        >
          <div className='flex flex-col space-y-2'>
            <h1 className='h-16 bg-white sticky top-0 z-10'>
              If you have not identified the potential buyer, please{' '}
              <button
                className='text-blue-01 cursor-pointer'
                onClick={() => handleNextClick({})}
              >
                skip.
              </button>
            </h1>
            <h1 className='h-16 bg-white sticky top-0 z-10'>
              If you have identified a potential buyer, please name the buyer
            </h1>
          </div>
          <div className='overflow-auto scrollbar rounded-lg flex flex-col space-y-6 pb-8'>
            <FormikInput
              name='potential_buyer'
              key='potential_buyer'
              label='Potential Buyer (if known)'
              labelClassName='mb-1 font-medium'
              fieldType='textarea'
              className='h-28'
            />

            <div className='flex justify-between items-center'>
              <div className=' flex flex-col'>
                <h1 className='font-medium'>
                  Which buyer type is the buyer you have identified?
                </h1>
                {errors.buyer_type && touched.buyer_type && (
                  <h1 className='text-red-500 text-sm'>
                    {errors.buyer_type as string}
                  </h1>
                )}
              </div>
              <Select
                className='w-[55rem]'
                options={buyerTypeOptions}
                selectedOption={values.buyer_type}
                onChange={(option: any) => setFieldValue('buyer_type', option)}
              />
            </div>

            <FormikInput
              name='conversation'
              key='conversation'
              label='Briefly explain any conversation you have had with this potential buyer about buying your business.'
              labelClassName='mb-1 font-medium'
              fieldType='textarea'
              className='h-28'
            />

            <FormikInput
              name='reason_for_choice'
              key='reason_for_choice'
              label='Briefly explain why you chose this buyer.'
              labelClassName='mb-1  font-medium'
              fieldType='textarea'
              className='h-28'
            />

            <FormikInput
              name='envisioned_deal'
              key='envisioned_deal'
              label='Briefly describe the deal you envision with this buyer.'
              labelClassName='mb-1  font-medium'
              fieldType='textarea'
              className='h-28'
            />
          </div>
          <div className='px-2 py-2'>
            <BackNextComponent
              backStep={() => handleBackClick(values)}
              buttonType='submit'
              isLoading={isLoading}
              onSaveToDraftClick={() => handleSaveAsDraftClick(values)}
              isNextDisable={false}
            />
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default BuyerTypeIdentified;
