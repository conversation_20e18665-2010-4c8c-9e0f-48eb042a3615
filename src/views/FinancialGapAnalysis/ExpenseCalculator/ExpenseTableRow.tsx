import classNames from 'classnames';
import React from 'react';
import { MdDelete, MdEdit } from 'react-icons/md';
import {
  EXPENSE_OTHER_KEY_NAME,
  formatCurrency,
} from './ExpenseCalculatorConfig';

interface Props {
  expenseFunctionName: string;
  expenseFunctionValue: string;
  monthlyExpenseValue: number;
  index: number;
  monthlyExpenseInputRefs?: any;
  onDeleteItem?: () => void;
  editExpenseFunctionNameRefs?: any;
  updateOtherExpenseNameFunction?: (
    value: string,
    indexOfOtherExpense: number
  ) => void;
  handleEditIconClick?: (index: number, inputRefs: any) => void;
  updateMonthlyExpenseFunction?: (
    value: number | null,
    expenseFunction: string,
    indexOfOtherExpense?: number
  ) => void;
  expenseFunctionValueClassName?: string;
  expenseValueContainerClassName?: string;
}
const ExpenseTableRow: React.FC<Props> = ({
  expenseFunctionName,
  expenseFunctionValue,
  monthlyExpenseValue,
  index,
  monthlyExpenseInputRefs,
  onDeleteItem,
  editExpenseFunctionNameRefs,
  updateOtherExpenseNameFunction,
  handleEditIconClick,
  updateMonthlyExpenseFunction,
  expenseFunctionValueClassName,
  expenseValueContainerClassName,
}) => (
  // If Increasing Height or Padding of Row. Also Update paginateData from ExpenseCalculatorConfig File
  <div className={classNames('grid grid-cols-10 font-[500]')}>
    <div className='px-3  col-span-4 flex items-center border-b border-l py-3 border-r border-gray-02'>
      {editExpenseFunctionNameRefs ? (
        <div className='flex items-center w-full  relative'>
          <input
            ref={(el) => {
              if (editExpenseFunctionNameRefs)
                editExpenseFunctionNameRefs.current[index] = el;
            }}
            className='text-xs xl:text-base h-full border-none outline-none truncate'
            onChange={(event) => {
              updateOtherExpenseNameFunction?.(event.target.value, index);
            }}
            value={expenseFunctionValue}
            type='text'
          />
          {handleEditIconClick && (
            <MdEdit
              onClick={() =>
                handleEditIconClick(index, editExpenseFunctionNameRefs)
              }
              className='text-blue-01 text-lg cursor-pointer flex-shrink-0 absolute right-0'
            />
          )}
        </div>
      ) : (
        <span
          className={classNames(
            'text-xs xl:text-base',
            expenseFunctionValueClassName
          )}
        >
          {expenseFunctionValue}
        </span>
      )}
    </div>
    <div className='px-1  2xl:px-3  col-span-3 flex items-center border-b  py-3 border-r border-gray-02'>
      <div
        className={classNames(
          'relative text-base w-full flex justify-center items-center',
          expenseValueContainerClassName
        )}
      >
        {updateMonthlyExpenseFunction ? (
          <div className='flex items-center w-full justify-center ml-6'>
            <span>$</span>
            <input
              ref={(el) => {
                if (monthlyExpenseInputRefs)
                  monthlyExpenseInputRefs!.current[index] = el;
              }}
              disabled={!monthlyExpenseInputRefs}
              className='h-full border-none outline-none truncate'
              value={formatCurrency(monthlyExpenseValue).replace('$', '')}
              onChange={(event) => {
                const value = +event.target.value
                  .replaceAll(',', '')
                  .replace('$', '');
                const isNumberValue = !Number.isNaN(value);
                if (isNumberValue) {
                  updateMonthlyExpenseFunction?.(
                    value,
                    expenseFunctionName,
                    expenseFunctionName === EXPENSE_OTHER_KEY_NAME
                      ? index
                      : undefined
                  );
                }
              }}
              style={{
                width: `${monthlyExpenseValue.toString().length + 3}ch`,
              }}
              min={0}
            />
          </div>
        ) : (
          <span>{formatCurrency(monthlyExpenseValue)}</span>
        )}
        {handleEditIconClick && (
          <MdEdit
            onClick={() => handleEditIconClick(index, monthlyExpenseInputRefs)}
            className='text-blue-01 text-lg cursor-pointer flex-shrink-0 absolute right-0'
          />
        )}
      </div>
    </div>
    <div className='relative col-span-3  border-b  border-r border-gray-02 bg-gray-08  flex items-center'>
      <div
        className={classNames(
          'flex items-center justify-center w-full text-base',
          expenseValueContainerClassName
        )}
      >
        <span className='truncate'>
          {formatCurrency(monthlyExpenseValue * 12)}
        </span>
      </div>
      {onDeleteItem && (
        <MdDelete
          className='absolute top-1/2 -translate-y-1/2  -right-6 text-red-500 cursor-pointer'
          size={20}
          onClick={onDeleteItem}
        />
      )}
    </div>
  </div>
);

export default ExpenseTableRow;
