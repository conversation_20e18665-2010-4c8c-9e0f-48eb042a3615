import classNames from 'classnames';
import React from 'react';

interface DetailItemProps {
  name: string;
  value: string | undefined;
  width?: string;
}
const DetailItem: React.FC<DetailItemProps> = (props) => {
  const { name, value, width } = props;

  return (
    <div
      className={classNames('flex h-fit items-start mb-4', width || 'w-[49%]')}
    >
      <span className='min-w-[12rem] whitespace-nowrap mr-2 font-medium'>
        {name}
      </span>
      <span className='text-center mr-4'>:</span>
      <span className='text-blue-01 font-medium'>{value ?? ''}</span>
    </div>
  );
};

export default DetailItem;
