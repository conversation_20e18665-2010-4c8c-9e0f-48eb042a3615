import React from 'react';
import { User } from 'models/entities/User';
import { NavigateFunction } from 'react-router';
import {
  AssessmentResponseType,
  BusinessValuationScreens,
  RouteKey,
  UserRouteType,
  UserType,
} from 'types/enum';
import BalanceSheetData1 from './BalanceSheetData/BalanceSheetData1';
import BalanceSheetData2 from './BalanceSheetData/BalanceSheetData2';
import BalanceSheetData3 from './BalanceSheetData/BalanceSheetData3';
import BasicInformation from './BasicInformation';
import { screenToNumberObject } from './BusinessValuationConfig';
import IncomeStatementData from './IncomeStatementData';
import InfoScreen from './InfoScreen';
import NonActiveFamilyBenefitsPandL from './PAndLNormalization/NonActiveFamilyBenefitsPandL';
import OwnerInsurancePandL from './PAndLNormalization/OwnerInsurancePandL';
import OwnerPandL from './PAndLNormalization/OwnerPandL';
import PersonalExpensesPandL from './PAndLNormalization/PersonalExpensesPandL';
import RentAdjustmentsPandL from './PAndLNormalization/RentAdjustmentsPandL';
import SpouseOrFamilyPandL from './PAndLNormalization/SpouseOrFamilyPandL';
import TotalNormalizationExpenses from './PAndLNormalization/TotalNormalizationExpenses';
import VehiclePandL from './PAndLNormalization/VehiclePandL';
import PaymentPage from './PaymentPage';

export const getScreen = (
  currentScreen: {
    screen: BusinessValuationScreens | undefined;
  },
  loggedInUser: User,
  businessValuationData: any,
  setBusinessValuationData: any,
  backNextClickHandler: (nextScreen: BusinessValuationScreens) => void,
  setSubmitType: any,
  navigate: NavigateFunction,
  businessOwnerProperties: any,
  updateBusinessOwner: (values: any) => void
) => {
  if (currentScreen?.screen === BusinessValuationScreens.PAYMENT_SCREEN) {
    return <PaymentPage />;
  }
  if (currentScreen?.screen === BusinessValuationScreens.BASIC_INFORMATION) {
    return (
      <BasicInformation
        businessOwnerProperties={businessOwnerProperties}
        data={businessValuationData}
        setData={(data: any) => {
          setBusinessValuationData({
            ...businessValuationData,
            basic_information: data,
          });
        }}
        nextStep={(values) => {
          updateBusinessOwner(values);
        }}
        backStep={() => {
          if (loggedInUser?.type === UserType.ADVISOR) {
            navigate(`/${UserRouteType.ADVISOR}/${RouteKey.DASHBOARD}`);
          } else {
            navigate(`/${UserRouteType.BUSINESS_OWNER}/${RouteKey.DASHBOARD}`);
          }
        }}
        user={loggedInUser}
      />
    );
  }
  if (currentScreen?.screen === BusinessValuationScreens.INFO_SCREEN) {
    return (
      <InfoScreen
        nextStep={() => {
          backNextClickHandler(BusinessValuationScreens.OWNER_P_AND_L);
        }}
      />
    );
  }
  if (currentScreen?.screen === BusinessValuationScreens.OWNER_P_AND_L) {
    return (
      <OwnerPandL
        data={businessValuationData}
        setData={(data: any) => {
          setBusinessValuationData({
            ...businessValuationData,
            owner_p_and_l: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(
            BusinessValuationScreens.SPOUSE_OR_FAMILY_P_AND_L
          );
        }}
        backStep={() => {
          backNextClickHandler(BusinessValuationScreens.BASIC_INFORMATION);
        }}
        onSaveToDraftClick={(data: any) => {
          const currentScreen = businessValuationData?.saved_screen || 0;
          if (currentScreen < screenToNumberObject.owner_p_and_l) {
            setBusinessValuationData({
              ...businessValuationData,
              owner_p_and_l: data,
              saved_screen: screenToNumberObject.owner_p_and_l,
            });
          } else {
            setBusinessValuationData({
              ...businessValuationData,
              owner_p_and_l: data,
            });
          }
          setSubmitType(AssessmentResponseType.DRAFT);
        }}
      />
    );
  }
  if (
    currentScreen?.screen === BusinessValuationScreens.SPOUSE_OR_FAMILY_P_AND_L
  ) {
    return (
      <SpouseOrFamilyPandL
        data={businessValuationData}
        setData={(data: any) => {
          setBusinessValuationData({
            ...businessValuationData,
            spouse_or_family_p_and_l: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(
            BusinessValuationScreens.OWNER_INSURANCE_P_AND_L
          );
        }}
        backStep={() => {
          backNextClickHandler(BusinessValuationScreens.OWNER_P_AND_L);
        }}
        onSaveToDraftClick={(data: any) => {
          const currentScreen = businessValuationData?.saved_screen || 0;
          if (currentScreen < screenToNumberObject.spouse_or_family_p_and_l) {
            setBusinessValuationData({
              ...businessValuationData,
              spouse_or_family_p_and_l: data,
              saved_screen: screenToNumberObject.spouse_or_family_p_and_l,
            });
          } else {
            setBusinessValuationData({
              ...businessValuationData,
              spouse_or_family_p_and_l: data,
            });
          }
          setSubmitType(AssessmentResponseType.DRAFT);
        }}
      />
    );
  }
  if (
    currentScreen?.screen === BusinessValuationScreens.OWNER_INSURANCE_P_AND_L
  ) {
    return (
      <OwnerInsurancePandL
        data={businessValuationData}
        setData={(data: any) => {
          setBusinessValuationData({
            ...businessValuationData,
            owner_insurance: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessValuationScreens.VEHICLE_P_AND_L);
        }}
        backStep={() => {
          backNextClickHandler(
            BusinessValuationScreens.SPOUSE_OR_FAMILY_P_AND_L
          );
        }}
        onSaveToDraftClick={(data: any) => {
          const currentScreen = businessValuationData?.saved_screen || 0;
          if (currentScreen < screenToNumberObject.owner_insurance_p_and_l) {
            setBusinessValuationData({
              ...businessValuationData,
              owner_insurance: data,
              saved_screen: screenToNumberObject.owner_insurance_p_and_l,
            });
          } else {
            setBusinessValuationData({
              ...businessValuationData,
              owner_insurance: data,
            });
          }
          setSubmitType(AssessmentResponseType.DRAFT);
        }}
      />
    );
  }
  if (currentScreen?.screen === BusinessValuationScreens.VEHICLE_P_AND_L) {
    return (
      <VehiclePandL
        data={businessValuationData}
        setData={(data: any) => {
          setBusinessValuationData({
            ...businessValuationData,
            vehicle_p_and_l: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(
            BusinessValuationScreens.RENT_ADJUSTMENTS_P_AND_L
          );
        }}
        backStep={() => {
          backNextClickHandler(
            BusinessValuationScreens.OWNER_INSURANCE_P_AND_L
          );
        }}
        onSaveToDraftClick={(data: any) => {
          const currentScreen = businessValuationData?.saved_screen || 0;
          if (currentScreen < screenToNumberObject.vehicle_p_and_l) {
            setBusinessValuationData({
              ...businessValuationData,
              vehicle_p_and_l: data,
              saved_screen: screenToNumberObject.vehicle_p_and_l,
            });
          } else {
            setBusinessValuationData({
              ...businessValuationData,
              vehicle_p_and_l: data,
            });
          }
          setSubmitType(AssessmentResponseType.DRAFT);
        }}
      />
    );
  }
  if (
    currentScreen?.screen === BusinessValuationScreens.RENT_ADJUSTMENTS_P_AND_L
  ) {
    return (
      <RentAdjustmentsPandL
        data={businessValuationData}
        setData={(data: any) => {
          setBusinessValuationData({
            ...businessValuationData,
            rent_adjustments: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(
            BusinessValuationScreens.NON_ACTIVE_FAMILY_BENEFITS_P_AND_L
          );
        }}
        backStep={() => {
          backNextClickHandler(BusinessValuationScreens.VEHICLE_P_AND_L);
        }}
        onSaveToDraftClick={(data: any) => {
          const currentScreen = businessValuationData?.saved_screen || 0;
          if (currentScreen < screenToNumberObject.rent_adjustments_p_and_l) {
            setBusinessValuationData({
              ...businessValuationData,
              rent_adjustments: data,
              saved_screen: screenToNumberObject.rent_adjustments_p_and_l,
            });
          } else {
            setBusinessValuationData({
              ...businessValuationData,
              rent_adjustments: data,
            });
          }
          setSubmitType(AssessmentResponseType.DRAFT);
        }}
      />
    );
  }
  if (
    currentScreen?.screen ===
    BusinessValuationScreens.NON_ACTIVE_FAMILY_BENEFITS_P_AND_L
  ) {
    return (
      <NonActiveFamilyBenefitsPandL
        data={businessValuationData}
        setData={(data: any) => {
          setBusinessValuationData({
            ...businessValuationData,
            non_active_family_benefits: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(
            BusinessValuationScreens.PERSONAL_EXPENSES_P_AND_L
          );
        }}
        backStep={() => {
          backNextClickHandler(
            BusinessValuationScreens.RENT_ADJUSTMENTS_P_AND_L
          );
        }}
        onSaveToDraftClick={(data: any) => {
          const currentScreen = businessValuationData?.saved_screen || 0;
          if (
            currentScreen <
            screenToNumberObject.non_active_family_benefits_p_and_l
          ) {
            setBusinessValuationData({
              ...businessValuationData,
              non_active_family_benefits: data,
              saved_screen:
                screenToNumberObject.non_active_family_benefits_p_and_l,
            });
          } else {
            setBusinessValuationData({
              ...businessValuationData,
              non_active_family_benefits: data,
            });
          }
          setSubmitType(AssessmentResponseType.DRAFT);
        }}
      />
    );
  }
  if (
    currentScreen?.screen === BusinessValuationScreens.PERSONAL_EXPENSES_P_AND_L
  ) {
    return (
      <PersonalExpensesPandL
        data={businessValuationData}
        setData={(data: any) => {
          setBusinessValuationData({
            ...businessValuationData,
            personal_expenses: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(
            BusinessValuationScreens.TOTAL_NORMALIZATION_EXPENSES
          );
        }}
        backStep={() => {
          backNextClickHandler(
            BusinessValuationScreens.NON_ACTIVE_FAMILY_BENEFITS_P_AND_L
          );
        }}
        onSaveToDraftClick={(data: any) => {
          const currentScreen = businessValuationData?.saved_screen || 0;
          if (currentScreen < screenToNumberObject.personal_expenses_p_and_l) {
            setBusinessValuationData({
              ...businessValuationData,
              personal_expenses: data,
              saved_screen: screenToNumberObject.personal_expenses_p_and_l,
            });
          } else {
            setBusinessValuationData({
              ...businessValuationData,
              personal_expenses: data,
            });
          }
          setSubmitType(AssessmentResponseType.DRAFT);
        }}
      />
    );
  }

  if (
    currentScreen?.screen ===
    BusinessValuationScreens.TOTAL_NORMALIZATION_EXPENSES
  ) {
    return (
      <TotalNormalizationExpenses
        data={businessValuationData}
        setData={(data: any) => {
          setBusinessValuationData({
            ...businessValuationData,
            total_normalization_expenses: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessValuationScreens.INCOME_STATEMENT_DATA);
        }}
        backStep={() => {
          backNextClickHandler(
            BusinessValuationScreens.PERSONAL_EXPENSES_P_AND_L
          );
        }}
        onSaveToDraftClick={(data: any) => {
          const currentScreen = businessValuationData?.saved_screen || 0;
          if (
            currentScreen < screenToNumberObject.total_normalization_expenses
          ) {
            setBusinessValuationData({
              ...businessValuationData,
              total_normalization_expenses: data,
              saved_screen: screenToNumberObject.total_normalization_expenses,
            });
          } else {
            setBusinessValuationData({
              ...businessValuationData,
              total_normalization_expenses: data,
            });
          }
          setSubmitType(AssessmentResponseType.DRAFT);
        }}
      />
    );
  }

  if (
    currentScreen?.screen === BusinessValuationScreens.INCOME_STATEMENT_DATA
  ) {
    return (
      <IncomeStatementData
        data={businessValuationData}
        setData={(data: any) => {
          setBusinessValuationData({
            ...businessValuationData,
            income_statement_data: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessValuationScreens.BALANCE_SHEET_DATA_1);
        }}
        backStep={() => {
          backNextClickHandler(
            BusinessValuationScreens.TOTAL_NORMALIZATION_EXPENSES
          );
        }}
        onSaveToDraftClick={(data: any) => {
          const currentScreen = businessValuationData?.saved_screen || 0;
          if (currentScreen < screenToNumberObject.income_statement_data) {
            setBusinessValuationData({
              ...businessValuationData,
              income_statement_data: data,
              saved_screen: screenToNumberObject.income_statement_data,
            });
          } else {
            setBusinessValuationData({
              ...businessValuationData,
              income_statement_data: data,
            });
          }
          setSubmitType(AssessmentResponseType.DRAFT);
        }}
      />
    );
  }
  if (currentScreen?.screen === BusinessValuationScreens.BALANCE_SHEET_DATA_1) {
    return (
      <BalanceSheetData1
        data={businessValuationData}
        setData={(data: any) => {
          setBusinessValuationData({
            ...businessValuationData,
            balance_sheet_data_1: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessValuationScreens.BALANCE_SHEET_DATA_2);
        }}
        backStep={() => {
          backNextClickHandler(BusinessValuationScreens.INCOME_STATEMENT_DATA);
        }}
        onSaveToDraftClick={(data: any) => {
          const currentScreen = businessValuationData?.saved_screen || 0;
          if (currentScreen < screenToNumberObject.balance_sheet_data_1) {
            setBusinessValuationData({
              ...businessValuationData,
              balance_sheet_data_1: data,
              saved_screen: screenToNumberObject.balance_sheet_data_1,
            });
          } else {
            setBusinessValuationData({
              ...businessValuationData,
              balance_sheet_data_1: data,
            });
          }
          setSubmitType(AssessmentResponseType.DRAFT);
        }}
      />
    );
  }
  if (currentScreen?.screen === BusinessValuationScreens.BALANCE_SHEET_DATA_2) {
    return (
      <BalanceSheetData2
        data={businessValuationData}
        setData={(data: any) => {
          setBusinessValuationData({
            ...businessValuationData,
            balance_sheet_data_2: data,
          });
        }}
        nextStep={() => {
          backNextClickHandler(BusinessValuationScreens.BALANCE_SHEET_DATA_3);
        }}
        backStep={() => {
          backNextClickHandler(BusinessValuationScreens.BALANCE_SHEET_DATA_1);
        }}
        onSaveToDraftClick={(data: any) => {
          const currentScreen = businessValuationData?.saved_screen || 0;
          if (currentScreen < screenToNumberObject.balance_sheet_data_2) {
            setBusinessValuationData({
              ...businessValuationData,
              balance_sheet_data_2: data,
              saved_screen: screenToNumberObject.balance_sheet_data_2,
            });
          } else {
            setBusinessValuationData({
              ...businessValuationData,
              balance_sheet_data_2: data,
            });
          }
          setSubmitType(AssessmentResponseType.DRAFT);
        }}
      />
    );
  }

  return (
    <BalanceSheetData3
      data={businessValuationData}
      setData={(data: any) => {
        setBusinessValuationData({
          ...businessValuationData,
          balance_sheet_data_3: data,
        });
      }}
      nextStep={() => {
        backNextClickHandler(BusinessValuationScreens.BALANCE_SHEET_DATA_3);
        setSubmitType(AssessmentResponseType.COMPLETE);
      }}
      backStep={() => {
        backNextClickHandler(BusinessValuationScreens.BALANCE_SHEET_DATA_2);
      }}
      onSaveToDraftClick={(data: any) => {
        const currentScreen = businessValuationData?.saved_screen || 0;
        if (currentScreen < screenToNumberObject.balance_sheet_data_3) {
          setBusinessValuationData({
            ...businessValuationData,
            balance_sheet_data_3: data,
            saved_screen: screenToNumberObject.balance_sheet_data_3,
          });
        } else {
          setBusinessValuationData({
            ...businessValuationData,
            balance_sheet_data_3: data,
          });
        }
        setSubmitType(AssessmentResponseType.DRAFT);
      }}
    />
  );
};
