import React from 'react';
import { FaAddressBook, FaEnvelope, FaPhoneAlt } from 'react-icons/fa';
import Button from 'shared-resources/components/Button/Button';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import { getInitials } from 'utils/helpers/Helpers';
import { User } from '../../models/entities/User';

interface Props {
  user?: User;
  onEdit: () => void;
}
const GeneralProfile: React.FC<Props> = (props) => {
  const { user, onEdit } = props;
  return (
    <div className='bg-white rounded-xl px-12.5 py-10 mt-7'>
      <div className='flex justify-between'>
        <div className='flex items-center space-x-16'>
          <div className='h-32 w-32 overflow-hidden rounded-full'>
            {user && (
              <div className='flex items-center font-medium w-full h-full text-3xl justify-center border-2 border-blue-01 rounded-full text-blue-01'>
                {getInitials(user)}
              </div>
            )}
          </div>
          <div className='flex flex-col space-y-1'>
            <h1 className='text-2xl font-semibold'>{user?.name}</h1>
            <h1 className='font-medium text-blue-01 capitalize'>
              {user?.type.split('_').join(' ')}
            </h1>
          </div>
        </div>
        <Button className='px-6 py-2 h-fit' type='button' onClick={onEdit}>
          Edit
        </Button>
      </div>
      <h1 className='mt-12 font-semibold'>Contact Info</h1>
      <hr className='h-0.5 bg-gray-400 w-full my-3' />
      <div className='grid grid-flow-row grid-rows-2 grid-cols-2 gap-6 max-w-[50rem]'>
        <div className='flex space-x-6 items-center'>
          <FaEnvelope className='min-w-4 h-4' />
          <h1 className='text-blue-01'>{user?.email}</h1>
        </div>
        {user?.phone && (
          <div className='flex flex-col'>
            <div className='flex space-x-6 items-center'>
              <FaPhoneAlt className='min-w-4 h-4' />
              <h1 className='text-blue-01'>{user.phone}</h1>
            </div>
          </div>
        )}
        {(user as BusinessOwner)?.street_address && (
          <div className='flex space-x-6 items-center'>
            <FaAddressBook className='min-w-4 h-4' />
            <h1 className='text-blue-01'>
              {(user as BusinessOwner)?.street_address}
            </h1>
          </div>
        )}
      </div>
    </div>
  );
};
export default GeneralProfile;
