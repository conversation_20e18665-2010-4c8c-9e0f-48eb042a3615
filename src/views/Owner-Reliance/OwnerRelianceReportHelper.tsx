import { headerOptions } from './OwnerRelianceConfig';

const getOptionWeight = (option: string) => {
  let weight = 0;
  if (option === headerOptions[2]) {
    weight = 4;
  } else if (option === headerOptions[3]) {
    weight = 3;
  } else if (option === headerOptions[4]) {
    weight = 2;
  } else if (option === headerOptions[5]) {
    weight = 1;
  }
  return weight;
};

export const calculateOwnerReliancePercentageScore = (response: any) => {
  const businessFunctionLength = response && Object.keys(response).length;
  const potentialWeight = businessFunctionLength * 4;
  let totalWeight = 0;

  // eslint-disable-next-line @typescript-eslint/no-unused-expressions
  response &&
    Object.values(response).forEach((key: any) => {
      if (key) totalWeight += getOptionWeight(key.selected_option);
    });
  const scorePercentage = (totalWeight / potentialWeight) * 100;
  const finalValue = parseFloat(scorePercentage.toFixed(2));

  return {
    scorePercentage: Number.isNaN(finalValue) ? 0 : finalValue,
    potentialWeight,
    totalWeight,
  };
};

export const calculateOwnerRelianceOverallScore = (response: any) => {
  let totalWeight = 0;
  let potentialWeight = 0;

  Object.keys(response).map((key: string) => {
    const data = calculateOwnerReliancePercentageScore(response[key]);
    totalWeight += data.totalWeight;
    potentialWeight += data.potentialWeight;
    return data;
  });

  const scorePercentage = (totalWeight / potentialWeight) * 100;
  const finalValue = parseFloat(scorePercentage.toFixed(2));
  return Number.isNaN(finalValue) ? 0 : finalValue;
};
