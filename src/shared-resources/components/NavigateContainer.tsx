import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import { localStorageService } from 'services/LocalStorageService';
import { getUserData } from 'store/selectors/user.selector';
import { getUserRouteType } from 'utils/helpers/Helpers';
import cx from 'classnames';
import { UserRouteType, UserType } from 'types/enum';

interface NavigateContainerProps {
  children: React.ReactNode;
  className?: string;
  defaultPrevRoute?: string;
  isEnableToolRoute?: boolean;
}
const NavigateContainer: React.FC<NavigateContainerProps> = (props) => {
  const { children, className, defaultPrevRoute, isEnableToolRoute } = props;
  const navigate = useNavigate();
  const user = useSelector(getUserData);
  const loggedInUserType = useMemo(
    () => localStorageService.getLoggedInUserType(),
    []
  );
  const params = useParams();
  const { id } = params;

  const handleBackButton = () => {
    navigate(
      isEnableToolRoute && loggedInUserType === UserType.ADVISOR
        ? `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${id}`
        : defaultPrevRoute ??
            `/${
              getUserRouteType(user?.type ?? loggedInUserType) ?? 'advisor'
            }/dashboard`
    );
  };
  return (
    <button
      onClick={handleBackButton}
      className={cx('flex h-fit justify-start hover:cursor-pointer', className)}
    >
      {children}
    </button>
  );
};

export default NavigateContainer;
