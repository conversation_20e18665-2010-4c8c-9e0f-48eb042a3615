import React, { useEffect, useState } from 'react';
import Button from 'shared-resources/components/Button/Button';
import { IoWarning } from 'react-icons/io5';
import Checkbox from 'shared-resources/components/CheckBox/Checkbox';
import { AwarenessTab } from 'types/business-owner.types';
import { convertToTitleCase } from 'utils/helpers/Helpers';

interface EnableToolModalProps {
  handleModalVisibility: (value: boolean) => void;
  enabled: boolean;
  tab: AwarenessTab;
  handleToggle: (value: boolean, send_email: boolean) => void;
  isEnabling: boolean;
  pingOwner: boolean;
}
const EnableToolModal: React.FC<EnableToolModalProps> = (props) => {
  const {
    handleModalVisibility,
    enabled,
    tab,
    handleToggle,
    isEnabling,
    pingOwner,
  } = props;
  const [sendEmail, setSendEmail] = useState(pingOwner);
  const [isEnabled, setIsEnabled] = useState(enabled);
  const handleCheckboxChange = () => {
    setSendEmail(!sendEmail);
  };

  useEffect(
    () => () => {
      setIsEnabled(enabled);
    },
    [enabled]
  );

  return (
    <div className='flex flex-col p-8'>
      <div className='flex items-start'>
        {isEnabled && <IoWarning className='min-w-7 min-h-7' color='red' />}
        <div className='flex flex-col ml-4'>
          {isEnabled && (
            <span className='font-medium'>
              Are you sure want to Disable {convertToTitleCase(tab?.name)} ?
            </span>
          )}
          {!isEnabled && (
            <Checkbox
              text={
                <span className='text-black-02'>
                  Keep checked to send your client an email invite with a secure
                  link to begin their {convertToTitleCase(tab?.name)}
                </span>
              }
              value={sendEmail}
              onChange={handleCheckboxChange}
            />
          )}
        </div>
      </div>
      <div className='flex justify-end gap-6 mt-4'>
        <Button
          className='px-6 py-2 w-[6.5rem]'
          disabled={isEnabling}
          isSubmitting={isEnabling}
          onClick={() => {
            handleToggle(!isEnabled, isEnabled ? false : sendEmail);
          }}
        >
          {isEnabled ? 'Disable' : 'Enable'}
        </Button>
        <Button
          className='px-6 py-2'
          theme='secondary'
          type='button'
          disabled={isEnabling}
          onClick={() => {
            handleModalVisibility(false);
          }}
        >
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default React.memo(EnableToolModal);
