import { toast } from 'react-toastify';
import { all, call, put, select, takeLatest } from 'redux-saga/effects';
import { assessmentReportService } from 'services/api-services/AssessmentReportService';
import { AssessmentReportActionType } from 'store/actions/actions.constants';
import {
  addAssessmentReport,
  addAssessmentReportError,
  addAssessmentReportLoading,
} from 'store/reducers/assessment-report.reducer';
import { updateBusinessOwnerAssessmentData } from 'store/reducers/business-owner.reducer';
import { AssessmentToolStatus, AssessmentTools } from 'types/enum';
import { SagaPayloadType } from '../../types/SagaPayload.type';
import { ReportPayload } from '../actions/assessment-report.action';

interface AssessmentReportSagaPayloadType extends SagaPayloadType {
  payload: ReportPayload;
}

interface SendReportSagaPayloadType extends SagaPayloadType {
  payload: {
    businessOwnerId: number;
    assessmentTool: AssessmentTools;
  };
}

function* fetchAssessmentReport(action: AssessmentReportSagaPayloadType): any {
  const { onError, ...data } = action.payload;
  try {
    yield put(addAssessmentReportLoading({ loading: true }));
    const response = yield call(assessmentReportService.fetchReport, data);
    yield put(addAssessmentReport(response));
    yield put(addAssessmentReportLoading({ loading: false }));
  } catch (e: any) {
    yield put(addAssessmentReportError({ message: e.response.data.message }));
    if (onError) {
      onError();
    }
  }
}

function* fetchAssessmentReportByBusinessOwner(action: any): any {
  const { onError, tool } = action.payload;
  try {
    yield put(addAssessmentReportLoading({ loading: true }));
    const response = yield call(
      assessmentReportService.fetchReportByBusinessOwner,
      tool
    );
    yield put(addAssessmentReport(response));
    yield put(addAssessmentReportLoading({ loading: false }));
  } catch (e: any) {
    yield put(addAssessmentReportError({ message: e.response.data.message }));
    toast.error(e.response.data.message, {
      position: 'top-right',
    });
    if (onError) {
      onError();
    }
  }
}

function* sendAssessmentReport(action: SendReportSagaPayloadType): any {
  const { assessmentTool, businessOwnerId } = action.payload;
  const assessmentDataMapping = yield select(
    (state) => state.businessOwner.assessmentDataMapping
  );
  const ownerAssessmentData = assessmentDataMapping[businessOwnerId];
  try {
    yield put(addAssessmentReportLoading({ loading: true }));
    const response = yield call(
      assessmentReportService.sendReport,
      businessOwnerId,
      assessmentTool
    );
    toast.success(response.message);
    yield put(
      updateBusinessOwnerAssessmentData({
        tool: assessmentTool,
        status: AssessmentToolStatus.ENABLE,
        id: businessOwnerId,
        assessmentData: ownerAssessmentData,
        isReportSentToOwner: true,
      })
    );
    yield put(addAssessmentReportLoading({ loading: false }));
  } catch (e: any) {
    yield put(addAssessmentReportError({ message: e.response.data.message }));
  }
}
export function* assessmentReportSagaWatcher() {
  yield all([
    takeLatest(AssessmentReportActionType.FETCH_REPORT, fetchAssessmentReport),
    takeLatest(AssessmentReportActionType.SEND_REPORT, sendAssessmentReport),
    takeLatest(
      AssessmentReportActionType.FETCH_REPORT_BY_BUSINESS_OWNER,
      fetchAssessmentReportByBusinessOwner
    ),
  ]);
}
