import classNames from 'classnames';
import React, { memo, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { getUserData } from 'store/selectors/user.selector';
import { UserType } from 'types/enum';
import { enumTextToOptionsText } from 'utils/helpers/Helpers';
import { formatCurrency } from 'views/FinancialGapAnalysis/ExpenseCalculator/ExpenseCalculatorConfig';
import {
  InsuranceTypeheaderOptions,
  Screen8Keys,
  Screen8TableResponseType,
  findEmptyKeys,
  getQuestionBykey,
  screen8TableResponse,
} from '../AssetProtectionConfig';

interface Props {
  data: any;
  handleNextClick: (values: any) => void;
  handlebackClick: (values: any) => void;
  handleSaveAsDraftClick: (values: any) => void;
}
const Screen8: React.FC<Props> = ({
  data,
  handleNextClick,
  handleSaveAsDraftClick,
  handlebackClick,
}) => {
  const [values, setValues] = useState(screen8TableResponse);
  const [onClickError, setOnclickError] = useState<boolean>(false);
  const user = useSelector(getUserData);

  useEffect(() => {
    if (data?.screen8) {
      setValues(data.screen8);
    }
  }, [data]);

  const isSubmitting = useSelector(getAssessmentToolLoading);

  const handleNextButton = () => {
    setOnclickError(true);
    const errors = findEmptyKeys(values as any);
    if (errors?.length) {
      let error = '';
      errors.forEach((key) => {
        error += `${enumTextToOptionsText(key).slice(0, -1)}, `;
      });
      error = error.slice(0, -2);
      toast.error(`${error} fields are required.`);
    }
    if (!errors.length) {
      setOnclickError(false);
      handleNextClick(values);
    }
  };

  const getBusinessOwnerTable = () => (
    <div>
      <div className='scrollbar h-[calc(100vh-20rem)] overflow-auto pr-1 mt-3 pb-2'>
        <div
          className={`bg-blue-01 z-10 tracking-[0.07rem] sticky top-0 grid grid-cols-3 text-white
            rounded-t-md  text-[0.9rem]`}
        >
          {InsuranceTypeheaderOptions.slice(0, 3).map((h, index) => (
            <div
              key={h}
              className={`px-5 flex items-center border-r border-gray-02 justify-center py-3 h-full ${
                index === 0 && 'rounded-tl-md'
              } ${index === 2 && ' border-r-0 rounded-t-md'} `}
            >
              <span className='text-center'>{h}</span>
            </div>
          ))}
        </div>
        <div>
          {Object.keys(values).map((key) => (
            <div
              key={key}
              className={classNames('grid grid-cols-3  text-[0.9rem]', {
                'bg-red-100':
                  onClickError && findEmptyKeys(values as any).includes(key),
              })}
            >
              <div
                className={`px-5
                 flex items-center border-b border-l py-1 border-r border-gray-02`}
              >
                <span className='text-xs xl:text-sm '>{key}</span>
              </div>

              <div className='flex py-2 justify-center border-b border-r border-gray-02 '>
                <button
                  className={`py-1 px-5 rounded-l-xl border-l-2 border-y-2 border-gray-300 ${
                    values[key as keyof Screen8TableResponseType].value ===
                      true && 'bg-blue-01'
                  }`}
                  onClick={() => {
                    setValues({
                      ...values,
                      [key]: {
                        ...values[key as keyof Screen8TableResponseType],
                        value: true,
                      },
                    });
                  }}
                >
                  Yes
                </button>
                <button
                  className={`py-1 px-5 rounded-r-xl border-2 border-gray-300 ${
                    values[key as keyof Screen8TableResponseType].value ===
                      false && 'bg-blue-01'
                  } `}
                  onClick={() => {
                    setValues({
                      ...values,
                      [key]: {
                        ...values[key as keyof Screen8TableResponseType],
                        value: false,
                      },
                    });
                  }}
                >
                  No
                </button>
              </div>

              <div
                className={`px-3 2xl:px-5 
                 flex items-center border-b py-1 border-r border-gray-02`}
              >
                <div className='flex gap-2 items-center w-full'>
                  <span>$</span>
                  <input
                    className='text-sm h-full border-none outline-none truncate bg-transparent'
                    value={formatCurrency(
                      values[key as keyof Screen8TableResponseType]
                        .coverage_amount
                    ).replace('$', '')}
                    onChange={(event) => {
                      const value = +event.target.value
                        .replaceAll(',', '')
                        .replace('$', '');
                      const isValidNumber = !Number.isNaN(value) && value >= 0;
                      if (isValidNumber)
                        setValues({
                          ...values,
                          [key]: {
                            ...values[key as keyof Screen8TableResponseType],
                            coverage_amount: value,
                          },
                        });
                    }}
                    min={0}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const getAdvisorTable = () => (
    <div>
      <div className='scrollbar h-[calc(100vh-20rem)] overflow-auto pr-1 mt-3 pb-2'>
        <div
          className={`bg-blue-01 z-10 tracking-[0.07rem] sticky top-0 grid grid-cols-4 text-white
            rounded-t-md  text-[0.9rem]`}
        >
          {InsuranceTypeheaderOptions.map((h, index) => (
            <div
              key={h}
              className={`px-5 flex items-center border-r border-gray-02 justify-center py-1 h-full ${
                index === 0 && 'rounded-tl-md'
              } ${index === 3 && ' border-r-0 rounded-t-md'} `}
            >
              <span className='text-center'>{h}</span>
            </div>
          ))}
        </div>
        <div>
          {Object.keys(values).map((key) => (
            <div
              key={key}
              className={classNames('grid grid-cols-4  text-[0.9rem] ', {
                'bg-red-100':
                  onClickError && findEmptyKeys(values as any).includes(key),
              })}
            >
              <div
                className={`px-5
                 flex items-center border-b border-l py-1 border-r border-gray-02`}
              >
                <span className='text-xs xl:text-sm '>{key}</span>
              </div>

              <div className='flex py-2 justify-center border-b border-r border-gray-02 '>
                <button
                  className={`py-1 px-5 rounded-l-xl border-l-2 border-y-2 border-gray-300 ${
                    values[key as keyof Screen8TableResponseType].value ===
                      true && 'bg-blue-01'
                  }`}
                  onClick={() => {
                    setValues({
                      ...values,
                      [key]: {
                        ...values[key as keyof Screen8TableResponseType],
                        value: true,
                      },
                    });
                  }}
                >
                  Yes
                </button>
                <button
                  className={`py-1 px-5 rounded-r-xl border-2 border-gray-300 ${
                    values[key as keyof Screen8TableResponseType].value ===
                      false && 'bg-blue-01'
                  } `}
                  onClick={() => {
                    setValues({
                      ...values,
                      [key]: {
                        ...values[key as keyof Screen8TableResponseType],
                        value: false,
                      },
                    });
                  }}
                >
                  No
                </button>
              </div>

              <div
                className={`px-3 2xl:px-5 
                 flex items-center border-b py-1 border-r border-gray-02`}
              >
                <div className='flex gap-2 items-center w-full'>
                  <span>$</span>
                  <input
                    className='text-sm h-full border-none outline-none truncate bg-transparent'
                    value={formatCurrency(
                      values[key as keyof Screen8TableResponseType]
                        .coverage_amount
                    ).replace('$', '')}
                    onChange={(event) => {
                      const value = +event.target.value
                        .replaceAll(',', '')
                        .replace('$', '');
                      const isValidNumber = !Number.isNaN(value) && value >= 0;
                      if (isValidNumber)
                        setValues({
                          ...values,
                          [key]: {
                            ...values[key as keyof Screen8TableResponseType],
                            coverage_amount: value,
                          },
                        });
                    }}
                    min={0}
                  />
                </div>
              </div>

              <div
                className={`px-3 2xl:px-5 
                 flex items-center border-b py-1 border-r border-gray-02`}
              >
                <div className='flex gap-2 items-center w-full'>
                  <span>$</span>
                  <input
                    className='text-sm h-full border-none outline-none truncate bg-transparent'
                    value={formatCurrency(
                      values[key as keyof Screen8TableResponseType]
                        .recommended_coverage
                    ).replace('$', '')}
                    onChange={(event) => {
                      const value = +event.target.value
                        .replaceAll(',', '')
                        .replace('$', '');
                      const isValidNumber = !Number.isNaN(value) && value >= 0;
                      if (isValidNumber)
                        setValues({
                          ...values,
                          [key]: {
                            ...values[key as keyof Screen8TableResponseType],
                            recommended_coverage: value,
                          },
                        });
                    }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // This the table screen
  return (
    <div className='px-10 py-5 font-medium'>
      <h2>{getQuestionBykey(Screen8Keys.INSURANCE_TYPE)}</h2>
      {user?.type === UserType.BUSINESS_OWNER
        ? getBusinessOwnerTable()
        : getAdvisorTable()}

      <div className='pr-5'>
        <BackNextComponent
          backStep={() => handlebackClick(values)}
          buttonType='submit'
          nextStep={() => handleNextButton()}
          isLoading={isSubmitting}
          isNextDisable={false}
          onSaveToDraftClick={() => {
            handleSaveAsDraftClick(values);
            setOnclickError(false);
          }}
        />
      </div>
    </div>
  );
};

export default memo(Screen8);
