import { <PERSON><PERSON><PERSON>y, Form, Formik, FormikErrors } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { BusinessContinuityScreens } from 'types/enum';
import {
  getKey,
  isRequiredFieldDependingOnValidationSchema,
} from 'utils/helpers/Helpers';
import * as yup from 'yup';
import Checkbox from 'shared-resources/components/CheckBox/Checkbox';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import ContinuityQuestionHeader from './shared-components/ContinuityQuestionHeader';
import ContuinuityRowView from './shared-components/ContuinuityRowView';

interface SoleOwnersProps {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  onSaveToDraftClick: (data: any) => void;
  data: any;
}
const SoleOwners: React.FC<SoleOwnersProps> = (props) => {
  const { nextStep, backStep, setData, onSaveToDraftClick, data } = props;

  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup.object().shape({
    partners: yup.array().of(
      yup.object({
        partner_name: yup.string().required('Partner Name is required'),
        phone: yup
          .string()
          .matches(
            /^(\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/,
            'Phone number not from US region'
          )
          .required('Phone is required'),
        active_in_business: yup
          .string()
          .required('Active In Business is required'),
        email: yup
          .string()
          .required('Email is required')
          .email('This must be a valid e-mail'),
        position_in_business: yup
          .string()
          .required('Position in Business is reuqired'),
      })
    ),
    is_sole_owner: yup.boolean().required(''),
  });

  const initialValues = {
    partners: data?.sole_owners?.partners || [
      {
        partner_name: '',
        phone: '',
        active_in_business: '',
        email: '',
        position_in_business: '',
        saved: false,
      },
    ],
    is_sole_owner: data?.sole_owners?.is_sole_owner,
  };

  return (
    <div className='flex flex-col rounded-lg w-full h-full'>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          setData({
            partners: values?.partners,
            is_sole_owner: values?.is_sole_owner,
          });
          nextStep();
        }}
        enableReinitialize
        validateOnMount
      >
        {({ values, setFieldValue, isValid, errors }) => (
          <Form className='relative px-5 py-3 w-full h-full flex flex-col gap-4 bg-white'>
            <FieldArray name='partners'>
              {({ push, remove }) => (
                <div className='flex flex-col  h-full w-full'>
                  <ContinuityQuestionHeader
                    fieldKey='partners'
                    push={push}
                    pushData={{
                      partner_name: '',
                      phone: '',
                      active_in_business: '',
                      email: '',
                      position_in_business: '',
                      saved: false,
                    }}
                  >
                    <div className='flex flex-col '>
                      <h2 className='font-medium'>
                        Are you sole owner of your business or do you have
                        partners ?
                      </h2>
                      <div className='flex gap-10 mt-4'>
                        <Checkbox
                          className={`!h-4 !mt-0 items-center ${
                            values.is_sole_owner === false
                              ? 'pointer-events-none'
                              : ''
                          } `}
                          text={<span className='font-medium'>Partners</span>}
                          onChange={() => {
                            setFieldValue('is_sole_owner', false);
                            setFieldValue('partners', [
                              {
                                partner_name: '',
                                phone: '',
                                active_in_business: '',
                                email: '',
                                position_in_business: '',
                                saved: false,
                              },
                            ]);
                          }}
                          value={values?.is_sole_owner === false}
                        />
                        <Checkbox
                          className={`!h-4 !mt-0 items-center ${
                            values?.is_sole_owner ? 'pointer-events-none' : ''
                          } `}
                          key='sole_owner'
                          text={<span className='font-medium'>Sole Owner</span>}
                          onChange={() => {
                            setFieldValue('is_sole_owner', true);
                            setFieldValue('partners', []);
                          }}
                          value={values?.is_sole_owner}
                        />
                      </div>
                    </div>
                  </ContinuityQuestionHeader>
                  {values.is_sole_owner === false && (
                    <div className='flex flex-col max-h-[calc(100vh-23rem)] scrollbar pr-1 overflow-auto'>
                      <ContuinuityRowView
                        fieldKey='partners'
                        formValues={values}
                        remove={remove}
                        initialValues={{
                          partner_name: '',
                          phone: '',
                          active_in_business: '',
                          email: '',
                          position_in_business: '',
                          saved: false,
                        }}
                      />
                      <div className=' flex flex-col '>
                        {!!values.partners?.length &&
                          values.partners?.map(
                            (formValues: any, index: number) =>
                              !formValues.saved && (
                                <div
                                  key={`${getKey(index)}_form`}
                                  className='flex flex-col border border-gray-02 p-4 mt-1'
                                >
                                  <div className='grid grid-cols-2 gap-y-2 gap-x-8 mt-5  overflow-auto scrollbar text-[1rem]'>
                                    <FormikInput
                                      name={`partners.${getKey(
                                        index
                                      )}.partner_name`}
                                      key={`partners.${getKey(
                                        index
                                      )}.partner_name`}
                                      label='Partner Name'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        'partner_name'
                                      )}
                                    />
                                    <FormikInput
                                      name={`partners.${getKey(index)}.phone`}
                                      key={`partners.${getKey(index)}.phone`}
                                      label='Phone'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        'phone'
                                      )}
                                    />
                                    <FormikInput
                                      name={`partners.${getKey(
                                        index
                                      )}.active_in_business`}
                                      key={`partners.${getKey(
                                        index
                                      )}.active_in_business`}
                                      label='Active In Business '
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        'active_in_business'
                                      )}
                                    />

                                    <FormikInput
                                      name={`partners.${getKey(
                                        index
                                      )}.position_in_business`}
                                      key={`partners.${getKey(
                                        index
                                      )}.position_in_business`}
                                      label='Position In Business'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        'position_in_business'
                                      )}
                                    />
                                    <FormikInput
                                      name={`partners.${getKey(index)}.email`}
                                      key={`partners.${getKey(index)}.email`}
                                      label='Email'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        'email'
                                      )}
                                    />
                                  </div>
                                  <div className='flex justify-end'>
                                    <Button
                                      onClick={() => {
                                        setFieldValue(
                                          `partners.${getKey(index)}.saved`,
                                          true
                                        );
                                      }}
                                      disabled={
                                        !!(
                                          errors?.partners as FormikErrors<any>[]
                                        )?.[index]
                                      }
                                      className='px-4 py-2'
                                    >
                                      Save
                                    </Button>
                                  </div>
                                </div>
                              )
                          )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </FieldArray>
            <BackNextComponent
              backStep={() => {
                backStep();
                setData(values);
              }}
              fieldKey={BusinessContinuityScreens.SOLE_OWNERS}
              formValues={values}
              isLoading={isLoading}
              isNextDisable={!isValid}
              onSaveToDraftClick={onSaveToDraftClick}
              buttonType='submit'
            />
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default SoleOwners;
