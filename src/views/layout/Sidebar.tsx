import { BusinessOwner } from 'models/entities/BusinessOwner';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router';
import {
  advisorSidebarRoutes,
  businessOwnerSidebarRoutes,
  adminSidebarRoutes,
  enterpriseAdminSidebarRoutes
} from 'routes/sidebarRoutes';
import { getUserData } from 'store/selectors/user.selector';
import {
  AssessmentToolStatus,
  AssessmentTools,
  RouteKey,
  UserRouteType,
  UserType,
} from 'types/enum';
import { SidebarRoutesConfigType } from 'types/routes/routes.type';
import {
  getOrderedAllStageAssessmentData,
  paymentNeededTabs,
} from 'utils/helpers/Helpers';
import { getBusinessOwnerLearnMoreLoading } from 'store/selectors/business-owner.selector';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import { MdArrowOutward } from 'react-icons/md';
import TooltipComponent from 'shared-resources/components/Tooltip/TooltipComponent';
import { businessOwnerLearnMore } from 'store/actions/business-owner.action';
import SidebarItem from '../../shared-resources/components/SidebarItem';

const Sidebar: React.FC = () => {
  const user = useSelector(getUserData);
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();

  const sidebarRoutesMap = {
    [UserType.ADVISOR]: advisorSidebarRoutes,
    [UserType.BUSINESS_OWNER]: businessOwnerSidebarRoutes,
    [UserType.ADMIN]: adminSidebarRoutes,
    [UserType.ENT_ADMIN]: enterpriseAdminSidebarRoutes,
  };
  
  const sidebarRoutes = user ? sidebarRoutesMap[user.type] || [] : [];

  const [updatedSidebarRoutes, setUpdatedSidebarRoutes] =
    useState<SidebarRoutesConfigType[]>(sidebarRoutes);

  const sidebarRoutesWithEnabledData = sidebarRoutes.map(
    (route: SidebarRoutesConfigType) => {
      if (
        user &&
        (route.key.includes(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}`
        ) ||
          (route.key.includes(
            `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}`
          ) &&
            !route.key.includes(`${RouteKey.BUSINESS_FINANCIAL_ANALYSIS}`)))
      ) {
        const tool = route.id?.replace('-', '_') as AssessmentTools;
        const orderedAwarenessData = getOrderedAllStageAssessmentData(
          user as BusinessOwner
        );

        const isEnabledTool =
          orderedAwarenessData.find((item) => item?.toolName === tool)?.toolData
            ?.status === AssessmentToolStatus.ENABLE;

        return {
          ...route,
          enabled: isEnabledTool,
        };
      }
      if (
        user &&
        route?.key.includes(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}/${RouteKey.BUSINESS_FINANCIAL_ANALYSIS}`
        )
      ) {
        const isEnabledTool = Object.keys(
          (user as BusinessOwner)?.assessment_data?.awareness ?? {}
        ).includes(AssessmentTools.BUSINESS_VALUATION);

        return { ...route, enabled: isEnabledTool };
      }
      return { ...route, enabled: true };
    }
  );

  useEffect(() => {
    let routes = sidebarRoutesWithEnabledData;

    if (user && user?.type === UserType.BUSINESS_OWNER) {
      const paymentIncompletedTabs = paymentNeededTabs.filter(
        (tab) => !(user as BusinessOwner)?.payment_details?.[tab]
      );
      routes = routes.filter(
        (route) => !paymentIncompletedTabs.includes(route.id as AssessmentTools)
      );
    }
    setUpdatedSidebarRoutes(routes);
  }, [user]);

  useEffect(() => {
    if (user) {
      if (
        location.pathname.includes(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.AWARENESS}`
        ) ||
        location.pathname.includes(
          `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.PLAN_DEVELOPMENT}`
        )
      ) {
        const isCurrentRouteAccessible = updatedSidebarRoutes?.find(
          (route) =>
            (location.pathname === route.key ||
              location.pathname ===
                `/${UserRouteType.BUSINESS_OWNER}/${
                  RouteKey.AWARENESS
                }/${route.id?.replace(/_/g, '-')}` ||
              location.pathname ===
                `/${UserRouteType.BUSINESS_OWNER}/${
                  RouteKey.PLAN_DEVELOPMENT
                }/${route.id?.replace(/_/g, '-')}`) &&
            route.enabled
        );

        if (!isCurrentRouteAccessible) {
          navigate(`/${UserRouteType.BUSINESS_OWNER}/${RouteKey.DASHBOARD}`);
        }
      }
    }
  }, [location.pathname, user, updatedSidebarRoutes]);

  const isSubscriptionPage =
    location.pathname === `/${UserRouteType.ADVISOR}/${RouteKey.SUBSCRIPTION}`;
  const learnMoreLoading = useSelector(getBusinessOwnerLearnMoreLoading);

  if (isSubscriptionPage) {
    return null;
  }

  const learnMoreSuccess = (route: SidebarRoutesConfigType) => {
    navigate(
      `/${UserRouteType.BUSINESS_OWNER}/${RouteKey.LEARN_MORE}?tool=${route.tooltipRouteTool}`
    );
  };

  const handleLearnMore = (route: SidebarRoutesConfigType) => {
    dispatch(
      businessOwnerLearnMore({
        tool: route.id as AssessmentTools,
        learnMoreSuccess: () => {
          learnMoreSuccess(route);
        },
      })
    );
  };

  return (
    <aside>
      {updatedSidebarRoutes.map(
        (route) =>
          !route.enabled &&
          route.tooltipText && (
            <TooltipComponent
              anchorSelect={`.${route.key.replaceAll('/', '_')}-item`}
              place='right-start'
              offset={0}
              className='relative z-30'
              key={route.key}
              clickable
            >
              {route.tooltipText}
              {route.key && (
                <button
                  onClick={() => handleLearnMore(route)}
                  className='text-xs text-blue-01 mt-2 flex items-center justify-start'
                >
                  {learnMoreLoading ? (
                    <Spinner customClassName='w-4 h-4' />
                  ) : (
                    <>
                      <span>Learn More</span>
                      <MdArrowOutward className='ml-1' size={14} />
                    </>
                  )}
                </button>
              )}
            </TooltipComponent>
          )
      )}
      <div className='fixed top-[5.625rem] z-20 p-5 h-full overflow-auto scrollbar max-h-[calc(100%-5.625rem)] no-scrollbar left-0 bg-white w-[11.625rem]'>
        {updatedSidebarRoutes.map((route) => (
          <SidebarItem
            key={route.key}
            sidebarRoute={route}
            disabled={!route.enabled}
          />
        ))}
      </div>
    </aside>
  );
};

export default Sidebar;
