import { Entity } from 'models/entity';
import { FollowUpType } from 'types/enum';

export interface FollowUpHistory extends Entity {
  assessmentRecordId: number;
  metadata: any;
  type: FollowUpType;
  createdAt: string;
}

export interface FollowUpSchedule extends Entity {
  assessmentRecordId: number;
  interval: number;
  isActive: boolean;
  followUps: number;
  nextFollowUpDate: string;
  sentFollowUps: number;
}

export interface FollowUpHistoryResponse {
  follow_up_record: FollowUpHistory[];
  follow_up_schedule: FollowUpSchedule;
}
