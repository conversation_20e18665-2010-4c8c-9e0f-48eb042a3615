import { createSelector } from 'reselect';
import { AppState } from '../reducers';

const toolCommentStore = (state: AppState) => state.toolComment;

export const getToolComments = createSelector(
  [toolCommentStore],
  (toolCommentState) => toolCommentState.comments
);

export const getToolCommentsLoading = createSelector(
  [toolCommentStore],
  (toolCommentState) => toolCommentState.loading
);

export const getToolCommentsError = createSelector(
  [toolCommentStore],
  (toolCommentState) => toolCommentState.error
);
