/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */
import React, { useEffect, useState } from 'react';

import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';

import { toast } from 'react-toastify';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import {
  fetchBusinessOwnerAssessment,
  fetchOwnAssessment,
  updateBusinessOwnerAssessment,
  updateOwnAssessment,
} from 'store/actions/assessment-tool.action';
import { resetAssessmentData } from 'store/reducers/assessment-tool.reducer';
import {
  getAssessmentToolLoading,
  getAssessmentToolReEvaluate,
  getAssessmentToolResponse,
  getAssessmentToolStatus,
  getBusinessOwnerProperties,
} from 'store/selectors/assessment-tool.selector';
import { getUserData } from 'store/selectors/user.selector';
import {
  AssessmentResponseType,
  AssessmentToolProgressStatus,
  AssessmentTools,
  RouteKey,
  UserType,
  ValueEnhancementTabs,
} from 'types/enum';
import {
  enumTextToOptionsText,
  getUserRouteType,
  thankYouPageContent,
} from 'utils/helpers/Helpers';
import ThankYouPage from 'views/layout/ThankYouPage';
import WithComments from 'shared-resources/components/ToolComments/WithComments';
import GetStarted from './GetStarted';
import TableValueComponent from './TableValueComponent';
import {
  handleBackTabSwitch,
  handleNextTabSwitch,
  numberToTabObject,
  tabToNumberObject,
  valueEnhancementResponse,
} from './ValueEnhancementConfig';

const ValueEnhancement: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const dispatch = useDispatch();
  const { id } = useParams();
  const isLoading = useSelector(getAssessmentToolLoading);
  const [initialError, setInitialError] = useState<boolean>(false);
  const [submitType, setSubmitType] = useState<AssessmentResponseType | null>(
    null
  );
  const assessmentReEvaluate = useSelector(getAssessmentToolReEvaluate);
  const loggedInUserData = useSelector(getUserData);
  const assessmentToolStatus = useSelector(getAssessmentToolStatus);
  const savedResponse: any = useSelector(getAssessmentToolResponse);
  const [response, setResponse] = useState(valueEnhancementResponse);
  const navigate = useNavigate();
  const businessOwnerProperties = useSelector(getBusinessOwnerProperties) as {
    first_name: string;
    last_name: string;
    business_name: string;
  };

  const [currentTab, setCurrentTab] = useState<
    | {
        tab: ValueEnhancementTabs;
      }
    | undefined
  >();

  useEffect(() => {
    const tab = new URLSearchParams(currentTab as Record<string, string>);
    setSearchParams(tab);
  }, [currentTab, setSearchParams, searchParams]);

  useEffect(() => {
    if (
      Object.values(savedResponse ?? {})?.length &&
      assessmentToolStatus !== AssessmentToolProgressStatus.COMPLETED
    ) {
      setResponse(savedResponse);
    } else {
      setResponse(valueEnhancementResponse);
    }
  }, [savedResponse, assessmentToolStatus]);

  useEffect(() => {
    if (
      assessmentToolStatus !== AssessmentToolProgressStatus.COMPLETED &&
      savedResponse?.saved_tab
    ) {
      setCurrentTab({ tab: numberToTabObject[savedResponse?.saved_tab] });
    } else {
      setCurrentTab({ tab: ValueEnhancementTabs.GET_STARTED });
    }
  }, [savedResponse]);

  function findEmptyKeys(
    object:
      | {
          [key: string]: {
            selected_option: string;
            comment: string;
          };
        }[]
  ) {
    const emptyObjectIDs = [];

    for (const key in object) {
      if (!object[key].selected_option) {
        emptyObjectIDs.push(key);
      }
    }

    return emptyObjectIDs;
  }
  useEffect(() => {
    if (
      assessmentToolStatus === AssessmentToolProgressStatus.COMPLETED &&
      assessmentReEvaluate &&
      !isLoading
    ) {
      dispatch(resetAssessmentData());
    }
  }, [assessmentReEvaluate, loggedInUserData, assessmentToolStatus]);

  const handleBackButton = () => {
    handleBackTabSwitch(currentTab, setCurrentTab);
  };
  const handleFormSubmit = (saveAsDraft: boolean, savedTab?: number) => {
    let updatedResponse = response;
    if (savedTab) {
      updatedResponse = {
        ...response,
        saved_tab: savedTab,
      };
    }
    if (!saveAsDraft && loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      updatedResponse = {
        ...updatedResponse,
        saved_tab: tabToNumberObject[ValueEnhancementTabs.FINANCIAL],
      };
    }
    if (loggedInUserData?.type === UserType.ADVISOR && id) {
      dispatch(
        updateBusinessOwnerAssessment({
          businessOwnerId: +id!,
          tool: AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES,
          assessment_response: updatedResponse,
          submit_type: saveAsDraft
            ? AssessmentResponseType.DRAFT
            : AssessmentResponseType.COMPLETE,
        })
      );
    } else {
      dispatch(
        updateOwnAssessment({
          tool: AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES,
          assessment_response: updatedResponse,
          submit_type: saveAsDraft
            ? AssessmentResponseType.DRAFT
            : AssessmentResponseType.SUBMIT,
          onSuccess: () =>
            !saveAsDraft &&
            navigate(`${UserType.BUSINESS_OWNER}/${RouteKey.DASHBOARD}`),
        })
      );
    }
  };

  const handleNextButton = () => {
    setInitialError(true);
    const errors = findEmptyKeys(response[currentTab?.tab as string]);

    if (errors?.length) {
      let error = '';
      errors.forEach((key: any) => {
        error += `${enumTextToOptionsText(key).slice(0, -1)}, `;
      });
      error = error.slice(0, -2);
      toast.error(`${error} fields are required.`);
    } else if (currentTab?.tab === ValueEnhancementTabs.FINANCIAL) {
      handleFormSubmit(false);
      setSubmitType(AssessmentResponseType.COMPLETE);
    } else {
      handleNextTabSwitch(currentTab, setCurrentTab);
    }
    if (!errors.length) {
      setInitialError(false);
    }
  };

  const handleFetchAssessmentError = () => {
    navigate(
      `/${getUserRouteType(loggedInUserData?.type as UserType)}/${
        RouteKey.DASHBOARD
      }`
    );
  };
  useEffect(() => {
    if (loggedInUserData?.type === UserType.ADVISOR && id) {
      dispatch(
        fetchBusinessOwnerAssessment({
          tool: AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES,
          businessOwnerId: +id!,
          onError: handleFetchAssessmentError,
        })
      );
    } else if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchOwnAssessment({
          tool: AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES,
          onError: handleFetchAssessmentError,
        })
      );
    }
  }, []);
  const handleSaveAsDraft = () => {
    const savedTab = response?.saved_tab || 0;
    if (currentTab?.tab && savedTab < tabToNumberObject[currentTab.tab]) {
      handleFormSubmit(true, tabToNumberObject[currentTab.tab]);
    } else {
      handleFormSubmit(true);
    }
    setSubmitType(AssessmentResponseType.DRAFT);
  };
  if (assessmentToolStatus === AssessmentToolProgressStatus.COMPLETED) {
    return (
      <ThankYouPage
        pageContent={thankYouPageContent(
          'Value Enhancement Opportunities',
          loggedInUserData?.type || 'business_owner',
          `${businessOwnerProperties?.first_name ?? ''} ${
            businessOwnerProperties?.last_name ?? ''
          }`
        )}
        loggedInUserData={loggedInUserData}
        isPasswordSet={false}
      />
    );
  }
  if (isLoading && !submitType) {
    return <Spinner />;
  }

  return (
    <>
      <h1 className='font-bold text-2xl mb-3'>
        Value Enhancement Opportunities
      </h1>
      <WithComments tool={AssessmentTools.VALUE_ENHANCEMENT_OPPORTUNITIES}>
        <div className='flex flex-col justify-between bg-white h-[calc(100vh-13rem)] px-4 py-5 w-full'>
          {currentTab?.tab === ValueEnhancementTabs.GET_STARTED && (
            <GetStarted
              onGetStartedClick={() => {
                setCurrentTab({ tab: ValueEnhancementTabs.GROWTH });
                setInitialError(false);
              }}
            />
          )}
          {currentTab &&
            currentTab?.tab !== ValueEnhancementTabs.GET_STARTED && (
              <TableValueComponent
                currentTab={currentTab?.tab}
                response={response}
                setResponse={setResponse}
                emptyKeys={
                  initialError
                    ? findEmptyKeys(response[currentTab?.tab as string])
                    : []
                }
              />
            )}
          {currentTab?.tab !== ValueEnhancementTabs.GET_STARTED && (
            <div className='pr-4'>
              <BackNextComponent
                backStep={handleBackButton}
                buttonType='submit'
                nextStep={handleNextButton}
                buttonText={
                  currentTab?.tab === ValueEnhancementTabs.FINANCIAL
                    ? (loggedInUserData?.type === UserType.BUSINESS_OWNER
                      ? 'Submit'
                      : 'Complete')
                    : 'Next'
                }
                isLoading={isLoading}
                isNextDisable={false}
                onSaveToDraftClick={handleSaveAsDraft}
              />
            </div>
          )}
        </div>
      </WithComments>
    </>
  );
};

export default ValueEnhancement;
