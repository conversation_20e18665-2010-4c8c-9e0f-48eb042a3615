import React, { FC, memo, useEffect, useState } from 'react';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import { AssessmentTools, InviteStatus } from 'types/enum';
import { useDispatch, useSelector } from 'react-redux';
import {
  advisorInviteLoadingSelector,
  advisorInvitesSelector,
} from 'store/selectors/advisor-invite.selector';
import { useParams } from 'react-router';
import {
  cancelOrRevokeAdvisorInviteAction,
  getAdvisorInvites,
  inviteSecondaryAdvisor,
} from 'store/actions/advisorInvite.action';
import { titleCaseAndRemoveUnderScoreOrHyphen } from 'utils/helpers/Helpers';
import Button from '../Button/Button';
import { SelectOptionValue } from '../Select/Select';
import Input from '../Input/Input';
import { FormikSelect } from '../Select/FormikSelect';
import AdvisorsCardsTable, { AdvisorType } from './AdvisorsCardsTable';

const validationSchema = Yup.object().shape({
  email: Yup.string()
    .email('Invalid email format')
    .required('Email is required'),
  tool: Yup.string().required('Tool selection is required'),
});

type ManageAccessModalProps = {
  showForm?: boolean;
  id: number;
  advisorId: number;
};
const ManageAccessModal: FC<ManageAccessModalProps> = ({ showForm, id, advisorId }) => {
  const dispatch = useDispatch();
  const advisorInvitesList = useSelector(advisorInvitesSelector);
  const advisorInviteLoading = useSelector(advisorInviteLoadingSelector);

  const [pendingAdvisorsArray, setPendingAdvisorsArray] = useState<
    AdvisorType[] | undefined
  >();

  const [additionalAdvisorsArray, setAdditionalAdvisorsArray] = useState<
    AdvisorType[] | undefined
  >();

  const [currentAdvisorObj, setCurrentAdvisorObj] = useState<{
    email: string;
    tool: SelectOptionValue;
  }>({
    email: '',
    tool: '',
  });

  const assessmentToolsArray = Object.entries(AssessmentTools).map(
    ([key, value]) => {
      if (value === AssessmentTools.FINANCIAL_GAP_ANALYSIS) {
        return {
          value,
          label: 'Wealth Gap Analysis',
        };
      }
      return {
        value,
        label: titleCaseAndRemoveUnderScoreOrHyphen(key),
      };
    }
  );

  const handleDelete = (id: number, status: InviteStatus) => {
    // api call to cancel or revoke invite
    dispatch(cancelOrRevokeAdvisorInviteAction({ id, status }));
  };

  // api call to getInvites
  useEffect(() => {
    dispatch(getAdvisorInvites({ id: id ? +id : 0, advisorId }));
  }, [id]);

  // filtering-out pending and autoaccepted advisors from invitelist
  useEffect(() => {
    const pendingInvites: AdvisorType[] = [];
    const acceptedInvites: AdvisorType[] = [];

    advisorInvitesList?.forEach((advisor) => {
      const invite = {
        id: advisor?.id,
        email: advisor.secondary_advisor_email,
        tool: advisor.assessment_tool,
      };

      if (advisor?.status === InviteStatus.PENDING) {
        pendingInvites.push(invite);
      } else if (advisor?.status === InviteStatus.ACCEPTED) {
        acceptedInvites.push(invite);
      }
    });

    setPendingAdvisorsArray(pendingInvites);
    setAdditionalAdvisorsArray(acceptedInvites);
  }, [advisorInvitesList]);

  return (
    <div className='border-1 px-6 py-4 h-[42rem] w-full flex flex-col gap-4 '>
      {showForm && (
        <Formik
          initialValues={currentAdvisorObj}
          validationSchema={validationSchema}
          validateOnChange
          onSubmit={(values, { resetForm }) => {
        // api call to send invite
        dispatch(
          inviteSecondaryAdvisor({
            email: values.email,
            tool: values.tool as AssessmentTools,
            business_owner_id: id ? +id : 0,
          })
        );
        setCurrentAdvisorObj({ email: '', tool: '' });
        resetForm();
          }}
        >
          {({ errors }) => (
        <Form>
          <Field
            name='email'
            as={Input}
            label='Email'
            error={errors?.email}
          />

          <div className='flex items-center gap-8 mt-2 '>
            <FormikSelect
          name='tool'
          placeholder='Select Tool'
          options={assessmentToolsArray}
          classname='w-full '
          menuClassname='max-h-72 overflow-auto scrollbar border-r-0'
          outerClassName='!h-[3.125rem]'
          labelClassName='!text-lg'
          label='Tool'
          errorClassname='mt-0'
            />

            <Button
          type='submit'
          isSubmitting={advisorInviteLoading}
          className='px-4 py-3 mb-1 mt-4 !w-24 !h-20'
            >
          Send
            </Button>
          </div>
        </Form>
          )}
        </Formik>
      )}
      <AdvisorsCardsTable
        advisorsArray={pendingAdvisorsArray}
        handleDelete={handleDelete}
        heading='Pending Invites'
      />
      <AdvisorsCardsTable
        advisorsArray={additionalAdvisorsArray}
        handleDelete={handleDelete}
        heading='Secondary Advisors'
        isSecondaryAdvisor
      />{' '}
    </div>
  );
};

export default memo(ManageAccessModal);
