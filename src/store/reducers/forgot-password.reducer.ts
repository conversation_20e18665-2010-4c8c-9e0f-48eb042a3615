import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
  ForgotPasswordState,
  ResetPasswordState,
  SetPublicPasswordState,
} from 'store/types/forgot-password.type';

const initialState: ForgotPasswordState &
  ResetPasswordState &
  SetPublicPasswordState = {
  reset: {},
  setPublic: {},
};

const forgotPasswordSlice = createSlice({
  name: 'forgotPassword',
  initialState,
  reducers: {
    forgot_password_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      state.loading = action.payload.loading;
    },
    forgot_password_success: (state) => {
      state.success = true;
      state.error = undefined;
      state.loading = false;
    },
    forgot_password_error: (state, action: PayloadAction<string>) => {
      state.success = false;
      state.error = action.payload;
    },
    reset_password_success: (state) => {
      state.reset.success = true;
      state.reset.error = undefined;
      state.reset.loading = false;
    },
    reset_password_error: (state, action: PayloadAction<string>) => {
      state.reset.success = false;
      state.reset.error = action.payload;
    },
    reset_password_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      state.reset.loading = action.payload.loading;
    },
    set_public_password_success: (state) => {
      state.setPublic.success = true;
      state.setPublic.error = undefined;
      state.setPublic.loading = false;
    },
    set_public_password_error: (state, action: PayloadAction<string>) => {
      state.setPublic.success = false;
      state.setPublic.error = action.payload;
    },
    set_public_password_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      state.setPublic.loading = action.payload.loading;
    },
  },
});

export const {
  forgot_password_loading: setForgotPasswordLoading,
  forgot_password_success: forgotPasswordSuccess,
  forgot_password_error: forgotPasswordError,
  reset_password_loading: setResetPasswordLoading,
  reset_password_success: resetPasswordSuccess,
  reset_password_error: resetPasswordError,
  set_public_password_success: setPublicPasswordSuccess,
  set_public_password_error: setPublicPasswordError,
  set_public_password_loading: setPublicPasswordLoading,
} = forgotPasswordSlice.actions;

export default forgotPasswordSlice.reducer;
