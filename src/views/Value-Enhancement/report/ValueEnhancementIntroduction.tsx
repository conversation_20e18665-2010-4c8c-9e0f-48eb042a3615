import React from 'react';

interface ValueEnhancementIntroductionProps {
  ownerName: string;
}

const ValueEnhancementIntroduction: React.FC<
  ValueEnhancementIntroductionProps
> = (props) => {
  const { ownerName } = props;
  const pillars = [
    { id: 1, name: 'Growth (GR)', abbr: 'GR' },
    { id: 7, name: 'Systems and Processes (SP)', abbr: 'SP' },
    { id: 2, name: 'Owner Reliance (OR)', abbr: 'OR' },
    { id: 8, name: 'Sales (SA)', abbr: 'SA' },
    { id: 3, name: 'Leadership Team (LT)', abbr: 'LT' },
    { id: 9, name: 'Marketing (MK)', abbr: 'MK' },
    { id: 4, name: 'Employees (EM)', abbr: 'EM' },
    { id: 10, name: 'Property, Plant, Equipment (PP)', abbr: 'PP' },
    { id: 5, name: 'Product (PD)', abbr: 'PD' },
    { id: 11, name: 'Legal (LE)', abbr: 'LE' },
    { id: 6, name: 'Customers (CU)', abbr: 'CU' },
    { id: 12, name: 'Financial (FN)', abbr: 'FN' },
  ];

  return (
    <div className='flex flex-col gap-3 justify-center w-3/5 py-32 mx-auto'>
      <h2 className='text-blue-01 text-xl font-semibold'>{`Congratulations ${ownerName}`}</h2>
      <p>
        You have taken an important step toward identifying the parts of your
        business that most influence your overall business value. Each of the
        Value Enhancement Opportunities is just that – an opportunity to improve
        the value of your business. There is a lot going on that determines
        ultimate business value as evidenced by the 72 Value Enhancement
        Opportunities listed.
      </p>

      <p>
        Each VEO belongs to one of twelve business pillars, and each pillar has
        six VEOs.
      </p>
      <div className='flex flex-col'>
        <h2 className='mb-2'>The Pillars are:</h2>
        <div className='grid grid-cols-2 gap-1 mb-4'>
          {pillars.map((pillar) => (
            <div key={pillar.id} className='flex gap-2 items-center'>
              <span className='font-medium w-4'>{pillar.id}.</span>
              <span>{pillar.name}</span>
            </div>
          ))}
        </div>
      </div>
      <p>
        Each VEO has its own VEO#. Some VEOs are more important than others in
        influencing overall value. There are 16 of these, and we categorize
        these VEOs as Essential VEOs. The rest are designated as Supporting
        VEOs. An example of each type is:
      </p>

      <ol className='list-decimal list-inside'>
        <li className='mb-2'>
          GR-2 is the VEO# for the question: Is the business scalable?
        </li>
        <li className='mb-2'>
          GR-1 (E) which is VEO# for the question: Has the business experienced
          consistent growth in the past 3-5 years?
        </li>
      </ol>

      <p>
        Notice the E at the end of VEO# GR-1. It means that that GR-1 (E) is an
        Essential VEO, GR-2 is a Supporting VEO.
      </p>

      <p>
        In this report, the Essential VEOs that represent enhancement
        opportunities to you (based upon your answers to each question) are
        listed with the Essential VEOs being listed above the Supporting VEOs.
        As you use this report to address your specific value enhancement
        opportunities, you will want to address the Essential VEOs on your list
        first.
      </p>
    </div>
  );
};

export default ValueEnhancementIntroduction;
