import { FieldArray, Form, Formik } from 'formik';
import React from 'react';
import { IoInformationCircle } from 'react-icons/io5';
import { useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { getKey } from 'utils/helpers/Helpers';
import * as yup from 'yup';
import { getYear } from '../BusinessValuationConfig';

interface Props {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  data: any;
  onSaveToDraftClick: (data: any) => void;
}
const VehiclePandL: React.FC<Props> = (props) => {
  const { nextStep, setData, backStep, data, onSaveToDraftClick } = props;
  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup.object().shape({
    vehiclePandL: yup.array().of(
      yup.object({
        [`annual_finance_payments_${getYear(1)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`annual_finance_payments_${getYear(2)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`annual_finance_payments_${getYear(3)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`insurance_${getYear(1)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`insurance_${getYear(2)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`insurance_${getYear(3)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`fuel_${getYear(1)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`fuel_${getYear(2)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`fuel_${getYear(3)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`maintenance_and_repairs_${getYear(1)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`maintenance_and_repairs_${getYear(2)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
        [`maintenance_and_repairs_${getYear(3)}`]: yup
          .string()
          .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
          .nullable(),
      })
    ),
  });

  const handleSubmit = (values: any) => {
    setData(values.vehiclePandL);
    nextStep();
  };

  return (
    <div className='flex flex-col '>
      <Formik
        initialValues={{
          vehiclePandL: data?.vehicle_p_and_l || [
            {
              [`annual_finance_payments_${getYear(1)}`]: '',
              [`annual_finance_payments_${getYear(2)}`]: '',
              [`annual_finance_payments_${getYear(3)}`]: '',
              [`insurance_${getYear(1)}`]: '',
              [`insurance_${getYear(2)}`]: '',
              [`insurance_${getYear(3)}`]: '',
              [`fuel_${getYear(1)}`]: '',
              [`fuel_${getYear(2)}`]: '',
              [`fuel_${getYear(3)}`]: '',
              [`maintenance_and_repairs_${getYear(1)}`]: '',
              [`maintenance_and_repairs_${getYear(2)}`]: '',
              [`maintenance_and_repairs_${getYear(3)}`]: '',
            },
          ],
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values }) => (
          <Form className='relative   pl-5 py-3  flex flex-col gap-4 bg-white'>
            <div className='relative z-10  text-xl items-center font-semibold'>
              <span className='absolute left-[26%] mt-2'>{getYear(1)}</span>
              <span className='absolute left-[52%] mt-2'>{getYear(2)}</span>
              <span className='absolute left-[78%] mt-2'>{getYear(3)}</span>
            </div>

            <FieldArray name='vehiclePandL'>
              {({ push }) => (
                <div className='h-[calc(100vh-21.5rem)] mt-7 overflow-auto scrollbar pr-4'>
                  {values.vehiclePandL &&
                    values.vehiclePandL.length > 0 &&
                    values.vehiclePandL.map(
                      (vehiclePandL: any, index: number) => (
                        <div key={getKey(index)} className=''>
                          <div
                            className={`flex gap-1  text-[1.2rem]  mb-4 ${
                              index > 0 ? 'mt-5' : ''
                            }`}
                          >
                            <span className='underline font-semibold'>
                              Vehicle(s) - beyond regular work use
                            </span>
                          </div>

                          <div className='grid grid-cols-4 gap-x-16 gap-y-1 mt-2 text-[1.1rem]'>
                            <div
                              id={`annualFinancePayments${index}`}
                              className='flex  items-center gap-2 w-60'
                            >
                              <IoInformationCircle className='text-blue-01' />
                              <div className='relative'>
                                <Tooltip
                                  anchorSelect={`#annualFinancePayments${index}`}
                                  place='right'
                                  offset={0}
                                  className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                  classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                >
                                  For Vehicle&apos;s that really aren&apos;t
                                  used in the business
                                </Tooltip>
                                <span>Annual Finance Payments</span>
                              </div>
                            </div>

                            <FormikInput
                              className='!h-10'
                              name={`vehiclePandL.${index}.annual_finance_payments_${getYear(
                                1
                              )}`}
                              key={`vehiclePandL.${getKey(
                                index
                              )}.annual_finance_payments_${getYear(1)}`}
                            />
                            <FormikInput
                              className='!h-10'
                              name={`vehiclePandL.${index}.annual_finance_payments_${getYear(
                                2
                              )}`}
                              key={`vehiclePandL.${getKey(
                                index
                              )}.annual_finance_payments_${getYear(2)}`}
                            />
                            <FormikInput
                              className='!h-10'
                              name={`vehiclePandL.${index}.annual_finance_payments_${getYear(
                                3
                              )}`}
                              key={`vehiclePandL.${getKey(
                                index
                              )}.annual_finance_payments_${getYear(3)}`}
                            />
                            <div
                              id={`insurance${index}`}
                              className='flex  items-center gap-2'
                            >
                              <IoInformationCircle className='text-blue-01' />
                              <div className='relative'>
                                <Tooltip
                                  anchorSelect={`#insurance${index}`}
                                  place='right'
                                  offset={0}
                                  className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                  classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                >
                                  Insurance on Vehicle&apos;s that really
                                  aren&apos;t used in the business
                                </Tooltip>
                                <span>Insurance</span>
                              </div>
                            </div>

                            <FormikInput
                              className='!h-10'
                              name={`vehiclePandL.${index}.insurance_${getYear(
                                1
                              )}`}
                              key={`vehiclePandL.${getKey(
                                index
                              )}.insurance_${getYear(1)}`}
                            />
                            <FormikInput
                              className='!h-10'
                              name={`vehiclePandL.${index}.insurance_${getYear(
                                2
                              )}`}
                              key={`vehiclePandL.${getKey(
                                index
                              )}.insurance_${getYear(2)}`}
                            />
                            <FormikInput
                              className='!h-10'
                              name={`vehiclePandL.${index}.insurance_${getYear(
                                3
                              )}`}
                              key={`vehiclePandL.${getKey(
                                index
                              )}.insurance_${getYear(3)}`}
                            />

                            <div
                              id={`fuel${index}`}
                              className='flex  items-center gap-2'
                            >
                              <IoInformationCircle className='text-blue-01' />
                              <div className='relative'>
                                <Tooltip
                                  anchorSelect={`#fuel${index}`}
                                  place='right'
                                  offset={0}
                                  className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-8 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                  classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                >
                                  Estimate of fuel expense on non-business
                                  vehicles
                                </Tooltip>
                                <span>Fuel</span>
                              </div>
                            </div>
                            <FormikInput
                              className='!h-10'
                              name={`vehiclePandL.${index}.fuel_${getYear(1)}`}
                              key={`vehiclePandL.${getKey(
                                index
                              )}.fuel_${getYear(1)}`}
                            />
                            <FormikInput
                              className='!h-10'
                              name={`vehiclePandL.${index}.fuel_${getYear(2)}`}
                              key={`vehiclePandL.${getKey(
                                index
                              )}.fuel_${getYear(2)}`}
                            />
                            <FormikInput
                              className='!h-10'
                              name={`vehiclePandL.${index}.fuel_${getYear(3)}`}
                              key={`vehiclePandL.${getKey(
                                index
                              )}.fuel_${getYear(3)}`}
                            />

                            <div
                              id={`repair${index}`}
                              className='flex  items-center gap-2 w-60 '
                            >
                              <IoInformationCircle className='text-blue-01' />
                              <div className='relative'>
                                <Tooltip
                                  anchorSelect={`#repair${index}`}
                                  place='right'
                                  offset={0}
                                  className=' !bg-gray-100 border !z-10  !w-[27rem] !font-[500]  !left-1 !top-4 border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
                                  classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                                >
                                  Maintenance and Repair on non-business
                                  vehicles
                                </Tooltip>
                                <span>Maintenance and Repairs</span>
                              </div>
                            </div>
                            <FormikInput
                              className='!h-10'
                              name={`vehiclePandL.${index}.maintenance_and_repairs_${getYear(
                                1
                              )}`}
                              key={`vehiclePandL.${getKey(
                                index
                              )}.maintenance_and_repairs_${getYear(1)}`}
                            />
                            <FormikInput
                              className='!h-10'
                              name={`vehiclePandL.${index}.maintenance_and_repairs_${getYear(
                                2
                              )}`}
                              key={`vehiclePandL.${getKey(
                                index
                              )}.maintenance_and_repairs_${getYear(2)}`}
                            />
                            <FormikInput
                              className='!h-10'
                              name={`vehiclePandL.${index}.maintenance_and_repairs_${getYear(
                                3
                              )}`}
                              key={`vehiclePandL.${getKey(
                                index
                              )}.maintenance_and_repairs_${getYear(3)}`}
                            />
                          </div>
                        </div>
                      )
                    )}

                  <Button
                    type='button'
                    className='px-6 py-2 !absolute top-[1rem] right-[3rem]'
                    onClick={() =>
                      push({
                        [`annual_finance_payments_${getYear(1)}`]: '',
                        [`annual_finance_payments_${getYear(2)}`]: '',
                        [`annual_finance_payments_${getYear(3)}`]: '',
                        [`insurance_${getYear(1)}`]: '',
                        [`insurance_${getYear(2)}`]: '',
                        [`insurance_${getYear(3)}`]: '',
                        [`fuel_${getYear(1)}`]: '',
                        [`fuel_${getYear(2)}`]: '',
                        [`fuel_${getYear(3)}`]: '',
                        [`maintenance_and_repairs_${getYear(1)}`]: '',
                        [`maintenance_and_repairs_${getYear(2)}`]: '',
                        [`maintenance_and_repairs_${getYear(3)}`]: '',
                      })
                    }
                  >
                    Add
                  </Button>
                </div>
              )}
            </FieldArray>
            <div className='pr-7'>
              <BackNextComponent
                backStep={backStep}
                buttonType='submit'
                isLoading={isLoading}
                isNextDisable={false}
                onSaveToDraftClick={() => {
                  onSaveToDraftClick(values.vehiclePandL);
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default VehiclePandL;
