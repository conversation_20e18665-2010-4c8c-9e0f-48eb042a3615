import { useField } from 'formik';
import React from 'react';
import { CheckboxProps } from 'shared-resources/types/Checbox.type';
import Checkbox from './Checkbox';

interface FormikCheckboxProps
  extends Omit<CheckboxProps, 'onChange' | 'value'> {
  name: string;
  valueChanged?: (val: any) => void;
}

const FormikCheckbox: React.FC<FormikCheckboxProps> = (props) => {
  const { name, valueChanged } = props;
  const [, meta, helpers] = useField(name);

  const { value, error } = meta;
  const { setValue } = helpers;

  return (
    <Checkbox
      value={value}
      onChange={(e) => {
        setValue(e.target.checked);
        valueChanged?.(e);
      }}
      error={error}
      {...props}
    />
  );
};

export default React.memo(FormikCheckbox);
