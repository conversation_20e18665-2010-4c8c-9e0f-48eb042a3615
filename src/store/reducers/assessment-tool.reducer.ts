import { createSlice, PayloadAction } from '@reduxjs/toolkit';

const initialState = {
  response: {},
  business_owner_properties: {},
  progress_status: '',
  loading: false,
  is_password_set: false,
  error: '',
  payment_loading: false,
  reevaluate: false,
  openCount: 0,
  lastFollowUpDiff: null,
  submitted_at: null,
};

export const assessmentToolSlice = createSlice({
  name: 'assessment-tool-slice',
  initialState,
  reducers: {
    addData: (state, action: PayloadAction<any>) => {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const {
        assessment_response: assessmentResponse,
        progress_status: progressStatus,
        business_owner_properties: businessOwnerProperties,
        is_password_set: isPasswordSet,
        open_count: openCount,
        reevaluate,
        submitted_at,
        last_follow_up_diff: lastFollowUpDiff,
      } = action.payload;

      state.response = assessmentResponse;
      state.business_owner_properties = {
        ...state.business_owner_properties,
        ...businessOwnerProperties,
        management_team: businessOwnerProperties?.management_team || '[]',
      };

      state.openCount = openCount;
      state.reevaluate = reevaluate;
      state.lastFollowUpDiff = lastFollowUpDiff;

      state.progress_status = progressStatus;
      state.loading = false;
      state.is_password_set = isPasswordSet;
      state.reevaluate = reevaluate;
      state.submitted_at = submitted_at;
    },
    updateBusinessOwnerProperty: (state, action: PayloadAction<any>) => {
      const { business_owner_properties: businessOwnerProperties } =
        action.payload;
      state.business_owner_properties = businessOwnerProperties;
    },
    addLoading: (state, action: PayloadAction<{ loading: boolean }>) => {
      const { loading } = action.payload;
      state.loading = loading;
    },
    addError: (state, action: PayloadAction<{ message: string }>) => {
      const { message } = action.payload;
      state.error = message;
      state.loading = false;
    },
    resetData: (state) => {
      state.response = {};
      state.progress_status = '';
    },
    resetProgressStatus: (state) => {
      state.progress_status = '';
    },
    setPaymentLoading: (state, action: PayloadAction<{ loading: boolean }>) => {
      const { loading } = action.payload;
      state.payment_loading = loading;
    },
    setAssessmentReEvaluate: (
      state,
      action: PayloadAction<{ reevaluate: boolean }>
    ) => {
      const { reevaluate } = action.payload;
      state.reevaluate = reevaluate;
    },
  },
});

export const {
  addData: addAssessmentData,
  updateBusinessOwnerProperty,
  addLoading: addAssessmentLoading,
  addError: addAssessmentError,
  resetData: resetAssessmentData,
  resetProgressStatus,
  setPaymentLoading,
  setAssessmentReEvaluate: setReEvaluate,
} = assessmentToolSlice.actions;

export default assessmentToolSlice.reducer;
