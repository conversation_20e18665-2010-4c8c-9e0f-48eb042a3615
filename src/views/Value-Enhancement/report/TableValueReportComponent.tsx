import React, { memo, useRef } from 'react';
import { titleCase } from 'utils/helpers/Helpers';
import {
  EnhancementVEOs,
  idToQuestion,
  reportHeaderOptions,
} from '../ValueEnhancementConfig';

interface TableValueReportComponentProps {
  response: EnhancementVEOs[];
  sectionTitle?: string;
  className?: string;
}
const TableValueReportComponent: React.FC<TableValueReportComponentProps> = ({
  response,
  sectionTitle,
  className,
}) => {
  const tableRef = useRef<HTMLDivElement | null>(null);

  return (
    <div
      className={`relative z-0  flex flex-col px-12 py-24 gap-2 ${className}`}
    >
      <span className='!text-base'>{sectionTitle}</span>
      <div
        className='h-full w-full rounded-t-md relative z-40 bg-white'
        ref={tableRef}
      >
        <div
          className={`bg-blue-01 z-10 tracking-[0.07rem] grid grid-cols-16 overflow-hidden text-white
            rounded-t-md `}
        >
          {reportHeaderOptions.map((h, index) => (
            <div
              key={h}
              className={`p-2 flex items-center border-r  border-gray-02   justify-center ${
                index === 0 && 'border-l rounded-tl-md col-span-2'
              } ${index === 1 && 'col-span-2'}  ${
                index === 2 && 'col-span-5'
              } ${index === 3 && 'col-span-7'}`}
            >
              <span className='text-center text-sm'>{h}</span>
            </div>
          ))}
        </div>
        <div>
          {response?.map((item) => (
            <div key={item.veo} className='grid text-sm grid-cols-16 span'>
              <div className='p-2 border-b border-l border-r border-gray-02 col-span-2'>
                <span>{item.veo}</span>
              </div>
              <div className='p-2 border-b border-r border-gray-02 col-span-2'>
                {titleCase(item.pillar.replace(/_/g, ' '))}
              </div>
              <div className='p-2 border-b border-r border-gray-02 col-span-5'>
                {idToQuestion[item.veo]?.question}
              </div>
              <div className='p-2 border-b border-r border-gray-02 col-span-7'>
                {idToQuestion[item.veo]?.buyers_desire}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default memo(TableValueReportComponent);
