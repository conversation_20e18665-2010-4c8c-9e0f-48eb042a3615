import { baseApiService } from './BaseApiService';

export interface SecondaryAdvisorSignUpDTO {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone: string;
}
export interface SecondaryAdvisorSignUpDTOBytoken
  extends SecondaryAdvisorSignUpDTO {
  token: string;
}
class SecondaryAdvisorService {
  static getInstance(): SecondaryAdvisorService {
    return new SecondaryAdvisorService();
  }

  async verifyNewAdvisor(token?: string): Promise<{ data: string }> {
    return baseApiService.post(`/public/invite/verify/?token=${token}`);
  }

  async signUpSecondaryAdvisor(
    data: SecondaryAdvisorSignUpDTOBytoken
  ): Promise<{ data: string }> {
    return baseApiService.post(
      `/advisor/signup/invite/?token=${data.token}`,
      data
    );
  }
}

export const secondaryAdvisorService = SecondaryAdvisorService.getInstance();
