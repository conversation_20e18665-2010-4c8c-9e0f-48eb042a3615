import { SubscriptionType, UserType } from 'types/enum';
import { PaymentActionType } from './actions.constants';

export interface GetPaymentLinkActionPayloadType {
  userType: UserType;
  userId?: number;
}
export const getPaymentLinkAction = (
  payload: GetPaymentLinkActionPayloadType
) => ({
  type: PaymentActionType.GET_PAYMENT_LINK,
  payload,
});

export const getAdvisorPaymentLinkAction = (payload: SubscriptionType) => ({
  type: PaymentActionType.GET_ADVISOR_PAYMENT_LINK,
  payload,
});

export const cancelAdvisorSubscriptionAction = () => ({
  type: PaymentActionType.CANCEL_ADVISOR_SUBSCRIPTION,
});

export const retryAdvisorPaymentAction = () => ({
  type: PaymentActionType.RETRY_ADVISOR_PAYMENT,
});

export const updateAdvisorForPayment = (userId?: number) => ({
  type: PaymentActionType.SEND_PAYMENT_REMINDER,
  payload: userId
});
