import { Switch } from '@headlessui/react';
import classNames from 'classnames';
import React from 'react';
import { ToggleProps } from 'shared-resources/types/Toggle.type';

const Toggle: React.FC<ToggleProps> = (props) => {
  const { enabled, setEnabled, disabled } = props;

  return (
    <Switch
      checked={enabled}
      disabled={disabled}
      onChange={setEnabled}
      className={classNames(
        disabled && 'pointer-events-none opacity-50',
        enabled ? 'bg-green-700 border-green-700' : 'bg-white border-black-01',
        'relative flex items-center flex-shrink-0 h-5 w-7.5 border-4 border-black-01 rounded-3xl cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none'
      )}
    >
      <div className='flex items-center w-full h-full'>
        <span
          aria-hidden='true'
          className={`${
            enabled ? 'translate-x-3 bg-white' : 'translate-x-[2px] bg-black-01'
          }
          absolute top-[8%] pointer-events-none flex items-center w-2.5 h-2.5  rounded-full  shadow-lg transform ring-0 transition ease-in-out duration-200`}
        />
      </div>
    </Switch>
  );
};

export default Toggle;
