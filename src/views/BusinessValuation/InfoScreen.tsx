import React from 'react';
import Button from 'shared-resources/components/Button/Button';

interface Props {
  nextStep: () => void;
}
const InfoScreen: React.FC<Props> = (props) => {
  const { nextStep } = props;

  return (
    <div className='flex flex-col  text-[1rem] leading-8'>
      <div className='pl-4 py-3 bg-white'>
        <div className=' h-[calc(100vh-17rem)] overflow-auto scrollbar '>
          <h2>
            There are two steps required of the Business Owner in providing data
            for an ExitSmarts Business Valuation:
          </h2>
          <div className='mt-4 font-semibold px-16'>
            <h3>
              • Complete the Sellers Discretionary Earnings (SDE) Section
              (below)
            </h3>
            <h3>
              • Complete the P&L and Balance Sheet Data Collection Section
              (below)
            </h3>
          </div>

          <div className='flex flex-col gap-6 mt-7  pr-4 '>
            <p>
              <span className='text-blue-01 underline font-semibold text-base'>
                Sellers Discretionary Earnings (SDE) Section : {'  '}
              </span>
              Sellers Discretionary Earnings (SDE) is a financial metric used to
              assess the earnings of a business, especially when valuing small
              to midsize enterprises. It represents the total earnings that a
              single owner-operator could generate from the business, and it
              includes not just the net income but also adds back certain
              expenses that are discretionary in nature.
              <br />
              <span className='font-bold'>Definition : </span> SDE is often
              defined as the net profit of the business before taxes, plus the
              owner’s salary and benefits, as well as any other non-essential or
              discretionary expenses that the owner decides to incur. It
              provides a more accurate picture of the earnings potential of the
              business from the perspective of a new owner.
              <br />
              <span className='font-bold'>Why It Matters : </span>{' '}
              <h3>
                <span className='font-semibold text-gray-600'>
                  • Valuation Tool :
                </span>{' '}
                When selling a business, SDE is commonly used to help determine
                its value. Potential buyers want to know how much money they can
                expect to make if they take over the business.
              </h3>
              <h3>
                <span className='font-semibold text-gray-600'>
                  • Financial Clarity :
                </span>{' '}
                By focusing on discretionary earnings, business owners can see a
                clearer representation of how much cash the business generates
                that is available to them as the owner or potential owner.
              </h3>
              <span className='font-bold'>Components of SDE : </span>{' '}
              <h3>
                <span className='font-semibold text-gray-600'>
                  • Net Profit :
                </span>{' '}
                Start with the bottom-line profit of the business.
              </h3>
              <h3>
                <span className='font-semibold text-gray-600'>
                  • Owner’s Compensation :
                </span>{' '}
                Add back the salary and benefits that the owner receives, as
                these might not be the same for a new owner.
              </h3>
              <h3>
                <span className='font-semibold text-gray-600'>
                  • Discretionary Expenses :
                </span>{' '}
                • Add back expenses that are not essential to operating the
                business, such as: Personal expenses run through the business
                (like personal travel) , Other perks that might not be necessary
                for the business to function effectively
              </h3>
              {/* ff */}
              <span className='font-bold'>Example : </span>{' '}
              <h3>
                Let’s say your business has a net profit of $100,000. You pay
                yourself a salary of $50,000. You had $10,000 in personal
                expenses charged to the business. Your SDE would be calculated
                as follows: SDE = Net Profit + Owner’s Salary + Discretionary
                Expenses SDE = $100,000 + $50,000 + $10,000 = $160,000
              </h3>
              <br />
              <h3>
                SDE is a two-way street - if salary and/or perks are less than
                market rate, then salary and perks need to be added to reflect
                normal market costs.
              </h3>
              <br />
              <span className='font-bold'>Summary : </span> SDE provides a
              standardized way to understand the financial health and
              operational efficiency of a business. It aids in setting a
              realistic price if they are considering selling and provides
              potential buyers with clear earnings potential. Accordingly, as
              you complete the P&L Section, do so with the mindset of capturing
              all of those perks that you have taken, because doing so results
              in a higher valuation.
            </p>

            <p>
              <span className='text-blue-01 underline font-semibold text-base'>
                P&L and Balance Sheet Data Collection Section: :{'  '}
              </span>
              This section requests three years of data (Current Year, Current
              Year minus one year, and Current Year minus two years). Since most
              tax returns are calendar year-end (full year ending December 31),
              the question arises about the definition of Current Year.
              Specifically, this question is about when to use the tax return
              (12/31/last year) as the Current Year and when to annualize the
              current year to create a more current picture. (Annualize means
              using as much data as you have available (year-to-date results,
              current operating trends, seasonality adjustments, and any know
              factors) to turn year-to-date numbers into projections for the
              current full year. An example would be using actual data from
              January through September, then projecting October through
              December to create a picture of the Current Year.) <br />
              <br />
              In ExitSmarts Indication of Value calculations we change the
              Current Year to the actual current year in July of each year. This
              means that data provided should come from the previous year tax
              return for all valuations done in the first six months of the year
              and that valuations done July through December should use
              annualized data. There is a tool tip with each entry in the P&L &
              Balance Sheet Data Collection Section.
            </p>
          </div>
        </div>
        <div className='flex justify-end pr-7 mt-2'>
          <Button
            onClick={() => nextStep()}
            className='rounded-xl px-7 py-1  font-medium'
            type='button'
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default InfoScreen;
