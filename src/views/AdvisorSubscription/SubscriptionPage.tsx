import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import { FaInfo } from 'react-icons/fa';
import { TiTick } from 'react-icons/ti';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router';
import ConfirmCancelSubscriptionModal from 'shared-resources/components/BusinessOwner/Modals/ConfirmCancelSubscriptionModal';
import Button from 'shared-resources/components/Button/Button';
import Modal from 'shared-resources/components/Modal/Modal';
import {
  cancelAdvisorSubscriptionAction,
  getAdvisorPaymentLinkAction,
  retryAdvisorPaymentAction,
} from 'store/actions/payment.action';
import { getPaymentLoading } from 'store/selectors/assessment-tool.selector';
import { getAdvisorSubscriptionData } from 'store/selectors/user.selector';
import { SubscriptionStatus, SubscriptionType, UserType } from 'types/enum';
import { getDefaultRoute } from 'utils/helpers/Helpers';
import transparentLogo from 'assets/transparentLogo.png';

const SubscriptionPage: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const loading = useSelector(getPaymentLoading);
  const advisorSubscriptionInfo: any = useSelector(getAdvisorSubscriptionData);
  const monthlyPlan = {
    name: 'Monthly',
    price: 399,
    onBoardFee: 750,
    savings: 0,
    features: [
      'Personalized Advisor Dashboard/Business Owner Dashboard per Client.',
      'Readiness Assessment',
      'Transition Objectives',
      'Business Valuation',
      'Business Continuity Instructions',
      'Owner Reliance',
      'Value Enhancement Opportunities',
      'Wealth Gap Analysis',
      'Stakeholder Alignment Meeting',
      'Asset Protection',
      'Buyer Type-Deal Structure',
    ],
  };

  useEffect(() => {
    if (advisorSubscriptionInfo.status !== SubscriptionStatus.INACTIVE) {
      navigate(getDefaultRoute(UserType.ADVISOR));
    }
  }, []);

  const annualPlan = {
    name: 'Annually',
    price: 4309,
    onBoardFee: 750,
    savings: 10,
    features: monthlyPlan.features,
  };

  const [selectedPlan, setSelectedPlan] = useState<SubscriptionType>(
    SubscriptionType.MONTHLY
  );

  const selectedPlanDetails =
    selectedPlan === SubscriptionType.MONTHLY ? monthlyPlan : annualPlan;

  const handlePurchase = () => {
    dispatch(getAdvisorPaymentLinkAction(selectedPlan));
  };

  const [confirmCancel, setConfirmCancel] = useState(false);

  const onConfirmCancel = () => {
    dispatch(cancelAdvisorSubscriptionAction());
    setConfirmCancel(false);
  };

  const isCancelled = advisorSubscriptionInfo?.cancel_at;

  const isRetryPayment =
    advisorSubscriptionInfo.stripe_status === 'unpaid' && !isCancelled;

  return (
    <div
      className={`flex flex-col items-center w-full gap-2 ${
        isRetryPayment ? 'bg-white h-full justify-start' : 'justify-center'
      }`}
    >
      <div>
        <img
          src={transparentLogo}
          alt='exit-smart-logo'
          className='object-contain w-[14.25rem] h-[3.188rem]'
        />
      </div>
      <div className='flex flex-col gap-1 mt-4'>
        <h2 className='text-[1.875rem] font-bold ml-3'>
          Address Your Clients Complex Exit Planning Needs
        </h2>
      </div>
      <div className='flex flex-col justify-center text-center items-center text-black-01 font-medium text-sm w-2/3 mt-4'>
        <p>
          Secure your licensed subscription today and begin making an impact on
          your clients exit planning journey. Choose between monthly or annual
          billing arrangements. All licenses are billed on a per advisor basis.
        </p>
      </div>
      {isRetryPayment ? (
        <>
          <div className='flex text-blue-01 font-[400] gap-2  items-center mt-6'>
            <FaInfo />
            <h2>Your Paymnet was Unsuccessfull</h2>
          </div>
          <div className='flex gap-6 mt-2'>
            <Button
              isSubmitting={loading}
              onClick={() => {
                dispatch(retryAdvisorPaymentAction());
              }}
              className='px-7 py-2'
            >
              Retry Payment
            </Button>
            <Button
              onClick={() => setConfirmCancel(true)}
              className={`px-6 py-3 border-transparent bg-blue-01 rounded-lg ${
                isCancelled ? ' bg-blue-300' : ''
              }`}
              theme='tertiary'
            >
              Cancel Subscription
            </Button>
          </div>
        </>
      ) : (
        <>
          <div className='h-[3.125rem] w-[13.625rem] rounded-lg bg-gray-08 p-1.5 flex space-x-2 '>
            <Button
              onClick={() => setSelectedPlan(SubscriptionType.MONTHLY)}
              className={classNames(
                'h-full w-[6.25rem] flex justify-center  items-center rounded-md ',
                selectedPlan === SubscriptionType.MONTHLY
                  ? ''
                  : '!text-gray-02 bg-transparent'
              )}
            >
              Monthly
            </Button>
            <Button
              onClick={() => setSelectedPlan(SubscriptionType.ANNUALLY)}
              className={classNames(
                'h-full w-[6.25rem] flex justify-center  items-center rounded-md',
                selectedPlan === SubscriptionType.ANNUALLY
                  ? ''
                  : '!text-gray-02 bg-transparent'
              )}
            >
              Annually
            </Button>
          </div>

          <div className='bg-gray-100 w-full 1.5xl:w-3/4 p-5 flex justify-between gap-3 rounded-2xl mt-3'>
            <div className='flex flex-col gap-1'>
              <span>
                <h3 className='text-blue-01 font-medium'>
                  {selectedPlanDetails.name}
                </h3>
                <h1 className='text-black-02'>
                  <span className='text-black-01 text-2xl font-bold'>
                    ${selectedPlanDetails.price}
                  </span>
                  <span>
                    {' '}
                    /
                    {selectedPlan === SubscriptionType.ANNUALLY
                      ? ' year'
                      : ' month'}
                  </span>
                  {selectedPlan === SubscriptionType.ANNUALLY && (
                    <div className='text-blue-01'>
                      ( {selectedPlanDetails.savings}% Off by paying Annually vs
                      Monthly )
                    </div>
                  )}
                </h1>
              </span>
              <span className='mt-4 flex flex-col gap-4'>
                <h3 className=''>
                  Awareness/Plan Development module includes the following suite
                  of tools to use with clients and dedicated training/support
                  from our customer success team:
                </h3>
                <div className='pr-1 flex flex-col gap-2'>
                  {selectedPlanDetails.features.map((feature, index) => (
                    <div key={+index} className='flex items-center gap-2'>
                      <TiTick className='text-xl text-blue-01' />
                      <h3>{feature}</h3>
                    </div>
                  ))}
                </div>
              </span>
            </div>
            <div className='flex flex-col items-center justify-center bg-white rounded-md font-[500]'>
              <div className='flex flex-col items-center text-center w-[27rem]'>
                <h1 className='text-xl'>
                  To commence subscription, a{' '}
                  <span className='text-blue-01 mx-2'>
                    ${selectedPlanDetails.price}
                  </span>
                  {selectedPlan === SubscriptionType.ANNUALLY
                    ? 'annual '
                    : 'monthly '}
                  fee is required
                  {!advisorSubscriptionInfo?.onboardingFee && (
                    <span className='mx-1'>
                      , along with a{' '}
                      <span className='text-blue-01'>
                        ${selectedPlanDetails.onBoardFee}
                      </span>{' '}
                      onboarding fee.
                    </span>
                  )}
                </h1>
              </div>
              <Button
                isSubmitting={loading}
                onClick={handlePurchase}
                className='py-4 px-10  mt-16  text-xl mx-auto'
              >
                Purchase Plan
              </Button>
            </div>
          </div>
        </>
      )}
      <Modal
        visible={confirmCancel}
        handleVisibility={setConfirmCancel}
        closeOnOutsideClick
      >
        <ConfirmCancelSubscriptionModal
          handleConfirmYourAccountModalVisibility={setConfirmCancel}
          onConfirm={onConfirmCancel}
        />
      </Modal>
    </div>
  );
};

export default SubscriptionPage;
