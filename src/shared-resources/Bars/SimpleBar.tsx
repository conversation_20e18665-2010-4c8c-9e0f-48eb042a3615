import cx from 'classnames';
import React from 'react';

interface Props {
  percentage: number;
  containerClassName?: string; // use to update bar height , inactive color, border radius
  activeBarClassName?: string; //  use to update active colour, border radius
}

const SimpleBar: React.FC<Props> = ({
  percentage,
  containerClassName,
  activeBarClassName,
}) => (
  <div className={cx('h-3 bg-blue-02 rounded-md w-full', containerClassName)}>
    <div
      className={cx('bg-green-01 h-full rounded-md', activeBarClassName)}
      style={{ width: `${percentage}%` }}
    />
  </div>
);

export default SimpleBar;
