import { ReportDTO } from 'dtos/report.dto';
import { AssessmentTools } from 'types/enum';
import { baseApiService } from './BaseApiService';

class AssessmentReportService {
  static getInstance(): AssessmentReportService {
    return new AssessmentReportService();
  }

  public async fetchReport(data: ReportDTO) {
    return baseApiService.get(
      `business-owner/${data.id}/assessment-report/${data.tool}`
    );
  }

  public async fetchReportByBusinessOwner(tool: AssessmentTools) {
    return baseApiService.get(`business-owner/assessment-report/${tool}`);
  }

  public async sendReport(businessOwnerId: number, tool: AssessmentTools) {
    return baseApiService.post(
      `business-owner/${businessOwnerId}/send-report`,
      {
        tool,
      }
    );
  }
}

export const assessmentReportService = AssessmentReportService.getInstance();
