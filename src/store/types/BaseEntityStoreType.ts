export interface BaseEntityStore {
  create: {
    loading?: boolean;
    error?: boolean;
    message?: string;
    errors?: {
      [key: string]: string;
    }[];
  };
  update: {
    loading?: boolean;
    error?: boolean;
    message?: string;
    errors?: {
      [key: string]: string;
    }[];
  };
  list?: { loading?: boolean; error?: boolean; message?: string };
  get?: { loading?: boolean; error?: boolean; message?: string };
}
