import { Form, Formik } from 'formik';
import React from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { RxCrossCircled } from 'react-icons/rx';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import Input from 'shared-resources/components/Input/Input';
import InputLabel from 'shared-resources/components/InputLabel/InputLabel';
import { businessOwnerUpdate } from 'store/actions/business-owner.action';
import { getUserData } from 'store/selectors/user.selector';
import { AssessmentTools, OwnerRelianceTabs } from 'types/enum';
import { formatDate, getFormattedDate, getKey } from 'utils/helpers/Helpers';
import * as yup from 'yup';

interface Props {
  onGestartedClick: () => void;
  response: any;
  setResponse: any;
}
const ManagementTeam: React.FC<Props> = ({
  onGestartedClick,
  response,
  setResponse,
}) => {
  const teamArray = response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.members || []; // existing members
  const params = useParams();
  const { id } = params;
  const dispatch = useDispatch();
  const loggedInUser = useSelector(getUserData);

  const handleAddClick = () => {
    const existingMembers =
      response[OwnerRelianceTabs.MANAGEMENT_TEAM].members || [];
    const newMember = { name: '' };
    setResponse({
      ...response,
      [OwnerRelianceTabs.MANAGEMENT_TEAM]: {
        ...response[OwnerRelianceTabs.MANAGEMENT_TEAM],
        members: [...existingMembers, newMember],
      },
    });
  };

  // delete member
  const deleteMember = (index: number) => {
    const newArray =
      Array.from(response[OwnerRelianceTabs.MANAGEMENT_TEAM].members) || [];
    newArray.splice(index, 1);
    setResponse({
      ...response,
      [OwnerRelianceTabs.MANAGEMENT_TEAM]: {
        ...response[OwnerRelianceTabs.MANAGEMENT_TEAM],
        members: newArray,
      },
    });
  };

  // edit member
  const editMember = (index: number, value: string) => {
    const newArray =
      Array.from(response[OwnerRelianceTabs.MANAGEMENT_TEAM].members) || [];
    const newData = { name: value };
    newArray[index] = newData;
    setResponse({
      ...response,
      [OwnerRelianceTabs.MANAGEMENT_TEAM]: {
        ...response[OwnerRelianceTabs.MANAGEMENT_TEAM],
        members: newArray,
      },
    });
  };

  const updateBusinessOwner = () => {
    dispatch(
      businessOwnerUpdate(
        loggedInUser!,
        {
          tool: AssessmentTools.OWNER_RELIANCE,
          first_name: response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.first_name,
          last_name: response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.last_name,
          business_name:
            response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.business_name,
          business_start_date: formatDate(
            response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.business_start_date ||
              '',
            'yyyy-MM-dd'
          ),
          management_team: JSON.stringify(
            Object.values(response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.members)
          ),
        },
        undefined,
        +id!,
        onGestartedClick
      )
    );
  };

  const validationSchema = yup.object().shape({
    business_name: yup.string().required('Business name is required'),
    first_name: yup.string().required('Business Owner First Name is required'),
    last_name: yup.string().required('Business Owner Last Name is required'),
  });

  return (
    <>
      <div className='pr-4 text-[1rem] relative h-[calc(100vh-20rem)] overflow-auto scrollbar'>
        <Formik
          initialValues={{
            business_name:
              response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.business_name || '',
            first_name:
              response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.first_name || '',
            last_name:
              response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.last_name || '',
          }}
          validationSchema={validationSchema}
          onSubmit={() => {}}
          enableReinitialize
          validateOnBlur
        >
          {() => (
            <Form>
              <div className='flex mt-2 space-x-10 w-[85%]'>
                <FormikInput
                  asterisk
                  name='business_name'
                  id='business_name'
                  label='Company Name'
                  className='text-blue-01 !h-12 font-bold'
                  valueChanged={(event) => {
                    setResponse({
                      ...response,
                      [OwnerRelianceTabs.MANAGEMENT_TEAM]: {
                        ...response[OwnerRelianceTabs.MANAGEMENT_TEAM],
                        business_name: event.target.value,
                      },
                    });
                  }}
                />
                <FormikInput
                  asterisk
                  label='Business Owner First Name'
                  className='text-blue-01 !w-full !h-12 font-bold'
                  name='first_name'
                  id='first_name'
                  valueChanged={(event) => {
                    setResponse({
                      ...response,
                      [OwnerRelianceTabs.MANAGEMENT_TEAM]: {
                        ...response[OwnerRelianceTabs.MANAGEMENT_TEAM],
                        first_name: event.target.value,
                      },
                    });
                  }}
                />
                <FormikInput
                  asterisk
                  label='Business Owner Last Name'
                  className='text-blue-01 !w-full !h-12 font-bold'
                  name='last_name'
                  id='last_name'
                  valueChanged={(event) => {
                    setResponse({
                      ...response,
                      [OwnerRelianceTabs.MANAGEMENT_TEAM]: {
                        ...response[OwnerRelianceTabs.MANAGEMENT_TEAM],
                        last_name: event.target.value,
                      },
                    });
                  }}
                />
              </div>
            </Form>
          )}
        </Formik>

        <InputLabel label='Date' />
        <div className='mt-3 w-[26.5%] border rounded-xl  border-gray-02'>
          <DatePicker
            className='text-blue-01 !h-12 !rounded-xl focus:outline-none py-2.5 px-5 font-bold'
            selected={
              response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.business_start_date
            }
            onChange={(date) => {
              setResponse({
                ...response,
                [OwnerRelianceTabs.MANAGEMENT_TEAM]: {
                  ...response[OwnerRelianceTabs.MANAGEMENT_TEAM],
                  business_start_date:
                    date && getFormattedDate(date?.toDateString()),
                },
              });
            }}
          />
        </div>
        <div className='relative mt-5'>
          <h2>Management Team Names</h2>
          <Button
            onClick={handleAddClick}
            className='px-5 py-2  !absolute !-top-3 right-1'
          >
            Add
          </Button>
          <div className='grid grid-cols-5 gap-x-5 mt-3 max-h-96'>
            {teamArray.map((t: { name: string }, index: number) => (
              <Input
                key={getKey(index)}
                trailingIcon={
                  <RxCrossCircled
                    className={`text-red-500 cursor-pointer ${
                      t.name ===
                        `${
                          response[OwnerRelianceTabs.MANAGEMENT_TEAM]
                            ?.first_name
                        } ${
                          response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.last_name
                        }` && 'hidden'
                    }`}
                    size={20}
                    onClick={() => {
                      deleteMember(index);
                    }}
                  />
                }
                className={`text-blue-01 !h-10 !bg-blue-02 !border !border-blue-01  font-bold ${
                  t.name ===
                    `${
                      response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.first_name
                    } ${
                      response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.last_name
                    }` && 'pointer-events-none'
                } `}
                value={t.name}
                onChange={(event) => {
                  editMember(index, event.target.value);
                }}
              />
            ))}
          </div>
        </div>
      </div>
      <div className='flex justify-end mr-10 mt-2'>
        <Button
          disabled={
            response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.members?.length < 1 ||
            !response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.business_name ||
            !response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.first_name ||
            !response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.last_name
          }
          onClick={() => {
            updateBusinessOwner();
          }}
          className='px-5 py-2'
        >
          Get Started
        </Button>
      </div>
    </>
  );
};

export default ManagementTeam;
