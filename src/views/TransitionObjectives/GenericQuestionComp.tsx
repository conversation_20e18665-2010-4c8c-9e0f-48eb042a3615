import React, { FC, memo, useEffect, useState } from 'react';
import { BusinessGoals } from 'shared-resources/types/TransitionObjectives.type';
import { generateUniqueKey } from 'utils/helpers/Helpers';
import Checkbox from 'shared-resources/components/CheckBox/Checkbox';
import { useSelector } from 'react-redux';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import { pick } from 'lodash';
import {
  QuestionType,
  compareBusinessGoals,
  initialAffectTableValues,
  initialStakeHoldersValue,
  initializeTableValues,
  intialDefaultTableValues,
} from './Questions';
import RadioOptionTab from './RadioOptionTab';
import QuestionsTableComp from './QuestionsTableComp';
import { getObjectives } from './TransitionObjectiveConfig';

type GenericQuestionCompProps = {
  questionObj: QuestionType;
  setIndex: (index: number) => void;
  setAnswer: (questionId: number, answerValue: any) => void;
  setClearValues: (clearValues: boolean) => void;
  clearValues: boolean;
};

const GenericQuestionComp: FC<GenericQuestionCompProps> = ({
  questionObj,
  setIndex,
  setAnswer,
  clearValues,
  setClearValues,
}) => {
  const [positionClassName, setPositionClassName] = useState('');
  const [selectedOption, setSelectedOption] = useState<null | string>(
    (questionObj?.position === 'centrallyAlignedCheckbox' ||
      questionObj?.position === 'startAlignedCheckbox') &&
      questionObj?.answer
      ? questionObj?.answer
      : null
  );

  const [textAreaAnswer, setTextAreaAnswer] = useState(
    questionObj?.position === 'centrallyAlignedTextArea' && questionObj?.answer
      ? questionObj?.answer
      : ''
  );

  const [answersPopulated, setAnswersPopulated] = useState(false);
  const [tableValuesTextArea, setTableValuesTextArea] = useState(
    questionObj?.tableType === 'stakeHolders' && questionObj?.answer
      ? questionObj?.answer?.stakeHolderNotes?.notes
      : ''
  );
  const [radioGroupInput, setRadioGroupInput] = useState<
    {
      key: string;
      value?: string | null;
    }[]
  >([]);

  const assessmentToolLoading = useSelector(getAssessmentToolLoading);
  const findAndSetIndex = (
    optionValue: string,
    isTextArea: boolean = false
  ) => {
    if (isTextArea && questionObj?.options) {
      setIndex(questionObj?.options[0].id);
    } else {
      const filteredOption = questionObj?.options?.filter(
        (opt) => opt.value === optionValue
      );
      if (filteredOption) {
        setIndex(filteredOption[0]?.id);
      }
    }
  };

  const handleRadioChange = (selection: {
    key: string;
    value?: string | null;
  }) => {
    setRadioGroupInput((inp) => {
      if (questionObj?.isMultipleSelect) {
        const newInp = inp.filter((o) =>
          questionObj.options?.map((op) => op.value).includes(o.key)
        );
        const inpFilter = newInp.map((item) => {
          const isOther = ['Additional', 'Other'].includes(selection?.key);
          if (item.key === selection.key && isOther) {
            return selection;
          }
          return item;
        });
        const isSelected = inpFilter.find((item) => item.key === selection.key);
        const isOther = ['Additional', 'Other'].includes(selection?.key);
        if (isSelected) {
          const inputFilter = inpFilter.filter(
            (item) => item.key !== selection.key
          );
          const savedOtherAdditionObj = questionObj?.answer?.find(
            (item: any) => item.key === 'Additional' || item.key === 'Other'
          );
          if (
            (isOther && selection.value === savedOtherAdditionObj?.value) ||
            !isOther ||
            !selection.value
          ) {
            setAnswer(questionObj?.id, inputFilter);
            findAndSetIndex(inputFilter[inputFilter.length - 1]?.key);
            return inputFilter;
          }
        }

        if (!isSelected) {
          inpFilter.push(selection);
        }
        setAnswer(questionObj?.id, inpFilter);
        findAndSetIndex(inpFilter[inpFilter.length - 1]?.key);
        return inpFilter;
      }
      setAnswer(questionObj?.id, [selection]);
      findAndSetIndex([selection][[selection].length - 1]?.key);
      return [selection];
    });
  };

  const [tableValues, setTableValues] = useState<BusinessGoals>(() =>
    initializeTableValues(questionObj)
  );

  useEffect(() => {
    if (
      questionObj?.tableType === 'stakeHolders' &&
      compareBusinessGoals(tableValues, intialDefaultTableValues) &&
      questionObj?.answer
    ) {
      setTableValues(questionObj?.answer);
      setTableValuesTextArea(questionObj?.answer?.stakeHolderNotes?.notes);
    }
  }, [tableValues, questionObj]);

  useEffect(() => {
    if (
      (questionObj?.position === 'centrallyAlignedCheckbox' ||
        questionObj?.position === 'startAlignedCheckbox') &&
      questionObj?.answer
    ) {
      setSelectedOption(questionObj?.answer);
    } else if (
      questionObj?.position === 'centrallyAlignedTextArea' &&
      questionObj?.answer
    ) {
      setTextAreaAnswer(questionObj?.answer);
    }
  }, [questionObj]);

  useEffect(() => {
    switch (questionObj?.position) {
      case 'centrallyAlignedCheckbox': {
        setPositionClassName('flex items-center justify-center mx-auto');
        break;
      }
      case 'startAlignedCheckbox': {
        setPositionClassName(' px-[3.688rem] py-[2.375rem] ');
        break;
      }
      case 'centrallyAlignedRadio': {
        setPositionClassName('flex items-center justify-center mx-auto');
        break;
      }
      case 'centrallyAlignedTextArea': {
        setPositionClassName('flex items-center justify-center mx-auto');
        if (questionObj?.options) {
          setIndex(questionObj?.options[0].id);
        }
        break;
      }
      default:
        setPositionClassName('');
    }
  }, [questionObj]);

  // on clicking back button, setting the next id for the previous ques
  useEffect(() => {
    if (
      (questionObj?.position === 'centrallyAlignedCheckbox' ||
        questionObj?.position === 'startAlignedCheckbox') &&
      questionObj?.answer
    ) {
      findAndSetIndex(questionObj?.answer);
    } else if (
      questionObj?.position === 'centrallyAlignedRadio' &&
      questionObj?.answer?.key
    ) {
      findAndSetIndex(questionObj?.answer?.key);
    } else if (
      questionObj?.position === 'centrallyAlignedTextArea' &&
      questionObj?.answer
    ) {
      findAndSetIndex('Please specify');
    }
  }, [questionObj]);

  // textAreaValueUpdates
  useEffect(() => {
    if (
      textAreaAnswer !== '' &&
      questionObj?.position === 'centrallyAlignedTextArea' &&
      questionObj?.answer !== textAreaAnswer
    ) {
      setAnswer(questionObj?.id, textAreaAnswer);
      findAndSetIndex('', true);
    }
  }, [textAreaAnswer, questionObj]);

  // tableValueUpdates
  useEffect(() => {
    if (tableValues && questionObj?.position === 'table') {
      if (questionObj?.tableType === 'stakeHolders') {
        if (
          !compareBusinessGoals(tableValues, questionObj?.answer) &&
          !compareBusinessGoals(tableValues, initialStakeHoldersValue)
        ) {
          setAnswer(questionObj?.id, tableValues);
        }

        if (
          !compareBusinessGoals(tableValues, questionObj?.answer) &&
          compareBusinessGoals(tableValues, initialStakeHoldersValue) &&
          questionObj?.answer &&
          !answersPopulated
        ) {
          setTableValues(questionObj?.answer);
        }

        const tableValuesWithoutNotes = pick(
          tableValues,
          Object.keys(tableValues).filter((val) => val !== 'stakeHolderNotes')
        );
        const filteredValuesForHaveDiscussed = Object.values(
          tableValuesWithoutNotes
        ).filter((val) => val.haveDiscussed === false);
        const filteredValuesForInAgreement = Object.values(
          tableValuesWithoutNotes
        ).filter((val) => val.inAgreement === false);
        if (filteredValuesForHaveDiscussed.length) {
          findAndSetIndex('noHaveDiscussed');
        }
        if (filteredValuesForInAgreement.length) {
          findAndSetIndex('noInAgreement');
        }
        if (
          filteredValuesForHaveDiscussed.length &&
          filteredValuesForInAgreement.length
        ) {
          findAndSetIndex('noHaveDiscussedInAgreement');
        }
        if (
          !filteredValuesForHaveDiscussed.length &&
          !filteredValuesForInAgreement.length
        ) {
          findAndSetIndex('noYesValue');
        }
      } else if (questionObj?.tableType === 'affect') {
        if (
          !compareBusinessGoals(tableValues, questionObj?.answer) &&
          !compareBusinessGoals(tableValues, initialAffectTableValues)
        ) {
          setAnswer(questionObj?.id, { ...tableValues });
        }
        if (
          !compareBusinessGoals(tableValues, questionObj?.answer) &&
          compareBusinessGoals(tableValues, initialAffectTableValues) &&
          questionObj?.answer &&
          !answersPopulated
        ) {
          setTableValues(questionObj?.answer);
        }
        findAndSetIndex('');
      } else {
        if (
          !compareBusinessGoals(tableValues, questionObj?.answer) &&
          !compareBusinessGoals(tableValues, intialDefaultTableValues)
        ) {
          setAnswer(questionObj?.id, tableValues);
        }
        if (
          !compareBusinessGoals(tableValues, questionObj?.answer) &&
          compareBusinessGoals(tableValues, intialDefaultTableValues) &&
          questionObj?.answer
        ) {
          setTableValues(questionObj?.answer);
        }
        findAndSetIndex('');
      }
    }
    setAnswersPopulated(true);
  }, [tableValues, questionObj, tableValuesTextArea]);

  useEffect(() => {
    if (questionObj?.tableType === 'stakeHolders') {
      setAnswer(questionObj?.id, {
        ...tableValues,
        stakeHolderNotes: {
          ...tableValues?.stakeHolderNotes,
          notes: tableValuesTextArea?.length ? tableValuesTextArea : null,
        },
      });
      setTableValues({
        ...tableValues,
        stakeHolderNotes: {
          ...questionObj?.answer?.stakeHolderNotes,
          notes: tableValuesTextArea?.length ? tableValuesTextArea : null,
        },
      });
    }
  }, [tableValuesTextArea]);

  // selectedOptionUpdates
  useEffect(() => {
    if (
      selectedOption &&
      (questionObj?.position === 'centrallyAlignedCheckbox' ||
        questionObj?.position === 'startAlignedCheckbox') &&
      questionObj?.answer !== selectedOption
    ) {
      findAndSetIndex(selectedOption);
      setAnswer(questionObj?.id, selectedOption);
    }
  }, [selectedOption, questionObj]);

  useEffect(() => {
    if (questionObj?.position === 'centrallyAlignedRadio') {
      const answer = questionObj?.answer || [];
      if (answer.length) {
        setRadioGroupInput(answer);
        findAndSetIndex(answer[answer.length - 1]?.key);
      }
    }
  }, [questionObj]);

  useEffect(() => {
    if (clearValues && !questionObj?.answer) {
      setRadioGroupInput([]);
      setClearValues(false);
    }
  }, [clearValues, questionObj]);

  if (assessmentToolLoading) {
    return <Spinner spinnerTheme='overlaySpinner' />;
  }

  return (
    <div className={`py-4 ${positionClassName}`}>
      <div className='flex flex-col'>
        <h3
          className={`text-lg font-semibold ${
            questionObj?.note ||
            questionObj?.position === 'startAlignedCheckbox'
              ? ''
              : 'mx-auto'
          }`}
        >
          {questionObj?.question}
        </h3>
        {questionObj?.note && (
          <h3 className='text-lg font-semibold'>{`(NOTE : ${questionObj?.note})`}</h3>
        )}

        {(questionObj?.position === 'centrallyAlignedCheckbox' ||
          questionObj?.position === 'startAlignedCheckbox') && (
          <div className='flex gap-20 mt-8'>
            {questionObj?.options?.map((opt) => (
              <div
                className='flex gap-3 items-center'
                key={generateUniqueKey(`${questionObj?.id}`)}
                onClick={() => setSelectedOption(opt.value)}
                role='button'
                tabIndex={0}
              >
                <Checkbox
                  className='border border-gray-400 h-2 cursor-pointer'
                  inputValue={opt.value}
                  value={selectedOption === opt.value}
                  onChange={(e) => {
                    setSelectedOption(e.target.value);
                  }}
                />
                <h3 className='text-lg font-medium mt-1.25'>{opt.value}</h3>
              </div>
            ))}
          </div>
        )}
        {questionObj?.position === 'centrallyAlignedTextArea' && (
          <div>
            <h3 className='text-base font-montserrat font-medium mt-10'>
              {questionObj?.options?.length &&
              questionObj?.options[0].value !== ''
                ? questionObj?.options[0].value
                : 'Please Specify'}
            </h3>
            <textarea
              value={textAreaAnswer}
              id='specify'
              rows={7}
              cols={87}
              className='mt-2 border-4 border-gray-400 focus:outline-none rounded-md p-1 resize-none'
              onChange={(e) => {
                setTextAreaAnswer(e.target.value);
              }}
            />
          </div>
        )}
        {questionObj?.position === 'centrallyAlignedRadio' && (
          <div className='flex flex-col gap-4 mt-7'>
            {questionObj?.options?.map((opt) => (
              <RadioOptionTab
                key={generateUniqueKey(`${questionObj?.id}`)}
                option={opt}
                handleRadioChange={handleRadioChange}
                selectedValue={radioGroupInput?.find(
                  (input) => input.key === opt.value
                )}
                isMulti={questionObj.isMultipleSelect}
              />
            ))}
          </div>
        )}
        <div className='mx-auto'>
          {questionObj?.position === 'table' && (
            <QuestionsTableComp
              headers={questionObj?.headers as string[]}
              objectives={getObjectives(questionObj)}
              tableValues={tableValues}
              setTableValues={setTableValues}
            />
          )}
          {questionObj?.tableType === 'stakeHolders' && (
            <div className='ml-10 pr-10'>
              <h3 className='text-base font-montserrat font-medium mt-10'>
                Please record any relevant notes here
              </h3>
              <textarea
                value={tableValuesTextArea}
                id='specify'
                rows={5}
                cols={87}
                className='mt-2  border-4 border-gray-400 focus:outline-none rounded-md p-1 resize-none w-full text-base'
                onChange={(e) => {
                  setTableValuesTextArea(e.target.value);
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
export default memo(GenericQuestionComp);
