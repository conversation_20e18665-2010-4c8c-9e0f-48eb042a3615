import React from 'react';
import classNames from 'classnames';

interface Props {
  title?: string;
  firstButton?: boolean;
  id: number | string;
  isActive: boolean;
  onHandleTabChange?: (id: number | string) => void;
  theme: 'primary' | 'success' | 'default';
}

const TabButton: React.FC<Props> = (props) => {
  const { title, firstButton, id, isActive, onHandleTabChange, theme } = props;

  const handleClick = () => {
    if (onHandleTabChange && isActive) {
      onHandleTabChange(id);
    }
  };
  const buttonConfig = {
    primary: {
      bgColor: 'bg-blue-01',
      borderColour: 'border-l-blue-01',
      textColor: 'text-white',
    },
    success: {
      bgColor: 'bg-green-01',
      borderColour: 'border-l-green-01',
      textColor: 'text-white',
    },
    default: {
      textColor: 'text-grey-300',
      bgColor: '',
      borderColour: '',
    },
  };

  return (
    <button
      className={classNames(
        'flex 3xl:w-[18.75rem] 2xl:w-[15.625rem] w-[12.5rem] bg-transparent',
        isActive ? 'cursor-pointer' : 'cursor-default'
      )}
      onClick={handleClick}
      id={`${id}`}
    >
      {!firstButton && theme !== 'default' && (
        <div
          className={classNames(
            'h-0 w-0   border-b-[1.600rem] border-l-[0.938rem] border-t-[1.563rem] border-solid border-b-transparent  border-t-transparent border-blue-02',
            buttonConfig[theme].bgColor
          )}
        />
        // This div creates a triangular shape on the left side of the button
      )}
      <div
        className={classNames(
          'h-12.5 w-full text-center px-3 2xl:px-3  3xl:px-auto py-3 text-base  2xl:text-lg 3xl:xl whitespace-nowrap font-medium',
          buttonConfig[theme].bgColor,
          buttonConfig[theme].textColor,
          { 'rounded-s-lg': firstButton }
        )}
      >
        {title}
      </div>
      {/* This div represents the main content of the button */}
      {theme !== 'default' && (
        <div
          className={classNames(
            'h-0 w-0 border-b-[1.625rem] border-l-[0.938rem] border-t-[1.563rem]  border-solid border-b-transparent  border-t-transparent',
            buttonConfig[theme].borderColour
          )}
        />
        // This div creates a triangular shape on the right side of the button
      )}
    </button>
  );
};

export default TabButton;
