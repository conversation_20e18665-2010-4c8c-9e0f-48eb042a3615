import React, { memo, useRef } from 'react';
import Checkbox from 'shared-resources/components/CheckBox/Checkbox';
import { titleCase } from 'utils/helpers/Helpers';
import {
  EnhancementVEOs,
  idToQuestion,
  reportBuilderHeaderOptions,
} from '../ValueEnhancementConfig';

interface TableValueReportComponentProps {
  response: EnhancementVEOs[];
  sectionTitle?: string;
  onCheckBoxClick: (value: string | string[], checkedAll?: boolean) => void;
  selectedRows: any;
}
const TableValueReportBuilderComponent: React.FC<
  TableValueReportComponentProps
> = ({ response, sectionTitle, onCheckBoxClick, selectedRows }) => {
  const tableRef = useRef<HTMLDivElement | null>(null);

  return (
    <div className='relative z-0 flex flex-col gap-2'>
      <span>{sectionTitle}</span>
      <div
        className='h-full w-full rounded-t-md relative z-40 bg-white'
        ref={tableRef}
      >
        <div
          className={`bg-blue-01 z-10 tracking-[0.07rem] grid grid-cols-16 overflow-hidden text-white
            rounded-t-md`}
        >
          {reportBuilderHeaderOptions.map((h, index) => (
            <div
              key={h}
              className={`p-2 flex items-center border-r border-gray-02 justify-center ${
                index === 0 && 'border-l rounded-tl-md col-span-1'
              } ${index === 1 && 'col-span-2'} ${
                index === 2 && 'col-span-2'
              }  ${index === 3 && 'col-span-5'} ${
                index === 4 && 'col-span-6'
              } `}
            >
              <span className='text-center text-sm'>
                {h || (
                  <Checkbox
                    className='!cursor-pointer mx-auto'
                    onChange={(event) => {
                      onCheckBoxClick(
                        response.map((item) => item.veo),
                        event.target.checked
                      );
                    }}
                  />
                )}
              </span>
            </div>
          ))}
        </div>
        <div>
          {response?.map((item) => (
            <div key={item.veo} className='grid text-sm grid-cols-16'>
              <div className='p-2 border-b border-l border-gray-02 col-span-1'>
                <Checkbox
                  className='!cursor-pointer mx-auto !mt-0'
                  value={selectedRows?.[item.veo] || false}
                  onChange={() => {
                    onCheckBoxClick(item.veo);
                  }}
                />
              </div>
              <div className='p-2 border-b border-l border-r border-gray-02 col-span-2'>
                <span>{item.veo}</span>
              </div>
              <div className='p-2 border-b border-r border-gray-02 col-span-2'>
                {titleCase(item.pillar.replace(/_/g, ' '))}
              </div>
              <div className='p-2 border-b border-r border-gray-02 col-span-5'>
                {idToQuestion[item.veo]?.question}
              </div>
              <div className='p-2 border-b border-r border-gray-02 col-span-6'>
                {idToQuestion[item.veo]?.buyers_desire}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default memo(TableValueReportBuilderComponent);
