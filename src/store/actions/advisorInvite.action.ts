import { AdvisorInviteDTO } from 'dtos/invite-advisor.dto';
import { InviteStatus } from 'types/enum';
import { AdvisorInviteActionType } from './actions.constants';

export const inviteSecondaryAdvisor = (payload: AdvisorInviteDTO) => ({
  type: AdvisorInviteActionType.INVITE_ADVISOR,
  payload,
});

export const getAdvisorInvites = (payload: { id: number, advisorId: number }) => ({
  type: AdvisorInviteActionType.GET_INVITES,
  payload,
});

export const cancelOrRevokeAdvisorInviteAction = (payload: {
  id: number;
  status: InviteStatus;
}) => ({
  type: AdvisorInviteActionType.CANCEL_OR_REVOKE_INVITE,
  payload,
});
