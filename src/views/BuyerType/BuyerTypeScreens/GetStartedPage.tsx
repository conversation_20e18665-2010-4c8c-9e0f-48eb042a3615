import React from 'react';
import Button from 'shared-resources/components/Button/Button';

interface Props {
  onGestartedClick: () => void;
}
const GetStartedPage: React.FC<Props> = ({ onGestartedClick }) => (
  <div className='flex flex-col h-full items-center justify-center gap-6 px-24'>
    <p className='text-center font-medium max-h-[calc(100vh-10rem)] leading-loose overflow-y-auto scrollbar pr-2'>
      The purpose of the Buyer Type-Deal Structure tool is to get the Business
      Owner to begin thinking about both (1) to whom they want to transition the
      business and (2) what the deal might look like. Most Business Owners have
      only a cursory understanding of these concepts, but they are extremely
      important to planning for, and achieving, a successful transition.
    </p>
    <div>
      <Button onClick={() => onGestartedClick()} className='px-6 py-2'>
        Get Started
      </Button>
    </div>
  </div>
);

export default GetStartedPage;
