import { <PERSON><PERSON>rray, Form, Formik, FormikErrors } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { FormikSelect } from 'shared-resources/components/Select/FormikSelect';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { BusinessContinuityScreens } from 'types/enum';
import {
  getKey,
  isRequiredFieldDependingOnValidationSchema,
} from 'utils/helpers/Helpers';
import * as yup from 'yup';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import ContinuityQuestionHeader from '../shared-components/ContinuityQuestionHeader';
import ContuinuityRowView from '../shared-components/ContuinuityRowView';
import { formEmpty } from '../Report/continuityReportHelper';

interface KeyAdvisorsProps {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  onSaveToDraftClick: (data: any) => void;
  data: any;
  question?: string;
  screen: BusinessContinuityScreens;
  title: string;
  options: { label: string; value: string }[];
}
const KeyAdvisors: React.FC<KeyAdvisorsProps> = (props) => {
  const {
    nextStep,
    backStep,
    setData,
    onSaveToDraftClick,
    data,
    question,
    screen,
    title,
    options,
  } = props;

  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup
    .object()
    .shape({
      [screen]: yup.array().of(
        yup.object({
          speciality: yup.string().required('Speciality is required'),
          name: yup.string().required('Name is required'),
          company: yup.string().required('Company is required'),
          phone: yup
            .string()
            .matches(
              /^(\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/,
              'Phone number not from US region'
            )
            .required('Phone is required'),
          specific_instructions: yup.string().nullable(),
        })
      ),
    })
    .required();

  const initialValues = {
    [screen]: data?.[screen] || [
      {
        speciality: '',
        name: '',
        company: '',
        phone: '',
        specific_instructions: '',
        saved: false,
      },
    ],
  };

  return (
    <div className='flex flex-col rounded-lg h-full'>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          setData(values?.[screen]);
          nextStep();
        }}
        enableReinitialize
        validateOnMount
      >
        {({ values, setFieldValue, isValid, errors }) => {
          const isFormEmpty = formEmpty(values?.[screen]);
          return (
            <Form className='relative p-5 h-full flex flex-col gap-4 bg-white'>
              <FieldArray name={screen}>
                {({ push, remove }) => (
                  <div className='flex flex-col   h-full'>
                    <ContinuityQuestionHeader
                      fieldKey={screen}
                      push={push}
                      pushData={{
                        speciality: '',
                        name: '',
                        company: '',
                        phone: '',
                        specific_instructions: '',
                        saved: false,
                      }}
                      title={title}
                    >
                      {question && (
                        <h2 className='font-medium mr-8'>{question}</h2>
                      )}
                    </ContinuityQuestionHeader>
                    <div
                      className={`flex flex-col ${
                        screen ===
                        BusinessContinuityScreens.ACCOUNTING_INFORMATION
                          ? 'max-h-[calc(100vh-28rem)]'
                          : 'max-h-[calc(100vh-25rem)]'
                      }  pr-1 scrollbar overflow-auto`}
                    >
                      <ContuinuityRowView
                        fieldKey={screen}
                        formValues={values}
                        remove={remove}
                        initialValues={{
                          speciality: '',
                          name: '',
                          company: '',
                          phone: '',
                          specific_instructions: '',
                          saved: false,
                        }}
                      />
                      <div className='flex flex-col'>
                        {!!values?.[screen]?.length &&
                          values?.[screen]?.map(
                            (formValues: any, index: number) =>
                              !formValues.saved && (
                                <div
                                  key={`${getKey(index)}_form`}
                                  className='flex flex-col border border-gray-02 p-4 mt-1 rounded-b-lg'
                                >
                                  <div className='grid grid-cols-2 gap-y-2 gap-x-8 mt-5  overflow-auto scrollbar text-[1rem]'>
                                    <FormikSelect
                                      name={`${screen}.${getKey(
                                        index
                                      )}.speciality`}
                                      key={`${screen}.${getKey(
                                        index
                                      )}.speciality`}
                                      label='Speciality'
                                      options={options}
                                      outerClassName='!h-10'
                                      labelClassName='!text-base'
                                      optionClassname='!p-1 rounded-md'
                                      placeholder='Select Speciality'
                                      menuClassname='!p-2'
                                    />
                                    <FormikInput
                                      name={`${screen}.${getKey(index)}.name`}
                                      key={`${screen}.${getKey(index)}.name`}
                                      label='Name'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${screen}.${getKey(index)}.name`
                                      )}
                                    />
                                    <FormikInput
                                      name={`${screen}.${getKey(
                                        index
                                      )}.company`}
                                      key={`${screen}.${getKey(index)}.company`}
                                      label='Company'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${screen}.${getKey(index)}.company`
                                      )}
                                    />

                                    <FormikInput
                                      name={`${screen}.${getKey(index)}.phone`}
                                      key={`${screen}.${getKey(index)}.phone`}
                                      label='Phone'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${screen}.${getKey(index)}.phone`
                                      )}
                                    />
                                    <div className='col-span-2'>
                                      <FormikInput
                                        name={`${screen}.${getKey(
                                          index
                                        )}.specific_instructions`}
                                        key={`${screen}.${getKey(
                                          index
                                        )}.specific_instructions`}
                                        fieldType='textarea'
                                        label='Any Specific Instructions'
                                        className='h-28'
                                        labelClassName='leading-6.5'
                                      />
                                    </div>
                                  </div>
                                  <div className='flex justify-end'>
                                    <Button
                                      onClick={() => {
                                        setFieldValue(
                                          `${screen}.${getKey(index)}.saved`,
                                          true
                                        );
                                      }}
                                      className='px-4 py-2'
                                      disabled={
                                        !!(
                                          errors?.[
                                            screen
                                          ] as FormikErrors<any>[]
                                        )?.[index]
                                      }
                                    >
                                      Save
                                    </Button>
                                  </div>
                                </div>
                              )
                          )}
                      </div>
                    </div>
                  </div>
                )}
              </FieldArray>
              <BackNextComponent
                backStep={() => {
                  backStep();
                  setData(values?.[screen]);
                }}
                nextStep={() => {
                  setData(values?.[screen]);
                  nextStep();
                }}
                fieldKey={screen}
                formValues={values}
                isLoading={isLoading}
                isNextDisable={isFormEmpty ? false : !isValid}
                onSaveToDraftClick={onSaveToDraftClick}
              />
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default KeyAdvisors;
