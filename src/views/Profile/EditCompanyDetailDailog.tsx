import { Form, Formik } from 'formik';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import { Advisor } from 'models/entities/Advisor';
import React, { FC, memo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { updateUserProfileAction } from 'store/actions/user.action';
import { getUserUpdateProfileLoading } from 'store/selectors/user.selector';
import { UserType } from 'types/enum';
import { formatDate, valuesTobeUpdated } from 'utils/helpers/Helpers';
import { localStorageService } from 'services/LocalStorageService';
import * as Yup from 'yup';

type EditCompanyDetailDailogProps = {
  user?: BusinessOwner | Advisor;
  onClose: () => void;
};

const getValidationSchema = (isBusinessOwner: boolean) => {
  if (isBusinessOwner) {
    return Yup.object({
      business_name: Yup.string().optional(),
      business_start_date: Yup.string().optional(),
      employee_count: Yup.string().optional(),
    });
  } else {
    return Yup.object({
      business_name: Yup.string().optional(),
    });
  }
};

const EditCompanyDetailDailog: FC<EditCompanyDetailDailogProps> = ({
  onClose,
  user,
}) => {
  const dispatch = useDispatch();
  const updateProfileLoading = useSelector(getUserUpdateProfileLoading);

  // Determine user type
  const userType = localStorageService.getLoggedInUserType();
  const isBusinessOwner = userType === UserType.BUSINESS_OWNER;
  const isAdvisor = userType === UserType.ADVISOR;

  const initialValues = {
    business_name: isBusinessOwner
      ? (user as BusinessOwner)?.business_name || ''
      : (user as Advisor)?.companyName || '',
    business_start_date: isBusinessOwner
      ? formatDate((user as BusinessOwner)?.business_start_date || '', 'yyyy-MM-dd')
      : '',
    employee_count: isBusinessOwner
      ? (user as BusinessOwner)?.employee_count || ''
      : '',
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={getValidationSchema(isBusinessOwner)}
      onSubmit={(values: any) => {
        let changedValues = {};
        let updatePayload: any = {
          first_name: user?.first_name,
          last_name: user?.last_name,
          userType: user?.type as UserType,
          onSuccess: (message: string) => {
            toast.success(message);
            onClose();
          },
        };

        if (isBusinessOwner) {
          changedValues = valuesTobeUpdated(
            {
              business_start_date: initialValues?.business_start_date,
              employee_count: initialValues?.employee_count,
            },
            values
          );
          updatePayload = {
            ...updatePayload,
            ...(changedValues as any),
            business_name: values?.business_name,
          };
        } else {
          // For Advisor, map business_name to companyName
          updatePayload = {
            ...updatePayload,
            companyName: values?.business_name,
          };
        }

        dispatch(updateUserProfileAction(updatePayload));
      }}
    >
      <Form>
        <div className='w-[46rem] rounded-xl bg-white shadow-lg relative'>
          <div className='p-6 space-y-3 mt-3'>
            <FormikInput
              name='business_name'
              key='business_name'
              label='Company Name'
              labelClassName='font-medium leading-6.5'
              className='border-2 w-full p-2 disabled:cursor-not-allowed !border-blue-01'
            />

            {isBusinessOwner && (
              <>
                <FormikInput
                  name='business_start_date'
                  key='business_start_date'
                  label='Business Start Date'
                  type='date'
                  labelClassName='font-medium leading-6.5'
                  className='border-2 w-full p-2 disabled:cursor-not-allowed !border-blue-01'
                />
                <FormikInput
                  name='employee_count'
                  key='employee_count'
                  label='Number Of Employees'
                  labelClassName='font-medium leading-6.5'
                  className='border-2 w-full p-2 disabled:cursor-not-allowed !border-blue-01'
                />
              </>
            )}

            <div className='flex justify-end gap-4'>
              <Button
                isSubmitting={updateProfileLoading}
                disabled={updateProfileLoading}
                type='submit'
                className='py-2 px-6'
              >
                Save
              </Button>
              <Button
                theme='secondary'
                type='button'
                onClick={onClose}
                className='py-2 px-6'
                disabled={updateProfileLoading}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      </Form>
    </Formik>
  );
};

export default memo(EditCompanyDetailDailog);
