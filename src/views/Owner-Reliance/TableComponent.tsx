import React, { memo, useEffect, useRef } from 'react';
import { MdDelete, MdEdit } from 'react-icons/md';
import Button from 'shared-resources/components/Button/Button';
import Radio from 'shared-resources/components/Radio/Radio';
import Select from 'shared-resources/components/Select/Select';
import { OwnerRelianceTabs } from 'types/enum';
import classNames from 'classnames';
import { getTitle, headerOptions } from './OwnerRelianceConfig';

interface Props {
  currentTab?: OwnerRelianceTabs;
  response: any;
  setResponse?: any;
  isRenderedForReport?: boolean;
  scorePercentage?: number;
  emptyKeys?: any;
  showReportTitle?: boolean;
  classname?: string;
}
const TableComponent: React.FC<Props> = ({
  currentTab,
  response,
  setResponse,
  isRenderedForReport,
  scorePercentage,
  emptyKeys,
  showReportTitle,
  classname,
}) => {
  const currentFunctionOwners = response?.management_team?.members.map(
    (m: { name: string }) => ({
      value: m.name,
      label: m.name,
    })
  );

  const inputRefs: any = useRef([]);

  const handleEditIconClick = (index: number) => {
    inputRefs.current[index].focus();
  };
  const tableRef = useRef<HTMLDivElement | null>(null);

  // set which option choosen for Current Function Owner dropdown
  const handleDropDownSelect = (
    value: string | null,
    businessFunction: string
  ) => {
    if (currentTab === OwnerRelianceTabs.OTHERS) {
      const otherFunctions: any[] =
        Array.from(response[OwnerRelianceTabs.OTHERS]) || [];
      const newArray = [...otherFunctions]; // Create a shallow copy of the original array
      newArray[+businessFunction] = {
        // Update the specific element
        ...newArray[+businessFunction], // Copy the properties of the element
        current_function_owner: value, // Update the current_function_owner property
      };
      setResponse({
        ...response,
        [currentTab as string]: newArray,
      });
    } else {
      setResponse({
        ...response,
        [currentTab as string]: {
          ...response[currentTab as string],
          [businessFunction]: {
            ...response[currentTab as string][businessFunction],
            current_function_owner: value,
          },
        },
      });
    }
  };

  // set which option choosen
  const handleRadioClick = (value: string | null, businessFunction: string) => {
    if (currentTab === OwnerRelianceTabs.OTHERS) {
      if (value === headerOptions[2] || headerOptions[3]) {
        const ownerName = `${
          response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.first_name
        } ${response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.last_name}`;
        const otherFunctions: any[] =
          Array.from(response[OwnerRelianceTabs.OTHERS]) || [];
        const newArray = [...otherFunctions]; // Create a shallow copy of the original array
        newArray[+businessFunction] = {
          // Update the specific element
          ...newArray[+businessFunction],
          current_function_owner: ownerName,
          selected_option: value,
        };
        setResponse({
          ...response,
          [currentTab as string]: newArray,
        });
      } else if (value === headerOptions[1]) {
        const otherFunctions: any[] =
          Array.from(response[OwnerRelianceTabs.OTHERS]) || [];
        const newArray = [...otherFunctions]; // Create a shallow copy of the original array
        newArray[+businessFunction] = {
          // Update the specific element
          ...newArray[+businessFunction],
          current_function_owner: '',
          selected_option: value,
        };
        setResponse({
          ...response,
          [currentTab as string]: newArray,
        });
      } else {
        const otherFunctions: any[] =
          Array.from(response[OwnerRelianceTabs.OTHERS]) || [];
        const newArray = [...otherFunctions]; // Create a shallow copy of the original array
        newArray[+businessFunction] = {
          // Update the specific element
          ...newArray[+businessFunction], // Copy the properties of the element
          selected_option: value, // Update the selected_option property
        };
        setResponse({
          ...response,
          [currentTab as string]: newArray,
        });
      }
    } else if (value === headerOptions[2] || value === headerOptions[3]) {
      const ownerName = `${
        response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.first_name
      } ${response[OwnerRelianceTabs.MANAGEMENT_TEAM]?.last_name}`;
      setResponse({
        ...response,
        [currentTab as string]: {
          ...response[currentTab as string],
          [businessFunction]: {
            ...response[currentTab as string][businessFunction],
            current_function_owner: ownerName,
            selected_option: value,
          },
        },
      });
    } else if (value === headerOptions[1]) {
      setResponse({
        ...response,
        [currentTab as string]: {
          ...response[currentTab as string],
          [businessFunction]: {
            ...response[currentTab as string][businessFunction],
            current_function_owner: '',
            selected_option: value,
          },
        },
      });
    } else
      setResponse({
        ...response,
        [currentTab as string]: {
          ...response[currentTab as string],
          [businessFunction]: {
            ...response[currentTab as string][businessFunction],
            selected_option: value,
          },
        },
      });
  };

  const updateOtherBusinessFunction = (
    value: string | null,
    businessFunction: number
  ) => {
    if (currentTab === OwnerRelianceTabs.OTHERS) {
      const otherFunctions: any[] =
        Array.from(response[OwnerRelianceTabs.OTHERS]) || [];
      const newArray = [...otherFunctions]; // Create a shallow copy of the original array
      newArray[+businessFunction] = {
        // Update the specific element
        ...newArray[+businessFunction], // Copy the properties of the element
        business_function_name: value, // Update the business_function_name property
      };
      setResponse({
        ...response,
        [currentTab as string]: newArray,
      });
    } else {
      setResponse({
        ...response,
        [currentTab as string]: {
          ...response[currentTab as string],
          [businessFunction]: {
            ...response[currentTab as string][businessFunction],
            selected_option: value,
          },
        },
      });
    }
  };

  const addOthers = () => {
    const otherFunctions: any[] =
      Array.from(response[OwnerRelianceTabs.OTHERS]) || [];
    const newAdded = {
      selected_option: '',
      current_function_owner: '',
      business_function_name: 'Others',
    };
    otherFunctions.push(newAdded);
    setResponse({
      ...response,
      [OwnerRelianceTabs.OTHERS]: otherFunctions,
    });
  };

  const removeOthers = (indexToRemove: number) => {
    const otherFunctions: any[] =
      Array.from(response[OwnerRelianceTabs.OTHERS]) || [];
    const newArray = [...otherFunctions]; // Create a shallow copy of the original array
    newArray.splice(indexToRemove, 1); // Remove the specific eleme
    setResponse({
      ...response,
      [OwnerRelianceTabs.OTHERS]: newArray,
    });
  };
  useEffect(() => {
    tableRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
  }, [currentTab]);

  return (
    <>
      {isRenderedForReport ? (
        <div className={`${showReportTitle ? 'pt-16' : 'pt-40'} ${classname}`}>
          {showReportTitle && (
            <div>
              <span className='text-xl block font-semibold text-center text-blue-01 underline'>
                Scoring Group Results
              </span>
              <div className='flex justify-between gap-7 mt-5 px-40 font-semibold'>
                <span className='underline !text-lg'>
                  Score For {currentTab && getTitle(currentTab)}
                </span>
                <span className='text-blue-01 underline !text-lg'>
                  {scorePercentage}% Owner Reliant
                </span>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className='flex  justify-between items-center'>
          <div className='flex gap-7'>
            <span>Scoring Group:</span>
            <span className='text-blue-01 font-semibold'>
              {currentTab && getTitle(currentTab)}
            </span>
          </div>
          {currentTab && currentTab === OwnerRelianceTabs.OTHERS && (
            <Button onClick={addOthers} className='mr-7.5 px-5 py-1.5'>
              Add
            </Button>
          )}
        </div>
      )}

      <div
        className={` ${
          currentTab === OwnerRelianceTabs.OTHERS ? 'pr-8' : 'pr-3'
        }  ${
          isRenderedForReport
            ? 'h-full pointer-events-none mt-4 pr-0'
            : 'h-[calc(100vh-22rem)]'
        }  relative scrollbar overflow-auto w-full rounded-t-md`}
        ref={tableRef}
      >
        <div
          className={`bg-blue-01 z-10 tracking-[0.07rem] sticky top-0 grid grid-cols-7 overflow-hidden text-white
           font-[500] rounded-t-md  text-[0.9rem]`}
        >
          {headerOptions.map((h, index) => (
            <div
              key={h}
              className={`px-5  flex items-center border-r  border-gray-02   justify-center py-1 h-full ${
                index === 0 && 'border-l rounded-tl-md '
              } ${index === 6 && 'rounded-t-md  '} `}
            >
              <span className='text-center'>{h}</span>
            </div>
          ))}
        </div>
        <div>
          {currentTab &&
            Object.keys(response[currentTab]).map((businessFunction, index) => (
              <div
                key={businessFunction}
                className={classNames(
                  'grid grid-cols-7 font-[500]  text-[0.9rem] ',
                  emptyKeys?.includes(businessFunction ?? [])
                    ? 'bg-red-100'
                    : ''
                )}
              >
                <div
                  className={`px-1  2xl:px-3 ${
                    index === Object.keys(response[currentTab]).length - 1 &&
                    'rounded-bl-md '
                  }  flex items-center border-b border-l py-1 border-r border-gray-02`}
                >
                  {currentTab === OwnerRelianceTabs.OTHERS ? (
                    <div className='flex items-center w-full'>
                      <input
                        ref={(el) => {
                          inputRefs.current[index] = el;
                        }}
                        className='text-sm h-full w-[90%] border-none outline-none truncate'
                        value={
                          response[currentTab][+businessFunction]
                            ?.business_function_name
                        }
                        onChange={(event) => {
                          updateOtherBusinessFunction(
                            event.target.value,
                            +businessFunction
                          );
                        }}
                      />
                      {!isRenderedForReport && (
                        <MdEdit
                          onClick={() => handleEditIconClick(index)}
                          className='text-blue-01 text-lg cursor-pointer'
                        />
                      )}
                    </div>
                  ) : (
                    <span className='text-xs xl:text-sm '>
                      {businessFunction}
                      {!isRenderedForReport && (
                        <span className=' text-red-600 ml-1'>*</span>
                      )}
                    </span>
                  )}
                </div>
                <Radio
                  showLabel={false}
                  className='grid grid-cols-5 col-span-5  !gap-x-0 items-center border-b justify-center h-full border-gray-02'
                  className2='flex items-center  border-r !border-gray-02 !h-[4.2rem]'
                  inputClassName='!h-5 !mx-auto !cursor-pointer'
                  selected={
                    response[currentTab][businessFunction].selected_option
                  }
                  options={[
                    {
                      label: `${headerOptions[1]}`,
                      value: `${headerOptions[1]}`,
                    },
                    {
                      label: `${headerOptions[2]}`,
                      value: `${headerOptions[2]}`,
                    },
                    {
                      label: `${headerOptions[3]}`,
                      value: `${headerOptions[3]}`,
                    },
                    {
                      label: `${headerOptions[4]}`,
                      value: `${headerOptions[4]}`,
                    },
                    {
                      label: `${headerOptions[5]}`,
                      value: `${headerOptions[5]}`,
                    },
                  ]}
                  onChange={(value) => {
                    handleRadioClick(value, businessFunction);
                  }}
                />
                <div
                  className={`relative cursor-pointer ${
                    index === Object.keys(response[currentTab]).length - 1 &&
                    'rounded-br-md'
                  } border-b  border-r border-gray-02`}
                >
                  {isRenderedForReport ? (
                    <div className='flex h-full items-center justify-center'>
                      {response[currentTab][businessFunction]
                        .current_function_owner || '-'}
                    </div>
                  ) : (
                    <Select
                      menuListPadding='0'
                      menuPadding='0'
                      disabled={
                        response[currentTab][businessFunction]
                          .selected_option === headerOptions[1]
                      }
                      optionPadding='0.2rem'
                      placeholder='Select'
                      controlHeight='2.125rem'
                      selectClassName='px-4 mt-3'
                      options={currentFunctionOwners}
                      selectedOption={
                        response[currentTab][businessFunction]
                          .current_function_owner
                      }
                      onChange={(e: string) => {
                        handleDropDownSelect(e as string, businessFunction);
                      }}
                    />
                  )}
                  {!isRenderedForReport &&
                    currentTab === OwnerRelianceTabs.OTHERS && (
                      <MdDelete
                        className='absolute top-1/2 -translate-y-1/2  -right-6 text-red-500 cursor-pointer'
                        size={20}
                        onClick={() => {
                          removeOthers(+businessFunction);
                        }}
                      />
                    )}
                </div>
              </div>
            ))}
        </div>
      </div>
    </>
  );
};

export default memo(TableComponent);
