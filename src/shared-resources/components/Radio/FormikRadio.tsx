import { useField } from 'formik';
import React, { FC, memo } from 'react';
// components
import InputHelper from '../Inputhelper/InputHelper';
import Radio, { RadioProps } from './Radio';

interface FormikRadioProps extends Omit<RadioProps, 'onChange' | 'selected'> {
  name: string;
  valueChanged?: (val: any) => void;
}

const FormikRadio: FC<FormikRadioProps> = (props) => {
  const { name, valueChanged } = props;
  const [, meta, helpers] = useField<string | null>(name);

  const { value, error, touched } = meta;
  const { setValue, setTouched } = helpers;

  return (
    <>
      <Radio
        selected={value}
        onChange={(e) => {
          setValue(e);
          valueChanged?.(e);
        }}
        onBlur={() => {
          setTouched(true);
        }}
        {...props}
      />
      {error && touched && <InputHelper type='error' text={error} />}
    </>
  );
};

export default memo(FormikRadio);
