import { Form, Formik, FormikValues } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import FormikRadio from 'shared-resources/components/Radio/FormikRadio';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import * as yup from 'yup';
import {
  Screen10Keys,
  YesNoOptions,
  getQuestionBykey,
} from '../AssetProtectionConfig';

interface Props {
  data: any;
  handleNextClick: (values: FormikValues) => void;
  handlebackClick: (values: FormikValues) => void;
  handleSaveAsDraftClick: (values: FormikValues) => void;
}
const Screen10: React.FC<Props> = ({
  handleNextClick,
  handlebackClick,
  handleSaveAsDraftClick,
  data,
}) => {
  const validationSchema = yup.object().shape({
    [Screen10Keys.IS_YOUR_BUSINESS_SUBJECT_TO_SAFETY_RISKS]: yup
      .string()
      .required('Value is required'),
    [Screen10Keys.DO_YOU_PROVIDE_TRAINING_TO_EMPLOYEES]: yup
      .string()
      .when([Screen10Keys.IS_YOUR_BUSINESS_SUBJECT_TO_SAFETY_RISKS], {
        is: (value: string) => value === 'yes',
        then: (schema) => schema.required('value is Required'),
        otherwise: (schema) => schema.nullable(),
      }),
    [Screen10Keys.DOES_YOUR_CURRENT_INSURANCE_PROTECT]: yup
      .string()
      .when(
        [
          Screen10Keys.IS_YOUR_BUSINESS_SUBJECT_TO_SAFETY_RISKS,
          Screen10Keys.DO_YOU_PROVIDE_TRAINING_TO_EMPLOYEES,
        ],
        {
          is: (safetyRisks: string, training: string) =>
            safetyRisks === 'yes' && training === 'yes',
          then: (schema) => schema.required('value is Required'),
          otherwise: (schema) => schema.nullable(),
        }
      ),
  });

  const initialValue = data?.screen10 || {
    [Screen10Keys.IS_YOUR_BUSINESS_SUBJECT_TO_SAFETY_RISKS]: '',
    [Screen10Keys.DO_YOU_PROVIDE_TRAINING_TO_EMPLOYEES]: '',
    [Screen10Keys.DOES_YOUR_CURRENT_INSURANCE_PROTECT]: '',
  };

  const isSubmitting = useSelector(getAssessmentToolLoading);

  return (
    <div className='flex flex-col h-full  gap-6 pl-10 py-10 font-medium'>
      <Formik
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={(values: FormikValues) => {
          handleNextClick(values);
        }}
        enableReinitialize
      >
        {({ values, setFieldValue, setTouched }) => (
          <Form className='flex flex-col justify-between '>
            <div className='h-[calc(100vh-20rem)] overflow-auto scrollbar'>
              <div>
                <div className='mt-4'>
                  <span className='font-semibold'>
                    {getQuestionBykey(
                      Screen10Keys.IS_YOUR_BUSINESS_SUBJECT_TO_SAFETY_RISKS
                    )}
                  </span>
                  <div className='mt-5 xl:w-[50%]'>
                    <FormikRadio
                      valueChanged={() => {
                        setTouched({
                          [Screen10Keys.IS_YOUR_BUSINESS_SUBJECT_TO_SAFETY_RISKS]:
                            false,
                        });
                        setFieldValue(
                          Screen10Keys.DO_YOU_PROVIDE_TRAINING_TO_EMPLOYEES,
                          ''
                        );
                        setFieldValue(
                          Screen10Keys.DOES_YOUR_CURRENT_INSURANCE_PROTECT,
                          ''
                        );
                      }}
                      labelClassName='!cursor-pointer'
                      className2='!w-fit'
                      className='grid grid-cols-2 gap-4'
                      name={
                        Screen10Keys.IS_YOUR_BUSINESS_SUBJECT_TO_SAFETY_RISKS
                      }
                      options={YesNoOptions}
                    />
                  </div>
                </div>
                {values[
                  Screen10Keys.IS_YOUR_BUSINESS_SUBJECT_TO_SAFETY_RISKS
                ] === 'yes' && (
                  <div className='mt-4'>
                    <span className='font-semibold'>
                      {getQuestionBykey(
                        Screen10Keys.DO_YOU_PROVIDE_TRAINING_TO_EMPLOYEES
                      )}
                    </span>

                    <div className='mt-5 xl:w-[50%]'>
                      <FormikRadio
                        labelClassName='!cursor-pointer'
                        className2='!w-fit'
                        valueChanged={() => {
                          setTouched({
                            [Screen10Keys.DO_YOU_PROVIDE_TRAINING_TO_EMPLOYEES]:
                              false,
                          });
                          setFieldValue(
                            Screen10Keys.DOES_YOUR_CURRENT_INSURANCE_PROTECT,
                            ''
                          );
                        }}
                        className='grid grid-cols-2 gap-4'
                        name={Screen10Keys.DO_YOU_PROVIDE_TRAINING_TO_EMPLOYEES}
                        options={YesNoOptions}
                      />
                    </div>
                  </div>
                )}

                {values[
                  Screen10Keys.IS_YOUR_BUSINESS_SUBJECT_TO_SAFETY_RISKS
                ] === 'yes' &&
                  values[Screen10Keys.DO_YOU_PROVIDE_TRAINING_TO_EMPLOYEES] ===
                    'yes' && (
                    <div className='mt-4'>
                      <span className='font-semibold'>
                        {getQuestionBykey(
                          Screen10Keys.DOES_YOUR_CURRENT_INSURANCE_PROTECT
                        )}
                      </span>

                      <div className='mt-5 xl:w-[50%]'>
                        <FormikRadio
                          labelClassName='!cursor-pointer'
                          className2='!w-fit'
                          className='grid grid-cols-2 gap-4'
                          name={
                            Screen10Keys.DOES_YOUR_CURRENT_INSURANCE_PROTECT
                          }
                          options={YesNoOptions}
                        />
                      </div>
                    </div>
                  )}
              </div>
            </div>
            <div className='pr-5'>
              <BackNextComponent
                backStep={() => handlebackClick(values)}
                buttonType='submit'
                isLoading={isSubmitting}
                isNextDisable={false}
                onSaveToDraftClick={() => {
                  handleSaveAsDraftClick(values);
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Screen10;
