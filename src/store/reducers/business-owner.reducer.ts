import {
  createEntityAdapter,
  createSlice,
  EntityId,
  EntityState,
  PayloadAction,
} from '@reduxjs/toolkit';
import { uniq } from 'lodash';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import { BaseEntityStore } from 'store/types/BaseEntityStoreType';
import {
  AssessmentData,
  AssessmentDataMapping,
  BusinessOwnerLearnMore,
  BusinessOwnersIndustries,
} from 'types/business-owner.types';
import {
  AssessmentStage,
  AssessmentTools,
  AssessmentToolStatus,
} from 'types/enum';
import { planDevelopmentTabs } from 'utils/helpers/Helpers';
import { PaginationMeta } from '../../types/pagination.types';

const businessOwnerAdapter = createEntityAdapter<BusinessOwner>();

const initialState: EntityState<BusinessOwner> &
  BaseEntityStore &
  AssessmentDataMapping &
  PaginationMeta &
  BusinessOwnersIndustries &
  BusinessOwnerLearnMore = {
  ...businessOwnerAdapter.getInitialState(),
  assessmentDataMapping: {},
  create: {},
  update: {},
  get: {},
  list: {},
  paginationMeta: {},
  industries: { items: [], loading: false },
  learnMoreLoading: false,
};

export const businessOwnerSlice = createSlice({
  name: 'business_owner',
  initialState,
  reducers: {
    add_many: businessOwnerAdapter.addMany,
    add_one: businessOwnerAdapter.setOne,
    set_business_owners: (
      state,
      action: PayloadAction<{
        ids: EntityId[];
        loadMore: boolean;
      }>
    ) => {
      const { ids, loadMore } = action.payload;
      state.ids = loadMore ? uniq([...state.ids, ...ids]) : ids;
    },
    set_created_business_owner: (
      state,
      action: PayloadAction<{ owner: BusinessOwner }>
    ) => {
      const { owner } = action.payload;
      const entity = { [owner.id]: owner };
      state.entities = { ...state.entities, ...entity };
      state.ids = [owner.id, ...state.ids];
    },
    set_update_business_owner: (state, action: any) => {
      const payload = action?.payload;
      const businessOwner = payload?.business_owner;

      if (payload && businessOwner) {
        const { id } = businessOwner;
        const entity = { [id]: businessOwner };
        state.entities = { ...state.entities, ...entity };
        state.ids = state.ids.map((id) =>
          id === businessOwner.id ? businessOwner.id : id
        );
      }
    },
    set_delete_business_owner: (state, action: any) => {
      const payload = action?.payload;
      const businessOwner = payload?.business_owner;
      if (payload && businessOwner) {
        const { id } = businessOwner;
        const entity = { [id]: businessOwner };
        state.entities = { ...state.entities, ...entity };
        state.ids = state.ids.filter((i) => i !== id);
      }
    },

    set_transfer_business_owner: (state, action: any) => {
      const payload = action?.payload;
      const businessOwner = payload?.business_owner;
      if (payload && businessOwner) {
        const { id } = businessOwner;
        const entity = { [id]: businessOwner };
        state.entities = { ...state.entities, ...entity };
        state.ids = state.ids.filter((i) => i !== id);
      }
    },

    set_create_business_owner_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.create.loading = loading;
    },
    set_update_business_owner_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.update.loading = loading;
    },
    set_create_business_owner_error: (
      state,
      action: PayloadAction<{ error: boolean; message?: string; errors?: any }>
    ) => {
      const { error, message, errors } = action.payload;
      state.create.error = error;
      state.create.errors = errors;
      state.create.message = message;
      state.create.loading = false;
    },
    set_update_business_owner_error: (
      state,
      action: PayloadAction<{ error: boolean; message?: string; errors?: any }>
    ) => {
      const { error, message, errors } = action.payload;
      state.update.error = error;
      state.update.errors = errors;
      state.update.message = message;
      state.update.loading = false;
    },
    set_get_business_owner_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.get = { ...state.get, loading };
    },
    set_get_business_owner_error: (
      state,
      action: PayloadAction<{ error: boolean; message?: string }>
    ) => {
      const { error, message } = action.payload;
      state.get = { ...state.get, loading: false, error, message };
    },
    set_business_owners_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.list = { ...state.list, loading };
    },
    set_business_owners_error: (
      state,
      action: PayloadAction<{ error: boolean; message?: string }>
    ) => {
      const { error, message } = action.payload;
      state.list = { ...state.list, error, message };
    },
    set_assessment_data: (
      state,
      action: PayloadAction<AssessmentDataMapping>
    ) => {
      const { assessmentDataMapping } = action.payload;

      state.assessmentDataMapping = {
        ...state.assessmentDataMapping,
        ...assessmentDataMapping,
      };
    },
    update_assessment_data: (
      state,
      action: PayloadAction<{
        tool: AssessmentTools;
        status: AssessmentToolStatus;
        id: number;
        assessmentData: AssessmentData;
        isReportSentToOwner?: boolean;
      }>
    ) => {
      const { tool, status, id, assessmentData, isReportSentToOwner } =
        action.payload;
      const stage = planDevelopmentTabs.includes(tool)
        ? AssessmentStage.PLAN_DEVELOPMENT
        : AssessmentStage.AWARENESS;
      const updatedAssessmentData = {
        ...assessmentData,
        [stage]: {
          ...assessmentData?.[stage],
          [tool]: {
            ...assessmentData?.[stage]?.[tool],
            status,
            is_report_sent_to_owner: isReportSentToOwner,
          },
        },
      };
      state.assessmentDataMapping = {
        ...state.assessmentDataMapping,
        [id]: updatedAssessmentData,
      };
    },
    set_page_data: (state, action: PayloadAction<PaginationMeta>) => {
      state.paginationMeta = action.payload.paginationMeta;
    },
    set_industries: (
      state,
      action: PayloadAction<{ industries: string[] }>
    ) => {
      const { industries } = action.payload;
      state.industries.items = industries.map((industry) => ({
        label: industry,
        value: industry,
      }));
      state.industries.loading = true;
    },
    clear_industries: (state) => {
      state.industries.items = [];
    },
    set_industries_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.industries.loading = loading;
    },
    set_learn_more_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.learnMoreLoading = loading;
    },
  },
});

export const {
  add_many: addBusinessOwners,
  add_one: addBusinessOwner,
  set_created_business_owner: setCreatedBusinessOwner,
  set_create_business_owner_loading: setCreateBusinessOwnerLoading,
  set_create_business_owner_error: setCreateBusinessOwnerError,
  set_update_business_owner_loading: setUpdateBusinessOwnerLoading,
  set_update_business_owner_error: setUpdateBusinessOwnerError,
  set_get_business_owner_loading: setGetBusinessOwnerLoading,
  set_get_business_owner_error: setGetBusinessOwnerError,
  set_assessment_data: SetBusinessOwnerAssessmentData,
  update_assessment_data: updateBusinessOwnerAssessmentData,
  set_business_owners_loading: setBusinessOwnersLoading,
  set_business_owners_error: setBusinessOwnersError,
  set_page_data: setBusinessOwnersPageData,
  set_business_owners: setBusinessOwners,
  set_industries: addBusinessOwnerIndustries,
  clear_industries: clearBusinessOwnerIndustries,
  set_industries_loading: setBusinessOwnerIndustriesLoading,
  set_learn_more_loading: setLearnMoreLoading,
  set_update_business_owner: setUpdateBusinessOwner,
  set_delete_business_owner: setDeleteBusinessOwner,
  set_transfer_business_owner: setTransferBusinessOwner,
} = businessOwnerSlice.actions;

export default businessOwnerSlice.reducer;
