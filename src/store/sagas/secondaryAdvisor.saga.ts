import { toast } from 'react-toastify';
import { call, takeLatest, all, put } from 'redux-saga/effects';
import { secondaryAdvisorService } from 'services/api-services/SecondaryAdvisorService';
import { SecondaryAdvisorActionType } from 'store/actions/actions.constants';
import {
  SinUpSecondaryAdvisorByTokenPayload,
  VerifySecondaryAdvisorByTokenPayload,
} from 'store/actions/secondary-advisor.action';
import {
  addVerifyLoading,
  addVerifyLoadingOne,
  verficationCompletedAction,
} from 'store/reducers/secondary-advisor.reducer';
import { SagaPayloadType } from 'types/SagaPayload.type';

interface AdvisorVerifyPayloadType extends SagaPayloadType {
  payload: VerifySecondaryAdvisorByTokenPayload;
}
interface SecondaryAdvisorInvitePayloadType extends SagaPayloadType {
  payload: SinUpSecondaryAdvisorByTokenPayload;
}

function* verifySecondaryAdvisorSaga(action: AdvisorVerifyPayloadType): any {
  const { onError, ...data } = action.payload;
  try {
    yield put(addVerifyLoading({ loading: true }));

    const response = yield call(
      secondaryAdvisorService.verifyNewAdvisor,
      data.token
    );

    yield put(verficationCompletedAction(response));
    yield put(addVerifyLoading({ loading: false }));
  } catch (e: any) {
    if (onError) {
      onError();
    }
    if (e) {
      yield put(addVerifyLoading({ loading: false }));
    }
  }
}
function* signUpSecondaryAdvisorSaga(
  action: SecondaryAdvisorInvitePayloadType
): any {
  const { onSucess, ...data } = action.payload;

  try {
    yield put(addVerifyLoadingOne({ loading: true }));

    yield call(secondaryAdvisorService.signUpSecondaryAdvisor, data);

    yield put(addVerifyLoadingOne({ loading: false }));
    if (onSucess) {
      onSucess();
    }
    toast.success('Signed Up Succesfully');
  } catch (e: any) {
    if (e) {
      yield put(addVerifyLoadingOne({ loading: false }));
    }
  }
}

export function* secondaryAdvisorVerifySagaWatcher() {
  yield all([
    takeLatest(
      SecondaryAdvisorActionType.VERIFY_SECONDARY_ADVISOR,
      verifySecondaryAdvisorSaga
    ),
    takeLatest(
      SecondaryAdvisorActionType.SIGNUP_SECONDARY_ADVISOR,
      signUpSecondaryAdvisorSaga
    ),
  ]);
}
