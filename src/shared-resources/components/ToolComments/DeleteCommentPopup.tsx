import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Modal from 'shared-resources/components/Modal/Modal';
import Button from 'shared-resources/components/Button/Button';
import { deleteComment } from 'store/actions/tool-comment.action';
import { getToolCommentsLoading } from 'store/selectors/tool-comment.selector';

interface DeleteCommentPopupProps {
  isOpen: boolean;
  onClose: () => void;
  commentId: number;
}

const DeleteCommentPopup: React.FC<DeleteCommentPopupProps> = ({
  isOpen,
  onClose,
  commentId,
}) => {
  const dispatch = useDispatch();
  const isLoading = useSelector(getToolCommentsLoading);

  const handleDelete = () => {
    dispatch(deleteComment(commentId));
    onClose();
  };

  return (
    <Modal
      visible={isOpen}
      handleVisibility={onClose}
      title='Delete Comment'
      classname='w-[400px]'
    >
      <div className='p-6'>
        <p className='mb-4'>Are you sure you want to delete this comment?</p>
        <div className='flex justify-end gap-4'>
          <Button
            onClick={handleDelete}
            disabled={isLoading}
            isSubmitting={isLoading}
            className='px-6 py-2 bg-red-01 border-red-01'
          >
            Delete
          </Button>
          <Button
            onClick={onClose}
            theme='secondary'
            disabled={isLoading}
            className='px-6 py-2'
          >
            Cancel
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteCommentPopup;
