import React, { useEffect, useState } from 'react';
import { IoCloudUploadOutline } from 'react-icons/io5';
import Input from 'shared-resources/components/Input/Input';
import { AssessmentTools, NonAssessmentToolTabs, UserType } from 'types/enum';
import { FaDownload } from 'react-icons/fa6';
import { FaFile, FaPaperPlane } from 'react-icons/fa';
import { TiDelete } from 'react-icons/ti';
import Button from 'shared-resources/components/Button/Button';
import { toast } from 'react-toastify';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchDocuments,
  uploadDocuments,
  getValuationAmount as getValuationAmountAction,
  updateValuationAmount,
} from 'store/actions/document.action';
import { sendReportToBO } from 'store/actions/report-builder.action';
import { useParams } from 'react-router';
import {
  documentsDataSelector,
  documentsListLoading,
  documentsLoading,
  getValuationAmount,
  getValuationAmountLoading,
} from 'store/selectors/document.selector';
import { getUserData } from 'store/selectors/user.selector';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import { setDocumentsLoading } from 'store/reducers/documents.reducer';
import { getReportType } from 'utils/helpers/Helpers';
import { reportService } from 'services/api-services/ReportBuilderService';
import { BiLoaderCircle } from 'react-icons/bi';
import { formatCurrency } from 'views/FinancialGapAnalysis/ExpenseCalculator/ExpenseCalculatorConfig';
import { isNaN } from 'lodash';

interface DocumentsModalProps {
  tool?: string;
  onCancelClick: (value: boolean) => void;
  setSelectedDocument: (value: any) => void;
  setShowDeleteDocumentPopUp: (value: boolean) => void;
}

const DocumentsModal: React.FC<DocumentsModalProps> = ({
  tool,
  onCancelClick,
  setSelectedDocument,
  setShowDeleteDocumentPopUp,
}) => {
  const documentData = useSelector(documentsDataSelector) as any;
  const listLoading = useSelector(documentsListLoading);
  const loading = useSelector(documentsLoading);
  const valuationAmount = useSelector(getValuationAmount);
  const isLoadingAmount = useSelector(getValuationAmountLoading);
  const userData = useSelector(getUserData);

  const { id } = useParams();
  const dispatch = useDispatch();
  const [tempData, setTempData] = useState<File[]>([]);
  const [disableInput, setDisableInput] = useState(false);
  const [tempValuationAmount, setTempValuationAmount] = useState<string>('');
  const [currentClickedDocument, setCurrentClickedDocument] =
    useState<any>(null);

  const isBusinessOwner = userData?.type === UserType.BUSINESS_OWNER;

  useEffect(() => {
    if (valuationAmount !== null && valuationAmount !== undefined) {
      setTempValuationAmount(valuationAmount.toString());
    } else {
      setTempValuationAmount('');
    }
  }, [valuationAmount]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const filesArray = Array.from(e.target.files).map((file) => file);
      if (filesArray.length > 4) {
        toast.error('You can upload maximum of 4 files');
      } else setTempData([...tempData, ...filesArray]);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files) {
      const filesArray = Array.from(e.dataTransfer.files);
      if (tempData.length + filesArray.length > 4) {
        toast.error('You can upload maximum of 4 files');
        return;
      }
      setTempData([...tempData, ...filesArray]);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragEnter = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleSave = () => {
    if (
      id &&
      tempValuationAmount !== valuationAmount?.toString() &&
      tool === AssessmentTools.BUSINESS_VALUATION
    ) {
      dispatch(
        updateValuationAmount({
          businessOwnerId: +id,
          valuationAmount: parseInt(tempValuationAmount, 10) || 0,
        })
      );
    }

    if (tempData.length > 0 && id) {
      dispatch(
        uploadDocuments({
          file: tempData,
          id,
          onSuccess: () => {},
          type: getReportType(tool as AssessmentTools | NonAssessmentToolTabs),
        })
      );
      setTempData([]);
    }
  };

  const isSaveEnabled = () =>
    tempData.length > 0 ||
    (tempValuationAmount !== valuationAmount?.toString() &&
      tempValuationAmount !== '');

  const handleDownload = (item: any) => {
    setCurrentClickedDocument(item);
    dispatch(setDocumentsLoading({ loading: true }));
    const reportApi =
      userData?.type === UserType.ADVISOR
        ? reportService.getReportByAdvisor(+id!, item.id)
        : reportService.getReportByBO(item.id);
    reportApi
      .then((res) => {
        const fileUrl = res.report.url;

        const link = document.createElement('a');

        fetch(fileUrl || '')
          .then((response) => response.blob())
          .then((blob) => {
            const blobUrl = URL.createObjectURL(blob);
            link.href = blobUrl;

            link.setAttribute('download', item.title);

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            URL.revokeObjectURL(blobUrl);

            dispatch(setDocumentsLoading({ loading: false }));
            toast.success('Document Started Downloading');
            setCurrentClickedDocument(null);
          })
          .catch(() => {
            toast.error('Error downloading document');
            dispatch(setDocumentsLoading({ loading: false }));
            setCurrentClickedDocument(null);
          });
      })
      .catch(() => {
        toast.error('Error retrieving document URL');
        dispatch(setDocumentsLoading({ loading: false }));
      });
  };

  const handleSendToBO = (item: any) => {
    dispatch(sendReportToBO(item.id, true));
  };

  useEffect(() => {
    if (tempData.length > 0) {
      setDisableInput(true);
    } else {
      setDisableInput(false);
    }
  }, [tempData]);

  useEffect(() => {
    if (userData && tool && !loading && !listLoading) {
      dispatch(
        fetchDocuments({
          id: userData?.type === UserType.ADVISOR ? id! : undefined,
          type: getReportType(tool as AssessmentTools | NonAssessmentToolTabs),
        })
      );
    }
  }, [id, userData, tool]);

  useEffect(() => {
    if (tool === AssessmentTools.BUSINESS_VALUATION) {
      if (userData?.type === UserType.ADVISOR && id) {
        dispatch(getValuationAmountAction({ businessOwnerId: +id }));
      } else {
        dispatch(getValuationAmountAction({}));
      }
    }
  }, [id, userData, tool]);

  return (
    <div className='flex flex-row p-8 justify-between space-x-6'>
      <div className='flex flex-col justify-between w-[50%]'>
        {tool === AssessmentTools.BUSINESS_VALUATION && (
          <Input
            label='Valuation Amount ($)'
            type='text'
            value={
              tempValuationAmount
                ? formatCurrency(Number(tempValuationAmount)).replace('$', '')
                : ''
            }
            onChange={(e) => {
              const value = e.target.value.replaceAll(',', '');
              const numValue = Number(value);
              if (!isNaN(numValue) || value === '') {
                setTempValuationAmount(value);
              }
            }}
            disabled={isLoadingAmount || isBusinessOwner}
          />
        )}
        {!isBusinessOwner && (
          <div className='flex flex-col'>
            <h1 className='font-medium mb-2'>Upload a Document</h1>
            <div className='flex items-center justify-center w-full'>
              <label
                htmlFor='dropzone-file'
                className='flex flex-col items-center justify-center w-full h-[20rem] border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100'
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
              >
                {tempData.length > 0 && (
                  <div className='relative flex space-x-6'>
                    {tempData.map((file, index) => (
                      <div className='flex  flex-col' key={file?.name}>
                        <TiDelete
                          size={20}
                          onClick={() => {
                            setTempData((prevData) =>
                              prevData.filter((_, i) => i !== index)
                            );
                          }}
                          className='text-gray-600 items-start ml-8 cursor-pointer'
                        />{' '}
                        <FaFile
                          size={40}
                          className='mb-3 text-gray-500 pointer-events-none'
                        />
                        <h1 className='w-12 text-sm text-nowrap truncate'>
                          {file?.name}
                        </h1>
                      </div>
                    ))}
                  </div>
                )}
                {tempData.length === 0 && (
                  <>
                    <div className='flex flex-col items-center justify-center pt-5 pb-6'>
                      <IoCloudUploadOutline
                        size={40}
                        className='mb-3 text-gray-400'
                      />
                      <p className='mb-2 text-xs text-gray-500 dark:text-gray-400'>
                        <span className='font-semibold'>Click to upload</span>{' '}
                        or drag and drop
                      </p>
                    </div>
                    {!disableInput && (
                      <input
                        id='dropzone-file'
                        type='file'
                        accept='*/*'
                        className='hidden'
                        multiple
                        onChange={handleFileChange}
                      />
                    )}
                  </>
                )}
              </label>
            </div>
            <div className='flex justify-end mt-4 space-x-4'>
              <Button
                className='px-6 py-2 w-[7.55rem]'
                disabled={!isSaveEnabled()}
                type='button'
                isSubmitting={loading || isLoadingAmount}
                onClick={handleSave}
              >
                Save
              </Button>
              <Button
                className='px-6 py-2'
                theme='secondary'
                type='button'
                onClick={() => onCancelClick(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </div>
      <div
        className={`flex flex-col ${
          isBusinessOwner && tool !== AssessmentTools.BUSINESS_VALUATION
            ? 'w-full'
            : 'w-[50%]'
        }`}
      >
        <h1 className='font-medium mb-4'>Documents</h1>
        {listLoading ? (
          <div className='h-48 flex justify-center '>
            <Spinner />
          </div>
        ) : (
          <div
            className={`flex flex-col space-y-4 pb-4 pr-4 ${
              documentData?.length === 0
                ? ''
                : 'h-[25rem] overflow-auto scrollbar'
            }`}
          >
            {documentData.length === 0 ? (
              <h1 className='text-center text-gray-500 mt-5'>
                No data available
              </h1>
            ) : (
              documentData?.map?.((item: any) => (
                <div
                  key={item?.id}
                  className='border shadow-lg rounded-lg px-4 py-4 flex items-center justify-between'
                >
                  <h1>{item?.title}</h1>
                  <div className='flex space-x-2 items-center'>
                    {!isBusinessOwner && !item.is_report_sent_to_owner && (
                      <FaPaperPlane
                        size={15}
                        onClick={() => handleSendToBO(item)}
                        className='text-gray-700 cursor-pointer'
                        title='Send to Business Owner'
                      />
                    )}
                    {currentClickedDocument?.id === item?.id && loading ? (
                      <BiLoaderCircle
                        size={15}
                        className='text-gray-700 cursor-pointer animate-spin'
                      />
                    ) : (
                      <FaDownload
                        size={15}
                        onClick={() => handleDownload(item)}
                        className='text-gray-700 cursor-pointer'
                      />
                    )}
                    {!isBusinessOwner && (
                      <TiDelete
                        size={20}
                        className='text-gray-700 cursor-pointer'
                        onClick={() => {
                          setSelectedDocument(item);
                          setShowDeleteDocumentPopUp(true);
                        }}
                      />
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentsModal;
