import React, { FC, memo } from 'react';
import Button from 'shared-resources/components/Button/Button';

interface Props {
  onGetStartedClick: () => void;
}

const GetStartedPage: FC<Props> = ({ onGetStartedClick }) => (
  <div className='h-[calc(100vh-12.4375rem)] flex flex-col gap-10 justify-center items-center mx-auto rounded-xl bg-white'>
    <p className='font-medium text-lg px-24'>
      This Transition Objectives Questionnaire is likely far more important than
      you realize. If you are considering a full exit, you need to know that a
      full 75% of Business Owners are unhappy about their decision to exit one
      year post-exit. The reason is not money. The unhappiness stems from not
      clearly and fully considering the next phase of life. This Questionnaire
      contains some open-ended questions. For your own sake, and to stay out of
      the 75% unhappy crowd, please take time when thinking through and
      recording your responses.
    </p>
    <div>
      <Button className='px-6 py-2' onClick={() => onGetStartedClick()}>
        Get Started
      </Button>
    </div>
  </div>
);
export default memo(GetStartedPage);
