import axios from 'axios';
import {
  ReportListResponseData,
  ReportResponseData,
} from 'types/report-builder.type';
import { ReportType } from 'types/enum';
import { BaseApiService, baseApiService } from './BaseApiService';

class ReportService {
  static getInstance(): ReportService {
    return new ReportService();
  }

  public async getReportPreSignedUrl(
    businessOwnerId: number,
    fileName: string,
    contentType: string,
    reportType: ReportType
  ) {
    return baseApiService.post(
      `business-owner/${businessOwnerId}/report/get-presigned-url`,
      {
        file_name: fileName,
        content_type: contentType,
        type: reportType,
      }
    );
  }

  public async uploadFileToS3(file: File, presignedUrl: string) {
    return axios.put(presignedUrl, file);
  }

  public async listReportsToAdvisor(
    businessOwnerId: number,
    reportType: ReportType
  ): Promise<ReportListResponseData> {
    return baseApiService.get(`business-owner/${businessOwnerId}/report/`, {
      params: {
        report_type: reportType,
      },
    });
  }

  public async listReportsToBO(
    reportType: ReportType
  ): Promise<ReportListResponseData> {
    return baseApiService.get('business-owner/report', {
      params: {
        report_type: reportType,
      },
    });
  }

  public async getReportByAdvisor(
    businessOwnerId: number,
    reportId: number
  ): Promise<ReportResponseData> {
    return baseApiService.get(
      `business-owner/${businessOwnerId}/report/${reportId}`
    );
  }

  public async getReportByBO(reportId: number): Promise<ReportResponseData> {
    return baseApiService.get(`business-owner/report/${reportId}`);
  }

  public async sendReportToBO(reportId: number, status: boolean) {
    return baseApiService.post(`business-owner/report/${reportId}/send-bo`, {
      status,
    });
  }

  public async getReportFileFromS3(s3Url: string): Promise<File> {
    const response = await axios.get(s3Url, { responseType: 'blob' });
    const fileName = s3Url.split('/').pop() ?? 'report';
    return new File([response.data], fileName, { type: response.data.type });
  }

  public async saveReport(
    businessOwnerId: number,
    reportTitle: string,
    url: string,
    type: ReportType
  ) {
    const splitUrl = url.split('?');
    const reportUrl = splitUrl?.[0];

    return baseApiService.post(
      `business-owner/${businessOwnerId}/report/save`,
      {
        title: reportTitle,
        url: reportUrl,
        type,
      }
    );
  }

  public async deleteDocument(id: number, report_id: number) {
    return baseApiService.delete(`business-owner/${id}/report/${report_id}`);
  }

  public async uploadFile(file: File, payload: any, id: number) {
    const { type, fileName, contentType } = payload;
    const presignedUrlResponse =
      await ReportService.getInstance().getReportPreSignedUrl(
        id,
        fileName,
        contentType,
        type
      ); // Cal the instance method

    if (presignedUrlResponse?.url) {
      const presignedUrl = presignedUrlResponse.url;
      await ReportService.getInstance().uploadFileToS3(file, presignedUrl); // Call the instance method

      return {
        url: presignedUrl.split('?')[0],
      };
    }

    throw new Error('Failed to obtain a valid presigned URL');
  }

  public async getValuationAmountForAdvisor(id: number) {
    return baseApiService.get(`/${BaseApiService.loggedInUserType}/business-owner/${id}/valuation-amount`);
  }

  public async getValuationAmountForBO() {
    return baseApiService.get('/business-owner/valuation-amount', {
      extras: { useAuth: true },
    });
  }

  public async updateValuationAmount(
    id: number,
    data: { valuation_amount: number }
  ) {
    return baseApiService.post(`/${BaseApiService.loggedInUserType}/business-owner/${id}/valuation-amount`, data);
  }
}

export const reportService = ReportService.getInstance();
