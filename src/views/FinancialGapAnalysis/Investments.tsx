import { Form, Formik } from 'formik';
import React from 'react';
import { IoInformationCircle } from 'react-icons/io5';
import { useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import * as yup from 'yup';
import { formatCurrency } from './ExpenseCalculator/ExpenseCalculatorConfig';
import {
  InvestmentsKeys,
  getCompundedInvestments,
  getKeyTitle,
} from './FinancialGapAnalysisConfig';

interface Props {
  nextStep: () => void;
  setData: (data: any) => void;
  backStep: () => void;
  data: any;
  onSaveToDraftClick: (data: any) => void;
}
const Investments: React.FC<Props> = (props) => {
  const { nextStep, setData, backStep, data, onSaveToDraftClick } = props;
  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup.object().shape({
    [InvestmentsKeys.TODAY_IN_INVESTMENTS]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [InvestmentsKeys.EXPECTED_INVESTMENT_GROWTH_RATE]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [InvestmentsKeys.CURRENT_AGE]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [InvestmentsKeys.EXPECT_TO_EXIT]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required')
      .test(
        'max_age',
        'Age must be less than 99',
        (value) => parseInt(value, 10) < 99
      )
      .test(
        'current_age',
        'Expected age must be greater than Current Age',
        (value, context) =>
          parseInt(value, 10) >
          parseInt(context.parent[InvestmentsKeys.CURRENT_AGE], 10)
      ),
    [InvestmentsKeys.YEARS_TO_EXIT]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
    [InvestmentsKeys.COMPOUNDED_INVESTMENTS]: yup
      .string()
      .matches(/^-?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/, 'Must be a valid number')
      .required('Value is required'),
  });
  const initialValue = data?.investments || {
    [InvestmentsKeys.TODAY_IN_INVESTMENTS]: '',
    [InvestmentsKeys.EXPECTED_INVESTMENT_GROWTH_RATE]: '',
    [InvestmentsKeys.CURRENT_AGE]: '',
    [InvestmentsKeys.EXPECT_TO_EXIT]: '',
    [InvestmentsKeys.YEARS_TO_EXIT]: '',
    [InvestmentsKeys.COMPOUNDED_INVESTMENTS]: '',
  };

  const handleSubmit = (values: any) => {
    nextStep();
    setData(values);
  };

  return (
    <div className='flex flex-col '>
      <Formik
        initialValues={initialValue}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnBlur
      >
        {({ values, setFieldValue }) => (
          <Form className='pl-5 py-3 bg-white'>
            <h2 className='text-xl font-semibold mt-7 underline'>
              Investments
            </h2>
            <div className='flex flex-col gap-6 h-[calc(100vh-25rem)] mt-12 overflow-auto scrollbar pr-4 text-[1rem]'>
              <div className='grid grid-cols-2 gap-x-10 items-end'>
                <FormikInput
                  leadingIcon='$'
                  labelIcon2={
                    <div className='flex  items-center gap-2'>
                      <IoInformationCircle
                        id='todayInvestments'
                        className='text-blue-01'
                      />
                      <div className='relative'>
                        <Tooltip
                          anchorSelect='#todayInvestments'
                          place='left-start'
                          offset={0}
                          className=' !bg-gray-100 border !z-10  !w-96 !font-[500]  !left-1 !top-7  !text-black-01 !text-[0.9rem] !rounded-lg'
                          classNameArrow='border-r  border-b  !w-[1.1rem] !h-3.5 !-left-[0.5rem] !top-[0.3rem]'
                        >
                          Include all stocks , bonds , 401k and other
                          investments that can be converted to cash, If needed
                          to fund post transition living.
                        </Tooltip>
                      </div>
                    </div>
                  }
                  valueChanged={(event) => {
                    const value = `${getCompundedInvestments(
                      values[InvestmentsKeys.YEARS_TO_EXIT],
                      Number(event.target.value.replaceAll(',', '')),
                      Number(
                        values[InvestmentsKeys.EXPECTED_INVESTMENT_GROWTH_RATE]
                      )
                    ).toFixed()}`;
                    setFieldValue(
                      InvestmentsKeys.COMPOUNDED_INVESTMENTS,
                      formatCurrency(Number(value)).toString().replace('$', '')
                    );
                    setFieldValue(
                      InvestmentsKeys.TODAY_IN_INVESTMENTS,
                      formatCurrency(
                        Number(event.target.value.replaceAll(',', ''))
                      )
                        .toString()
                        .replace('$', '')
                    );
                  }}
                  name={`${InvestmentsKeys.TODAY_IN_INVESTMENTS}`}
                  key={`${InvestmentsKeys.TODAY_IN_INVESTMENTS}`}
                  label={getKeyTitle(InvestmentsKeys.TODAY_IN_INVESTMENTS)}
                  labelClassName='!text-[1rem]'
                />
                <FormikInput
                  valueChanged={(event) => {
                    const value = `${getCompundedInvestments(
                      values[InvestmentsKeys.YEARS_TO_EXIT],
                      Number(
                        values[InvestmentsKeys.TODAY_IN_INVESTMENTS].replaceAll(
                          ',',
                          ''
                        )
                      ),
                      Number(event.target.value)
                    ).toFixed()}`;
                    setFieldValue(
                      InvestmentsKeys.COMPOUNDED_INVESTMENTS,
                      formatCurrency(Number(value)).toString().replace('$', '')
                    );
                  }}
                  name={`${InvestmentsKeys.EXPECTED_INVESTMENT_GROWTH_RATE}`}
                  key={`${InvestmentsKeys.EXPECTED_INVESTMENT_GROWTH_RATE}`}
                  label={getKeyTitle(
                    InvestmentsKeys.EXPECTED_INVESTMENT_GROWTH_RATE
                  )}
                  labelClassName='!text-[1rem]'
                />
              </div>
              <div className='grid grid-cols-2 gap-x-10 items-end'>
                <FormikInput
                  valueChanged={(event) => {
                    const value = `${getCompundedInvestments(
                      Number(values[InvestmentsKeys.EXPECT_TO_EXIT]) -
                        Number(event.target.value),
                      Number(
                        values[InvestmentsKeys.TODAY_IN_INVESTMENTS].replaceAll(
                          ',',
                          ''
                        )
                      ),
                      Number(
                        values[InvestmentsKeys.EXPECTED_INVESTMENT_GROWTH_RATE]
                      )
                    ).toFixed()}`;
                    setFieldValue(
                      InvestmentsKeys.YEARS_TO_EXIT,
                      Number(values[InvestmentsKeys.EXPECT_TO_EXIT]) -
                        Number(event.target.value)
                    );
                    setFieldValue(
                      InvestmentsKeys.COMPOUNDED_INVESTMENTS,
                      formatCurrency(Number(value)).toString().replace('$', '')
                    );
                  }}
                  className='!my-auto'
                  name={`${InvestmentsKeys.CURRENT_AGE}`}
                  key={`${InvestmentsKeys.CURRENT_AGE}`}
                  label={getKeyTitle(InvestmentsKeys.CURRENT_AGE)}
                  labelClassName='!text-[1rem]'
                />
                <FormikInput
                  valueChanged={(event) => {
                    const value = `${getCompundedInvestments(
                      Number(event.target.value) -
                        Number(values[InvestmentsKeys.CURRENT_AGE]),
                      Number(
                        values[InvestmentsKeys.TODAY_IN_INVESTMENTS].replaceAll(
                          ',',
                          ''
                        )
                      ),
                      Number(
                        values[InvestmentsKeys.EXPECTED_INVESTMENT_GROWTH_RATE]
                      )
                    ).toFixed()}`;
                    setFieldValue(
                      InvestmentsKeys.YEARS_TO_EXIT,
                      Number(event.target.value) -
                        Number(values[InvestmentsKeys.CURRENT_AGE])
                    );
                    setFieldValue(
                      InvestmentsKeys.COMPOUNDED_INVESTMENTS,
                      formatCurrency(Number(value)).toString().replace('$', '')
                    );
                  }}
                  name={`${InvestmentsKeys.EXPECT_TO_EXIT}`}
                  key={`${InvestmentsKeys.EXPECT_TO_EXIT}`}
                  label={getKeyTitle(InvestmentsKeys.EXPECT_TO_EXIT)}
                  labelClassName='!text-[1rem]'
                />
              </div>
              <div className='grid grid-cols-2 gap-x-10 items-end'>
                <FormikInput
                  disabled
                  className='!mb-0.5'
                  name={`${InvestmentsKeys.YEARS_TO_EXIT}`}
                  key={`${InvestmentsKeys.YEARS_TO_EXIT}`}
                  label={getKeyTitle(InvestmentsKeys.YEARS_TO_EXIT)}
                  labelClassName='!text-[1rem]'
                />

                <FormikInput
                  leadingIcon='$'
                  disabled
                  className='!mb-0.5'
                  name={`${InvestmentsKeys.COMPOUNDED_INVESTMENTS}`}
                  key={`${InvestmentsKeys.COMPOUNDED_INVESTMENTS}`}
                  label={getKeyTitle(InvestmentsKeys.COMPOUNDED_INVESTMENTS)}
                  labelClassName='!text-[1rem]'
                />
              </div>
            </div>

            <div className='pr-7'>
              <BackNextComponent
                backStep={backStep}
                buttonType='submit'
                isLoading={isLoading}
                isNextDisable={false}
                onSaveToDraftClick={() => onSaveToDraftClick(values)}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Investments;
