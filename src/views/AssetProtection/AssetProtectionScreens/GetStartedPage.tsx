import React from 'react';
import Button from 'shared-resources/components/Button/Button';

interface Props {
  onGestartedClick: () => void;
}
const GetStartedPage: React.FC<Props> = ({ onGestartedClick }) => (
  <div className='flex flex-col h-full justify-center items-center gap-16 px-10'>
    <p className='text-center font-medium max-h-[calc(100vh-20rem)] overflow-y-auto scrollbar pr-2'>
      This process will empower you to quickly analyze and protect your most
      important assets.
    </p>
    <div>
      <Button onClick={() => onGestartedClick()} className='px-6 py-2'>
        Get Started
      </Button>
    </div>
  </div>
);

export default GetStartedPage;
