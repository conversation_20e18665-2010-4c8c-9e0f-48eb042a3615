import { SecondaryAdvisorSignUpDTOBytoken } from 'services/api-services/SecondaryAdvisorService';
import { SecondaryAdvisorActionType } from './actions.constants';

export interface VerifySecondaryAdvisorByTokenPayload
  extends VerifySecondaryAdvisorByTokenDTO {
  onError?: () => void;
}
export interface SinUpSecondaryAdvisorByTokenPayload
  extends SecondaryAdvisorSignUpDTOBytoken {
  onSucess?: () => void;
}
export interface VerifySecondaryAdvisorByTokenDTO {
  token: string;
}
export const verifySecondaryAdvisorByToken = (
  payload: VerifySecondaryAdvisorByTokenPayload
) => ({
  type: SecondaryAdvisorActionType.VERIFY_SECONDARY_ADVISOR,
  payload,
});

export const signUpSecondaryAdvisor = (payload: any) => ({
  type: SecondaryAdvisorActionType.SIGNUP_SECONDARY_ADVISOR,
  payload,
});
