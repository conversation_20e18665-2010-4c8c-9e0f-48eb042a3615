import classNames from 'classnames';
import React, { FunctionComponent } from 'react';

interface Props {
  htmlFor?: string;
  label?: string;
  asterisk?: boolean;
  className?: string;
}

const InputLabel: FunctionComponent<Props> = ({
  label,
  htmlFor,
  className,
  asterisk,
}) => {
  const renderLabel = (
    <label
      htmlFor={htmlFor}
      className={classNames('block font-medium leading-none', className)}
    >
      {label}
      {asterisk && <span className='text-red-500 ml-1'>*</span>}
    </label>
  );

  return label ? renderLabel : null;
};

export default InputLabel;
