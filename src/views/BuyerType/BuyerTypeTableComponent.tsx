import React, { memo } from 'react';

interface BuyerTypeRow {
  heading: string;
  value: any;
}

interface BuyerTypeToolResponse {
  headingArray: string[]; // Array for the two heading columns
  rows: BuyerTypeRow[];
}

interface Props {
  response: BuyerTypeToolResponse[];
  heading: string;
  text: string;
  isDealStructure?: boolean;
}

const BuyerTypeTableComponent: React.FC<Props> = ({
  response,
  heading,
  text,
  isDealStructure,
}) => (
  <>
    <div className='flex flex-col w-full space-y-5 mt-5'>
      <span
        className={`text-2xl text-26  ${
          isDealStructure ? 'font-medium' : 'text-blue-01 font-bold '
        } `}
      >
        {heading}
      </span>
      <span className=''>{text}</span>
    </div>

    <div className='flex flex-col space-y-7 mt-5'>
      {response.map((toolResponse) => (
        <div
          className='relative scrollbar overflow-auto  '
          key={toolResponse.headingArray.join('-')} // Join heading array to use as key
        >
          {/* Table Headings */}
          <div
            className={`bg-blue-01 z-10  sticky  overflow-hidden text-white
            rounded-t-lg px-10 `}
          >
            <div className='grid grid-cols-6 h-10 '>
              <div className='col-span-3 text-xl text-center  border-r pt-2 border-gray-02 font-semibold'>
                {toolResponse.headingArray[0]} {/* Display "Buyer Type" */}
              </div>
              <div className='col-span-3 text-xl font-semibold border-l pt-2 border-gray-02 text-center'>
                {toolResponse.headingArray[1]} {/* Display "Value" */}
              </div>
            </div>
          </div>

          {/* Rows Section */}
          <div className='rounded-b-3xl'>
            {(toolResponse?.rows || []).map((businessFunction) => (
              <div
                className='grid grid-cols-6 font-[500] s  text-[0.9rem]'
                key={businessFunction.heading}
              >
                <div className='px-1 2xl:px-3 col-span-3 text-center border-b border-l py-2 border-r border-gray-02'>
                  <span className='text-xs  xl:text-sm'>
                    {businessFunction.heading}
                  </span>
                </div>
                <div className='px-1 2xl:px-3 col-span-3 text-center border-b border-l py-2 border-r flex justify-center items-center border-gray-02'>
                  <span className='text-xs xl:text-sm break-all'>
                    {businessFunction.value}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  </>
);

export default memo(BuyerTypeTableComponent);
