import { AssessmentToolDTO } from 'dtos/assessment-tools.dto';
import { BusinessOwnerDTO, BusinessOwnerTransferRequest } from 'dtos/business-owner.dto';
import { User } from 'models/entities/User';
import { BusinessOwnersFilters } from 'types/business-owner.types';
import { AssessmentTools } from 'types/enum';
import { BusinessOwnerActionType } from './actions.constants';

export interface BusinessOwnerCreateActionPayloadType {
  businessOwnerCreatePayload: BusinessOwnerDTO;
  onSuccess: () => void;
}

export interface UpdateAssessmentToolActionPayloadType {
  id: number;
  assessmentTool: AssessmentToolDTO;
  onSuccess: (message: string) => void;
  onError: (error: string) => void;
}

export const fetchBusinessOwners = (payload: {
  filters: BusinessOwnersFilters;
}) => ({
  type: BusinessOwnerActionType.GET_BUSINESS_OWNERS,
  payload,
});

export const businessOwnerCreate = (
  payload: BusinessOwnerCreateActionPayloadType
) => ({
  type: BusinessOwnerActionType.CREATE_BUSINESS_OWNER,
  payload,
});

export const updateBusinessOwner = (payload: {
  id: string;
  data: BusinessOwnerDTO;
  onSuccess: () => void;
}) => ({
  type: BusinessOwnerActionType.UPDATE_BUSINESS_OWNER_ADVISOR,
  payload,
});
export const deleteBusinessOwner = (payload: {
  id: string;
  onSuccess: () => void;
}) => ({
  type: BusinessOwnerActionType.DELETE_BUSINESS_OWNER,
  payload,
});

export const transferBusinessOwner = (payload: {
  id: string;
  transferRequest: BusinessOwnerTransferRequest;
  onSuccess: () => void;
}) => ({
  type: BusinessOwnerActionType.TRANSFER_BUSINESS_OWNER,
  payload,
});

export const fetchBusinessOwner = (payload: string) => ({
  type: BusinessOwnerActionType.GET_BUSINESS_OWNER,
  payload,
});

export const businessOwnerUpdate = (
  user: User,
  data: BusinessOwnerDTO,
  tool?: AssessmentTools,
  id?: number,
  callback?: any
) => ({
  type: BusinessOwnerActionType.UPDATE_BUSINESS_OWNER,
  payload: { id, data, user, callback, tool },
});

export const updateAssessmentTool = (
  payload: UpdateAssessmentToolActionPayloadType
) => ({
  type: BusinessOwnerActionType.UPDATE_ASSESSMENT_TOOL,
  payload,
});

export const fetchBusinessOwnerIndustries = (payload: { search: string }) => ({
  type: BusinessOwnerActionType.GET_BUSINESS_OWNER_INDUSTRIES,
  payload,
});

export const businessOwnerLearnMore = (payload: {
  tool: AssessmentTools;
  learnMoreSuccess: () => void;
}) => ({
  type: BusinessOwnerActionType.LEARN_MORE,
  payload,
});
