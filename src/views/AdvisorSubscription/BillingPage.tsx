import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';
import ConfirmCancelSubscriptionModal from 'shared-resources/components/BusinessOwner/Modals/ConfirmCancelSubscriptionModal';
import Modal from 'shared-resources/components/Modal/Modal';
import { cancelAdvisorSubscriptionAction } from 'store/actions/payment.action';
import { getAdvisorSubscriptionData } from 'store/selectors/user.selector';
import {
  convertToTitleCase,
  enumTextToOptionsText,
  getFormattedDate,
  getStripeMessageByStatus,
} from 'utils/helpers/Helpers';

const BillingPage: React.FC = () => {
  const advisorSubscriptionInfo: any = useSelector(getAdvisorSubscriptionData);
  const [confirmCancel, setConfirmCancel] = useState(false);
  const dispatch = useDispatch();

  const onConfirmCancel = () => {
    dispatch(cancelAdvisorSubscriptionAction());
    setConfirmCancel(false);
  };

  const isCancelled = advisorSubscriptionInfo?.cancel_at;

  return (
    <div className='container mx-auto p-4'>
      <div className='bg-white rounded-xl px-12.5 py-10 mt-7 shadow-lg'>
        <h1 className='text-xl font-semibold mb-6'>
          Current Subscription Details
        </h1>
        <div className='grid grid-cols-2 gap-y-3'>
          <div className='col-span-1 font-[500]'>Type</div>
          <div className='col-span-1'>
            {convertToTitleCase(advisorSubscriptionInfo.type)}
          </div>

          <div className='col-span-1 font-[500]'> Status</div>
          <div className='col-span-1'>
            {enumTextToOptionsText(advisorSubscriptionInfo.status)}
          </div>

          <div className='col-span-1 font-[500]'>
            {' '}
            {isCancelled ? 'End Date' : 'Renewal Date'}
          </div>
          <div className='col-span-1'>
            {getFormattedDate(advisorSubscriptionInfo.expire_at)}
          </div>

          <div className='col-span-1 font-[500]'>Payment Status</div>
          <div className='col-span-1'>
            {getStripeMessageByStatus(advisorSubscriptionInfo?.stripe_status)}
          </div>
        </div>

        <div className='flex relative justify-end mt-8'>
          <button
            id='cancel-button'
            onClick={() => !isCancelled && setConfirmCancel(true)}
            className={`px-6 py-3 text-white border-transparent bg-blue-01 rounded-lg ${
              isCancelled ? ' bg-blue-300' : ''
            }`}
          >
            Cancel Subscription
          </button>
          {isCancelled && (
            <Tooltip
              anchorSelect='#cancel-button'
              place='top'
              offset={0}
              className='!bg-gray-100 border !px-3 !py-2 !-top-16 !z-10 !w-80 !font-[500] border-blue-01 !text-black-01 !text-[0.9rem] !rounded-lg'
              classNameArrow='border-r  border-b border-r-blue-01 border-b-blue-01 !w-[0.8rem] !h-3 !left-1/2 !-bottom-[0.4rem] '
            >
              {`Your Subscription has already cancelled and valid till date ${getFormattedDate(
                advisorSubscriptionInfo.expire_at
              )} `}
            </Tooltip>
          )}
        </div>
      </div>
      <Modal
        visible={confirmCancel}
        handleVisibility={setConfirmCancel}
        closeOnOutsideClick
      >
        <ConfirmCancelSubscriptionModal
          handleConfirmYourAccountModalVisibility={setConfirmCancel}
          onConfirm={onConfirmCancel}
        />
      </Modal>
    </div>
  );
};

export default BillingPage;
