import React, { FC, memo } from 'react';
import { useSearchParams } from 'react-router-dom';
import { capitalizeURL } from 'utils/helpers/Helpers';
import { AssessmentTools } from 'types/enum';
import notificationSent from '../../assets/notification.svg';
import tooltipData from '../../constant/tooltip-data.json';

type LearnMoreProps = {};

const LearnMore: FC<LearnMoreProps> = () => {
  const [searchParams] = useSearchParams();
  const tool = searchParams.get('tool');
  const formattedTool = tool?.replace('-', '_');
  const toolText = (tooltipData as Record<AssessmentTools, string>)?.[
    formattedTool as AssessmentTools
  ];

  let transitionQuote = '';
  const splittedText = toolText?.split('.');
  if (formattedTool === AssessmentTools.TRANSITION_OBJECTIVES) {
    transitionQuote = splittedText.at(2) as string;
  }

  return (
    <div className='h-[calc(100vh-9.6875rem)]'>
      <h1 className='text-2xl font-semibold'>{tool && capitalizeURL(tool)}</h1>
      <div className='h-[calc(100vh-12.4375rem)] flex flex-col items-center justify-center gap-4 bg-white rounded-xl mt-3 p-10'>
        <img src={notificationSent} alt='Notification Sent' />
        <div className='flex flex-col gap-2 justify-center items-center text-center'>
          <h3 className='max-w-[45rem] text-center'>
            Your Advisor has been notified of your inquiry and will get back to
            you shortly.
          </h3>
          {formattedTool === AssessmentTools.TRANSITION_OBJECTIVES ? (
            <div className='flex flex-col gap-1'>
              <p>{[splittedText?.at(0), splittedText?.at(1)].join('.')}.</p>
              <p>{transitionQuote}</p>
            </div>
          ) : (
            <p className='text-center'>{toolText}</p>
          )}
        </div>
      </div>
    </div>
  );
};
export default memo(LearnMore);
