import { RouteKey } from 'types/enum';
import { SidebarRoutesConfigType } from 'types/routes/routes.type';
import AdvisorSignUp from 'views/layout/AdvisorSignUp';
import ReadinessAssessment from 'views/Readiness-Assesment/ReadinessAssessment';

export const publicRoutes: Array<SidebarRoutesConfigType> = [
  {
    name: 'Public Readiness Assessment',
    key: `/public/${RouteKey.READINESS_ASSESSMENT}`,
    component: ReadinessAssessment,
  },
  {
    name: 'Secondary Advisor SignUp',
    key: `/public/${RouteKey.ADVISOR_INVITE}`,
    component: AdvisorSignUp,
  },
];
