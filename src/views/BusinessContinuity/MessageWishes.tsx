import { <PERSON><PERSON><PERSON>y, Form, Formik } from 'formik';
import React from 'react';
import { useSelector } from 'react-redux';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { BusinessContinuityScreens, UserType } from 'types/enum';
import { getKey } from 'utils/helpers/Helpers';
import * as yup from 'yup';
import { getUserData } from 'store/selectors/user.selector';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import ContinuityQuestionHeader from './shared-components/ContinuityQuestionHeader';

interface MessageWishesProps {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  onSaveToDraftClick: (data: any) => void;
  data: any;
}
const MessageWishes: React.FC<MessageWishesProps> = (props) => {
  const { nextStep, backStep, setData, onSaveToDraftClick, data } = props;
  const loggedInUser = useSelector(getUserData);

  const isLoading = useSelector(getAssessmentToolLoading);
  const validationSchema = yup.object().shape({
    [BusinessContinuityScreens.MESSAGE_WISHES]: yup.array().of(
      yup.object({
        message: yup.string().nullable(),
      })
    ),
  });

  const initialValues = {
    [BusinessContinuityScreens.MESSAGE_WISHES]: data?.[
      BusinessContinuityScreens.MESSAGE_WISHES
    ] || [
      {
        message: '',
      },
    ],
  };

  return (
    <div className='flex flex-col h-full rounded-lg'>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          setData(values?.[BusinessContinuityScreens.MESSAGE_WISHES]);
          nextStep();
        }}
        enableReinitialize
        validateOnMount
      >
        {({ values, isValid }) => (
          <Form className='relative p-5 h-full flex flex-col gap-4 bg-white'>
            <FieldArray name={BusinessContinuityScreens.MESSAGE_WISHES}>
              {({ push }) => (
                <div className='flex flex-col h-full'>
                  <ContinuityQuestionHeader
                    fieldKey={BusinessContinuityScreens.MESSAGE_WISHES}
                    push={push}
                    pushData={{
                      message: '',
                    }}
                    classname='flex-col  !items-end gap-4'
                    buttonText='Message'
                  >
                    <div className='flex flex-col justify-center items-center gap-3 px-10'>
                      <h2 className='font-medium text-center'>
                        In this section you have the ability to add any special
                        messages/wishes that you want communicated to your loved
                        ones and potentially to advisors.
                      </h2>
                      <span className='font-medium text-blue-01 text-center'>
                        Note that these messages/wishes are not binding unless
                        incluced in your specific legal documents
                      </span>
                    </div>
                  </ContinuityQuestionHeader>
                  <div className=' flex flex-col max-h-[calc(100vh-29rem)] pr-2 scrollbar overflow-auto'>
                    {!!values?.[BusinessContinuityScreens.MESSAGE_WISHES]
                      ?.length &&
                      values?.[BusinessContinuityScreens.MESSAGE_WISHES]?.map(
                        (formValues: any, index: number) => (
                          <div
                            key={`${
                              BusinessContinuityScreens.MESSAGE_WISHES
                            }.${getKey(index)}`}
                            className='flex flex-col'
                          >
                            <FormikInput
                              name={`${
                                BusinessContinuityScreens.MESSAGE_WISHES
                              }.${getKey(index)}.message`}
                              key={`${
                                BusinessContinuityScreens.MESSAGE_WISHES
                              }.${getKey(index)}.message`}
                              fieldType='textarea'
                              label='Message'
                              className='h-28'
                              labelClassName='leading-6.5'
                            />
                          </div>
                        )
                      )}
                  </div>
                </div>
              )}
            </FieldArray>
            <BackNextComponent
              backStep={() => {
                backStep();
                setData(values?.[BusinessContinuityScreens.MESSAGE_WISHES]);
              }}
              nextStep={() => {
                if (loggedInUser?.type === UserType.BUSINESS_OWNER) {
                  setData(values?.[BusinessContinuityScreens.MESSAGE_WISHES]);
                  nextStep();
                }
              }}
              fieldKey={BusinessContinuityScreens.MESSAGE_WISHES}
              formValues={values}
              isLoading={isLoading}
              isNextDisable={!isValid}
              onSaveToDraftClick={onSaveToDraftClick}
              buttonText={
                loggedInUser?.type === UserType.BUSINESS_OWNER
                  ? 'Next'
                  : 'Submit'
              }
              buttonType={
                loggedInUser?.type === UserType.BUSINESS_OWNER
                  ? 'button'
                  : 'submit'
              }
            />
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default MessageWishes;
