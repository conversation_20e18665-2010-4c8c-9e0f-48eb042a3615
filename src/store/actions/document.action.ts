import { ReportType } from 'types/enum';
import {
  DocumentsActionType,
  ValuationAmountActionType,
} from './actions.constants';

export interface PresignedUrlsPayload {
  content_type: string;
  file_name: string;
  type: ReportType;
}

export interface UploadDocumentsPayload {
  file: File[];
  id: string;
  onSuccess?: (value: string) => void;
  type: ReportType;
}

export const uploadDocuments = (payload: UploadDocumentsPayload) => ({
  type: DocumentsActionType.UPLOAD_DOCUMENT,
  payload,
});

export const fetchDocuments = (payload: { id?: string; type: ReportType }) => ({
  type: DocumentsActionType.FETCH_DOCUMENTS,
  payload,
});

export const deleteDocument = (payload: {
  id: string;
  type: ReportType;
  reportId: string;
  onSuccess?: () => void;
}) => ({
  type: DocumentsActionType.DELETE_DOCUMENT,
  payload,
});

export const getValuationAmount = (payload: { businessOwnerId?: number }) => ({
  type: ValuationAmountActionType.GET_VALUATION_AMOUNT,
  payload,
});

export const updateValuationAmount = (payload: {
  businessOwnerId: number;
  valuationAmount: number;
  onSuccess?: () => void;
}) => ({
  type: ValuationAmountActionType.UPDATE_VALUATION_AMOUNT,
  payload,
});
