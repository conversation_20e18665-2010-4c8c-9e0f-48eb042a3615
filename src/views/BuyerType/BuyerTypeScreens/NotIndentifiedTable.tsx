import React, { memo, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import Checkbox from 'shared-resources/components/CheckBox/Checkbox';
import Radio from 'shared-resources/components/Radio/Radio';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import { convertToTitleCase } from 'utils/helpers/Helpers';
import {
  BuyerTypeScreens,
  ChoiceOptions,
  NotIdentifiedTableHeaderOptions,
  NotIdentifiedTableResponseType,
  findErrors,
  notIdentifiedTableResponse,
} from '../BuyerTypeConfig';

interface Props {
  data: any;
  handleNextClick?: (values: any) => void;
  handleBackClick?: (values: any) => void;
  handleSaveAsDraftClick?: (values: any) => void;
  isReport?: boolean;
}
const NotIndentifiedTable: React.FC<Props> = ({
  data,
  handleNextClick,
  handleSaveAsDraftClick,
  handleBackClick,
  isReport = false,
}) => {
  const [values, setValues] = useState(
    isReport && data?.[BuyerTypeScreens.NOT_IDENTIFIED_TABLE]
      ? data?.[BuyerTypeScreens.NOT_IDENTIFIED_TABLE]
      : notIdentifiedTableResponse
  );
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);

  const getCheckedKeys = (obj: any) =>
    Object?.keys(obj || {}).filter((key) => obj[key]?.checked);
  useEffect(() => {
    if (data?.[BuyerTypeScreens.NOT_IDENTIFIED_TABLE]) {
      setValues(data?.[BuyerTypeScreens.NOT_IDENTIFIED_TABLE]);
      setSelectedTypes(
        getCheckedKeys(data?.[BuyerTypeScreens.NOT_IDENTIFIED_TABLE])
      );
    }
  }, [data, data?.[BuyerTypeScreens.NOT_IDENTIFIED_TABLE]]);

  const isSubmitting = useSelector(getAssessmentToolLoading);

  const handleNextButton = () => {
    if (selectedTypes[0] === 'I am indifferent regarding Buyer Type') {
      handleNextClick?.(values);
      return;
    }
    if (selectedTypes?.length < 3) {
      toast.error('Please select 3 Buyer Types');
      return;
    }

    const { duplicateKey, errors } = findErrors(values, selectedTypes);

    if (duplicateKey) {
      toast.error(
        `You can't choose ${convertToTitleCase(
          duplicateKey
        )} for multiple Buyer Types.`
      );
    } else if (errors.length) {
      errors.forEach((key) => {
        toast.error(`Choice for ${convertToTitleCase(key)} are required.`);
      });
    } else {
      handleNextClick?.(values);
    }
  };

  const handleCheckboxChange = (key: string) => {
    const isChecked =
      values[key as keyof NotIdentifiedTableResponseType].checked;

    if (key === 'I am indifferent regarding Buyer Type') {
      setSelectedTypes(() => [key]);
      setValues({
        ...notIdentifiedTableResponse,
        [key as keyof NotIdentifiedTableResponseType]: {
          checked: !isChecked,
          choice: undefined,
        },
      });
      if (isChecked) {
        setSelectedTypes((prev) => prev?.filter((item) => item !== key));
      }
      return;
    }

    // Allow unchecking

    if (isChecked) {
      setSelectedTypes((prev) => prev?.filter((item) => item !== key));
    } else if (selectedTypes?.length < 3) {
      setSelectedTypes((prev) => [...prev, key]);
    } else {
      // Prevent additional selections

      return;
    }
    setValues({
      ...values,
      [key as keyof NotIdentifiedTableResponseType]: {
        ...values[key as keyof NotIdentifiedTableResponseType],
        checked: !isChecked,
        choice: undefined,
      },
    });

    // Update values
  };

  // This the table screen
  return (
    <div className='px-10 py-5 font-medium'>
      <p>
        Regardless of whether you have identified a potential buyer, the deal is
        not finalized yet, and it is good to understand the different buyer
        types. Given your current knowledge of buyer types, please rank the ones
        you think would be most appealing to you. (Choose your top three
        preferences by first selecting the three types, and then ranking them.)
      </p>
      <div>
        <div
          className={`pr-1 mt-3 pb-2 ${
            isReport ? '' : 'scrollbar h-[calc(100vh-24.5rem)] overflow-auto '
          }`}
        >
          <div
            className={`bg-blue-01 z-10 tracking-[0.07rem]  grid grid-cols-5 text-white
            rounded-t-md text-[0.9rem] ${isReport ? '' : 'sticky top-0'}`}
          >
            {NotIdentifiedTableHeaderOptions.map((h, index) => (
              <div
                key={h}
                className={`px-5 flex items-center border-r  border-gray-02 justify-center py-4 h-full ${
                  index === 0 && 'rounded-tl-md col-span-2'
                } ${index === 3 && ' border-r-0 rounded-t-md'} `}
              >
                <span className='text-center'>{h}</span>
              </div>
            ))}
          </div>
          {Object.keys(values).map((key) => (
            <div
              key={key}
              className='grid grid-cols-10'
              hidden={key === 'selectedTypes'}
            >
              <div
                className={`col-span-4 text-[0.9rem] flex items-center gap-3 px-5 py-1 border-b border-r border-l border-gray-02  
             `}
              >
                <Checkbox
                  disabled={
                    (!values[key as keyof NotIdentifiedTableResponseType]
                      .checked &&
                      selectedTypes?.length >= 3) ||
                    (!values[key as keyof NotIdentifiedTableResponseType]
                      .checked &&
                      selectedTypes[0] ===
                        'I am indifferent regarding Buyer Type')
                  }
                  value={
                    values[key as keyof NotIdentifiedTableResponseType].checked
                  }
                  className='mb-1 cursor-pointer'
                  onChange={() => {
                    if (!isReport) {
                      handleCheckboxChange(key);
                    }
                  }}
                />
                <span>{key}</span>
              </div>
              <Radio
                showLabel={false}
                className={`grid grid-cols-3 col-span-6 w-full !gap-x-0 items-center border-b justify-center h-full border-gray-02 ${
                  !values[key as keyof NotIdentifiedTableResponseType]
                    .checked && 'pointer-events-none'
                }`}
                className2='flex items-center  border-r !border-gray-02 !h-[3.2rem]'
                inputClassName='!h-5 !mx-auto !cursor-pointer'
                selected={
                  values[key as keyof NotIdentifiedTableResponseType].choice
                }
                options={ChoiceOptions}
                onChange={(value) => {
                  if (
                    key !== 'I am indifferent regarding Buyer Type' &&
                    !isReport
                  ) {
                    setValues({
                      ...values,
                      [key as keyof NotIdentifiedTableResponseType]: {
                        ...values[key as keyof NotIdentifiedTableResponseType],
                        choice: value,
                      },
                    });
                  }
                }}
              />
            </div>
          ))}
        </div>
      </div>
      {!isReport && (
        <div className='2xl:mt-3'>
          <BackNextComponent
            backStep={() => handleBackClick?.(values)}
            nextStep={handleNextButton}
            buttonType='submit'
            isLoading={isSubmitting}
            isNextDisable={false}
            onSaveToDraftClick={() => {
              handleSaveAsDraftClick?.(values);
            }}
          />
        </div>
      )}
    </div>
  );
};

export default memo(NotIndentifiedTable);
