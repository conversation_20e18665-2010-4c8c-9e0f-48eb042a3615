import { ReportDTO } from 'dtos/report.dto';
import { AssessmentTools } from 'types/enum';
import { AssessmentReportActionType } from './actions.constants';

export interface ReportPayload extends ReportDTO {
  onError?: () => void;
}

export const fetchAssessmentReport = (payload: ReportPayload) => ({
  type: AssessmentReportActionType.FETCH_REPORT,
  payload,
});

export const fetchAssessmentReportByBusinessOwner = (payload: {
  tool: AssessmentTools;
  onError?: () => void;
}) => ({
  type: AssessmentReportActionType.FETCH_REPORT_BY_BUSINESS_OWNER,
  payload,
});

export const sendAssessmentReport = (payload: {
  businessOwnerId: number;
  assessmentTool: AssessmentTools;
}) => ({
  type: AssessmentReportActionType.SEND_REPORT,
  payload,
});
