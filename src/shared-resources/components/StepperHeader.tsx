import React from 'react';
import TabButton from './TabButton/TabButton';

interface TabData {
  name: string;
  id: number | string;
  isActive: boolean;
  firstButton?: boolean;
  isCompleted?: boolean;
  theme: 'primary' | 'success' | 'default';
}

interface Props {
  tabData: TabData[];
  onHandleTabChange?: (id: number | string) => void;
}

const StepperHeader: React.FC<Props> = (props) => {
  const { tabData, onHandleTabChange } = props;

  const handleTabChange = (id: number | string) => {
    if (onHandleTabChange) {
      onHandleTabChange(id);
    }
  };

  return (
    <div
      id='readiness-header'
      className='flex h-[4.375rem] w-full justify-between items-center px-3.5 mb-4 bg-blue-02'
    >
      {tabData?.map((a, index) => (
        <TabButton
          key={a.id}
          title={a.name}
          id={a.id}
          isActive={a.isActive}
          firstButton={index === 0}
          theme={a.theme}
          onHandleTabChange={handleTabChange}
        />
      ))}
    </div>
  );
};
export default StepperHeader;
