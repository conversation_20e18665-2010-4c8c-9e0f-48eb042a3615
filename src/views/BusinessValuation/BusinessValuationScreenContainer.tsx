import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import WithComments from 'shared-resources/components/ToolComments/WithComments';
import {
  fetchBusinessOwnerAssessment,
  fetchOwnAssessment,
  updateBusinessOwnerAssessment,
  updateOwnAssessment,
} from 'store/actions/assessment-tool.action';
import { businessOwnerUpdate } from 'store/actions/business-owner.action';
import { resetProgressStatus } from 'store/reducers/assessment-tool.reducer';
import {
  getAssessmentToolLoading,
  getAssessmentToolReEvaluate,
  getAssessmentToolResponse,
  getAssessmentToolStatus,
  getBusinessOwnerProperties,
} from 'store/selectors/assessment-tool.selector';
import { getUserData } from 'store/selectors/user.selector';
import {
  AssessmentResponseType,
  AssessmentToolProgressStatus,
  AssessmentTools,
  BusinessValuationScreens,
  RouteKey,
  UserType,
} from 'types/enum';
import { getUserRouteType, thankYouPageContent } from 'utils/helpers/Helpers';
import ThankYouPage from 'views/layout/ThankYouPage';
import {
  getDate,
  getHeaderTitle,
  numberToScreenObject,
  screenToNumberObject,
} from './BusinessValuationConfig';
import BusinessValuationHeader from './BusinessValuationHeader';
import { getScreen } from './BusinessValuationScreenConfig';

const BusinessValuationScreenContainer: React.FC = () => {
  const { id } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const savedDraftData: any = useSelector(getAssessmentToolResponse);
  const progressStatus = useSelector(getAssessmentToolStatus);
  const businessOwnerProperties: any = useSelector(getBusinessOwnerProperties);
  const [businessValuationData, setBusinessValuationData] = useState<any>();
  const [submitType, setSubmitType] = useState<AssessmentResponseType | null>(
    null
  );

  const isLoading = useSelector(getAssessmentToolLoading);

  const [currentScreen, setCurrentScreen] = useState<{
    screen: BusinessValuationScreens | undefined;
  }>();

  const backNextClickHandler = (nextScreen: BusinessValuationScreens) => {
    setCurrentScreen({ screen: nextScreen });
  };
  const assessmentReEvaluate = useSelector(getAssessmentToolReEvaluate);
  const loggedInUser = useSelector(getUserData);
  const dispatch = useDispatch();

  useEffect(() => {
    if (
      progressStatus === AssessmentToolProgressStatus.COMPLETED &&
      assessmentReEvaluate &&
      !isLoading
    ) {
      dispatch(resetProgressStatus());
    }
  }, [assessmentReEvaluate, loggedInUser, progressStatus]);

  useEffect(() => {
    if (businessOwnerProperties?.payment_received) {
      if (
        currentScreen?.screen !== BusinessValuationScreens.BASIC_INFORMATION
      ) {
        const screenNumber: number = savedDraftData?.saved_screen;
        if (
          progressStatus !== AssessmentToolProgressStatus.COMPLETED &&
          screenNumber &&
          !assessmentReEvaluate
        ) {
          setCurrentScreen({
            screen: numberToScreenObject[screenNumber],
          });
        } else {
          setCurrentScreen({
            screen: BusinessValuationScreens.BASIC_INFORMATION,
          });
        }
      }
    } else {
      setCurrentScreen({
        screen: BusinessValuationScreens.PAYMENT_SCREEN,
      });
    }
  }, [savedDraftData, progressStatus, businessOwnerProperties]);

  useEffect(() => {
    const screen = new URLSearchParams(currentScreen as Record<string, string>);
    setSearchParams(screen);
  }, [currentScreen, setSearchParams, searchParams]);

  const updateBusinessOwner = (values: any) => {
    dispatch(
      businessOwnerUpdate(
        loggedInUser!,
        {
          tool: AssessmentTools.BUSINESS_VALUATION,
          business_name: values.business_name,
          first_name: values.first_name,
          last_name: values.last_name,
          phone: values.phone,
          business_start_date: values?.business_start_date
            ? getDate(new Date(values?.business_start_date))
            : '',
          email: values?.email,
          employee_count: values?.employee_count,
          business_type: values?.business_type,
          street_address: values?.street_address,
          zip_code: values?.zip_code,
        },
        AssessmentTools.BUSINESS_VALUATION,
        +id!,
        setCurrentScreen
      )
    );
  };

  const handleFetchAssessmentError = () => {
    navigate(
      `/${getUserRouteType(loggedInUser?.type as UserType)}/${
        RouteKey.DASHBOARD
      }`
    );
  };
  useEffect(() => {
    if (id && loggedInUser?.type === UserType.ADVISOR) {
      dispatch(
        fetchBusinessOwnerAssessment({
          tool: AssessmentTools.BUSINESS_VALUATION,
          businessOwnerId: +id!,
          onError: handleFetchAssessmentError,
        })
      );
    }
    if (loggedInUser?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchOwnAssessment({
          tool: AssessmentTools.BUSINESS_VALUATION,
          onError: handleFetchAssessmentError,
        })
      );
    }
  }, []);

  useEffect(() => {
    if (
      progressStatus !== AssessmentToolProgressStatus.COMPLETED ||
      assessmentReEvaluate
    ) {
      setBusinessValuationData(savedDraftData);
    }
  }, [savedDraftData]);

  const handleFormSubmit = (saveAsDraft: boolean) => {
    let data = {
      ...businessValuationData,
      basic_information: {
        type_of_ownership:
          businessValuationData?.basic_information?.type_of_ownership,
        reason_for_valuation:
          businessValuationData?.basic_information?.reason_for_valuation,
      },
    };

    if (assessmentReEvaluate && currentScreen?.screen) {
      data = {
        ...businessValuationData,
        saved_screen: screenToNumberObject[currentScreen.screen],
      };
    }

    if (!saveAsDraft && loggedInUser?.type === UserType.BUSINESS_OWNER) {
      data = {
        ...data,
        saved_screen:
          screenToNumberObject[BusinessValuationScreens.BALANCE_SHEET_DATA_3],
      };
    }

    if (loggedInUser?.type === UserType.ADVISOR && id) {
      dispatch(
        updateBusinessOwnerAssessment({
          businessOwnerId: +id!,
          tool: AssessmentTools.BUSINESS_VALUATION,
          assessment_response: data,
          submit_type: saveAsDraft
            ? AssessmentResponseType.DRAFT
            : AssessmentResponseType.COMPLETE,
        })
      );
    } else {
      dispatch(
        updateOwnAssessment({
          tool: AssessmentTools.BUSINESS_VALUATION,
          assessment_response: data,
          submit_type: saveAsDraft
            ? AssessmentResponseType.DRAFT
            : AssessmentResponseType.SUBMIT,
          onSuccess: () =>
            !saveAsDraft &&
            navigate(`${UserType.BUSINESS_OWNER}/${RouteKey.DASHBOARD}`),
        })
      );
    }

    setSubmitType(null);
  };

  useEffect(() => {
    if (submitType === AssessmentResponseType.DRAFT) {
      handleFormSubmit(true);
    } else if (submitType === AssessmentResponseType.COMPLETE) {
      handleFormSubmit(false);
    }
  }, [businessValuationData]);

  if (isLoading) {
    return <Spinner />;
  }

  if (progressStatus === AssessmentToolProgressStatus.COMPLETED) {
    return (
      <ThankYouPage
        pageContent={thankYouPageContent(
          'Business Valuation Overview',
          loggedInUser?.type || UserType.BUSINESS_OWNER,
          `${businessOwnerProperties?.first_name ?? ''} ${
            businessOwnerProperties?.last_name ?? ''
          }`
        )}
        loggedInUserData={loggedInUser}
        isPasswordSet={false}
      />
    );
  }

  return (
    <div className='flex flex-col gap-3 h-full'>
      <BusinessValuationHeader
        secondaryTitle={getHeaderTitle(currentScreen?.screen)}
      />
      <WithComments
        containerClassname='h-full'
        classname='h-full bg-white'
        tool={AssessmentTools.BUSINESS_VALUATION}
      >
        {getScreen(
          currentScreen!,
          loggedInUser!,
          businessValuationData,
          setBusinessValuationData,
          backNextClickHandler,
          setSubmitType,
          navigate,
          businessOwnerProperties,
          updateBusinessOwner
        )}
      </WithComments>
    </div>
  );
};

export default BusinessValuationScreenContainer;
