// Auth
export enum AuthActionType {
  LOGIN = 'auth/login',
  LOGOUT = 'auth/logout',
  LOGIN_COMPLETED = 'auth/login/completed',
  LOGIN_ERROR = 'auth/login/error',
  GET_MFA_CODE = 'auth/mfa/code',
  RESEND_MFA_CODE = 'auth/mfa/resend-code',

  FETCH_ME = 'auth/fetch/me',
  FETCH_ME_COMPLETED = 'auth/fetch/me/completed',
  FETCH_ME_ERROR = 'auth/fetch/me/error',
}
export enum UserActionType {
  UPDATE_USER_PROFILE = 'user/profile/update',
  SETUP_MFA = 'user/mfa/setup',
  SETUP_MFA_COMPLETED = 'user/mfa/setup/completed',
  SETUP_MFA_ERROR = 'user/mfa/setup/error',
  VERIFY_MFA_SETUP = 'user/mfa/verify-setup',
  VERIFY_MFA_SETUP_COMPLETED = 'user/mfa/verify-setup/completed',
  VERIFY_MFA_SETUP_ERROR = 'user/mfa/verify-setup/error',
  VERIFY_MFA_TOTP = 'user/mfa/verify-totp',
}

export enum BusinessOwnerActionType {
  GET_BUSINESS_OWNERS = 'business-owner/list',
  GET_BUSINESS_OWNER = 'business-owner/fetch',
  CREATE_BUSINESS_OWNER = 'business-owner/create',
  UPDATE_BUSINESS_OWNER = 'business-owner/update',
  UPDATE_BUSINESS_OWNER_ADVISOR = 'business-owner/update/advisor',
  DELETE_BUSINESS_OWNER = 'business-owner/delete',
  TRANSFER_BUSINESS_OWNER = 'business-owner/transfer',
  // Assessment Tool
  UPDATE_ASSESSMENT_TOOL = 'business-owner/assessment-tool/update',
  // Industries
  GET_BUSINESS_OWNER_INDUSTRIES = 'business-owner/industries',
  // Learn More
  LEARN_MORE = 'business-owner/learn-more',
}

export enum AdvisorActionType {
  GET_ADVISORS = 'advisor/list',
  GET_ADVISOR = 'advisor/fetch',
  CREATE_ADVISOR = 'advisor/create',
  UPDATE_ADVISOR = 'advisor/update',
  DELETE_ADVISOR = 'advisor/delete',
  ACCESS_ADVISOR = 'advisor/access',
}

export enum EnterpriseAdminActionType {
  GET_ENTERPRISE_ADMINS = 'enterprise-admin/list',
  GET_ENTERPRISE_ADMIN = 'enterprise-admin/fetch',
  CREATE_ENTERPRISE_ADMIN = 'enterprise-admin/create',
  UPDATE_ENTERPRISE_ADMIN = 'enterprise-admin/update',
  DELETE_ENTERPRISE_ADMIN = 'enterprise-admin/delete',
}

export enum EnterpriseActionType {
  GET_ENTERPRISES = 'enterprise/list',
  GET_ENTERPRISE = 'enterprise/fetch',
}


export enum AdvisorInviteActionType {
  INVITE_ADVISOR = 'advisor/invite',
  GET_INVITES = 'advisor/invites',
  CANCEL_OR_REVOKE_INVITE = 'delete/invite',
}

export enum DocumentsActionType {
  UPLOAD_DOCUMENT = 'document/upload',
  SAVE_DOCUMENT = 'document/save',
  FETCH_DOCUMENTS = 'document/list',
  DELETE_DOCUMENT = 'document/delete',
}

export enum AssessmentToolActionType {
  GET_OWN_ASSESSMENT = 'assessment-tool/fetch',
  UPDATE_OWN_ASSESSMENT = 'assessment-tool/update',
  GET_BUSINESS_OWNER_ASSESSMENT = 'assessment-tool/business-owner/get',
  UPDATE_BUSINESS_OWNER_ASSESSMENT = 'assessment-tool/business-owner/update',
  GET_PUBLIC_ASSESSMENT_BY_TOKEN = 'assessment-tool/public/fetch',
  UPDATE_PUBLIC_ASSESSMENT_BY_TOKEN = 'assessment-tool/public/update',
  UPDATE_ASSESSMENT_RE_EVALUATE = 'assessment-tool/update/re-evaluate',
  OPEN_COUNT_INCREMENT = 'assessment-tool/open-count/increment',
}
export enum ForgotPasswordActionType {
  FORGOT_PASSWORD = 'forgot-password/forgot-password',
  // Reset Password
  RESET_PASSWORD = 'forgot-password/reset-password',
  SET_PUBLIC_PASSWORD = 'public-password/set',
}

export enum AssessmentReportActionType {
  FETCH_REPORT = 'report/fetch',
  FETCH_REPORT_BY_BUSINESS_OWNER = 'report/fetch/by/business-owner',
  SEND_REPORT = 'report/send',
}

export enum FollowUpActionType {
  GET_FOLLOW_UPS = 'follow-up/list',
  CREATE_FOLLOW_UP = 'follow-up/create',
  CANCEL_UPCOMING_FOLLOW_UP = 'follow-up/upcoming/cancel',
}

export enum PaymentActionType {
  GET_PAYMENT_LINK = 'get/payment/link',
  GET_ADVISOR_PAYMENT_LINK = 'get/advisor/payment/link',
  CANCEL_ADVISOR_SUBSCRIPTION = 'cancel/advisor/subscription',
  RETRY_ADVISOR_PAYMENT = 'retry/advisor/payment',
  SEND_PAYMENT_REMINDER = 'SEND_PAYMENT_REMINDER',
}

export enum DocumentActionType {
  UPLOAD_DOCUMENT = 'upload/document',
}

export enum SecondaryAdvisorActionType {
  VERIFY_SECONDARY_ADVISOR = 'verify/secondary-advisor',
  SIGNUP_SECONDARY_ADVISOR = 'signup/secondary-advisor',
}

export enum ToolCommentActionType {
  FETCH_COMMENTS = 'tool-comment/fetch',
  CREATE_COMMENT = 'tool-comment/create',
  UPDATE_COMMENT = 'tool-comment/update',
  DELETE_COMMENT = 'tool-comment/delete',
}

// Add these to the existing action types
export enum ReportActionType {
  CREATE_REPORT = 'CREATE_REPORT',
  CREATE_REPORT_SUCCESS = 'CREATE_REPORT_SUCCESS',
  CREATE_REPORT_ERROR = 'CREATE_REPORT_ERROR',
  FETCH_REPORTS = 'FETCH_REPORTS',
  FETCH_REPORT = 'FETCH_REPORT',
  FETCH_REPORTS_SUCCESS = 'FETCH_REPORTS_SUCCESS',
  FETCH_REPORTS_ERROR = 'FETCH_REPORTS_ERROR',
  SEND_REPORT_TO_BO = 'SEND_REPORT_TO_BO',
  SEND_REPORT_TO_BO_SUCCESS = 'SEND_REPORT_TO_BO_SUCCESS',
  SEND_REPORT_TO_BO_ERROR = 'SEND_REPORT_TO_BO_ERROR',
}

export enum ValuationAmountActionType {
  GET_VALUATION_AMOUNT = 'GET_VALUATION_AMOUNT',
  UPDATE_VALUATION_AMOUNT = 'UPDATE_VALUATION_AMOUNT',
}
