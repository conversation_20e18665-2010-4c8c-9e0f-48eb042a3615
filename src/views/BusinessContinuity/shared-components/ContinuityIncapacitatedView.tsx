import React from 'react';
import { BusinessContinuityScreens } from 'types/enum';
import {
  getKey,
  isRequiredFieldDependingOnValidationSchema,
  stringifyNumber,
} from 'utils/helpers/Helpers';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { FieldArray, Formik, Form, FormikErrors } from 'formik';
import * as yup from 'yup';
import { useSelector } from 'react-redux';
import { getAssessmentToolLoading } from 'store/selectors/assessment-tool.selector';
import Button from 'shared-resources/components/Button/Button';
import { capitalize } from 'lodash';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import ContinuityQuestionHeader from './ContinuityQuestionHeader';
import ContuinuityRowView from './ContuinuityRowView';
import { formEmpty } from '../Report/continuityReportHelper';

interface ContinuityIncapacitatedViewProps {
  nextStep: () => void;
  backStep: () => void;
  setData: (data: any) => void;
  onSaveToDraftClick: (data: any) => void;
  data: any;
  question: string;
  screen: BusinessContinuityScreens;
}
const ContinuityIncapacitatedView: React.FC<
  ContinuityIncapacitatedViewProps
> = (props) => {
  const {
    nextStep,
    backStep,
    setData,
    onSaveToDraftClick,
    data,
    question,
    screen,
  } = props;

  const isLoading = useSelector(getAssessmentToolLoading);
  const itemSchema = yup.object().shape({
    priority: yup.string(),
    name: yup.string().required('Name is required'),
    relationship: yup.string().required('Realtionship is required'),
    email: yup
      .string()
      .required('Email is required')
      .email('This must be a valid e-mail'),
    phone: yup
      .string()
      .matches(
        /^(\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/,
        'Phone number not from US region'
      )
      .required('Phone is required'),
    specific_instructions: yup.string().nullable(),
  });
  const validationSchema = yup
    .object()
    .shape({
      [screen]: yup.array().of(itemSchema),
    })
    .required();

  const initialValues = {
    [screen]: data?.[screen] || [
      {
        priority: '',
        name: '',
        relationship: '',
        email: '',
        phone: '',
        specific_instructions: '',
        saved: false,
      },
    ],
  };

  return (
    <div className='flex rounded-lg flex-col h-full'>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          setData(values?.[screen]);
          nextStep();
        }}
        enableReinitialize
        validateOnMount
      >
        {({ values, setFieldValue, isValid, errors }) => {
          const isFormEmpty = formEmpty(values?.[screen]);
          return (
            <Form className='relative p-5 h-full flex flex-col gap-4 bg-white'>
              <FieldArray name={screen}>
                {({ push, remove }) => (
                  <div className='flex flex-col h-full'>
                    <ContinuityQuestionHeader
                      fieldKey={screen}
                      push={push}
                      pushData={{
                        priority: '',
                        name: '',
                        relationship: '',
                        email: '',
                        phone: '',
                        specific_instructions: '',
                        saved: false,
                      }}
                    >
                      <h2 className='font-medium mr-8'>{question}</h2>
                    </ContinuityQuestionHeader>
                    <div className=' flex flex-col max-h-[calc(100vh-23rem)] h-full  pr-1 scrollbar overflow-auto'>
                      <ContuinuityRowView
                        fieldKey={screen}
                        formValues={values}
                        remove={remove}
                        initialValues={{
                          priority: '',
                          name: '',
                          relationship: '',
                          email: '',
                          phone: '',
                          specific_instructions: '',
                          saved: false,
                        }}
                        isDragDisabled={false}
                      />
                      <div className='flex flex-col'>
                        {!!values?.[screen]?.length &&
                          values?.[screen]?.map(
                            (formValues: any, index: number) =>
                              !formValues?.saved && (
                                <div
                                  key={`${getKey(index)}_form`}
                                  className='flex flex-col border border-gray-02 p-4 mt-1 rounded-b-lg'
                                >
                                  <h2 className='font-semibold text-blue-01'>
                                    {
                                      values?.[screen]?.[getKey(index)]
                                        ?.priority
                                    }
                                  </h2>
                                  <div className='grid grid-cols-2 gap-y-2 gap-x-8 mt-5  overflow-auto  scrollbar text-[1rem]'>
                                    <FormikInput
                                      name={`${screen}.${getKey(index)}.name`}
                                      key={`${screen}.${getKey(index)}.name`}
                                      label='Name'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${screen}.${getKey(index)}.name`
                                      )}
                                    />
                                    <FormikInput
                                      name={`${screen}.${getKey(
                                        index
                                      )}.relationship`}
                                      key={`${screen}.${getKey(
                                        index
                                      )}.relationship`}
                                      label='Relationship'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${screen}.${getKey(
                                          index
                                        )}.relationship`
                                      )}
                                    />
                                    <FormikInput
                                      name={`${screen}.${getKey(index)}.email`}
                                      key={`${screen}.${getKey(index)}.email`}
                                      label='Email'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        'email'
                                      )}
                                    />
                                    <FormikInput
                                      name={`${screen}.${getKey(index)}.phone`}
                                      key={`${screen}.${getKey(index)}.phone`}
                                      label='Phone'
                                      labelClassName='mb-1'
                                      className='!h-10'
                                      asterisk={isRequiredFieldDependingOnValidationSchema(
                                        validationSchema,
                                        `${screen}.${getKey(index)}.phone`
                                      )}
                                    />
                                    <div className='col-span-2'>
                                      <FormikInput
                                        name={`${screen}.${getKey(
                                          index
                                        )}.specific_instructions`}
                                        key={`${screen}.${getKey(
                                          index
                                        )}.specific_instructions`}
                                        fieldType='textarea'
                                        label='Any Specific Instructions'
                                        className='h-28'
                                        labelClassName='leading-6.5'
                                      />
                                    </div>
                                  </div>
                                  <div className='flex justify-end'>
                                    <Button
                                      onClick={() => {
                                        setFieldValue(
                                          `${screen}.${getKey(index)}.saved`,
                                          true
                                        );
                                        setFieldValue(
                                          `${screen}.${getKey(index)}.priority`,
                                          capitalize(stringifyNumber(index))
                                        );
                                      }}
                                      className='px-4 py-2'
                                      disabled={
                                        !!(
                                          errors?.[
                                            screen
                                          ] as FormikErrors<any>[]
                                        )?.[index]
                                      }
                                    >
                                      Save
                                    </Button>
                                  </div>
                                </div>
                              )
                          )}
                      </div>
                    </div>
                  </div>
                )}
              </FieldArray>
              <BackNextComponent
                backStep={() => {
                  backStep();
                  setData(values?.[screen]);
                }}
                nextStep={() => {
                  setData(values?.[screen]);
                  nextStep();
                }}
                fieldKey={screen}
                formValues={values}
                isLoading={isLoading}
                isNextDisable={isFormEmpty ? false : !isValid}
                onSaveToDraftClick={onSaveToDraftClick}
              />
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default ContinuityIncapacitatedView;
